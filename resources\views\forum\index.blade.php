<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? 'Форум - Echoes of Eternity' }}</title>

    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/forum/forum-main.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">
        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources ?? ['current_hp' => $userProfile->hp, 'current_mp' => $userProfile->mp]" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="false" :unreadMessagesCount="0" :hasBrokenItems="false"
                :brokenItemsCount="0" />
        </x-layout.hp-mp-bar>

        {{-- Валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" />



        {{-- Навигация форума --}}
        <x-forum.navigation :breadcrumbs="$breadcrumbs" title="Форум" />

        {{-- Сообщения --}}
        <x-layout.game-flash-messages />

        {{-- Основной блок форума --}}
        <div class="p-2 mb-3 bg-[#38352c] rounded-lg border border-[#a6925e]">
            {{-- Секция новостей - всегда наверху --}}
            <div class="mb-3">
                <a href="{{ route('forum.news.index', ['type' => 'news']) }}"
                    class="block bg-gradient-to-r from-[#412e1d] to-[#614834] p-2 rounded border border-[#a6925e] hover:border-[#e5b769] transition-all duration-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div
                                class="bg-[#e5b769] text-[#2f2d2b] w-8 h-8 rounded-full flex items-center justify-center mr-2">
                                <span class="text-lg">📢</span>
                            </div>
                            <div>
                                <h3 class="text-[#e5b769] font-semibold">Новости игры</h3>
                                <p class="text-[#d9d3b8] text-xs">Обновления, события и важные анонсы</p>
                            </div>
                        </div>
                        <div class="text-[#d9d3b8] text-xs whitespace-nowrap ml-2">
                            <span>Тем: {{ \App\Models\Forum\News::where('is_announcement', false)->count() }}</span>
                        </div>
                    </div>
                </a>
            </div>

            {{-- Секция объявлений --}}
            <div class="mb-3">
                <a href="{{ route('forum.news.index', ['type' => 'announcements']) }}"
                    class="block bg-gradient-to-r from-[#3e2d42] to-[#593d5e] p-2 rounded border border-[#a6925e] hover:border-[#e5b769] transition-all duration-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div
                                class="bg-[#bd4949] text-white w-8 h-8 rounded-full flex items-center justify-center mr-2">
                                <span class="text-lg">📜</span>
                            </div>
                            <div>
                                <h3 class="text-[#e5b769] font-semibold">Объявления</h3>
                                <p class="text-[#d9d3b8] text-xs">Важные объявления от администрации</p>
                            </div>
                        </div>
                        <div class="text-[#d9d3b8] text-xs whitespace-nowrap ml-2">
                            <span class="px-2 py-0.5 bg-[#bd4949] text-white rounded">Важно</span>
                        </div>
                    </div>
                </a>
            </div>

            {{-- Список категорий форума --}}
            <x-forum.category-list :categories="$categories" />

            {{-- Статистика форума --}}
            <div class="mt-4 bg-[#2d2a22] p-2 rounded border border-[#514b3c] text-xs text-[#d9d3b8]">
                <p>Всего тем: {{ \App\Models\Forum\Topic::count() + \App\Models\Forum\News::count() }}</p>
                <p>Онлайн Администрация:
                    @php
                        $adminMods = \App\Models\User::whereIn('role', ['admin', 'moderator'])
                            ->get();
                    @endphp

                    @if($adminMods->count() > 0)
                        @foreach($adminMods as $user)
                            <span
                                class="text-{{ $user->role === 'admin' ? '[#e5b769]' : '[#a6925e]' }}">{{ $user->name }}</span>{{ !$loop->last ? ', ' : '' }}
                        @endforeach
                    @else
                        нет администраторов
                    @endif
                </p>
                <p>Последнее сообщение:
                    @if($lastPost = \App\Models\Forum\Post::latest()->first())
                        {{ $lastPost->created_at->locale('ru')->diffForHumans() }} от {{ $lastPost->user->name }}
                    @else
                        нет сообщений
                    @endif
                </p>
            </div>
        </div>
    </div>

    {{-- Нижние кнопки навигации --}}
    <x-layout.navigation-buttons />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount ?? 0" />
</body>

</html>
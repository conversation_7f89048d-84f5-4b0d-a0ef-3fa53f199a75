@props(['userEffects' => collect()])

<div class="active-effects">
    @php
        $isStunned = $userEffects->contains(function ($effect) {
            return $effect->skill_id == 10 && $effect->isActive();
        });
    @endphp

    @php
        // Фильтруем только активные эффекты
        $activeEffects = $userEffects->filter(function ($effect) {
            return $effect->isActive();
        });
    @endphp

    @if ($activeEffects->isEmpty())
        <p class="text-gray-400">Нет активных эффектов.</p>
    @else
        <div class="flex flex-row flex-wrap pl-0 ml-0 gap-1 items-start">
            @foreach ($activeEffects as $effect)
                @if($effect->remaining_duration > 0)
                    <div class="flex flex-col items-center justify-center text-center w-4">
                        @if($effect->skill)
                            <img src="{{ asset($effect->skill->icon ?? 'assets/default_effect.png') }}"
                                alt="{{ $effect->skill->name ?? 'Неизвестный эффект' }}"
                                class="w-4 h-4 {{ $effect->skill_id == 10 ? 'animate-pulse' : ($effect->skill->type === 'debuff' ? 'text-red-400' : 'text-green-400') }}">
                        @elseif($effect->effect_type == 'mine_detection')
                            {{-- Специальная иконка для дебафа рудников --}}
                            <img src="{{ asset('assets/obelisk_mark.png') }}" 
                                alt="Замечен в рудниках" 
                                class="w-4 h-4 text-red-400 animate-pulse" 
                                title="{{ $effect->effect_name ?? 'Замечен в рудниках' }}"
                                style="filter: hue-rotate(45deg) brightness(0.8);"{{-- Делаем иконку более красноватой --}}>
                        @else
                            <img src="{{ asset('assets/default_effect.png') }}" alt="Неизвестный эффект" class="w-4 h-4 text-gray-400">
                        @endif
                        <span
                            class="text-[10px] w-full {{ $effect->skill_id == 10 ? 'text-yellow-400' : ($effect->effect_type == 'mine_detection' ? 'text-red-400' : (($effect->skill && $effect->skill->type === 'debuff') ? 'text-red-400' : 'text-green-400')) }}">
                            {{ round($effect->remaining_duration) }}с
                        </span>
                    </div>
                @endif
            @endforeach
        </div>
    @endif
</div>

{{-- Возвращаем переменную isStunned для использования в родительском шаблоне --}}
@php
    $__isStunned = $isStunned ?? false;
@endphp
<?php

/**
 * Скрипт настройки системы автоатак мобов в рудниках
 * 
 * Выполняет:
 * 1. Проверку существования таблиц
 * 2. Запуск миграций
 * 3. Проверку структуры БД
 * 4. Тестирование системы
 * 
 * Запуск: php setup_mine_detection_system.php
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Models\MineMark;

echo "🔧 НАСТРОЙКА СИСТЕМЫ АВТОАТАК МОБОВ В РУДНИКАХ\n";
echo "==============================================\n\n";

try {
    // 1. Проверка подключения к базе данных
    echo "1️⃣ Проверка подключения к базе данных...\n";
    
    try {
        DB::connection()->getPdo();
        echo "   ✅ Подключение к PostgreSQL успешно\n";
        
        $dbName = DB::connection()->getDatabaseName();
        echo "   📊 База данных: {$dbName}\n";
    } catch (Exception $e) {
        throw new Exception("❌ Не удалось подключиться к базе данных: " . $e->getMessage());
    }
    
    // 2. Проверка существующих таблиц
    echo "\n2️⃣ Проверка существующих таблиц...\n";
    
    $requiredTables = [
        'users' => 'Пользователи',
        'user_profiles' => 'Профили пользователей',
        'locations' => 'Локации',
        'mine_locations' => 'Локации рудников',
        'mobs' => 'Мобы',
        'mine_marks' => 'Метки рудников'
    ];
    
    $missingTables = [];
    
    foreach ($requiredTables as $table => $description) {
        if (Schema::hasTable($table)) {
            echo "   ✅ {$description} ({$table}) - существует\n";
        } else {
            echo "   ❌ {$description} ({$table}) - НЕ СУЩЕСТВУЕТ\n";
            $missingTables[] = $table;
        }
    }
    
    // 3. Запуск миграций если нужно
    if (!empty($missingTables)) {
        echo "\n3️⃣ Запуск недостающих миграций...\n";
        
        if (in_array('mine_marks', $missingTables)) {
            echo "   🔄 Запуск миграции mine_marks...\n";
            
            try {
                Artisan::call('migrate', [
                    '--path' => 'database/migrations/2025_07_19_120000_create_mine_marks_table.php',
                    '--force' => true
                ]);
                echo "   ✅ Миграция mine_marks выполнена успешно\n";
            } catch (Exception $e) {
                echo "   ⚠️  Попытка запуска всех миграций...\n";
                Artisan::call('migrate', ['--force' => true]);
                echo "   ✅ Все миграции выполнены\n";
            }
        }
    } else {
        echo "\n3️⃣ Все необходимые таблицы существуют\n";
    }
    
    // 4. Проверка структуры таблицы mine_marks
    echo "\n4️⃣ Проверка структуры таблицы mine_marks...\n";
    
    if (Schema::hasTable('mine_marks')) {
        $columns = Schema::getColumnListing('mine_marks');
        echo "   📋 Найдено колонок: " . count($columns) . "\n";
        
        $requiredColumns = [
            'id', 'player_id', 'mine_location_id', 'location_id', 
            'location_name', 'expires_at', 'is_active', 'last_attack_at', 
            'attack_count', 'created_at', 'updated_at'
        ];
        
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (empty($missingColumns)) {
            echo "   ✅ Все необходимые колонки присутствуют\n";
        } else {
            echo "   ❌ Отсутствующие колонки: " . implode(', ', $missingColumns) . "\n";
        }
        
        // Показываем все колонки
        echo "   📝 Колонки таблицы: " . implode(', ', $columns) . "\n";
    } else {
        throw new Exception("❌ Таблица mine_marks все еще не существует после миграции");
    }
    
    // 5. Тест создания записи
    echo "\n5️⃣ Тестирование модели MineMark...\n";
    
    try {
        // Получаем первого пользователя и локацию рудника для теста
        $user = DB::table('users')->first();
        $mineLocation = DB::table('mine_locations')->first();
        $location = DB::table('locations')->first();
        
        if (!$user || !$mineLocation || !$location) {
            echo "   ⚠️  Недостаточно данных для тестирования (нужны: пользователь, рудник, локация)\n";
            echo "      Пользователи: " . ($user ? 'есть' : 'нет') . "\n";
            echo "      Рудники: " . ($mineLocation ? 'есть' : 'нет') . "\n";
            echo "      Локации: " . ($location ? 'есть' : 'нет') . "\n";
        } else {
            // Очищаем старые тестовые записи
            MineMark::where('player_id', $user->id)
                ->where('location_name', 'LIKE', '%ТЕСТ%')
                ->delete();
            
            // Создаем тестовую запись
            $testMark = MineMark::create([
                'player_id' => $user->id,
                'mine_location_id' => $mineLocation->id,
                'location_id' => $location->id,
                'location_name' => 'ТЕСТ - ' . ($mineLocation->name ?? 'Тестовый рудник'),
                'expires_at' => now()->addMinutes(5),
                'is_active' => true,
                'attack_count' => 0
            ]);
            
            echo "   ✅ Тестовая запись создана с ID: {$testMark->id}\n";
            echo "   📊 Игрок: " . ($user->name ?? 'ID:' . $user->id) . "\n";
            echo "   📊 Рудник: " . ($mineLocation->name ?? 'ID:' . $mineLocation->id) . "\n";
            echo "   ⏰ Истекает: {$testMark->expires_at}\n";
            
            // Проверяем загрузку записи
            $loadedMark = MineMark::find($testMark->id);
            if ($loadedMark) {
                echo "   ✅ Запись успешно загружена из БД\n";
                
                // Удаляем тестовую запись
                $loadedMark->delete();
                echo "   🗑️  Тестовая запись удалена\n";
            } else {
                echo "   ❌ Не удалось загрузить запись из БД\n";
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Ошибка при тестировании модели: " . $e->getMessage() . "\n";
    }
    
    // 6. Проверка сервисов
    echo "\n6️⃣ Проверка сервисов...\n";
    
    try {
        $mineDetectionService = app(\App\Services\MineDetectionService::class);
        echo "   ✅ MineDetectionService успешно создан\n";
        
        // Тест очистки (должен работать без ошибок)
        $cleaned = $mineDetectionService->cleanupExpiredMarks();
        echo "   ✅ Очистка истекших меток работает (очищено: {$cleaned})\n";
        
    } catch (Exception $e) {
        echo "   ❌ Ошибка в сервисах: " . $e->getMessage() . "\n";
    }
    
    // 7. Итоговая проверка
    echo "\n7️⃣ Итоговая проверка системы...\n";
    
    $systemReady = true;
    $issues = [];
    
    // Проверяем все компоненты
    if (!Schema::hasTable('mine_marks')) {
        $systemReady = false;
        $issues[] = "Таблица mine_marks не существует";
    }
    
    if (!class_exists('App\Models\MineMark')) {
        $systemReady = false;
        $issues[] = "Модель MineMark не найдена";
    }
    
    if (!class_exists('App\Services\MineDetectionService')) {
        $systemReady = false;
        $issues[] = "Сервис MineDetectionService не найден";
    }
    
    if (!class_exists('App\Jobs\MineAutoAttackJob')) {
        $systemReady = false;
        $issues[] = "Джоб MineAutoAttackJob не найден";
    }
    
    if ($systemReady) {
        echo "   🎉 СИСТЕМА ПОЛНОСТЬЮ ГОТОВА К РАБОТЕ!\n\n";
        
        echo "📋 СЛЕДУЮЩИЕ ШАГИ:\n";
        echo "================\n";
        echo "1. Запустите планировщик: php artisan schedule:work\n";
        echo "2. Или протестируйте вручную: php artisan mine:auto-attack\n";
        echo "3. Для полного теста: php test_mine_detection_system.php\n\n";
        
        echo "💡 ПРИНЦИП РАБОТЫ:\n";
        echo "==================\n";
        echo "• Игрок добывает ресурс → получает дебаф 'Замечен' на 5 минут\n";
        echo "• Каждые 15-30 секунд мобы атакуют замеченных игроков\n";
        echo "• Между атаками на одного игрока минимум 30 секунд\n";
        echo "• Метки автоматически очищаются каждые 2 минуты\n\n";
        
    } else {
        echo "   ❌ СИСТЕМА НЕ ГОТОВА!\n";
        echo "\nПроблемы:\n";
        foreach ($issues as $issue) {
            echo "• {$issue}\n";
        }
    }
    
} catch (Exception $e) {
    echo "\n❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "📍 Файл: " . $e->getFile() . ":" . $e->getLine() . "\n";
    
    if (method_exists($e, 'getSql')) {
        echo "🔍 SQL: " . $e->getSql() . "\n";
    }
    
    exit(1);
}
<?php

namespace App\Events;

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Событие обнаружения игрока в руднике
 * Срабатывает когда игрок получает дебаф "Замечен" при добыче ресурсов
 */
class MineDetectionEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $player;
    public MineLocation $mineLocation;
    public MineMark $mineMark;

    /**
     * Создать новое событие обнаружения
     *
     * @param User $player Обнаруженный игрок
     * @param MineLocation $mineLocation Локация рудника
     * @param MineMark $mineMark Метка обнаружения в руднике
     */
    public function __construct(User $player, MineLocation $mineLocation, MineMark $mineMark)
    {
        $this->player = $player;
        $this->mineLocation = $mineLocation;
        $this->mineMark = $mineMark;
    }

    /**
     * Получить каналы вещания события
     *
     * @return array
     */
    public function broadcastOn(): array
    {
        return [];
    }
}
<?php

namespace App\Http\Controllers;

use App\Models\Dungeon;
use App\Models\User;
use App\Models\Party;
use App\Models\PartyMember;
use App\Models\DungeonInstance;
use App\Models\DungeonRewardDistribution;
use App\Events\DungeonStarted;
use App\Services\DungeonRewardGenerationService;
use App\Services\DungeonRewardDistributionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\PartyService;
use App\Services\DungeonReadinessService;
use Illuminate\View\View;

class DungeonController extends Controller
{
    protected PartyService $partyService;
    protected DungeonRewardGenerationService $rewardGenerationService;
    protected DungeonRewardDistributionService $rewardDistributionService;
    protected DungeonReadinessService $readinessService;

    public function __construct(
        PartyService $partyService,
        DungeonRewardGenerationService $rewardGenerationService,
        DungeonRewardDistributionService $rewardDistributionService,
        DungeonReadinessService $readinessService
    ) {
        $this->partyService = $partyService;
        $this->rewardGenerationService = $rewardGenerationService;
        $this->rewardDistributionService = $rewardDistributionService;
        $this->readinessService = $readinessService;
    }
    /**
     * Отображает список доступных подземелий.
     *
     * @return View
     */
    public function index(): View
    {
        // Получаем текущего пользователя
        $user = Auth::user();

        // Получаем или создаем профиль пользователя (UserProfile)
        $userProfile = $user->profile()->firstOrNew([]);

        // Если это новая запись, сохраняем её с дефолтными значениями
        if (!$userProfile->exists) {
            $userProfile->level = 1;
            $userProfile->max_hp = 100; // или другое начальное значение HP
            $userProfile->max_mp = 50;  // или другое начальное значение MP
            $userProfile->current_hp = 100; // Устанавливаем текущее HP
            $userProfile->current_mp = 50;  // Устанавливаем текущее MP
            $userProfile->save();
        }

        $userLevel = $userProfile->level;
        $userGearScore = $this->calculateUserGearScore($user);

        // Получаем актуальные ресурсы пользователя
        $actualResources = $userProfile->getActualResources();

        // Получаем все активные подземелья с пагинацией (3 на страницу)
        $perPage = 3;
        $currentPage = request()->input('page', 1);

        $dungeonsQuery = Dungeon::active()
            ->ordered()
            ->with([
                'rewards' => function ($query) {
                    $query->orderBy('drop_chance', 'desc')
                        ->limit(10); // Ограничиваем количество наград для оптимизации
                },
                'universalRewards' => function ($query) {
                    $query->orderBy('drop_chance', 'desc')
                        ->limit(10); // Ограничиваем количество наград для оптимизации
                }
            ]);

        $totalCount = $dungeonsQuery->count();
        $dungeons = $dungeonsQuery->paginate($perPage);

        // Загружаем полиморфные связи только для наград с rewardable_id
        foreach ($dungeons as $dungeon) {
            $rewardsWithRelations = $dungeon->universalRewards->filter(function ($reward) {
                return $reward->rewardable_id && in_array($reward->rewardable_type, [
                    \App\Models\DungeonUniversalReward::TYPE_ITEM,
                    \App\Models\DungeonUniversalReward::TYPE_RESOURCE
                ]);
            });

            if ($rewardsWithRelations->isNotEmpty()) {
                $rewardsWithRelations->load('rewardable');
            }
        }

        // Подсчитываем онлайн игроков для футера
        $onlineCount = User::where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)->count();

        // Хлебные крошки
        $breadcrumbs = [
            ['name' => 'Главная', 'url' => route('home')],
            ['name' => 'Подземелья', 'url' => null]
        ];

        return view('dungeons.index', compact(
            'dungeons',
            'totalCount',
            'user',
            'userProfile',
            'userLevel',
            'userGearScore',
            'actualResources',
            'onlineCount',
            'breadcrumbs'
        ));
    }

    /**
     * Вычисляет GearScore пользователя на основе экипированных предметов.
     *
     * @param User $user
     * @return int
     */
    private function calculateUserGearScore(User $user): int
    {
        // Получаем все экипированные предметы пользователя
        $equippedItems = $user->gameItems()->where('is_equipped', true)->get();

        $totalGearScore = 0;

        foreach ($equippedItems as $item) {
            // Суммируем все характеристики предмета для расчета GS
            $itemGS = $item->strength +
                $item->intelligence +
                $item->recovery +
                $item->armor +
                $item->crit_chance +
                $item->crit_damage +
                $item->hp +
                $item->mp +
                $item->resistance_fire +
                $item->resistance_lightning;

            $totalGearScore += $itemGS;
        }

        return $totalGearScore;
    }

    /**
     * Отображает детальную информацию о подземелье.
     *
     * @param Dungeon $dungeon
     * @return View
     */
    public function show(Dungeon $dungeon): View
    {
        // Загружаем все награды подземелья
        $dungeon->load([
            'rewards' => function ($query) {
                $query->orderBy('drop_chance', 'desc');
            },
            'universalRewards' => function ($query) {
                $query->orderBy('drop_chance', 'desc');
            }
        ]);

        // Загружаем полиморфные связи только для наград с rewardable_id
        $rewardsWithRelations = $dungeon->universalRewards->filter(function ($reward) {
            return $reward->rewardable_id && in_array($reward->rewardable_type, [
                \App\Models\DungeonUniversalReward::TYPE_ITEM,
                \App\Models\DungeonUniversalReward::TYPE_RESOURCE
            ]);
        });

        if ($rewardsWithRelations->isNotEmpty()) {
            $rewardsWithRelations->load('rewardable');
        }

        $user = Auth::user();
        $userProfile = $user->profile()->firstOrNew([]);

        // Если это новая запись, сохраняем её с дефолтными значениями
        if (!$userProfile->exists) {
            $userProfile->level = 1;
            $userProfile->max_hp = 100; // или другое начальное значение HP
            $userProfile->max_mp = 50;  // или другое начальное значение MP
            $userProfile->current_hp = 100; // Устанавливаем текущее HP
            $userProfile->current_mp = 50;  // Устанавливаем текущее MP
            $userProfile->save();
        }

        $userLevel = $userProfile->level;
        $userGearScore = $this->calculateUserGearScore($user);

        // Получаем актуальные ресурсы пользователя
        $actualResources = $userProfile->getActualResources();

        // Проверяем доступность подземелья для пользователя
        $canEnter = $dungeon->canPlayerEnterByLevel($userLevel);
        $isGearScoreRecommended = $dungeon->isGearScoreRecommended($userGearScore);

        $onlineCount = User::where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)->count();

        // Хлебные крошки
        $breadcrumbs = [
            ['name' => 'Главная', 'url' => route('home')],
            ['name' => 'Подземелья', 'url' => route('dungeons.index')],
            ['name' => $dungeon->name, 'url' => null]
        ];

        return view('dungeons.show', compact(
            'dungeon',
            'user',
            'userProfile',
            'userLevel',
            'userGearScore',
            'actualResources',
            'canEnter',
            'isGearScoreRecommended',
            'onlineCount',
            'breadcrumbs'
        ));
    }

    /**
     * Отображает лобби подземелья с участниками группы.
     *
     * @param Dungeon $dungeon
     * @return View
     */
    public function lobby(Dungeon $dungeon): View
    {
        $user = Auth::user();
        $userProfile = $user->profile()->firstOrNew([]);

        // Если это новая запись, сохраняем её с дефолтными значениями
        if (!$userProfile->exists) {
            $userProfile->level = 1;
            $userProfile->max_hp = 100;
            $userProfile->max_mp = 50;
            $userProfile->current_hp = 100;
            $userProfile->current_mp = 50;
            $userProfile->save();
        }

        $userLevel = $userProfile->level;
        $userGearScore = $this->calculateUserGearScore($user);

        // Получаем актуальные ресурсы пользователя
        $actualResources = $userProfile->getActualResources();

        // Проверяем доступность подземелья для пользователя
        $canEnter = $dungeon->canPlayerEnterByLevel($userLevel);

        if (!$canEnter) {
            return redirect()->route('dungeons.show', $dungeon)
                ->with('error', 'У вас недостаточный уровень для входа в это подземелье.');
        }

        // Устанавливаем статус лобби для пользователя
        $user->update([
            'in_dungeon_id' => $dungeon->id,
            'in_dungeon_status' => 'lobby',
            'dungeon_entered_at' => now(),
        ]);

        // Получаем активную группу пользователя
        $activeParty = $user->activeParty;

        // Если у пользователя нет активной группы, создаем одиночную группу
        if (!$activeParty) {
            $activeParty = Party::create([
                'leader_id' => $user->id,
                'name' => $user->name . ' (Соло)',
                'max_members' => Party::DEFAULT_MAX_MEMBERS, // Используем стандартный размер для возможности приглашений
                'status' => Party::STATUS_ACTIVE,
                'is_public' => false,
                'auto_accept' => false,
            ]);

            // Добавляем пользователя в группу
            $activeParty->members()->create([
                'user_id' => $user->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'joined_at' => now(),
                'is_ready' => false,
            ]);
        }

        // Загружаем всех участников группы с их профилями
        $partyMembers = $activeParty->activeUsers()
            ->with(['profile'])
            ->get();

        // Используем DungeonReadinessService для проверки готовности группы
        $readinessCheck = $this->readinessService->validatePartyReadiness($dungeon, $activeParty);

        // Получаем информацию о готовности
        $allReady = $readinessCheck['ready'];
        $canStartDungeon = $readinessCheck['can_start'];
        $memberCount = $partyMembers->count();
        $readinessErrors = $readinessCheck['errors'];
        $readinessWarnings = $readinessCheck['warnings'];

        $onlineCount = User::where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)->count();

        // Хлебные крошки
        $breadcrumbs = [
            ['name' => 'Главная', 'url' => route('home')],
            ['name' => 'Подземелья', 'url' => route('dungeons.index')],
            ['name' => $dungeon->name, 'url' => route('dungeons.show', $dungeon)],
            ['name' => 'Лобби', 'url' => null]
        ];

        return view('dungeons.lobby', compact(
            'dungeon',
            'user',
            'userProfile',
            'userLevel',
            'userGearScore',
            'actualResources',
            'activeParty',
            'partyMembers',
            'allReady',
            'canStartDungeon',
            'memberCount',
            'readinessErrors',
            'readinessWarnings',
            'onlineCount',
            'breadcrumbs'
        ));
    }

    /**
     * Переключает статус готовности участника в лобби.
     *
     * @param Request $request
     * @param Dungeon $dungeon
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleReady(Request $request, Dungeon $dungeon)
    {
        $user = Auth::user();
        $activeParty = $user->activeParty;

        if (!$activeParty) {
            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'Вы не состоите в группе.');
        }

        // Находим участника в группе
        $partyMember = $activeParty->members()
            ->where('user_id', $user->id)
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->first();

        if (!$partyMember) {
            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'Вы не являетесь участником этой группы.');
        }

        try {
            // Переключаем статус готовности
            $partyMember->is_ready = !$partyMember->is_ready;
            $partyMember->save();

            $message = $partyMember->is_ready ? 'Вы готовы к началу!' : 'Вы отменили готовность';

            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'Ошибка при изменении статуса готовности: ' . $e->getMessage());
        }
    }

    /**
     * Вход в подземелье (только для лидера группы)
     *
     * @param Dungeon $dungeon
     * @return \Illuminate\Http\RedirectResponse
     */
    public function enterDungeon(Dungeon $dungeon)
    {
        $user = Auth::user();
        $userProfile = $user->profile;

        // Проверяем, что игрок может войти в подземелье
        if (!$dungeon->canPlayerEnterByLevel($userProfile->level)) {
            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'У вас недостаточный уровень для входа в это подземелье.');
        }

        // Получаем активную группу пользователя
        $activeParty = $user->activeParty;
        if (!$activeParty) {
            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'Вы должны состоять в группе для входа в подземелье.');
        }

        // Проверяем, что пользователь является лидером группы
        if (!$activeParty->isLeader($user->id)) {
            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'Только лидер группы может начать подземелье.');
        }

        // Используем сервис для комплексной проверки готовности группы
        $readinessCheck = $this->readinessService->validatePartyReadiness($dungeon, $activeParty);

        if (!$readinessCheck['can_start']) {
            $errorMessage = 'Невозможно начать подземелье:';

            if (!empty($readinessCheck['errors'])) {
                $errorMessage .= ' ' . implode('; ', $readinessCheck['errors']);
            }

            if (!empty($readinessCheck['warnings'])) {
                $errorMessage .= ' Предупреждения: ' . implode('; ', $readinessCheck['warnings']);
            }

            Log::warning('[DungeonController] Попытка начать подземелье с неготовой группой', [
                'dungeon_id' => $dungeon->id,
                'party_id' => $activeParty->id,
                'leader_id' => $user->id,
                'readiness_check' => $readinessCheck
            ]);

            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', $errorMessage);
        }

        // Дополнительная проверка: получаем участников еще раз для финальной валидации
        $partyMembers = $activeParty->activeUsers()->get();
        $finalReadinessCheck = $this->readinessService->validatePartyReadiness($dungeon, $activeParty);

        if (!$finalReadinessCheck['can_start']) {
            Log::error('[DungeonController] Критическая ошибка: состояние группы изменилось между проверками', [
                'dungeon_id' => $dungeon->id,
                'party_id' => $activeParty->id,
                'leader_id' => $user->id,
                'first_check' => $readinessCheck,
                'final_check' => $finalReadinessCheck
            ]);

            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'Состояние группы изменилось. Попробуйте еще раз.');
        }

        // Получаем всех участников группы после валидации
        $partyMembers = $activeParty->activeUsers()->get();

        // Используем блокировку для предотвращения race condition
        DB::beginTransaction();

        try {
            // Проверяем, есть ли уже активный инстанс для этой группы
            $existingInstance = \App\Models\DungeonInstance::getActiveForParty($dungeon, $activeParty);

            if ($existingInstance) {
                // Проверяем, завершено ли подземелье (все мобы побеждены)
                if ($existingInstance->areAllMobsDefeated()) {
                    // Подземелье завершено, но инстанс еще активен - завершаем его
                    $existingInstance->complete();

                    Log::info('Завершен старый инстанс подземелья при создании нового', [
                        'old_instance_id' => $existingInstance->id,
                        'dungeon_id' => $dungeon->id,
                        'party_id' => $activeParty->id,
                        'leader_id' => $user->id
                    ]);

                    // Создаем новый инстанс подземелья для группы
                    $dungeonInstance = DungeonInstance::createForParty($dungeon, $activeParty);
                } else {
                    // Инстанс активен и не завершен, используем его
                    $dungeonInstance = $existingInstance;
                }
            } else {
                // Создаем новый инстанс подземелья для группы
                $dungeonInstance = DungeonInstance::createForParty($dungeon, $activeParty);
            }

            // Переводим всех участников группы в подземелье
            foreach ($partyMembers as $member) {
                $member->enterDungeon($dungeon, 'battle');

                // Инициализируем стадию подземелья для каждого участника с привязкой к инстансу
                \App\Models\DungeonStage::initializeForUser($member->id, $dungeon->id, $dungeonInstance->id);
            }

            DB::commit();

            // Запускаем событие начала подземелья для принудительного редиректа всех участников
            event(new DungeonStarted($dungeon, $activeParty, $partyMembers, $user->id));

            // Перенаправляем лидера на страницу боя подземелья
            return redirect()->route('dungeons.battle', $dungeon)
                ->with('info', 'Ваша группа начала подземелье!');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при входе в подземелье', [
                'dungeon_id' => $dungeon->id,
                'party_id' => $activeParty->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('error', 'Произошла ошибка при входе в подземелье. Попробуйте еще раз.');
        }
    }

    /**
     * Выход из подземелья
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function leaveDungeon()
    {
        $user = Auth::user();

        if (!$user->isInDungeon()) {
            return redirect()->route('home')
                ->with('error', 'Вы не находитесь в подземелье.');
        }

        $dungeon = $user->currentDungeon;

        DB::beginTransaction();

        try {
            // Если пользователь в группе, выводим его из группы
            if ($user->hasActiveParty()) {
                $activeParty = $user->activeParty;

                // Логируем выход из группы
                Log::info('Пользователь покидает группу при выходе из подземелья', [
                    'user_id' => $user->id,
                    'party_id' => $activeParty->id,
                    'dungeon_id' => $dungeon->id ?? null
                ]);

                // Выходим из группы
                $this->partyService->leaveParty($user);
            }

            // Выводим пользователя из подземелья
            $user->leaveDungeon();

            DB::commit();

            Log::info('Пользователь покинул подземелье', [
                'user_id' => $user->id,
                'dungeon_id' => $dungeon->id ?? null
            ]);

            return redirect()->route('dungeons.lobby', $dungeon)
                ->with('success', 'Вы покинули подземелье.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при выходе из подземелья', [
                'user_id' => $user->id,
                'dungeon_id' => $dungeon->id ?? null,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('home')
                ->with('error', 'Произошла ошибка при выходе из подземелья.');
        }
    }

    /**
     * Показывает страницу подтверждения выхода из подземелья
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function showConfirmLeavePage()
    {
        $user = Auth::user();

        if (!$user->isInDungeon()) {
            return redirect()->route('home')
                ->with('error', 'Вы не находитесь в подземелье.');
        }

        $dungeon = $user->currentDungeon;
        $intendedUrl = session('intended_url', route('home'));

        return view('dungeons.confirm-leave', [
            'dungeon' => $dungeon,
            'intendedUrl' => $intendedUrl,
            'user' => $user
        ]);
    }

    /**
     * Принудительный выход из подземелья с перенаправлением на желаемую страницу
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function forceLeave(Request $request)
    {
        $user = Auth::user();

        if (!$user->isInDungeon()) {
            return redirect()->route('home')
                ->with('error', 'Вы не находитесь в подземелье.');
        }

        $dungeon = $user->currentDungeon;
        $intendedUrl = $request->input('intended_url', route('home'));

        DB::beginTransaction();

        try {
            // Если пользователь в группе, выводим его из группы
            if ($user->hasActiveParty()) {
                $activeParty = $user->activeParty;

                // Логируем выход из группы
                Log::info('Пользователь покидает группу при принудительном выходе из подземелья', [
                    'user_id' => $user->id,
                    'party_id' => $activeParty->id,
                    'dungeon_id' => $dungeon->id ?? null
                ]);

                // Выходим из группы
                $this->partyService->leaveParty($user);
            }

            // Выводим пользователя из подземелья
            $user->leaveDungeon();

            DB::commit();

            Log::info('Пользователь принудительно покинул подземелье', [
                'user_id' => $user->id,
                'dungeon_id' => $dungeon->id ?? null,
                'intended_url' => $intendedUrl
            ]);

            // Очищаем сессию
            session()->forget('intended_url');

            return redirect($intendedUrl)
                ->with('success', 'Вы покинули подземелье.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при принудительном выходе из подземелья', [
                'user_id' => $user->id,
                'dungeon_id' => $dungeon->id ?? null,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('home')
                ->with('error', 'Произошла ошибка при выходе из подземелья.');
        }
    }

    /**
     * Завершает подземелье и показывает интерфейс распределения наград
     *
     * @param Dungeon $dungeon
     * @return View|\Illuminate\Http\RedirectResponse
     */
    public function complete(Dungeon $dungeon)
    {
        $user = Auth::user();

        // Проверяем, что пользователь в подземелье
        if (!$user->isInDungeon() || $user->currentDungeon->id !== $dungeon->id) {
            return redirect()->route('dungeons.index')
                ->with('error', 'Вы не находитесь в этом подземелье.');
        }

        // Получаем активную группу
        $activeParty = $user->activeParty;
        if (!$activeParty) {
            return redirect()->route('dungeons.index')
                ->with('error', 'Вы не состоите в группе.');
        }

        // Получаем инстанс подземелья
        $dungeonInstance = DungeonInstance::getActiveForParty($dungeon, $activeParty);
        if (!$dungeonInstance) {
            return redirect()->route('dungeons.index')
                ->with('error', 'Активный инстанс подземелья не найден.');
        }

        try {
            DB::beginTransaction();

            // Проверяем, есть ли уже сгенерированные награды
            $existingRewards = $this->rewardDistributionService->getRewardsForDistribution($dungeonInstance);

            if ($existingRewards->isEmpty()) {
                // Генерируем награды для группы
                $partyMembers = $activeParty->activeUsers()->get();
                $rewards = $this->rewardGenerationService->generateRewardsForParty(
                    $dungeonInstance,
                    $activeParty,
                    $partyMembers
                );

                Log::info('Сгенерированы награды для завершения подземелья', [
                    'dungeon_instance_id' => $dungeonInstance->id,
                    'party_id' => $activeParty->id,
                    'rewards_count' => $rewards->count()
                ]);
            } else {
                $rewards = $existingRewards;
            }

            // Разрешаем конфликты наград на основе БА
            $this->rewardDistributionService->resolveRewardConflicts($dungeonInstance);

            // Проверяем, может ли пользователь завершить подземелье
            $completionCheck = $this->rewardDistributionService->canCompleteForUser($user, $dungeonInstance);

            // Получаем обновленные награды после разрешения конфликтов
            $finalRewards = $this->rewardDistributionService->getRewardsForDistribution($dungeonInstance);

            DB::commit();

            // Получаем участников группы
            $partyMembers = $activeParty->activeUsers;

            // Получаем данные для layout
            $userProfile = $user->profile;
            $actualResources = $userProfile->getActualResources();
            $onlineCount = User::where('is_online', true)->count();

            return view('dungeons.complete', [
                'dungeon' => $dungeon,
                'dungeonInstance' => $dungeonInstance,
                'rewards' => $finalRewards,
                'partyMembers' => $partyMembers,
                'currentUser' => $user,
                'canComplete' => $completionCheck['can_complete'],
                'completionMessage' => $completionCheck['message'],
                // Данные для layout
                'user' => $user,
                'userProfile' => $userProfile,
                'actualResources' => $actualResources,
                'onlineCount' => $onlineCount
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при завершении подземелья', [
                'user_id' => $user->id,
                'dungeon_id' => $dungeon->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('dungeons.battle', $dungeon)
                ->with('error', 'Произошла ошибка при завершении подземелья.');
        }
    }

    /**
     * Финально завершает подземелье и распределяет награды
     *
     * @param Dungeon $dungeon
     * @return \Illuminate\Http\RedirectResponse
     */
    public function finalize(Dungeon $dungeon)
    {
        $user = Auth::user();

        // Проверяем, что пользователь в подземелье
        if (!$user->isInDungeon() || $user->currentDungeon->id !== $dungeon->id) {
            return redirect()->route('home')
                ->with('error', 'Вы не находитесь в этом подземелье.');
        }

        // Получаем активную группу
        $activeParty = $user->activeParty;
        if (!$activeParty) {
            return redirect()->route('home')
                ->with('error', 'Вы не состоите в группе.');
        }

        // Получаем инстанс подземелья
        $dungeonInstance = DungeonInstance::getActiveForParty($dungeon, $activeParty);
        if (!$dungeonInstance) {
            return redirect()->route('home')
                ->with('error', 'Активный инстанс подземелья не найден.');
        }

        try {
            DB::beginTransaction();

            // Проверяем, может ли пользователь завершить подземелье
            $completionCheck = $this->rewardDistributionService->canCompleteForUser($user, $dungeonInstance);

            if (!$completionCheck['can_complete']) {
                return redirect()->route('dungeons.complete', $dungeon)
                    ->with('error', $completionCheck['message']);
            }

            // Распределяем награды игроку
            $distributionResult = $this->rewardDistributionService->distributeRewardsToUser($user, $dungeonInstance);

            if (!$distributionResult['success']) {
                return redirect()->route('dungeons.complete', $dungeon)
                    ->with('error', $distributionResult['message'] ?? 'Ошибка при распределении наград.');
            }

            // Получаем всех участников группы для логирования
            $partyMembers = $activeParty->activeUsers()->get();

            // Выводим пользователя из подземелья
            $user->leaveDungeon();

            // Очищаем кэш связанный с завершением подземелья для пользователя
            Cache::forget("dungeon_completion_redirect_{$user->id}");
            Cache::forget("dungeon_completed_{$user->id}");

            // Проверяем сколько участников группы еще в подземелье
            $remainingMembers = $dungeonInstance->party->activeUsers()
                ->whereHas('statistics', function ($query) use ($dungeon) {
                    $query->where('current_location', 'Подземелье: ' . $dungeon->name);
                })->get();

            $remainingCount = $remainingMembers->count();

            Log::info('Игрок покинул подземелье', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'dungeon_id' => $dungeon->id,
                'dungeon_instance_id' => $dungeonInstance->id,
                'party_id' => $activeParty->id,
                'remaining_members_count' => $remainingCount,
                'remaining_member_ids' => $remainingMembers->pluck('id')->toArray(),
                'total_party_members' => $partyMembers->count()
            ]);

            // НЕ завершаем инстанс автоматически
            // Инстанс будет завершен через отдельную систему очистки или таймер
            // Это позволяет игрокам оставаться на странице наград независимо друг от друга

            // Запускаем событие финального завершения подземелья
            event(new \App\Events\DungeonFinalized(
                $dungeonInstance,
                $activeParty,
                $partyMembers, // Уже является Collection
                $user,
                $distributionResult['distributed_rewards'] ?? [],
                $remainingCount === 0
            ));

            DB::commit();

            $rewardsCount = count($distributionResult['distributed_rewards']);
            $message = $rewardsCount > 0
                ? "Подземелье завершено! Вы получили {$rewardsCount} наград."
                : "Подземелье завершено!";

            // Добавляем информацию о статусе группы
            if ($remainingCount > 0) {
                $message .= " Остальные участники группы ({$remainingCount}) еще в подземелье.";
            } else {
                $message .= " Все участники группы покинули подземелье.";
            }

            return redirect()->route('home')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при финальном завершении подземелья', [
                'user_id' => $user->id,
                'dungeon_id' => $dungeon->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('dungeons.complete', $dungeon)
                ->with('error', 'Произошла ошибка при завершении подземелья.');
        }
    }
}

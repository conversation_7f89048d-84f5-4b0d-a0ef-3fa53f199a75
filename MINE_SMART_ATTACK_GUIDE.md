# 🎯 РУКОВОДСТВО ПО УМНОЙ СИСТЕМЕ АТАК МОБОВ В РУДНИКАХ

## ✅ ПРОБЛЕМА РЕШЕНА

**Проблема**: Мобы в рудниках атаковали игроков с дебафом "Замечен" случайным образом, без умного распределения и приоритетов.

**Решение**: Создана интеллектуальная система распределения мобов с приоритетами, балансировкой и динамическим переключением целей.

## 🔧 НОВЫЕ КОМПОНЕНТЫ

### 1. MineTargetDistributionService
- **Путь**: `app/Services/MineTargetDistributionService.php`
- **Функции**:
  - Умное распределение мобов между игроками
  - Приоритеты по HP игроков (игроки с низким HP атакуются слабыми мобами)
  - Ограничение количества мобов на игрока (максимум 3)
  - Кулдаун между атаками (25 секунд)
  - Динамическое переключение целей (20% вероятность)

### 2. Улучшенный MineAutoAttackJob
- **Путь**: `app/Jobs/MineAutoAttackJob.php`
- **Изменения**:
  - Интеграция с MineTargetDistributionService
  - Группировка игроков по локациям для оптимизации
  - Умный выбор пар моб-игрок
  - Улучшенное логирование

### 3. Команды для тестирования
- **TestMineSmartAttackSystem**: `php artisan test:mine-smart-attack`
- **TestMineSystemWithAdmin**: `php artisan test:mine-admin`
- **CheckMineData**: `php artisan check:mine-data`

## 🎮 АЛГОРИТМ РАБОТЫ

### Приоритеты выбора игроков:
1. **Игроки с низким HP** (≤30%) - приоритет №1
2. **Равномерное распределение** - игроки с наименьшим количеством атакующих мобов
3. **Кулдаун** - минимум 25 секунд между атаками на одного игрока

### Приоритеты выбора мобов:
1. **Для игроков с низким HP** - выбираются более слабые мобы (60% самых слабых)
2. **Для игроков с высоким HP** (≥80%) - могут атаковать сильные мобы (40% самых сильных)
3. **Для остальных** - случайный выбор

### Ограничения:
- **Максимум 3 моба** на одного игрока одновременно
- **Кулдаун 25 секунд** между атаками
- **Время жизни назначений** - 5 минут в Redis

## 🧪 ТЕСТИРОВАНИЕ

### Базовое тестирование:
```bash
# Проверка данных
php artisan check:mine-data

# Тестирование без атак (dry-run)
php artisan test:mine-smart-attack --dry-run --stats

# Тестирование с админом
php artisan test:mine-admin
```

### Тестирование с реальными атаками:
```bash
# Реальная атака
php artisan test:mine-admin --attack

# Очистка тестовых данных
php artisan test:mine-admin --cleanup
```

### Запуск автоатак:
```bash
# Ручной запуск
php artisan mine:auto-attack

# Планировщик (автоматически каждые 15-30 секунд)
php artisan schedule:work
```

## 📊 МОНИТОРИНГ

### Проверка активных меток:
```sql
SELECT * FROM mine_marks WHERE is_active = 1 AND expires_at > NOW();
```

### Проверка планировщика:
```bash
php artisan schedule:list | grep mine_auto_attack
```

### Логи системы:
- Основные логи: `storage/logs/laravel.log`
- Поиск по: `MineAutoAttackJob`, `MineTargetDistribution`

## 🎯 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ

✅ **Система успешно протестирована**:
- Создание меток "Замечен" работает
- Умное распределение мобов функционирует
- Приоритеты по HP применяются корректно
- Кулдауны соблюдаются
- Атаки выполняются успешно
- Планировщик запускает задачи

### Тестовые данные:
- **Игрок**: admin (ID: 7)
- **Локация**: аааааааааааа (ID: 137)
- **Рудник**: аааааааааааа (ID: 172)
- **Моб**: Огр (ID: 14, HP: 455)

### Результат тестовой атаки:
- HP админа: 9992/10000 (получил 8 урона)
- Система корректно рассчитала урон
- Кулдаун применился правильно

## 🚀 ПРОИЗВОДИТЕЛЬНОСТЬ

### Оптимизации:
- **Группировка по локациям** - обработка игроков группами
- **Redis кэширование** - назначения мобов хранятся в Redis
- **Ограничение атак** - максимум 20 атак за раз
- **Очистка истекших назначений** - автоматическая очистка

### Планировщик:
- **Основные атаки**: каждые 15 секунд
- **Дополнительные атаки**: каждые 30 секунд (70% вероятность)
- **Защита от перекрытия**: withoutOverlapping()

## 🎉 ЗАКЛЮЧЕНИЕ

Умная система атак мобов в рудниках полностью реализована и протестирована. Система обеспечивает:

1. **Справедливое распределение** мобов между игроками
2. **Интеллектуальные приоритеты** по состоянию игроков
3. **Балансировку нагрузки** через кулдауны и ограничения
4. **Высокую производительность** через оптимизации
5. **Надежность** через защиту от ошибок и перекрытий

Система готова к продакшену! 🚀

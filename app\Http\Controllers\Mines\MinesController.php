<?php
namespace App\Http\Controllers\Mines; // Исправленный namespace

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller; // Импорт базового класса
use App\Models\User;
use App\Models\Mob;
use App\Models\MineLocation; // Добавляем импорт модели MineLocation
use App\Models\Location; // Добавляем импорт модели Location
use App\Models\SpawnedResource; // Добавляем импорт модели SpawnedResource
use App\Models\Message; // Добавляем для работы с сообщениями
use App\Models\Equipment; // Добавляем для работы с экипировкой
use App\Services\MineTargetResetService; // Добавляем сервис для сброса целей
use App\Services\LocationAccessService; // Добавляем сервис для проверки доступа к локациям
use App\Services\FlashMessageService; // Добавляем сервис для флеш-сообщений
use App\Services\MineDetectionService; // Добавляем сервис для дебафа обнаружения
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str; // Добавляем для работы со строками

class MinesController extends Controller
{
    protected MineTargetResetService $mineTargetResetService;
    protected LocationAccessService $locationAccessService;
    protected FlashMessageService $flashMessageService;

    public function __construct(MineTargetResetService $mineTargetResetService, LocationAccessService $locationAccessService, FlashMessageService $flashMessageService)
    {
        $this->mineTargetResetService = $mineTargetResetService;
        $this->locationAccessService = $locationAccessService;
        $this->flashMessageService = $flashMessageService;
    }
    /**
     * Отображение списка всех аванпостов.
     */
    public function index()
    {
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы просматривать эту страницу.');
        }

        // Проверяем доступ к рудникам для пользователя в группе со смешанными расами
        if (!$this->locationAccessService->canAccessLocation($user, 'Рудники')) {
            $reason = $this->locationAccessService->getAccessDeniedReason($user, 'Рудники');
            $this->flashMessageService->error($reason, '⚠️');
            return redirect()->back();
        }

        // Инициализируем переменные для статистики игроков
        $solWarriors = 0;
        $solMages = 0;
        $solKnights = 0;
        $lunWarriors = 0;
        $lunMages = 0;
        $lunKnights = 0;
        $mobsCount = 0;

        // Получаем созданные через админку локации шахт (только корневые/базовые) с пагинацией
        // Загружаем связанную базовую локацию для получения изображений
        $customMineLocations = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->with('baseLocation')
            ->orderBy('order')
            ->paginate(4); // 4 рудника на страницу

        // Преобразуем пагинированные данные в нужный формат
        $customMineLocations->getCollection()->transform(function ($mineLocation) {
            // Генерируем имя маршрута на основе slug локации
            $routeName = 'battle.mines.custom.' . $mineLocation->slug;

            // Формируем путь к изображению из базовой локации (таблица locations)
            // Это обеспечивает синхронизацию с админкой, где изображения сохраняются в locations
            $imagePath = 'assets/default_mine.png'; // Значение по умолчанию

            if ($mineLocation->baseLocation && $mineLocation->baseLocation->image_path) {
                $imagePath = $mineLocation->baseLocation->image_path;
            }

            // Получаем игроков в этой локации
            $locationStats = $this->getPlayersInLocation($mineLocation->name);
            $locationPlayers = $locationStats['playersInLocation'];

            // Получаем ресурсы в этой локации
            $locationResources = $mineLocation->spawnedResources()
                ->where('is_active', true)
                ->with('resource')
                ->get()
                ->unique('resource_id')
                ->map(function ($spawnedResource) {
                    return $spawnedResource->resource;
                });

            // Получаем мобов в этой локации (только рудничных мобов)
            $locationMobs = Mob::where('location_id', $mineLocation->location_id)
                ->where('mob_type', 'mine') // Только рудничные мобы
                ->where('hp', '>', 0)
                ->with(['itemDrops.item', 'resourceDrops.resource'])
                ->get();

            // Собираем уникальные предметы, которые могут выпасть с мобов
            $locationPossibleItems = collect();
            foreach ($locationMobs as $mob) {
                foreach ($mob->itemDrops as $itemDrop) {
                    $locationPossibleItems->push([
                        'item' => $itemDrop->item,
                        'drop_chance' => $itemDrop->drop_chance,
                        'min_quantity' => $itemDrop->min_quantity,
                        'max_quantity' => $itemDrop->max_quantity
                    ]);
                }
            }
            $locationPossibleItems = $locationPossibleItems->unique(function ($item) {
                return $item['item']->id;
            });

            // Добавляем дополнительные поля к объекту MineLocation
            $mineLocation->route = $routeName;
            $mineLocation->image = $imagePath;
            $mineLocation->is_custom = true;
            $mineLocation->players = $locationPlayers;
            $mineLocation->resources = $locationResources;
            $mineLocation->possible_items = $locationPossibleItems;

            return $mineLocation;
        });

        // Получаем актуальные ресурсы пользователя (HP/MP с учетом восстановления)
        try {
            $userActualResources = $user->profile->getActualResources();
            $currentHp = $userActualResources['current_hp'];
            $currentMp = $userActualResources['current_mp'];

            // Отладочная информация
            \Log::info("MinesController: Актуальные ресурсы получены", [
                'user_id' => $user->id,
                'current_hp' => $currentHp,
                'current_mp' => $currentMp
            ]);
        } catch (\Exception $e) {
            // При ошибке используем значения из БД
            $currentHp = $user->profile->current_hp ?? $user->profile->hp;
            $currentMp = $user->profile->current_mp ?? $user->profile->mp;

            // Отладочная информация об ошибке
            \Log::error("MinesController: Ошибка при получении актуальных ресурсов", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'fallback_hp' => $currentHp,
                'fallback_mp' => $currentMp
            ]);
        }

        // Проверяем, достаточно ли HP для перехода (75% HP требуется для входа в рудники)
        $maxHp = $user->profile->max_hp;
        $requiredHpPercent = 75; // 75% HP требуется для входа в рудники
        $hpPercent = $maxHp > 0 ? ($currentHp / $maxHp) * 100 : 0;
        $canEnterMines = $hpPercent >= $requiredHpPercent;

        // Отладочная информация о проверке входа
        \Log::info("MinesController: Проверка входа в рудники", [
            'user_id' => $user->id,
            'current_hp' => $currentHp,
            'max_hp' => $maxHp,
            'hp_percent' => $hpPercent,
            'required_percent' => $requiredHpPercent,
            'can_enter_mines' => $canEnterMines
        ]);

        // Подготавливаем актуальные ресурсы для отображения
        $actualResources = [
            'current_hp' => max(0, min($user->profile->max_hp, $currentHp)),
            'current_mp' => max(0, min($user->profile->max_mp, $currentMp))
        ];

        // Проверяем наличие непрочитанных сообщений и сломанных предметов
        $unreadMessagesCount = $user->getUnreadMessagesCount();
        $hasUnreadMessages = $unreadMessagesCount > 0;
        $brokenItemsCount = $user->getBrokenItemsCount();
        $hasBrokenItems = $brokenItemsCount > 0;

        // Подготавливаем данные для представления
        $viewData = [
            'userProfile' => $user->profile,
            'actualResources' => $actualResources,
            'hasUnreadMessages' => $hasUnreadMessages,
            'unreadMessagesCount' => $unreadMessagesCount,
            'hasBrokenItems' => $hasBrokenItems,
            'brokenItemsCount' => $brokenItemsCount,
            'experienceProgress' => null, // Можно добавить расчет прогресса опыта если нужно
            'minesPaginator' => $customMineLocations, // Добавляем пагинатор для рудников
            'canEnterMines' => $canEnterMines,
            'solWarriors' => $solWarriors,
            'solMages' => $solMages,
            'solKnights' => $solKnights,
            'lunWarriors' => $lunWarriors,
            'lunMages' => $lunMages,
            'lunKnights' => $lunKnights,
            'mobsCount' => $mobsCount,
            'breadcrumbs' => [
                ['url' => route('home'), 'title' => 'Город'],
                ['url' => route('battle.index'), 'title' => 'Битва'],
                ['url' => route('battle.mines.index'), 'title' => 'Рудники'],
            ],
            'onlineCount' => User::where('last_activity_timestamp', '>', now()->subMinutes(15)->timestamp)->count()
        ];

        // Если HP <= 0, добавляем сообщение об ошибке
        if (!$canEnterMines) {
            $viewData['error'] = 'У вас слишком мало здоровья. Восстановитесь!';
        }

        return view('battle.mines', $viewData);
    }

    /**
     * Получает статистику игроков в указанной локации
     *
     * @param string $location Название локации
     * @return array
     */
    private function getPlayersInLocation($location)
    {
        $playersInLocation = User::whereHas('statistics', function ($q) use ($location) {
            $q->where('current_location', $location);
        })
            ->with('profile')
            ->get();

        $solariusPlayers = $playersInLocation->where('profile.race', 'solarius');
        $lunariusPlayers = $playersInLocation->where('profile.race', 'lunarius');

        return [
            'playersInLocation' => $playersInLocation,
            'solWarriors' => $solariusPlayers->where('profile.class', 'warrior')->count(),
            'solMages' => $solariusPlayers->where('profile.class', 'mage')->count(),
            'solKnights' => $solariusPlayers->where('profile.class', 'priest')->count(),
            'lunWarriors' => $lunariusPlayers->where('profile.class', 'warrior')->count(),
            'lunMages' => $lunariusPlayers->where('profile.class', 'mage')->count(),
            'lunKnights' => $lunariusPlayers->where('profile.class', 'priest')->count(),
        ];
    }

    /**
     * Отображение пользовательской локации шахты
     *
     * @param Request $request Запрос
     * @param string $slug Slug локации
     * @return \Illuminate\View\View
     */
    public function showCustomMine(Request $request, $slug)
    {
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы просматривать эту страницу.');
        }

        // Ищем локацию шахты по slug
        $mineLocation = MineLocation::where('slug', $slug)->first();

        if (!$mineLocation) {
            abort(404, 'Локация шахты не найдена.');
        }

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;

        // Получаем ресурсы, связанные с локацией
        $resources = $mineLocation->spawnedResources;

        // Получаем подлокации, если они есть
        $subLocations = $mineLocation->sublocations;

        // Получаем пользователей в этой локации
        $stats = $this->getPlayersInLocation($mineLocation->name);

        // Инициализируем переменные для текущей цели
        $targetType = $user->current_target_type;
        $targetResource = null;
        $targetMob = null;
        $target = null;

        // Хлебные крошки для навигации
        $breadcrumbs = [
            ['label' => 'Главная', 'url' => route('home')],
            ['label' => 'Битва', 'url' => route('battle.index')],
            ['label' => 'Рудники', 'url' => route('battle.mines.index')],
            ['label' => $mineLocation->name, 'url' => '#']
        ];

        // Журнал боя (пустой для начала)
        $battleLogs = [];

        // Если у пользователя есть цель, получаем ее детали
        if ($targetType === 'resource' && $user->current_target_id) {
            $targetResource = $mineLocation->spawnedResources()->find($user->current_target_id);

            // Если ресурс не найден или неактивен, сбрасываем цель
            if (!$targetResource || !$targetResource->is_active) {
                $user->current_target_type = null;
                $user->current_target_id = null;
                $user->save();
                $targetType = null;
                $targetResource = null;
            }
        }

        // Проверяем и сбрасываем цели при переходе между подлокациями рудника
        $previousMineLocation = $this->mineTargetResetService->getPreviousMineLocationFromSessionById();
        $targetWasReset = $this->mineTargetResetService->checkAndResetTargetsOnMineLocationChange(
            $user,
            $mineLocation,
            $previousMineLocation
        );

        // Сохраняем текущую подлокацию в сессии для следующего перехода
        $this->mineTargetResetService->storeMineLocationInSession($mineLocation);

        // Обновляем статистику пользователя
        if ($user->statistics) {
            $user->statistics->current_location = $mineLocation->name;
            $user->statistics->save();
        }

        return view('battle.mines.locations.default', [
            'userProfile' => $user->profile,
            'mineLocation' => $mineLocation,
            'baseLocation' => $baseLocation,
            'resources' => $resources,
            'subLocations' => $subLocations,
            'breadcrumbs' => $breadcrumbs,
            'battleLogs' => $battleLogs,
            'targetType' => $targetType,
            'targetResource' => $targetResource,
            'targetMob' => $targetMob,
            'target' => $target,
            'solWarriors' => $stats['solWarriors'],
            'solMages' => $stats['solMages'],
            'solKnights' => $stats['solKnights'],
            'lunWarriors' => $stats['lunWarriors'],
            'lunMages' => $stats['lunMages'],
            'lunKnights' => $stats['lunKnights'],
        ]);
    }

    /**
     * Выбор ресурса в пользовательской локации шахты
     *
     * @param Request $request Запрос
     * @param string $slug Slug локации
     * @param int $id ID ресурса
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectResource(Request $request, $slug, $id)
    {
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Ищем локацию шахты по slug
        $mineLocation = MineLocation::where('slug', $slug)->firstOrFail();

        // Находим выбранный ресурс
        $resource = $mineLocation->spawnedResources()->where('id', $id)->firstOrFail();

        // Проверяем, что ресурс активен
        if (!$resource->is_active) {
            return redirect()->route('battle.mines.custom.show', $slug)
                ->with('error', 'Этот ресурс уже исчерпан.');
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'resource';
        $user->current_target_id = $resource->id;
        $user->save();

        // Обновляем статистику пользователя
        if ($user->statistics) {
            $user->statistics->current_location = $mineLocation->name;
            $user->statistics->save();
        }

        return redirect()->route('battle.mines.custom.show', $slug)
            ->with('success', 'Вы выбрали ресурс ' . $resource->resource->name . ' своей целью.');
    }

    /**
     * Добыча ресурса в пользовательской локации шахты
     *
     * @param Request $request Запрос
     * @param string $slug Slug локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function hitResource(Request $request, $slug)
    {
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'resource' || !$user->current_target_id) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Сначала выберите ресурс для добычи.');
        }

        // Ищем локацию шахты по slug
        $mineLocation = MineLocation::where('slug', $slug)->firstOrFail();

        // Находим выбранный ресурс
        $resource = $mineLocation->spawnedResources()->where('id', $user->current_target_id)->first();

        if (!$resource) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Ресурс, который вы пытались добыть, исчез.');
        }

        // Проверяем, что ресурс активен
        if (!$resource->is_active) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Этот ресурс уже исчерпан.');
        }

        // Вычисляем урон, наносимый ресурсу (может быть улучшено с учетом характеристик игрока, экипировки и т.д.)
        $damage = rand(1, 5); // Простая реализация

        // Уменьшаем прочность ресурса
        $resource->durability = max(0, $resource->durability - $damage);
        $resource->save();

        // Добавляем опыт пользователю
        $user->profile->experience += $damage;

        // Проверяем уровень пользователя
        if ($user->profile->experience >= $user->profile->experience_to_next_level) {
            $user->profile->level += 1;
            $user->profile->experience -= $user->profile->experience_to_next_level;
            $user->profile->experience_to_next_level = $user->profile->level * 100; // Простая формула

            $levelUpMessage = "Поздравляем! Вы достигли " . $user->profile->level . " уровня.";
        }

        $user->profile->save();

        // Применяем дебаф "Замечен" при добыче ресурса
        Log::info('🎯 [MINE DEBUG] Начало применения дебафа обнаружения', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'mine_location_id' => $mineLocation->id,
            'mine_location_name' => $mineLocation->name,
            'location_id' => $mineLocation->location_id,
            'resource_id' => $resource->id,
            'damage_dealt' => $damage
        ]);
        
        try {
            $detectionEffect = null;
            
            // Проверяем, существует ли таблица mine_marks
            if (\Illuminate\Support\Facades\Schema::hasTable('mine_marks')) {
                Log::info('🎯 [MINE DEBUG] Используем MineDetectionService (mine_marks)');
                $mineDetectionService = app(MineDetectionService::class);
                $detectionEffect = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
                Log::info('🎯 [MINE DEBUG] Результат MineDetectionService', [
                    'effect_created' => $detectionEffect ? 'YES' : 'NO',
                    'effect_type' => $detectionEffect ? get_class($detectionEffect) : 'NULL',
                    'effect_id' => $detectionEffect ? $detectionEffect->id : 'NULL'
                ]);
            } else {
                Log::info('🎯 [MINE DEBUG] Используем MineDetectionServiceFallback (active_effects)');
                $mineDetectionService = app(\App\Services\MineDetectionServiceFallback::class);
                $detectionEffect = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
                Log::info('🎯 [MINE DEBUG] Результат MineDetectionServiceFallback', [
                    'effect_created' => $detectionEffect ? 'YES' : 'NO',
                    'effect_type' => $detectionEffect ? get_class($detectionEffect) : 'NULL',
                    'effect_id' => $detectionEffect ? $detectionEffect->id : 'NULL'
                ]);
            }
            
            if ($detectionEffect) {
                $levelUpMessage = ($levelUpMessage ?? '') . " Вы были замечены при добыче ресурса!";
                Log::info('🎯 [MINE DEBUG] Дебаф успешно применен, уведомление добавлено');
            } else {
                Log::warning('🎯 [MINE DEBUG] Дебаф НЕ был применен!');
            }
        } catch (\Exception $e) {
            Log::error('🎯 [MINE DEBUG] Ошибка при применении дебафа обнаружения при добыче', [
                'user_id' => $user->id,
                'mine_location_id' => $mineLocation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Если ресурс исчерпан, добавляем его в инвентарь пользователя и деактивируем
        if ($resource->durability <= 0) {
            // Добавление ресурса в инвентарь (пример)
            $userResource = $user->resources()->where('resource_id', $resource->resource_id)->first();

            if ($userResource) {
                // Увеличиваем количество существующего ресурса
                $userResource->pivot->quantity += rand(1, 3); // Добавляем случайное количество
                $userResource->pivot->save();
            } else {
                // Добавляем новый ресурс в инвентарь
                $user->resources()->attach($resource->resource_id, [
                    'quantity' => rand(1, 3),
                    'location' => 'inventory'
                ]);
            }

            // Деактивируем ресурс
            $resource->is_active = false;
            $resource->save();

            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('success', 'Вы полностью добыли ресурс: ' . $resource->resource->name . '. '
                    . (isset($levelUpMessage) ? $levelUpMessage : ''));
        }

        $successMessage = 'Вы добываете ресурс. Нанесено ' . $damage . ' урона.';
        if (isset($levelUpMessage)) {
            $successMessage .= ' ' . $levelUpMessage;
        }
        
        return redirect()->route('battle.mines.custom.index', $slug)
            ->with('success', $successMessage);
    }
}
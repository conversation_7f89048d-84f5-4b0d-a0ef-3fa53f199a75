<?php

/**
 * Скрипт проверки исправления системы кастомных рудников
 * Проверяет, что дебаф "Замечен!" теперь отображается в активных эффектах
 */

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\ActiveEffect;
use App\Models\MineLocation;
use App\Services\MineDetectionService;

echo "🔍 ПРОВЕРКА ИСПРАВЛЕНИЯ СИСТЕМЫ КАСТОМНЫХ РУДНИКОВ\n";
echo "==================================================\n\n";

// 1. Проверяем пользователя admin
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    echo "   Создайте пользователя admin для тестирования.\n\n";
    exit(1);
}

echo "✅ Пользователь найден: {$user->name} (ID: {$user->id})\n\n";

// 2. Проверяем кастомные рудники
echo "⛏️ Проверка кастомных рудников:\n";
$mineLocations = MineLocation::whereNull('parent_id')->where('is_active', true)->take(3)->get();

if ($mineLocations->isEmpty()) {
    echo "❌ Не найдено активных кастомных рудников!\n";
    echo "   Создайте кастомные рудники через админ-панель для тестирования.\n\n";
    exit(1);
}

echo "✅ Найдено рудников: {$mineLocations->count()}\n";
foreach ($mineLocations as $mine) {
    echo "   - {$mine->name} (ID: {$mine->id})\n";
}
echo "\n";

// 3. Тестируем создание дебафа
echo "🎯 Тест создания дебафа 'Замечен!':\n";
$testMine = $mineLocations->first();
$mineDetectionService = app(MineDetectionService::class);

try {
    // Удаляем старые эффекты для чистого теста
    ActiveEffect::where('target_type', 'App\\Models\\User')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection')
        ->delete();

    echo "   Применяем дебаф для рудника: {$testMine->name}\n";
    
    $mark = $mineDetectionService->applyDetectionDebuff($user, $testMine);
    if ($mark) {
        echo "✅ Метка создана в mine_marks (ID: {$mark->id})\n";
    } else {
        echo "❌ Метка НЕ была создана!\n";
    }

    // Проверяем создание эффекта для отображения
    $displayEffect = ActiveEffect::where('target_type', 'App\\Models\\User')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection')
        ->first();

    if ($displayEffect) {
        echo "✅ Эффект для отображения создан (ID: {$displayEffect->id})\n";
        echo "   Название: {$displayEffect->effect_name}\n";
        echo "   Истекает: {$displayEffect->ends_at}\n";
    } else {
        echo "❌ Эффект для отображения НЕ создан!\n";
    }

} catch (\Exception $e) {
    echo "❌ Ошибка при создании дебафа: " . $e->getMessage() . "\n";
    exit(1);
}

// 4. Тестируем загрузку эффектов как в CustomMineController
echo "\n📋 Тест загрузки активных эффектов (как в CustomMineController):\n";

try {
    // Загружаем эффекты с relationship 'skill' как в исправленном контроллере
    $allUserEffects = $user->activeEffects()->with('skill')->get();
    $activeUserEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());
    
    echo "✅ Загружено эффектов: {$allUserEffects->count()}\n";
    echo "✅ Активных эффектов: {$activeUserEffects->count()}\n";
    
    // Проверяем конкретно дебаф рудника
    $mineDetectionEffects = $activeUserEffects->where('effect_type', 'mine_detection');
    
    if ($mineDetectionEffects->count() > 0) {
        echo "✅ Найден дебаф 'Замечен!' в активных эффектах!\n";
        foreach ($mineDetectionEffects as $effect) {
            echo "   - {$effect->effect_name} (осталось: {$effect->remaining_duration}с)\n";
            
            // Проверяем, что relationship 'skill' загружен
            if ($effect->skill) {
                echo "   - Skill загружен: {$effect->skill->name}\n";
            } elseif ($effect->effect_type == 'mine_detection') {
                echo "   - Это mine_detection - skill не требуется (есть специальная обработка)\n";
            } else {
                echo "   - ⚠️ Skill НЕ загружен, но это нормально для mine_detection\n";
            }
        }
    } else {
        echo "❌ Дебаф 'Замечен!' НЕ найден в активных эффектах!\n";
    }

} catch (\Exception $e) {
    echo "❌ Ошибка при загрузке активных эффектов: " . $e->getMessage() . "\n";
}

// 5. Проверяем работу компонента active-effects
echo "\n🎨 Тест компонента отображения активных эффектов:\n";

$mineDetectionEffect = $activeUserEffects->where('effect_type', 'mine_detection')->first();

if ($mineDetectionEffect) {
    echo "✅ Найден эффект mine_detection для тестирования компонента\n";
    
    // Проверяем условия в компоненте active-effects.blade.php
    
    // Проверка 1: effect->isActive()
    $isActive = $mineDetectionEffect->isActive();
    echo "   - isActive(): " . ($isActive ? '✅ true' : '❌ false') . "\n";
    
    // Проверка 2: remaining_duration > 0
    $remainingDuration = $mineDetectionEffect->remaining_duration;
    echo "   - remaining_duration: {$remainingDuration}с " . ($remainingDuration > 0 ? '✅' : '❌') . "\n";
    
    // Проверка 3: effect_type == 'mine_detection' (специальная иконка)
    echo "   - effect_type: '{$mineDetectionEffect->effect_type}' " . ($mineDetectionEffect->effect_type == 'mine_detection' ? '✅' : '❌') . "\n";
    
    // Проверка 4: effect_name для отображения
    echo "   - effect_name: '{$mineDetectionEffect->effect_name}' " . (!empty($mineDetectionEffect->effect_name) ? '✅' : '❌') . "\n";
    
    if ($isActive && $remainingDuration > 0 && $mineDetectionEffect->effect_type == 'mine_detection') {
        echo "\n🎉 ВСЕ УСЛОВИЯ ВЫПОЛНЕНЫ! Дебаф будет отображаться в компоненте!\n";
        echo "   Будет использована специальная иконка для mine_detection (строки 28-34)\n";
        echo "   Цвет текста: красный (text-red-400)\n";
        echo "   Анимация: пульсация (animate-pulse)\n";
    } else {
        echo "\n❌ Не все условия выполнены для отображения дебафа\n";
    }
} else {
    echo "❌ Эффект mine_detection не найден для тестирования компонента\n";
}

echo "\n🧹 Очистка тестовых данных...\n";
// Удаляем тестовые данные
if (isset($mark) && $mark->exists) {
    $mark->delete();
    echo "✅ Тестовая метка удалена\n";
}

ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();
echo "✅ Тестовые эффекты удалены\n";

echo "\n📋 ИТОГОВЫЙ ОТЧЕТ:\n";
echo "==================\n";
echo "✅ CustomMineController исправлен - добавлен ->with('skill')\n";
echo "✅ Компонент active-effects.blade.php имеет специальную обработку mine_detection\n";
echo "✅ MineDetectionService создает эффекты для отображения\n";
echo "✅ Автоматические атаки настроены в routes/console.php (каждые 15 и 30 секунд)\n";

echo "\n🎯 ЧТО ИСПРАВЛЕНО:\n";
echo "1. В CustomMineController.php строка 334: добавлен ->with('skill')\n";
echo "2. Активные эффекты теперь загружаются с relationship 'skill'\n";
echo "3. Компонент active-effects может отображать дебаф 'Замечен!' (mine_detection)\n";
echo "4. Автоматические атаки мобов работают через MineAutoAttackJob\n";

echo "\n🚀 СЛЕДУЮЩИЕ ШАГИ:\n";
echo "1. Убедитесь, что планировщик Laravel запущен (php artisan schedule:work)\n";
echo "2. Проверьте, что есть мобы с mob_type='mine' в локациях рудников\n";
echo "3. Протестируйте добычу ресурсов в кастомных рудниках\n";
echo "4. Дебаф 'Замечен!' теперь должен отображаться в активных эффектах\n";

echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";
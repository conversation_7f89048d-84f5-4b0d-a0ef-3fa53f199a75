<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Schema;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Обновление UserLocationService для использования is_defeated ===\n\n";

// Проверяем, есть ли поле is_defeated
$hasDefeatedField = Schema::hasColumn('user_profiles', 'is_defeated');

if (!$hasDefeatedField) {
    echo "❌ Поле 'is_defeated' не найдено в таблице user_profiles\n";
    echo "Сначала выполните: php run_defeat_migration.php\n";
    exit(1);
}

echo "✓ Поле 'is_defeated' найдено\n";

$serviceFile = 'app/Services/battle/UserLocationService.php';
$fileContent = file_get_contents($serviceFile);

if ($fileContent === false) {
    echo "❌ Не удалось прочитать файл {$serviceFile}\n";
    exit(1);
}

echo "Обновляем файл {$serviceFile}...\n";

// Обновляем первый метод getPlayersInLocation
$oldPattern1 = '// ИСПРАВЛЕНИЕ: Исключаем мертвых игроков из счетчиков
                $q->where(\'current_hp\', \'>\', 0);
                // TODO: Добавить ->where(\'is_defeated\', false) после выполнения миграции';

$newPattern1 = '// ИСПРАВЛЕНИЕ: Исключаем мертвых и поверженных игроков из счетчиков
                $q->where(\'current_hp\', \'>\', 0)
                  ->where(\'is_defeated\', false);';

$fileContent = str_replace($oldPattern1, $newPattern1, $fileContent);

// Обновляем второй метод getPlayersCountInLocation
$oldPattern2 = '// ИСПРАВЛЕНИЕ: Базовая фильтрация - исключаем мертвых игроков
        $query->whereHas(\'profile\', function ($q) use ($race, $class) {
            $q->where(\'current_hp\', \'>\', 0);
            // TODO: Добавить ->where(\'is_defeated\', false) после выполнения миграции';

$newPattern2 = '// ИСПРАВЛЕНИЕ: Базовая фильтрация - исключаем мертвых и поверженных игроков
        $query->whereHas(\'profile\', function ($q) use ($race, $class) {
            $q->where(\'current_hp\', \'>\', 0)
              ->where(\'is_defeated\', false);';

$fileContent = str_replace($oldPattern2, $newPattern2, $fileContent);

// Обновляем лог сообщения
$oldLogPattern1 = '\'query_conditions\' => [
                \'current_hp > 0\',
                \'онлайн за последние 5 минут\'
            ]';

$newLogPattern1 = '\'query_conditions\' => [
                \'current_hp > 0\',
                \'is_defeated = false\',
                \'онлайн за последние 5 минут\'
            ]';

$fileContent = str_replace($oldLogPattern1, $newLogPattern1, $fileContent);

$oldLogPattern2 = '\'applied_filters\' => [
                \'current_hp > 0\',
                \'онлайн за последние 5 минут\'
            ]';

$newLogPattern2 = '\'applied_filters\' => [
                \'current_hp > 0\',
                \'is_defeated = false\',
                \'онлайн за последние 5 минут\'
            ]';

$fileContent = str_replace($oldLogPattern2, $newLogPattern2, $fileContent);

// Сохраняем обновленный файл
$backupFile = $serviceFile . '.backup.' . date('Y-m-d-H-i-s');
file_put_contents($backupFile, file_get_contents($serviceFile));
echo "✓ Создана резервная копия: {$backupFile}\n";

if (file_put_contents($serviceFile, $fileContent) !== false) {
    echo "✓ Файл {$serviceFile} успешно обновлен\n";
    echo "✓ Теперь счетчики будут исключать поверженных игроков\n";
} else {
    echo "❌ Не удалось сохранить обновленный файл\n";
    exit(1);
}

echo "\n=== Обновление завершено ===\n";
echo "Теперь перезапустите приложение для применения изменений\n";
-- ИСПРАВЛЕНИЕ СКРЫТЫХ БОТОВ В ПОДЛОКАЦИИ xzxzxzx

-- 1. Находим ID подлокации xzxzxzx
SELECT id, name, location_id, is_active 
FROM mine_locations 
WHERE name = 'xzxzxzx';

-- 2. Находим всех ботов с именами атакующих
SELECT id, name, location, mine_location_id, is_active, created_by_admin
FROM bots 
WHERE name IN ('nvvng2', 'cccccc2', 'nvvng', 'иииииииииииии2222')
ORDER BY name;

-- 3. Находим ID подлокации (замените X на правильный ID)
SET @mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx' LIMIT 1);

-- 4. ИСПРАВЛЯЕМ конкретных ботов
UPDATE bots 
SET location = 'xzxzxzx',
    mine_location_id = @mine_location_id,
    next_action_time = NULL
WHERE name IN ('nvvng2', 'cccccc2', 'nvvng', 'иииииииииииии2222');

-- 5. ИСПРАВЛЯЕМ всех ботов с ID локации для этой подлокации
UPDATE bots 
SET location = 'xzxzxzx',
    mine_location_id = @mine_location_id,
    next_action_time = NULL
WHERE location = CAST(@mine_location_id AS CHAR)
  AND created_by_admin = true;

-- 6. Проверяем результат
SELECT 
    b.id,
    b.name,
    b.location,
    b.mine_location_id,
    b.is_active,
    b.created_by_admin,
    CASE 
        WHEN b.location = 'xzxzxzx' THEN '✅ ПРАВИЛЬНО'
        ELSE '❌ НЕПРАВИЛЬНО'
    END as status
FROM bots b
WHERE b.name IN ('nvvng2', 'cccccc2', 'nvvng', 'иииииииииииии2222')
   OR b.location = 'xzxzxzx'
   OR b.mine_location_id = @mine_location_id
ORDER BY b.name;

-- 7. Проверяем, сколько ботов теперь в админке
SELECT COUNT(*) as total_bots_in_admin
FROM bots 
WHERE location = 'xzxzxzx' 
  AND created_by_admin = true;

-- 8. Находим оставшиеся проблемные боты
SELECT 
    'Боты с ID локации' as problem_type,
    COUNT(*) as count
FROM bots 
WHERE created_by_admin = true 
  AND location REGEXP '^[0-9]+$'

UNION ALL

SELECT 
    'Боты в подлокации xzxzxzx' as problem_type,
    COUNT(*) as count
FROM bots 
WHERE location = 'xzxzxzx' 
  AND created_by_admin = true;
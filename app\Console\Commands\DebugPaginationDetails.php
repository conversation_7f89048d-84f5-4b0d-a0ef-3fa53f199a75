<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;

class DebugPaginationDetails extends Command
{
    protected $signature = 'debug:pagination-details {--page=1 : Номер страницы для тестирования}';
    protected $description = 'Детальная диагностика пагинации рудников';

    public function handle()
    {
        $page = $this->option('page');
        $this->info("=== ДЕТАЛЬНАЯ ДИАГНОСТИКА ПАГИНАЦИИ (Страница $page) ===");

        // Симулируем точно такой же запрос как в контроллере
        $customMineLocations = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->with('baseLocation')
            ->orderBy('order')
            ->paginate(4, ['*'], 'page', $page);

        $this->info("=== ОСНОВНАЯ ИНФОРМАЦИЯ ===");
        $this->info("Всего элементов: " . $customMineLocations->total());
        $this->info("Элементов на странице: " . $customMineLocations->perPage());
        $this->info("Текущая страница: " . $customMineLocations->currentPage());
        $this->info("Последняя страница: " . $customMineLocations->lastPage());
        $this->info("Элементов на текущей странице: " . $customMineLocations->count());

        $this->info("\n=== МЕТОДЫ ПАГИНАЦИИ ===");
        $this->info("hasPages(): " . ($customMineLocations->hasPages() ? 'true' : 'false'));
        $this->info("hasMorePages(): " . ($customMineLocations->hasMorePages() ? 'true' : 'false'));
        $this->info("onFirstPage(): " . ($customMineLocations->onFirstPage() ? 'true' : 'false'));
        $this->info("previousPageUrl(): " . ($customMineLocations->previousPageUrl() ?? 'null'));
        $this->info("nextPageUrl(): " . ($customMineLocations->nextPageUrl() ?? 'null'));

        $this->info("\n=== РАСЧЕТЫ ===");
        $totalItems = $customMineLocations->total();
        $perPage = $customMineLocations->perPage();
        $currentPage = $customMineLocations->currentPage();
        
        $expectedPages = ceil($totalItems / $perPage);
        $this->info("Ожидаемое количество страниц: $expectedPages");
        $this->info("Должна ли быть следующая страница: " . ($currentPage < $expectedPages ? 'ДА' : 'НЕТ'));

        $this->info("\n=== ЭЛЕМЕНТЫ НА ТЕКУЩЕЙ СТРАНИЦЕ ===");
        foreach ($customMineLocations as $index => $mine) {
            $this->info("  " . ($index + 1) . ". {$mine->name} (ID: {$mine->id}, активен: " . ($mine->is_active ? 'Да' : 'Нет') . ")");
        }

        // Проверяем, что происходит на следующей странице
        if ($customMineLocations->hasMorePages()) {
            $nextPage = $currentPage + 1;
            $this->info("\n=== ПРОВЕРКА СЛЕДУЮЩЕЙ СТРАНИЦЫ ($nextPage) ===");
            
            $nextPageItems = MineLocation::whereNull('parent_id')
                ->where('is_active', true)
                ->with('baseLocation')
                ->orderBy('order')
                ->paginate(4, ['*'], 'page', $nextPage);

            $this->info("Элементов на следующей странице: " . $nextPageItems->count());
            foreach ($nextPageItems as $index => $mine) {
                $this->info("  " . ($index + 1) . ". {$mine->name} (ID: {$mine->id})");
            }
        }

        // Проверяем все активные локации
        $this->info("\n=== ВСЕ АКТИВНЫЕ ЛОКАЦИИ ===");
        $allActive = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('order')
            ->get();
        
        $this->info("Всего активных основных локаций: " . $allActive->count());
        foreach ($allActive as $index => $mine) {
            $pageNum = floor($index / 4) + 1;
            $this->info("  " . ($index + 1) . ". {$mine->name} (страница: $pageNum, порядок: {$mine->order})");
        }

        // Проверяем, есть ли проблемы с базой данных
        $this->info("\n=== ПРОВЕРКА БАЗЫ ДАННЫХ ===");
        $totalInDb = MineLocation::count();
        $activeInDb = MineLocation::where('is_active', true)->count();
        $mainActiveInDb = MineLocation::whereNull('parent_id')->where('is_active', true)->count();
        
        $this->info("Всего локаций в БД: $totalInDb");
        $this->info("Активных локаций: $activeInDb");
        $this->info("Активных основных локаций: $mainActiveInDb");

        // Выводы и рекомендации
        $this->info("\n=== ВЫВОДЫ ===");
        if (!$customMineLocations->hasMorePages() && $totalItems > $perPage) {
            $this->error("ПРОБЛЕМА: hasMorePages() возвращает false, хотя должна быть следующая страница!");
            $this->info("Возможные причины:");
            $this->info("1. Проблема с расчетом пагинации Laravel");
            $this->info("2. Проблема с запросом к базе данных");
            $this->info("3. Кэширование старых результатов");
        } elseif ($customMineLocations->hasMorePages()) {
            $this->info("✓ Пагинация работает корректно");
        } else {
            $this->warn("Недостаточно элементов для следующей страницы");
        }

        return 0;
    }
}
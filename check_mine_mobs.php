<?php

/**
 * Быстрая проверка мобов в рудниках для автоатак
 */

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\MineLocation;
use App\Models\Mob;
use Illuminate\Support\Facades\DB;

echo "🔍 ПРОВЕРКА МОБОВ В РУДНИКАХ\n";
echo "============================\n\n";

// Получаем активные кастомные рудники
$mineLocations = MineLocation::where('is_active', true)
    ->whereNull('parent_id')
    ->with('baseLocation')
    ->get();

echo "Найдено активных кастомных рудников: {$mineLocations->count()}\n\n";

foreach ($mineLocations as $mine) {
    echo "🏔️ Рудник: {$mine->name} (ID: {$mine->id})\n";
    echo "   Base Location ID: {$mine->location_id}\n";
    
    // Все мобы в локации рудника
    $allMobs = Mob::where('location_id', $mine->location_id)->get();
    echo "   Всего мобов в локации: {$allMobs->count()}\n";
    
    if ($allMobs->count() > 0) {
        echo "   Типы мобов:\n";
        $mobTypes = $allMobs->groupBy('mob_type');
        foreach ($mobTypes as $type => $mobs) {
            $aliveCount = $mobs->where('hp', '>', 0)->count();
            echo "     - {$type}: {$mobs->count()} (живых: {$aliveCount})\n";
        }
    }
    
    // Специально для автоатак: мобы с mob_type='mine'
    $mineMobs = Mob::where('location_id', $mine->location_id)
        ->where('mob_type', 'mine')
        ->where('hp', '>', 0)
        ->get();
    
    echo "   🤖 Мобов для автоатак (mob_type='mine', hp > 0): {$mineMobs->count()}\n";
    
    if ($mineMobs->count() > 0) {
        foreach ($mineMobs->take(3) as $mob) {
            echo "     ✅ {$mob->name} (HP: {$mob->hp}, ID: {$mob->id})\n";
        }
        if ($mineMobs->count() > 3) {
            echo "     ... и ещё " . ($mineMobs->count() - 3) . " мобов\n";
        }
    } else {
        echo "     ❌ НЕТ мобов для автоатак!\n";
        
        // Предлагаем решение
        if ($allMobs->count() > 0) {
            echo "     💡 Можно изменить mob_type на 'mine' у существующих мобов:\n";
            foreach ($allMobs->take(2) as $mob) {
                echo "       UPDATE mobs SET mob_type='mine' WHERE id={$mob->id}; -- {$mob->name}\n";
            }
        } else {
            echo "     💡 Нужно создать мобов в этой локации\n";
        }
    }
    
    echo "\n";
}

// Проверяем глобально все мобы с mob_type='mine'
echo "🌍 ГЛОБАЛЬНАЯ ПРОВЕРКА:\n";
$globalMineMobs = Mob::where('mob_type', 'mine')
    ->where('hp', '>', 0)
    ->get();
    
echo "Всего мобов с mob_type='mine' и hp > 0: {$globalMineMobs->count()}\n";

if ($globalMineMobs->count() == 0) {
    echo "\n❌ КРИТИЧЕСКАЯ ПРОБЛЕМА: Нет мобов для автоатак!\n";
    echo "Система автоатак не будет работать.\n\n";
    
    echo "🔧 РЕШЕНИЯ:\n";
    echo "1. Изменить тип существующих мобов:\n";
    echo "   UPDATE mobs SET mob_type='mine' WHERE location_id IN (\n";
    foreach ($mineLocations as $mine) {
        echo "     {$mine->location_id}, -- {$mine->name}\n";
    }
    echo "   );\n\n";
    
    echo "2. Или создать новых мобов:\n";
    foreach ($mineLocations->take(2) as $mine) {
        echo "   INSERT INTO mobs (name, location_id, mob_type, hp, max_hp, level, faction_id, race_id)\n";
        echo "   VALUES ('Рудничный страж', {$mine->location_id}, 'mine', 100, 100, 5, 1, 1);\n";
    }
} else {
    echo "✅ Есть мобы для автоатак в рудниках\n";
    
    // Показываем распределение по локациям
    $mobsByLocation = $globalMineMobs->groupBy('location_id');
    echo "Распределение по локациям:\n";
    foreach ($mobsByLocation as $locationId => $mobs) {
        $locationName = $mineLocations->where('location_id', $locationId)->first()->name ?? "Локация {$locationId}";
        echo "  - {$locationName}: {$mobs->count()} мобов\n";
    }
}

echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА\n";
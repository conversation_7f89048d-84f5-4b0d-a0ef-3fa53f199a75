<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserProfile;
use App\Models\UserStatistic;
use Illuminate\Support\Facades\Auth;
use App\Models\Location;
use App\Models\Mob;
use Illuminate\Support\Facades\Redis;
use App\Services\ExperienceService;
use App\Services\BattleLogService;
use App\Services\CurrencyService;
use App\Services\DamageService;
use Illuminate\Support\Facades\Log;

use App\Services\RewardService;
class BattleController extends Controller
{
    /**
     * Конструктор контроллера боя
     * Внедряем ExperienceService, BattleLogService и DamageService через DI-контейнер Laravel
     * Это гарантирует, что для расчёта урона всегда используется единая логика (DamageService),
     * а все зависимости (например, CombatFormulaService) автоматически подставляются через контейнер
     * Такой подход обеспечивает единообразие, тестируемость и простоту поддержки логики боя
     */
    protected ExperienceService $experienceService;
    protected BattleLogService $battleLogService;
    protected DamageService $damageService;

    public function __construct(
        ExperienceService $experienceService,
        BattleLogService $battleLogService,
        DamageService $damageService
    ) {
        $this->experienceService = $experienceService;
        $this->battleLogService = $battleLogService;
        $this->damageService = $damageService;
    }

    public function index()
    {
        $user = Auth::user();
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы.');
        }

        $userProfile = $user->profile;

        // Получаем актуальные ресурсы пользователя
        $actualResources = $userProfile->getActualResources();

        // Русский комментарий: Получаем количество игроков онлайн для отображения в футере через OnlineStatusService
        $onlineStatusService = app(\App\Services\OnlineStatusService::class);
        $onlineCount = $onlineStatusService->getOnlineCount();

        // Русский комментарий: Расчет прогресса опыта для отображения в интерфейсе
        // Используем оптимизированный метод getExperienceProgress() из модели UserProfile
        $experienceProgress = null;
        if ($userProfile->level < 85) { // Максимальный уровень согласно таблице level_thresholds
            $experienceProgress = $userProfile->getExperienceProgress();
        }

        // Создаем хлебные крошки для страницы битвы
        $breadcrumbs = [
            ['label' => 'Главная', 'url' => route('home')],
            ['label' => 'Битва', 'url' => route('battle.index')],
        ];

        return view('battle.index', [
            'userProfile' => $userProfile,
            'actualResources' => $actualResources,
            'breadcrumbs' => $breadcrumbs,
            'onlineCount' => $onlineCount,
            'experienceProgress' => $experienceProgress,
            'hasUnreadMessages' => false,            // Заглушка для уведомлений о сообщениях
            'unreadMessagesCount' => 0,              // Заглушка для количества непрочитанных сообщений
            'hasBrokenItems' => false,               // Заглушка для сломанных предметов
            'brokenItemsCount' => 0,                 // Заглушка для количества сломанных предметов
        ]);
    }

    public function ElvenHaven(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Вы должны быть авторизованы.');
        }

        $userProfile = $user->profile;
        $userStatistics = $user->statistics;

        $location = Location::with('mobs')->where('name', 'Эльфийская Гавань')->firstOrFail();

        $mobsInLocation = Mob::where('location', 'Эльфийская Гавань')
            ->where('hp', '>', 0)
            ->get();

        $mob = $user->currentTarget;

        if ($mob && $mob->hp <= 0) {
            $user->current_target_id = null;
            $user->save();
            $mob = null;
        }

        $battleLogs = $this->battleLogService->getLogs('location:ElvenHaven');

        return view('battle.ElvenHaven', [
            'userProfile' => $userProfile,
            'location' => $location,
            'mob' => $mob,
            'mobsInLocation' => $mobsInLocation,
            'battleLogs' => $battleLogs,
        ]);
    }

    // public function selectMob(Request $request)
    // {
    //     $mob = Mob::where('location', 'Эльфийская Гавань')
    //         ->where('hp', '>', 0)
    //         ->inRandomOrder()
    //         ->first();

    //     if (!$mob) {
    //         return redirect()->route('battle.ElvenHaven')->with('error', 'Моб не найден.');
    //     }

    //     $user = Auth::user();
    //     $user->current_target_id = $mob->id;
    //     $user->save();

    //     return redirect()->route('battle.ElvenHaven');
    // }

    public function attack(Request $request)
    {
        $user = Auth::user();
        $mob = $user->currentTarget;

        if (!$mob || $mob->hp <= 0) {
            return redirect()->route('battle.ElvenHaven')->with('error', 'Цель не существует или уже побеждена.');
        }

        $playerDamage = $this->damageService->calculatePlayerDamage();
        $mobDamage = $this->damageService->calculateMobDamage();

        $mob->hp = max($mob->hp - $playerDamage, 0);
        $mob->save();

        $user->profile->hp = max($user->profile->hp - $mobDamage, 0);
        $user->profile->save();

        $this->battleLogService->addLog('location:ElvenHaven', "Вы нанесли {$playerDamage} урона мобу {$mob->name}.", 'info');
        $this->battleLogService->addLog('location:ElvenHaven', "{$mob->name} нанес вам {$mobDamage} урона.", 'danger');

        if ($mob->hp <= 0) {
            $user->statistics->increment('pve_wins');
            $user->current_target_id = null;

            $experienceGained = $mob->gaining_experience;
            $this->experienceService->awardExperience($user->profile, $experienceGained);

            $this->battleLogService->addLog('location:ElvenHaven', "Вы победили моба {$mob->name}!", 'success');
            $this->battleLogService->addLog('location:ElvenHaven', "Вы получили {$experienceGained} опыта.", 'success');
        }

        return redirect()->route('battle.ElvenHaven')->with('info', 'Бой продолжается.');
    }

    /**
     * Отображает страницу поражения игрока
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function defeat(Request $request)
    {
        $user = Auth::user();

        // Получаем актуальные значения HP/MP пользователя с учетом регенерации
        $actualResources = $user->profile->getActualResources();
        $actualHp = $actualResources['current_hp'];

        // Проверяем, действительно ли игрок проиграл (HP = 0 или есть флаг поражения)
        $isDefeatedInDb = false;
        try {
            $isDefeatedInDb = $user->profile->is_defeated ?? false;
        } catch (\Exception $e) {
            // Столбец еще не создан, используем только сессию
            $isDefeatedInDb = false;
        }
        
        $isDefeated = $actualHp <= 0 || session('is_defeated') === true || $isDefeatedInDb;

        // Если игрок не проиграл, перенаправляем его на главную страницу
        if (!$isDefeated) {
            Log::warning("Попытка доступа к странице поражения игроком с HP > 0", [
                'user_id' => $user->id,
                'current_hp' => $actualHp,
                'is_defeated_flag' => session('is_defeated')
            ]);

            return redirect()->route('dashboard')
                ->with('warning', 'Вы не можете посетить страницу поражения, так как не проиграли.');
        }

        // Устанавливаем флаг поражения в сессии
        session(['is_defeated' => true]);

        // Сбрасываем флаг player_dead, чтобы регенерация начала работать сразу
        session(['player_dead' => false]);

        // Гарантируем, что HP игрока равно 0
        $user->profile->current_hp = 0;
        $user->profile->last_regeneration_at = now();
        $user->profile->save();

        // Обновляем данные в Redis через PlayerHealthService
        $healthService = app(\App\Services\PlayerHealthService::class);

        // Проверяем, что пользователь авторизован и является экземпляром User
        if ($user instanceof User) {
            $healthService->setPlayerHP($user, 0, 'defeat_page_with_regeneration');
        }

        // Логируем запуск регенерации
        Log::info("Запущена регенерация HP после смерти игрока", [
            'player_id' => $user->id,
            'current_hp' => $user->profile->current_hp,
            'max_hp' => $user->profile->max_hp,
            'player_dead_flag' => session('player_dead'),
            'is_defeated_flag' => session('is_defeated')
        ]);

        // Получаем информацию о последнем атаковавшем (убийце)
        $killerType = session('killer_type');
        $killerId = session('killer_id');
        
        // Пытаемся получить информацию из БД (если столбцы существуют)
        try {
            $killerType = $killerType ?? $user->profile->defeated_by_type;
            $killerId = $killerId ?? $user->profile->defeated_by_id;
        } catch (\Exception $e) {
            // Столбцы еще не созданы, используем только сессию
        }
        $killerName = null;

        // Определяем имя убийцы в зависимости от типа
        if ($killerType && $killerId) {
            switch ($killerType) {
                case 'player':
                    $killer = User::find($killerId);
                    $killerName = $killer ? $killer->name : 'Неизвестный игрок';
                    break;
                case 'bot':
                    $killer = \App\Models\Bot::find($killerId);
                    $killerName = $killer ? $killer->name : 'Неизвестный бот';
                    break;
                case 'mob':
                    $killer = Mob::find($killerId);
                    $killerName = $killer ? $killer->name : 'Неизвестный моб';
                    break;
                default:
                    $killerName = 'Неизвестный противник';
            }
        }

        // Если информация об убийце не найдена, но есть последний атаковавший
        if ((!$killerType || !$killerId) && $user->last_attacker_type && $user->last_attacker_id) {
            $killerType = $user->last_attacker_type;
            $killerId = $user->last_attacker_id;

            // Определяем имя убийцы
            switch ($killerType) {
                case 'player':
                    $killer = User::find($killerId);
                    $killerName = $killer ? $killer->name : 'Неизвестный игрок';
                    break;
                case 'bot':
                    $killer = \App\Models\Bot::find($killerId);
                    $killerName = $killer ? $killer->name : 'Неизвестный бот';
                    break;
                case 'mob':
                    $killer = Mob::find($killerId);
                    $killerName = $killer ? $killer->name : 'Неизвестный моб';
                    break;
                default:
                    $killerName = 'Неизвестный противник';
            }
        }

        // Передаем данные в представление
        return view('battle.defeat', [
            'userProfile' => $user->profile,
            'killerType' => $killerType,
            'killerName' => $killerName,
            'actualResources' => $user->profile->getActualResources(),
            'isDefeatState' => true // Добавляем флаг состояния поражения для шаблона
        ]);
    }

    /**
     * Обрабатывает возрождение игрока после поражения
     *
     * @param string $location Локация для возрождения (tavern, city, map)
     * @return \Illuminate\Http\RedirectResponse
     */
    public function respawn(string $location = 'tavern')
    {
        $user = Auth::user();

        // Определяем, куда перенаправлять игрока
        $redirectRoute = 'masters.index'; // По умолчанию - таверна
        $locationName = 'Таверна';

        if ($location === 'city') {
            $redirectRoute = 'home';
            $locationName = 'Город';
        }

        // Устанавливаем небольшое количество HP и запускаем процесс регенерации
        // Обновляем HP через PlayerHealthService
        $healthService = app(\App\Services\PlayerHealthService::class);

        // Вычисляем начальное HP при возрождении (5% от максимального HP)
        $maxHP = $user->profile->max_hp;
        $initialHP = max(1, round($maxHP * 0.05)); // Минимум 1 HP, максимум 5% от максимального HP

        // Проверяем, что пользователь авторизован и является экземпляром User
        if ($user instanceof User) {
            // Устанавливаем начальное HP и обновляем last_update, чтобы началась регенерация
            $healthService->setPlayerHP($user, $initialHP, "respawn_to_{$locationName}");

            // Также обновляем HP в профиле пользователя
            $user->profile->current_hp = $initialHP;
            $user->profile->last_regeneration_at = now();
            $user->profile->save();
        }

        // Обновляем текущую локацию игрока
        if ($user->statistics) {
            $user->statistics->current_location = $locationName;
            $user->statistics->save();
        }

        // Очищаем информацию об убийце и флаг поражения
        session()->forget(['killer_type', 'killer_id', 'is_defeated', 'player_dead', 'death_timestamp', 'recent_victory_time']);

        // Явно устанавливаем флаг поражения в false для гарантии
        session(['is_defeated' => false]);

        // Очищаем флаг поражения в базе данных (если столбцы существуют)
        try {
            $user->profile->update([
                'is_defeated' => false,
                'defeated_by_type' => null,
                'defeated_by_id' => null,
                'defeated_at' => null
            ]);
        } catch (\Exception $e) {
            // Столбцы еще не созданы, пропускаем
            Log::warning("Не удалось обновить поля поражения: " . $e->getMessage());
        }

        // Вычисляем процент HP для логирования
        $hpPercent = round(($initialHP / $maxHP) * 100, 1);

        // Логируем возрождение
        Log::info("Игрок возродился после поражения", [
            'player_id' => $user->id,
            'location' => $locationName,
            'hp' => $initialHP,
            'max_hp' => $maxHP,
            'hp_percent' => $hpPercent
        ]);

        // Перенаправляем на выбранную локацию
        return redirect()->route($redirectRoute)
            ->with('success', "Вы возродились в локации {$locationName}. Ваше здоровье восстановлено до {$initialHP} единиц ({$hpPercent}%) и будет продолжать восстанавливаться.");
    }
}

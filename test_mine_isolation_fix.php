<?php

/**
 * Тестовый скрипт для проверки исправления изоляции таргетов в рудниках
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// Инициализируем Laravel приложение
$app = new Application(realpath(__DIR__));

$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// Теперь у нас есть доступ к Laravel
echo "🧪 Тестирование исправления изоляции таргетов в рудниках\n";
echo "======================================================\n\n";

try {
    // Тестируем получение локаций рудников
    $mineLocations = App\Models\MineLocation::where('is_active', true)->get();
    
    echo "📍 Найдено активных локаций рудников: " . $mineLocations->count() . "\n";
    
    foreach ($mineLocations as $location) {
        echo "\n🗂️  Локация: {$location->name} (slug: {$location->slug})\n";
        echo "   - Является подлокацией: " . ($location->isSubLocation() ? 'Да' : 'Нет') . "\n";
        
        if ($location->isSubLocation()) {
            echo "   - Родительская локация: {$location->parent->name}\n";
        }
        
        // Тестируем ботов в локации
        $botsInLocation = App\Models\Bot::where('is_active', true)
            ->where('hp', '>', 0);
            
        if ($location->isSubLocation()) {
            $botsInLocation->where('mine_location_id', $location->id);
        } else {
            $baseLocation = $location->baseLocation;
            if ($baseLocation) {
                $botsInLocation->where('location', $baseLocation->name)
                    ->where(function($query) use ($location) {
                        $query->whereNull('mine_location_id')
                              ->orWhere('mine_location_id', $location->id);
                    });
            }
        }
        
        $bots = $botsInLocation->get();
        echo "   - Ботов в локации: " . $bots->count() . "\n";
        
        foreach ($bots as $bot) {
            echo "     • {$bot->name} (ID: {$bot->id}, раса: {$bot->race}, mine_location_id: " . ($bot->mine_location_id ?? 'NULL') . ")\n";
        }
        
        // Тестируем мобов в локации
        if ($location->isSubLocation()) {
            $mobsInLocation = App\Models\Mob::where('mine_location_id', $location->id)
                ->where('hp', '>', 0)
                ->get();
        } else {
            $mobsInLocation = App\Models\Mob::where('location', $location->name)
                ->where('hp', '>', 0)
                ->get();
        }
        
        echo "   - Мобов в локации: " . $mobsInLocation->count() . "\n";
        
        foreach ($mobsInLocation as $mob) {
            echo "     • {$mob->name} (ID: {$mob->id}, mine_location_id: " . ($mob->mine_location_id ?? 'NULL') . ")\n";
        }
    }
    
    // Тестируем сервис изоляции
    echo "\n🔧 Тестирование MineTargetResetService\n";
    $resetService = new App\Services\MineTargetResetService();
    
    // Находим тестовые локации
    $testSubLocation = App\Models\MineLocation::where('parent_id', '!=', null)
        ->where('is_active', true)
        ->first();
        
    $testBaseLocation = App\Models\MineLocation::where('parent_id', null)
        ->where('is_active', true)
        ->first();
    
    if ($testSubLocation && $testBaseLocation) {
        echo "📝 Тестовая подлокация: {$testSubLocation->name}\n";
        echo "📝 Тестовая базовая локация: {$testBaseLocation->name}\n";
        
        // Находим тестового бота
        $testBot = App\Models\Bot::where('is_active', true)->first();
        
        if ($testBot) {
            echo "\n🤖 Тестовый бот: {$testBot->name} (ID: {$testBot->id})\n";
            
            // Проверяем доступность бота в подлокации
            $availableInSub = $resetService->isTargetAvailableInMineLocation('bot', $testBot->id, $testSubLocation);
            echo "   - Доступен в подлокации '{$testSubLocation->name}': " . ($availableInSub ? 'Да' : 'Нет') . "\n";
            
            // Проверяем доступность бота в базовой локации
            $availableInBase = $resetService->isTargetAvailableInMineLocation('bot', $testBot->id, $testBaseLocation);
            echo "   - Доступен в базовой локации '{$testBaseLocation->name}': " . ($availableInBase ? 'Да' : 'Нет') . "\n";
        }
    }
    
    echo "\n✅ Тест завершен успешно!\n";
    echo "\n📋 Исправления применены:\n";
    echo "   1. ✅ Исправлена изоляция ботов в CustomMineController::selectBot()\n";
    echo "   2. ✅ Исправлена изоляция ботов в CustomMineController::attackBot()\n";
    echo "   3. ✅ Исправлена изоляция игроков в CustomMineController::attackPlayer()\n";
    echo "   4. ✅ Исправлена изоляция игроков в CustomMineController::retaliate()\n";
    echo "   5. ✅ Исправлена изоляция игроков в CustomMineController::attackAnyPlayer()\n";
    echo "   6. ✅ Улучшена проверка целей в MineLocationController::handleUserTarget()\n";
    echo "   7. ✅ Сделан публичным метод isTargetAvailableInMineLocation в MineTargetResetService\n";
    
    echo "\n🎯 Теперь таргеты изолированы:\n";
    echo "   • Игрок в подлокации может атаковать только ботов/игроков В ЭТОЙ подлокации\n";
    echo "   • Игрок в базовой локации может атаковать только ботов/игроков базовой локации\n";
    echo "   • Нет кросс-таргетинга между разными подлокациями или между подлокацией и базовой локацией\n";

} catch (Exception $e) {
    echo "❌ Ошибка при тестировании: " . $e->getMessage() . "\n";
    echo "Трассировка: " . $e->getTraceAsString() . "\n";
}
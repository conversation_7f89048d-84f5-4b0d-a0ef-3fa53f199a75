@echo off
echo =================================================
echo ЗАПУСК МИГРАЦИЙ ДЛЯ СИСТЕМЫ АВТОАТАК В РУДНИКАХ
echo =================================================
echo.

echo 1. Проверка статуса миграций...
php artisan migrate:status

echo.
echo 2. Запуск только новой миграции mine_marks...
php artisan migrate --path=database/migrations/2025_07_21_160000_create_mine_marks_table_fixed.php

echo.
echo 3. Проверка создания таблицы mine_marks...
php artisan tinker --execute="
use Illuminate\Support\Facades\Schema;
if (Schema::hasTable('mine_marks')) {
    echo '✅ Таблица mine_marks успешно создана' . PHP_EOL;
    echo 'Колонки: ' . implode(', ', Schema::getColumnListing('mine_marks')) . PHP_EOL;
} else {
    echo '❌ Таблица mine_marks НЕ создана' . PHP_EOL;
}
"

echo.
echo 4. Тестирование системы...
php setup_mine_detection_system.php

echo.
echo 5. Тестирование добычи ресурса...
echo Теперь можете зайти в игру и попробовать добыть ресурс в руднике!
echo Для проверки автоатак запустите: php artisan mine:auto-attack

echo.
pause
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bots', function (Blueprint $table) {
            // Проверяем, существует ли колонка mine_location_id
            if (!Schema::hasColumn('bots', 'mine_location_id')) {
                $table->unsignedBigInteger('mine_location_id')->nullable()->after('location');
                echo "Добавлена колонка mine_location_id в таблицу bots\n";
            } else {
                echo "Колонка mine_location_id уже существует в таблице bots\n";
            }
            
            // Проверяем внешний ключ
            $foreignKeys = collect(DB::select("
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'bots' 
                AND constraint_type = 'FOREIGN KEY' 
                AND constraint_name LIKE '%mine_location_id%'
            "))->pluck('constraint_name');
            
            if ($foreignKeys->isEmpty() && Schema::hasTable('mine_locations')) {
                $table->foreign('mine_location_id')->references('id')->on('mine_locations')->onDelete('set null');
                echo "Добавлен внешний ключ mine_location_id в таблицу bots\n";
            }
            
            // Проверяем индекс
            $indexes = collect(DB::select("
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'bots' 
                AND indexdef LIKE '%mine_location_id%'
            "))->pluck('indexname');
            
            if ($indexes->isEmpty()) {
                $table->index(['mine_location_id', 'is_active', 'hp']);
                echo "Добавлен индекс для mine_location_id в таблицу bots\n";
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bots', function (Blueprint $table) {
            $table->dropForeign(['mine_location_id']);
            $table->dropIndex(['mine_location_id', 'is_active', 'hp']);
            $table->dropColumn('mine_location_id');
        });
    }
};
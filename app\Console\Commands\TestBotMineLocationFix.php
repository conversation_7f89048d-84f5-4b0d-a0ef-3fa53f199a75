<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Bot;
use App\Models\MineLocation;
use App\Models\Location;
use App\Services\battle\FactionCountService;
use App\Services\RedisKeyService;

class TestBotMineLocationFix extends Command
{
    protected $signature = 'test:bot-mine-location-fix';
    protected $description = 'Тестирует исправления для отображения ботов в рудниках';

    public function handle()
    {
        $this->info('=== Тестирование исправлений для ботов в рудниках ===');

        // Тест 1: Проверка структуры таблицы bots
        $this->info('1. Проверка структуры таблицы bots...');
        $hasColumn = \Schema::hasColumn('bots', 'mine_location_id');
        $this->info("Поле mine_location_id в таблице bots: " . ($hasColumn ? 'ЕСТЬ' : 'ОТСУТСТВУЕТ'));

        // Тест 2: Проверка Redis сервиса
        $this->info('2. Проверка RedisKeyService...');
        try {
            $redisService = app(RedisKeyService::class);
            $testKey = $redisService->getBotLocationKey('test_location');
            $this->info("Тест ключа для ботов: $testKey");
            
            $mineKey = $redisService->getMineBotLocationKey(1);
            $this->info("Тест ключа для рудника: $mineKey");
            $this->info("RedisKeyService: РАБОТАЕТ");
        } catch (\Exception $e) {
            $this->error("Ошибка RedisKeyService: " . $e->getMessage());
        }

        // Тест 3: Проверка TTL конфигурации
        $this->info('3. Проверка TTL конфигурации...');
        try {
            $ttlConfig = config('cache_ttl');
            $this->info("Конфигурация TTL загружена: " . ($ttlConfig ? 'ДА' : 'НЕТ'));
            if ($ttlConfig) {
                $this->info("TTL для ботов: " . ($ttlConfig['bots']['data'] ?? 'НЕ ЗАДАН'));
            }
        } catch (\Exception $e) {
            $this->error("Ошибка TTL конфигурации: " . $e->getMessage());
        }

        // Тест 4: Проверка FactionCountService
        $this->info('4. Проверка FactionCountService...');
        try {
            $factionService = app(FactionCountService::class);
            
            // Находим первую локацию рудника
            $mineLocation = MineLocation::with('baseLocation')->where('is_active', true)->first();
            if ($mineLocation) {
                $this->info("Тестируем на локации рудника: {$mineLocation->name}");
                
                // Тест подсчета для базовой локации
                $baseCounts = $factionService->getLocationFactionCounts($mineLocation->baseLocation->name);
                $this->info("Подсчет для базовой локации: " . json_encode($baseCounts['total_counts']));
                
                // Тест подсчета для рудника
                if (method_exists($factionService, 'getMineFactionCounts')) {
                    $mineCounts = $factionService->getMineFactionCounts($mineLocation->id);
                    $this->info("Подсчет для рудника: " . json_encode($mineCounts['total_counts']));
                }
            } else {
                $this->warn("Не найдена активная локация рудника для тестирования");
            }
        } catch (\Exception $e) {
            $this->error("Ошибка FactionCountService: " . $e->getMessage());
        }

        // Тест 5: Проверка ботов с mine_location_id
        $this->info('5. Проверка ботов с mine_location_id...');
        if ($hasColumn) {
            $botsWithMineLocation = Bot::whereNotNull('mine_location_id')->count();
            $this->info("Ботов с mine_location_id: $botsWithMineLocation");
            
            $allActiveBots = Bot::where('is_active', true)->count();
            $this->info("Всего активных ботов: $allActiveBots");
        }

        // Тест 6: Проверка админ контроллера
        $this->info('6. Проверка админ контроллера...');
        try {
            $controller = app(\App\Http\Controllers\Admin\BotController::class);
            $this->info("BotController загружен: ДА");
            
            // Проверяем, что метод handleMineLocationAssignment существует
            if (method_exists($controller, 'handleMineLocationAssignment')) {
                $this->info("Метод handleMineLocationAssignment: ЕСТЬ");
            } else {
                $this->error("Метод handleMineLocationAssignment: ОТСУТСТВУЕТ");
            }
        } catch (\Exception $e) {
            $this->error("Ошибка BotController: " . $e->getMessage());
        }

        // Тест 7: Симуляция создания бота для рудника
        $this->info('7. Симуляция создания бота для рудника...');
        try {
            $mineLocation = MineLocation::with('baseLocation')->where('is_active', true)->first();
            if ($mineLocation && $hasColumn) {
                $testBotData = [
                    'name' => 'TestBot_' . time(),
                    'race' => 'solarius',
                    'class' => 'warrior',
                    'level' => 1,
                    'hp' => 100,
                    'max_hp' => 100,
                    'mp' => 50,
                    'max_mp' => 50,
                    'strength' => 10,
                    'intelligence' => 10,
                    'dexterity' => 10,
                    'armor' => 5,
                    'magic_resistance' => 5,
                    'location' => $mineLocation->baseLocation->name,
                    'mine_location_id' => $mineLocation->id,
                    'is_active' => true,
                    'created_by_admin' => true,
                    'base_damage' => 10,
                ];
                
                $testBot = Bot::create($testBotData);
                $this->info("Тестовый бот создан: ID {$testBot->id}");
                $this->info("Локация: {$testBot->location}");
                $this->info("Mine Location ID: {$testBot->mine_location_id}");
                
                // Удаляем тестового бота
                $testBot->delete();
                $this->info("Тестовый бот удален");
            } else {
                $this->warn("Не удалось создать тестового бота - нет подходящей локации или поля mine_location_id");
            }
        } catch (\Exception $e) {
            $this->error("Ошибка создания тестового бота: " . $e->getMessage());
        }

        $this->info('=== Тестирование завершено ===');
        
        // Итоговая сводка
        $this->info('ИТОГОВАЯ СВОДКА:');
        $this->info('- Миграция поля mine_location_id: ' . ($hasColumn ? 'ВЫПОЛНЕНА' : 'ТРЕБУЕТСЯ'));
        $this->info('- RedisKeyService: НАСТРОЕН');
        $this->info('- TTL конфигурация: НАСТРОЕНА');
        $this->info('- FactionCountService: ОБНОВЛЕН');
        $this->info('- BotController: ОБНОВЛЕН');
        $this->info('- CustomMineController: ОБНОВЛЕН');
        
        return 0;
    }
}
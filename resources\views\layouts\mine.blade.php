<!DOCTYPE html>
<html lang="en">
@php use Illuminate\Support\Facades\Auth; @endphp {{-- Используем фасад Auth --}}

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Эльфийская Гавань - {{ Auth::check() ? Auth::user()->name : 'Гость' }}</title>

    {{-- Безопасная загрузка ассетов --}}
    <x-secure-assets />

    {{-- Подключение основных стилей в head --}}
    @vite(['resources/css/app.css', 'resources/css/battle-animations.css'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    {{-- <PERSON><PERSON><PERSON><PERSON>ной контейнер страницы --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

       

        {{-- Сообщения --}}
        <div class="text-center flex justify-center space-x-1">
            @if (session('welcome_message'))
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    {{ session('welcome_message') }}
                </div>
            @endif
        </div>

        {{-- Блок изображения локации --}}
        <div class="mb-2">
            {{-- Отображение активных эффектов --}}
            <x-layout.active-effects :userEffects="$userEffects" />

            {{-- Определяем, оглушен ли пользователь --}}
            @php
                $isStunned = $userEffects->contains(function ($effect) {
                    return $effect->skill_id == 14 && $effect->isActive();
                });
            @endphp

            {{-- Заголовок рудника без изображения --}}
            <x-layout.mines-header :breadcrumbs="$breadcrumbs" :title="$locationTitle ?? $locationName ?? 'Рудник'" />
        </div>
 {{-- Flash-сообщения --}}
        <x-game-flash-messages />
        {{-- Основное содержимое --}}
        @yield('content')

        {{-- Панель быстрого использования зелий --}}
        <div class="mt-2 mb-2">
            <x-user.quick-potion-bar class="flex justify-center items-center" />
        </div>

        {{-- Панель умений --}}
        <x-battle.skills-panel routePrefix="{{ $routePrefix ?? 'battle.outposts.elven_haven' }}"
            :isStunned="$isStunned" />

        {{-- Блок навигации по локациям рудника (только в layout, убираем дублирование) --}}
        @if(request()->is('battle/mines/*') && isset($subLocations))
            <x-battle.mines.navigation-block :isStunned="$isStunned ?? false" routePrefix="battle.mines.custom"
                currentLocation="{{ request()->route('slug') ?? 'default' }}" :subLocations="$subLocations ?? null" />
        @endif

        {{-- Логи боя --}}
        <x-battle.battle-logs :battleLogs="$battleLogs" />

        {{-- Нижние кнопки навигации --}}
        <x-layout.navigation-buttons />
    </div>

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />



    {{-- Подключение скриптов --}}
    @vite(['resources/js/app.js', 'resources/js/global/csrf.js', 'resources/js/attackLimiter.js', 'resources/js/layout/footer-counters.js', 'resources/js/layout/server-time.js'])
</body>

</html>
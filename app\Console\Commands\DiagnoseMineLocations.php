<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;

class DiagnoseMineLocations extends Command
{
    protected $signature = 'diagnose:mine-locations';
    protected $description = 'Диагностирует проблемы с отображением локаций рудников';

    public function handle()
    {
        $this->info('=== ДИАГНОСТИКА ЛОКАЦИЙ РУДНИКОВ ===');

        // Получаем все локации из админки
        $allLocations = MineLocation::all();
        $this->info("Всего локаций в админке: " . $allLocations->count());

        // Проверяем, какие локации отображаются на странице
        $displayedLocations = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->with('baseLocation')
            ->orderBy('order')
            ->get();
        
        $this->info("Отображается на странице: " . $displayedLocations->count());

        $this->info("\n=== АНАЛИЗ ВСЕХ ЛОКАЦИЙ ===");
        
        $activeCount = 0;
        $inactiveCount = 0;
        $sublocationsCount = 0;
        $noBaseLocationCount = 0;

        foreach ($allLocations as $location) {
            $status = [];
            
            // Проверяем активность
            if ($location->is_active) {
                $status[] = "АКТИВЕН";
                $activeCount++;
            } else {
                $status[] = "НЕАКТИВЕН";
                $inactiveCount++;
            }

            // Проверяем, является ли подлокацией
            if ($location->parent_id) {
                $status[] = "ПОДЛОКАЦИЯ (parent_id: {$location->parent_id})";
                $sublocationsCount++;
            } else {
                $status[] = "ОСНОВНАЯ ЛОКАЦИЯ";
            }

            // Проверяем базовую локацию
            if (!$location->baseLocation) {
                $status[] = "НЕТ БАЗОВОЙ ЛОКАЦИИ";
                $noBaseLocationCount++;
            } else {
                $status[] = "Базовая: {$location->baseLocation->name}";
            }

            $statusText = implode(' | ', $status);
            $this->info("  {$location->name} (ID: {$location->id}) - $statusText");
        }

        $this->info("\n=== СТАТИСТИКА ===");
        $this->info("Активных: $activeCount");
        $this->info("Неактивных: $inactiveCount");
        $this->info("Подлокаций: $sublocationsCount");
        $this->info("Без базовой локации: $noBaseLocationCount");

        // Показываем, почему локации не отображаются
        $this->info("\n=== ПРИЧИНЫ НЕОТОБРАЖЕНИЯ ===");
        
        $hiddenByInactive = MineLocation::whereNull('parent_id')
            ->where('is_active', false)
            ->count();
        
        $hiddenBySublocations = MineLocation::whereNotNull('parent_id')
            ->where('is_active', true)
            ->count();

        if ($hiddenByInactive > 0) {
            $this->warn("Скрыто неактивных основных локаций: $hiddenByInactive");
        }
        
        if ($hiddenBySublocations > 0) {
            $this->warn("Скрыто подлокаций: $hiddenBySublocations");
        }

        // Показываем локации, которые можно активировать
        $canActivate = MineLocation::whereNull('parent_id')
            ->where('is_active', false)
            ->whereHas('baseLocation')
            ->get();

        if ($canActivate->count() > 0) {
            $this->info("\n=== ЛОКАЦИИ, КОТОРЫЕ МОЖНО АКТИВИРОВАТЬ ===");
            foreach ($canActivate as $location) {
                $this->info("  {$location->name} (ID: {$location->id})");
            }
        }

        // Предлагаем решения
        $this->info("\n=== РЕКОМЕНДАЦИИ ===");
        
        if ($displayedLocations->count() <= 4) {
            $this->warn("У вас отображается только {$displayedLocations->count()} локации!");
            
            if ($hiddenByInactive > 0) {
                $this->info("1. Активируйте неактивные локации:");
                $this->info("   php artisan fix:activate-mine-locations");
            }
            
            if ($hiddenBySublocations > 0) {
                $this->info("2. Некоторые локации являются подлокациями и не отображаются в списке");
                $this->info("   Подлокации отображаются внутри основных локаций");
            }
            
            if ($noBaseLocationCount > 0) {
                $this->info("3. Некоторые локации не имеют базовой локации");
                $this->info("   Настройте базовые локации в админке");
            }
        } else {
            $this->info("Локаций достаточно для пагинации!");
        }

        return 0;
    }
}
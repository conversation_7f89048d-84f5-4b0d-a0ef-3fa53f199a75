<?php

/**
 * Скрипт для диагностики и исправления проблем с ботами в рудниках
 * Выполняет проверку изоляции подлокаций и исправляет найденные проблемы
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\UserStatistic;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

class MineBotDiagnostic {
    
    public function run() {
        echo "🔍 ДИАГНОСТИКА СИСТЕМЫ БОТОВ В РУДНИКАХ\n";
        echo "=====================================\n\n";
        
        // 1. Проверяем структуру данных
        $this->checkDataStructure();
        
        // 2. Проверяем изоляцию подлокаций
        $this->checkSubLocationIsolation();
        
        // 3. Исправляем найденные проблемы
        $this->fixIssues();
        
        // 4. Тестируем систему
        $this->testSystem();
        
        echo "\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА\n";
    }
    
    private function checkDataStructure() {
        echo "1. ПРОВЕРКА СТРУКТУРЫ ДАННЫХ\n";
        echo "----------------------------\n";
        
        // Проверяем рудники
        $mineLocations = MineLocation::where('is_active', true)->get();
        echo "Активных рудников: " . $mineLocations->count() . "\n";
        
        foreach ($mineLocations as $mine) {
            $botsCount = Bot::where('location', $mine->name)->count();
            echo "  📍 {$mine->name}: {$botsCount} ботов\n";
        }
        
        // Проверяем ботов с неправильными локациями
        $botsWithIdLocation = Bot::whereRaw('location REGEXP \'^[0-9]+$\'')->get();
        if ($botsWithIdLocation->count() > 0) {
            echo "⚠️  Найдено ботов с ID локации вместо названия: {$botsWithIdLocation->count()}\n";
        }
        
        echo "\n";
    }
    
    private function checkSubLocationIsolation() {
        echo "2. ПРОВЕРКА ИЗОЛЯЦИИ ПОДЛОКАЦИЙ\n";
        echo "-------------------------------\n";
        
        $mineLocations = MineLocation::where('is_active', true)->get();
        $issues = [];
        
        foreach ($mineLocations as $mine) {
            // Получаем ботов в этой подлокации
            $bots = Bot::where('location', $mine->name)->where('is_active', true)->get();
            
            // Получаем игроков в этой подлокации
            $players = User::whereHas('statistics', function ($query) use ($mine) {
                $query->where('current_location', $mine->name);
            })->with(['profile', 'statistics'])->get();
            
            echo "📍 {$mine->name}:\n";
            echo "   🤖 Ботов: {$bots->count()}\n";
            echo "   👥 Игроков: {$players->count()}\n";
            
            // Проверяем совпадение локаций
            foreach ($bots as $bot) {
                $wrongTargets = User::whereHas('statistics', function ($query) use ($mine) {
                    $query->where('current_location', '!=', $mine->name);
                })->whereHas('profile', function ($query) use ($bot) {
                    $enemyRace = $bot->race === 'solarius' ? 'lunarius' : 'solarius';
                    $query->where('race', $enemyRace);
                })->get();
                
                if ($wrongTargets->count() > 0) {
                    $issues[] = [
                        'bot' => $bot->name,
                        'location' => $mine->name,
                        'wrong_targets' => $wrongTargets->count()
                    ];
                }
            }
            
            // Проверяем расы
            $solariumPlayers = $players->filter(fn($p) => $p->profile->race === 'solarius')->count();
            $lunariumPlayers = $players->filter(fn($p) => $p->profile->race === 'lunarius')->count();
            
            echo "   ☀️  Solarius: {$solariumPlayers}\n";
            echo "   🌙 Lunarius: {$lunariumPlayers}\n";
            echo "\n";
        }
        
        if (!empty($issues)) {
            echo "⚠️  Найдены проблемы с изоляцией:\n";
            foreach ($issues as $issue) {
                echo "   - {$issue['bot']} в {$issue['location']}: {$issue['wrong_targets']} неправильных целей\n";
            }
        } else {
            echo "✅ Проблем с изоляцией не найдено\n";
        }
        
        echo "\n";
    }
    
    private function fixIssues() {
        echo "3. ИСПРАВЛЕНИЕ ПРОБЛЕМ\n";
        echo "---------------------\n";
        
        // Исправляем ботов с ID локации
        $botsFixed = 0;
        $mineLocations = MineLocation::where('is_active', true)->get();
        
        foreach ($mineLocations as $mine) {
            $bots = Bot::where('location', (string) $mine->id)->get();
            
            foreach ($bots as $bot) {
                $oldLocation = $bot->location;
                $bot->location = $mine->name;
                $bot->mine_location_id = $mine->id;
                $bot->save();
                
                echo "✅ {$bot->name}: '{$oldLocation}' → '{$bot->location}'\n";
                $botsFixed++;
            }
        }
        
        if ($botsFixed > 0) {
            echo "✅ Исправлено локаций у ботов: {$botsFixed}\n";
        } else {
            echo "✅ Исправлений не потребовалось\n";
        }
        
        // Сбрасываем кулдауны
        Bot::where('created_by_admin', true)->update(['next_action_time' => null]);
        echo "✅ Сброшены кулдауны всех ботов\n";
        
        echo "\n";
    }
    
    private function testSystem() {
        echo "4. ТЕСТИРОВАНИЕ СИСТЕМЫ\n";
        echo "----------------------\n";
        
        $mineLocations = MineLocation::where('is_active', true)->take(3)->get();
        
        foreach ($mineLocations as $mine) {
            $bots = Bot::where('location', $mine->name)->where('is_active', true)->get();
            
            echo "📍 Тестирование {$mine->name}:\n";
            
            foreach ($bots as $bot) {
                $enemyRace = $bot->race === 'solarius' ? 'lunarius' : 'solarius';
                
                // Проверяем поиск целей с правильной изоляцией
                $targets = User::whereHas('profile', function ($query) use ($enemyRace) {
                    $query->where('race', $enemyRace)->where('current_hp', '>', 0);
                })->whereHas('statistics', function ($query) use ($mine) {
                    $query->where('current_location', $mine->name);
                })->count();
                
                echo "   🤖 {$bot->name} ({$bot->race}): {$targets} целей ({$enemyRace})\n";
            }
        }
        
        echo "\n";
    }
}

// Запуск диагностики
$diagnostic = new MineBotDiagnostic();
$diagnostic->run();
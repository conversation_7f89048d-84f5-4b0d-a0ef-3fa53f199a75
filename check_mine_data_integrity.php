<?php

/**
 * Проверка целостности данных для системы рудников
 */

require_once __DIR__ . '/vendor/autoload.php';

// Инициализируем Laravel приложение
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\MineLocation;
use App\Models\Location;
use App\Models\User;
use Illuminate\Support\Facades\Schema;

echo "🔍 ПРОВЕРКА ЦЕЛОСТНОСТИ ДАННЫХ РУДНИКОВ\n";
echo "======================================\n\n";

try {
    // 1. Проверяем пользователя admin
    echo "1️⃣ Проверка пользователей...\n";
    
    $admin = User::where('name', 'admin')->first();
    if ($admin) {
        echo "   ✅ Admin найден: {$admin->name} (ID: {$admin->id})\n";
        if ($admin->profile) {
            echo "   ✅ Профиль admin существует (ID: {$admin->profile->id})\n";
        } else {
            echo "   ❌ У admin нет профиля!\n";
        }
    } else {
        echo "   ❌ Пользователь admin не найден!\n";
        
        $anyUser = User::with('profile')->first();
        if ($anyUser) {
            echo "   💡 Найден другой пользователь: {$anyUser->name} (ID: {$anyUser->id})\n";
            $admin = $anyUser; // Используем для дальнейших тестов
        }
    }
    
    // 2. Проверяем локации рудников
    echo "\n2️⃣ Проверка локаций рудников...\n";
    
    $mineLocations = MineLocation::all();
    echo "   📊 Всего локаций рудников: " . $mineLocations->count() . "\n";
    
    if ($mineLocations->count() == 0) {
        echo "   ❌ НЕТ ЛОКАЦИЙ РУДНИКОВ!\n";
        echo "   💡 Создайте локации рудников через админ-панель или сидеры\n";
        exit(1);
    }
    
    foreach ($mineLocations as $mine) {
        echo "   📍 {$mine->name} (ID: {$mine->id})\n";
        echo "      - Slug: {$mine->slug}\n";
        echo "      - Location ID: " . ($mine->location_id ?: 'NULL ❌') . "\n";
        echo "      - Активна: " . ($mine->is_active ? 'Да' : 'Нет') . "\n";
        
        if ($mine->location_id) {
            $baseLocation = Location::find($mine->location_id);
            if ($baseLocation) {
                echo "      - Базовая локация: {$baseLocation->name} ✅\n";
            } else {
                echo "      - Базовая локация: НЕ НАЙДЕНА ❌\n";
            }
        }
        
        // Проверяем ресурсы
        $resources = $mine->spawnedResources()->count();
        echo "      - Ресурсов: {$resources}\n";
        
        echo "\n";
    }
    
    // 3. Выбираем локацию для тестов
    echo "3️⃣ Выбор локации для тестов...\n";
    
    $testMine = $mineLocations->where('is_active', true)->first();
    if (!$testMine) {
        echo "   ❌ Нет активных локаций рудников!\n";
        $testMine = $mineLocations->first();
        if ($testMine) {
            echo "   💡 Используем неактивную локацию: {$testMine->name}\n";
        }
    } else {
        echo "   ✅ Выбрана активная локация: {$testMine->name}\n";
    }
    
    if (!$testMine) {
        echo "   ❌ Нет локаций для тестов!\n";
        exit(1);
    }
    
    // 4. Проверяем критические поля
    echo "\n4️⃣ Проверка критических полей...\n";
    
    $issues = [];
    
    if (!$testMine->location_id) {
        $issues[] = "location_id равен NULL";
        echo "   ❌ location_id равен NULL!\n";
    } else {
        echo "   ✅ location_id: {$testMine->location_id}\n";
    }
    
    if (!$testMine->name) {
        $issues[] = "name пустое";
        echo "   ❌ name пустое!\n";
    } else {
        echo "   ✅ name: {$testMine->name}\n";
    }
    
    if (!$admin) {
        $issues[] = "нет пользователя для тестов";
        echo "   ❌ Нет пользователя для тестов!\n";
    } else {
        echo "   ✅ Пользователь для тестов: {$admin->name}\n";
    }
    
    // 5. Проверяем структуру таблиц
    echo "\n5️⃣ Проверка структуры таблиц...\n";
    
    $tables = ['mine_marks', 'active_effects', 'mine_locations', 'users'];
    foreach ($tables as $table) {
        $exists = Schema::hasTable($table);
        echo "   Таблица {$table}: " . ($exists ? '✅' : '❌') . "\n";
        
        if (!$exists && $table == 'mine_marks') {
            $issues[] = "отсутствует таблица mine_marks";
        }
    }
    
    // 6. Резюме
    echo "\n6️⃣ РЕЗЮМЕ ПРОВЕРКИ:\n";
    echo "===================\n";
    
    if (empty($issues)) {
        echo "✅ Все проверки прошли успешно!\n";
        echo "💡 Данные выглядят корректными для системы обнаружения\n\n";
        
        echo "📋 Параметры для тестов:\n";
        echo "   - Пользователь: {$admin->name} (ID: {$admin->id})\n";
        echo "   - Рудник: {$testMine->name} (ID: {$testMine->id})\n";
        echo "   - Базовая локация: ID {$testMine->location_id}\n";
        echo "   - Slug для URL: {$testMine->slug}\n";
        
    } else {
        echo "❌ НАЙДЕНЫ ПРОБЛЕМЫ:\n";
        foreach ($issues as $i => $issue) {
            echo "   " . ($i + 1) . ". " . ucfirst($issue) . "\n";
        }
        
        echo "\n🔧 РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ:\n";
        
        if (in_array("location_id равен NULL", $issues)) {
            echo "   1. Обновите поле location_id в mine_locations:\n";
            echo "      UPDATE mine_locations SET location_id = 1 WHERE location_id IS NULL;\n";
        }
        
        if (in_array("отсутствует таблица mine_marks", $issues)) {
            echo "   2. Создайте таблицу mine_marks:\n";
            echo "      php artisan migrate --path=database/migrations/2025_07_21_160000_create_mine_marks_table_fixed.php\n";
        }
    }
    
    // 7. Пробный тест создания дебафа
    if (empty($issues) && $admin && $testMine) {
        echo "\n7️⃣ ПРОБНЫЙ ТЕСТ СОЗДАНИЯ ДЕБАФА...\n";
        echo "==================================\n";
        
        try {
            if (Schema::hasTable('mine_marks')) {
                echo "   🎯 Тест с MineDetectionService...\n";
                $service = app(\App\Services\MineDetectionService::class);
                $result = $service->applyDetectionDebuff($admin, $testMine);
                
                if ($result) {
                    echo "   ✅ Дебаф создан успешно! (ID: {$result->id})\n";
                } else {
                    echo "   ❌ Не удалось создать дебаф\n";
                }
            } else {
                echo "   🎯 Тест с MineDetectionServiceFallback...\n";
                $service = app(\App\Services\MineDetectionServiceFallback::class);
                $result = $service->applyDetectionDebuff($admin, $testMine);
                
                if ($result) {
                    echo "   ✅ Дебаф создан успешно! (ID: {$result->id})\n";
                } else {
                    echo "   ❌ Не удалось создать дебаф\n";
                }
            }
        } catch (\Exception $e) {
            echo "   ❌ Ошибка при тестировании: {$e->getMessage()}\n";
            echo "   📍 {$e->getFile()}:{$e->getLine()}\n";
        }
    }
    
    echo "\nПроверка завершена!\n";
    
} catch (Exception $e) {
    echo "\n❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "📍 {$e->getFile()}:{$e->getLine()}\n";
}
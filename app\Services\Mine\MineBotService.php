<?php

namespace App\Services\Mine;

use App\Models\Bot;
use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Services\PlayerHealthService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Collection;

/**
 * Основной сервис для управления ботами рудников
 * Адаптирован с системы ботов аванпостов для работы в рудниках
 * Заменяет сложную Lua-архитектуру на простую PHP-логику с Redis кэшированием
 */
class MineBotService
{
    protected MineBotCacheService $cacheService;
    protected MineBotAttackService $attackService;
    protected MineBotHealingService $healingService;
    protected PlayerHealthService $playerHealthService;
    protected array $config;

    public function __construct(
        MineBotCacheService $cacheService,
        MineBotAttackService $attackService,
        MineBotHealingService $healingService,
        PlayerHealthService $playerHealthService
    ) {
        $this->cacheService = $cacheService;
        $this->attackService = $attackService;
        $this->healingService = $healingService;
        $this->playerHealthService = $playerHealthService;
        $this->config = config('mine_bots');
    }

    /**
     * Обрабатывает всех ботов в руднике
     */
    public function processMineBots(string $mineName): array
    {
        $startTime = microtime(true);
        $stats = [
            'mine' => $mineName,
            'bots_processed' => 0,
            'attacks_executed' => 0,
            'heals_executed' => 0,
            'errors' => 0,
            'execution_time' => 0,
        ];

        try {
            Log::info("MineBotService: Начало обработки ботов в руднике {$mineName}");

            // Проверяем, что это действительно рудник
            if (!$this->isValidMine($mineName)) {
                throw new \Exception("Локация {$mineName} не является активным рудником");
            }

            // Кэшируем данные локации
            $this->cacheLocationData($mineName);

            // Получаем активных ботов в руднике
            $bots = $this->getActiveMineBots($mineName);

            if ($bots->isEmpty()) {
                Log::info("MineBotService: Нет активных ботов в руднике {$mineName}");
                return $stats;
            }

            Log::info("MineBotService: Найдено {$bots->count()} активных ботов в руднике {$mineName}");

            // Обрабатываем ботов пакетами для оптимизации
            $batchSize = $this->config['processing']['batch_size'];
            $batches = $bots->chunk($batchSize);

            foreach ($batches as $batch) {
                $batchStats = $this->processBotBatch($batch, $mineName);
                $stats['bots_processed'] += $batchStats['bots_processed'];
                $stats['attacks_executed'] += $batchStats['attacks_executed'];
                $stats['heals_executed'] += $batchStats['heals_executed'];
                $stats['errors'] += $batchStats['errors'];

                // Проверяем лимиты памяти и времени
                if ($this->shouldStopProcessing($startTime)) {
                    Log::warning("MineBotService: Остановка обработки из-за лимитов времени/памяти");
                    break;
                }
            }

        } catch (\Exception $e) {
            Log::error("MineBotService: Ошибка обработки ботов в руднике {$mineName}: " . $e->getMessage());
            $stats['errors']++;
        }

        $stats['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);

        Log::info("MineBotService: Завершена обработка рудника {$mineName}", $stats);

        return $stats;
    }

    /**
     * Обрабатывает пакет ботов
     */
    protected function processBotBatch(Collection $bots, string $mineName): array
    {
        $stats = [
            'bots_processed' => 0,
            'attacks_executed' => 0,
            'heals_executed' => 0,
            'errors' => 0,
        ];

        foreach ($bots as $bot) {
            try {
                $this->processSingleBot($bot, $mineName, $stats);
                $stats['bots_processed']++;
            } catch (\Exception $e) {
                Log::error("MineBotService: Ошибка обработки бота {$bot->id}: " . $e->getMessage());
                $stats['errors']++;
            }
        }

        return $stats;
    }

    /**
     * Обрабатывает одного бота
     */
    protected function processSingleBot(Bot $bot, string $mineName, array &$stats): void
    {
        // Проверяем, готов ли бот к действию
        if (!$this->isBotReadyForAction($bot)) {
            return;
        }

        // Кэшируем состояние бота
        $this->cacheService->cacheBotState($bot);

        // Регенерируем HP/MP
        $this->regenerateBotStats($bot);

        // Обрабатываем действия в зависимости от класса
        switch ($bot->class) {
            case 'priest':
                // Жрецы сначала лечат, потом атакуют
                if ($this->healingService->processHealing($bot, $mineName)) {
                    $stats['heals_executed']++;
                } elseif ($this->attackService->processAttack($bot, $mineName)) {
                    $stats['attacks_executed']++;
                }
                break;

            case 'warrior':
            case 'mage':
            default:
                // Воины и маги только атакуют
                if ($this->attackService->processAttack($bot, $mineName)) {
                    $stats['attacks_executed']++;
                }
                break;
        }

        // Обновляем время следующего действия
        $this->updateBotNextActionTime($bot);
    }

    /**
     * Проверяет, готов ли бот к действию
     */
    protected function isBotReadyForAction(Bot $bot): bool
    {
        $now = Carbon::now();

        // Проверяем базовые условия
        if (!$bot->is_active || $bot->hp <= 0) {
            return false;
        }

        // Проверяем время следующего действия
        if ($bot->next_action_time && $bot->next_action_time->gt($now)) {
            return false;
        }

        return true;
    }

    /**
     * Регенерирует HP и MP бота
     */
    protected function regenerateBotStats(Bot $bot): void
    {
        $now = Carbon::now();
        $needsUpdate = false;

        // Регенерация HP (каждые 10 секунд)
        if (!$bot->last_hp_regen_time || $bot->last_hp_regen_time->diffInSeconds($now) >= 10) {
            if ($bot->hp < $bot->max_hp) {
                $regenAmount = max(1, intval($bot->max_hp * 0.02)); // 2% от максимального HP
                $bot->hp = min($bot->max_hp, $bot->hp + $regenAmount);
                $bot->last_hp_regen_time = $now;
                $needsUpdate = true;
            }
        }

        // Регенерация MP (каждые 8 секунд)
        if (!$bot->last_mp_regen_time || $bot->last_mp_regen_time->diffInSeconds($now) >= 8) {
            if ($bot->mp < $bot->max_mp) {
                $regenAmount = max(1, intval($bot->max_mp * 0.03)); // 3% от максимального MP
                $bot->mp = min($bot->max_mp, $bot->mp + $regenAmount);
                $bot->last_mp_regen_time = $now;
                $needsUpdate = true;
            }
        }

        if ($needsUpdate) {
            $bot->save();
        }
    }

    /**
     * Обновляет время следующего действия бота
     */
    protected function updateBotNextActionTime(Bot $bot): void
    {
        $minInterval = $this->config['attacks']['interval_min'];
        $maxInterval = $this->config['attacks']['interval_max'];
        $randomInterval = rand($minInterval, $maxInterval);

        $bot->next_action_time = Carbon::now()->addSeconds($randomInterval);
        $bot->save();
    }

    /**
     * Получает активных ботов в руднике
     */
    protected function getActiveMineBots(string $mineName): Collection
    {
        return Bot::where('location', $mineName)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->where('created_by_admin', true) // Только боты, созданные через админ-панель
            ->orderBy('next_action_time', 'asc')
            ->limit($this->config['processing']['max_bots_per_tick'])
            ->get();
    }

    /**
     * Кэширует данные локации
     */
    protected function cacheLocationData(string $mineName): void
    {
        // Кэшируем ботов
        $this->cacheService->cacheLocationBots($mineName);

        // Кэшируем игроков
        $this->cacheService->cacheLocationPlayers($mineName);
    }

    /**
     * Проверяет, является ли локация активным рудником
     */
    protected function isValidMine(string $mineName): bool
    {
        // Проверяем в основной таблице locations
        $isMainLocation = Location::where('name', $mineName)
            ->where('location_type', 'mine')
            ->where('is_active', true)
            ->exists();

        if ($isMainLocation) {
            return true;
        }

        // Проверяем в таблице mine_locations
        return MineLocation::where('name', $mineName)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Проверяет, нужно ли остановить обработку
     */
    protected function shouldStopProcessing(float $startTime): bool
    {
        $executionTime = microtime(true) - $startTime;
        $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB

        return $executionTime > $this->config['processing']['execution_timeout'] ||
            $memoryUsage > $this->config['processing']['memory_limit'];
    }

    /**
     * Получает статистику рудника
     */
    public function getMineStats(string $mineName): array
    {
        $bots = Bot::where('location', $mineName)
            ->where('created_by_admin', true)
            ->get();

        // Определяем онлайн-статус по активности в последние 20 минут
        $onlineThreshold = now()->timestamp - (20 * 60); // 20 минут назад

        // Получаем игроков в руднике
        $players = User::whereHas('statistics', function ($query) use ($mineName) {
            $query->where('current_location', $mineName);
        })
            ->where(function ($query) use ($onlineThreshold) {
                // Игрок считается онлайн если:
                // 1. is_online = true ИЛИ
                // 2. last_activity_timestamp больше порога (активность в последние 20 минут)
                $query->where('is_online', true)
                    ->orWhere('last_activity_timestamp', '>', $onlineThreshold);
            })
            ->with(['profile', 'statistics'])
            ->get();

        return [
            'mine' => $mineName,
            'total_bots' => $bots->count(),
            'active_bots' => $bots->where('is_active', true)->where('hp', '>', 0)->count(),
            'dead_bots' => $bots->where('hp', '<=', 0)->count(),
            'online_players' => $players->count(),
            'solarius_players' => $players->filter(function ($player) {
                return $player->profile && $player->profile->race === 'solarius';
            })->count(),
            'lunarius_players' => $players->filter(function ($player) {
                return $player->profile && $player->profile->race === 'lunarius';
            })->count(),
            'bot_classes' => [
                'warrior' => $bots->where('class', 'warrior')->count(),
                'mage' => $bots->where('class', 'mage')->count(),
                'priest' => $bots->where('class', 'priest')->count(),
            ],
        ];
    }

    /**
     * Получает список всех активных рудников
     */
    public function getAllActiveMines(): Collection
    {
        // Получаем рудники из основной таблицы locations
        $mainMines = Location::where('location_type', 'mine')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Получаем рудники из таблицы mine_locations
        $customMines = MineLocation::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Объединяем и убираем дубликаты по имени
        $allMines = collect();

        foreach ($mainMines as $mine) {
            $allMines->push((object) ['name' => $mine->name, 'type' => 'main']);
        }

        foreach ($customMines as $mine) {
            if (!$allMines->contains('name', $mine->name)) {
                $allMines->push((object) ['name' => $mine->name, 'type' => 'custom']);
            }
        }

        return $allMines->sortBy('name');
    }
}

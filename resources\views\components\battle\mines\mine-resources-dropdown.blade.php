@props([
    'mine' => null,
    'index' => 0,
    'resources' => [],
    'possibleItems' => [],
    'canEnterMines' => false
])

{{--
Компонент для отображения выпадающего меню с ресурсами рудника
Принимает:
- mine: данные рудника
- index: индекс рудника для уникальных ID
- resources: коллекция ресурсов
- possibleItems: коллекция возможных предметов
- canEnterMines: может ли игрок войти в рудники (проверка HP)
--}}

{{-- Контент для выпадающего меню (без контейнера, так как он уже создан в родительском шаблоне) --}}
    {{-- Описание рудника --}}
    @if($mine->description ?? false)
        <div class="mb-4 p-3 bg-[#3a3631] rounded-lg border border-[#514b3c]">
            <h4 class="text-[#e5b769] font-semibold mb-2 text-sm">Описание локации</h4>
            <p class="text-[#d3c6a6] text-xs leading-relaxed">{{ $mine->description }}</p>
        </div>
    @endif

    {{-- Игроки в локации --}}
    @if(isset($mine->players) && $mine->players->count() > 0)
        <div class="mb-4 p-3 bg-[#3a3631] rounded-lg border border-[#514b3c]">
            <h4 class="text-[#e5b769] font-semibold mb-2 text-sm flex items-center">
                
                Игроки в локации ({{ $mine->players->count() }})
            </h4>
            <div class="grid grid-cols-2 gap-2">
                @foreach($mine->players->take(6) as $player)
                    <div class="flex items-center space-x-2 text-xs">
                        <span class="w-2 h-2 rounded-full {{ $player->profile->race === 'solarius' ? 'bg-yellow-500' : 'bg-blue-500' }}"></span>
                        <span class="text-[#d3c6a6] truncate">{{ $player->name }}</span>
                        <span class="text-[#9a9483] text-xs">({{ $player->profile->level }})</span>
                    </div>
                @endforeach
                @if($mine->players->count() > 6)
                    <div class="text-[#9a9483] text-xs col-span-2 text-center">
                        и еще {{ $mine->players->count() - 6 }} игроков...
                    </div>
                @endif
            </div>
        </div>
    @endif

    {{-- Ресурсы с пагинацией --}}
    <div class="mb-4">
        <h4 class="text-[#e5b769] font-semibold mb-3 text-sm flex items-center">
            
            Доступные ресурсы
        </h4>

        @if($resources && $resources->count() > 0)
            <div id="resources-container-{{ $index }}" class="resources-paginated-container">
                {{-- Контейнер для ресурсов --}}
                <div class="grid grid-cols-4 gap-3 mb-3" id="resources-grid-{{ $index }}">
                    {{-- Ресурсы будут загружены через JavaScript --}}
                </div>

                {{-- Пагинация ресурсов --}}
                <div id="resources-pagination-{{ $index }}" class="flex justify-center items-center space-x-2">
                    {{-- Пагинация будет загружена через JavaScript --}}
                </div>
            </div>

            {{-- Скрытые данные для JavaScript --}}
            <script type="application/json" id="resources-data-{{ $index }}">
                {!! json_encode($resources->map(function($resource) {
                    return [
                        'id' => $resource->id,
                        'name' => $resource->name,
                        'icon' => $resource->icon_path ?? null,
                        'description' => $resource->description ?? ''
                    ];
                })->values()->toArray()) !!}
            </script>
        @else
            <div class="text-center py-4 bg-[#3a3631] rounded-lg border border-[#514b3c]">
                <span class="text-[#9a9483] text-xs">Нет доступных ресурсов</span>
            </div>
        @endif
    </div>

    {{-- Возможные предметы с пагинацией --}}
    <div class="mb-4">
        <h4 class="text-[#e5b769] font-semibold mb-3 text-sm flex items-center">
           
            Возможные награды
        </h4>

        @if($possibleItems && $possibleItems->count() > 0)
            <div id="items-container-{{ $index }}" class="items-paginated-container">
                {{-- Контейнер для предметов --}}
                <div class="grid grid-cols-4 gap-3 mb-3" id="items-grid-{{ $index }}">
                    {{-- Предметы будут загружены через JavaScript --}}
                </div>

                {{-- Пагинация предметов --}}
                <div id="items-pagination-{{ $index }}" class="flex justify-center items-center space-x-2">
                    {{-- Пагинация будет загружена через JavaScript --}}
                </div>
            </div>

            {{-- Скрытые данные для JavaScript --}}
            <script type="application/json" id="items-data-{{ $index }}">
                {!! json_encode($possibleItems->map(function($itemData) {
                    return [
                        'id' => $itemData['item']->id,
                        'name' => $itemData['item']->name,
                        'icon' => $itemData['item']->icon_path ?? null,
                        'description' => $itemData['item']->description ?? '',
                        'drop_chance' => $itemData['drop_chance'],
                        'min_quantity' => $itemData['min_quantity'],
                        'max_quantity' => $itemData['max_quantity']
                    ];
                })->values()->toArray()) !!}
            </script>
        @else
            <div class="text-center py-4 bg-[#3a3631] rounded-lg border border-[#514b3c]">
                <span class="text-[#9a9483] text-xs">Нет доступных предметов</span>
            </div>
        @endif
    </div>

    {{-- Кнопка перехода --}}
    <div class="mt-4 p-4 flex justify-center">
        @if (isset($canEnterMines) && $canEnterMines)
            {{-- Если HP достаточно, показываем активную кнопку --}}
            <button
                onclick="window.location='{{ route('battle.mines.custom.index', ['slug' => $mine->slug]) }}'"
                class="w-full py-3 text-[#2f2d2b] font-bold uppercase tracking-wide
                    bg-gradient-to-b from-[#e5b769] to-[#c4a76d] rounded-lg border border-[#a6925e]
                    shadow-[inset_0px_4px_8px_rgba(0,0,0,0.5), inset_0px_-4px_8px_rgba(255,255,255,0.1)]
                    transition-all duration-200 relative
                    hover:scale-105 hover:shadow-[0px_4px_10px_rgba(255,215,105,0.5)]
                    active:scale-95 active:shadow-[inset_0px_4px_8px_rgba(0,0,0,0.6)]">
                <span class="relative z-10">Войти в рудник</span>
            </button>
        @else
            {{-- Если HP недостаточно, показываем неактивную кнопку --}}
            <button disabled
                class="w-full py-3 text-[#7a7666] font-bold uppercase tracking-wide
                    bg-gradient-to-b from-[#4a4a3d] to-[#3a3631] rounded-lg border border-[#514b3c]
                    shadow-[inset_0px_2px_4px_rgba(0,0,0,0.3)]
                    cursor-not-allowed opacity-60">
                <span class="relative z-10">Недостаточно здоровья</span>
            </button>
        @endif
    </div>

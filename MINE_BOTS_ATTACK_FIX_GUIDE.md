# Исправление атак ботов в рудниках

## Проблемы
1. **Основная**: Боты в кастомных рудниках не атаковали игроков автоматически, в отличие от аванпостов, где они работали корректно.
2. **Дополнительная**: Боты атаковали игроков из других подлокаций рудника, а должны атаковать только в пределах своей конкретной подлокации.

## Причины
1. В `BotBehaviorService.php` была принудительная логика атак только для аванпостов (строки 76-79), но не для рудников.
2. В методах `chooseBotTarget` и `collectLocationStats` не учитывались точные подлокации рудников.

## Исправление

### 1. Добавлен метод проверки рудников в `OutpostLocationTrait.php`
```php
protected function isMineLocation(string $locationName): bool
```
Проверяет, является ли локация рудником по типу `location_type = 'mine'`.

### 2. Обновлена логика атак в `BotBehaviorService.php`
Добавлена принудительная атака для рудников с проверкой подлокации (строки 82-102):
- **ПРОВЕРКА ПОДЛОКАЦИИ**: Боты атакуют только игроков в той же точной подлокации
- Боты враждебных рас **ВСЕГДА** атакуют игроков противоположной расы в своей подлокации
- Боты союзных рас **НЕ** атакуют игроков своей расы
- Логика аналогична аванпостам, но с дополнительной проверкой локации

### 3. Добавлены методы проверки
```php
protected function areEnemyRaces(string $race1, string $race2): bool
protected function areInSameMineSubLocation(Bot $bot, User $player): bool
```

### 4. Исправлен метод выбора цели `chooseBotTarget`
- Теперь ищет игроков только в точной подлокации бота (`$bot->location`)
- Исключены игроки из других подлокаций рудника

### 5. Обновлен сбор статистики `collectLocationStats`
- Использует LIKE запрос для сбора общей статистики по рудникам
- Позволяет сбалансированные расчеты вероятностей

## Результат
- **Solarius** боты атакуют **Lunarius** игроков **только в своей подлокации рудника**
- **Lunarius** боты атакуют **Solarius** игроков **только в своей подлокации рудника**
- Союзные боты не атакуют союзных игроков
- Боты **НЕ** атакуют игроков из других подлокаций (даже враждебных рас)
- Поведение аналогично аванпостам, но с ограничением по подлокациям

## Тестирование

### Команда для тестирования
```bash
php artisan test:mine-bots-attack-fix --verbose
```

### Ручная проверка
1. Зайти в **конкретную подлокацию рудника** с игроком одной расы
2. Убедиться, что боты противоположной расы **в той же подлокации** атакуют автоматически
3. Убедиться, что боты той же расы НЕ атакуют
4. Убедиться, что боты **из других подлокаций** НЕ атакуют (даже враждебных рас)
5. Переместиться в другую подлокацию и повторить проверку

### Проверка логов
```bash
tail -f storage/logs/laravel.log | grep "Принудительная атака в Руднике"
```

## Файлы изменены
- `app/Services/BotBehaviorService.php` - основная логика
- `app/Traits/OutpostLocationTrait.php` - добавлен метод проверки рудников
- `app/Console/Commands/TestMineBotsAttackFix.php` - тестовая команда

## Безопасность
- Изменения не ломают существующую логику аванпостов
- Добавлена только недостающая функциональность для рудников
- Сохранена проверка рас для предотвращения неправильных атак
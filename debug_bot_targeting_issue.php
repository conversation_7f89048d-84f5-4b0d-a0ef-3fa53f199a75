<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use App\Services\battle\FactionCountService;
use App\Services\battle\LocationPlayerCacheService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Отладка проблемы с таргетированием ботов ===\n\n";

$testLocation = 'аааааааааааа';
$testRace = 'lunarius'; // Предполагаем, что бот1 - лунариус
$testClass = 'mage';

echo "Тестируем локацию: {$testLocation}\n";
echo "Ищем ботов расы: {$testRace}\n\n";

// 1. Проверяем локацию рудника
echo "=== 1. Проверка локации рудника ===\n";
$mineLocation = MineLocation::where('name', $testLocation)->first();

if ($mineLocation) {
    echo "✓ Локация рудника найдена:\n";
    echo "  ID: {$mineLocation->id}\n";
    echo "  Название: {$mineLocation->name}\n";
    echo "  Активна: " . ($mineLocation->is_active ? 'ДА' : 'НЕТ') . "\n";
    echo "  Это подлокация: " . ($mineLocation->isSubLocation() ? 'ДА' : 'НЕТ') . "\n";
    
    if ($mineLocation->baseLocation) {
        echo "  Базовая локация: {$mineLocation->baseLocation->name}\n";
    }
} else {
    echo "❌ Локация рудника не найдена\n";
}

echo "\n=== 2. Поиск ботов через FactionCountService (используется в счетчиках) ===\n";
$factionCountService = app(FactionCountService::class);

// Используем тот же метод, что и в FactionCountService
$botsViaFactionService = Bot::where('race', $testRace)
    ->where('class', $testClass)
    ->where('is_active', true)
    ->where('hp', '>', 0)
    ->whereNull('death_time')
    ->whereNotNull('location')
    ->where('location', '!=', '');

if ($mineLocation) {
    $botsViaFactionService->where('mine_location_id', $mineLocation->id);
} else {
    $botsViaFactionService->where('location', $testLocation)
                         ->whereNull('mine_location_id');
}

$botsFromFactionService = $botsViaFactionService->get();

echo "Ботов найдено через FactionCountService: " . $botsFromFactionService->count() . "\n";
foreach ($botsFromFactionService as $bot) {
    echo "  {$bot->name} (ID: {$bot->id}) - HP: {$bot->hp}/{$bot->max_hp}, активен: " . ($bot->is_active ? 'ДА' : 'НЕТ') . "\n";
}

echo "\n=== 3. Поиск ботов через LocationPlayerCacheService (используется для атаки) ===\n";
$locationCacheService = app(LocationPlayerCacheService::class);

// Проверяем ботов через кеш сервис
$botsViaLocationCache = $locationCacheService->getCachedBotsInLocation($testLocation, $testRace);

echo "Ботов найдено через LocationPlayerCacheService: " . $botsViaLocationCache->count() . "\n";
foreach ($botsViaLocationCache as $bot) {
    echo "  {$bot->name} (ID: {$bot->id}) - HP: {$bot->hp}/{$bot->max_hp}, активен: " . ($bot->is_active ? 'ДА' : 'НЕТ') . "\n";
}

echo "\n=== 4. Прямой поиск ботов в БД (все фильтры) ===\n";

// Прямой поиск как в LocationPlayerCacheService но с подробностями
$directBotQuery = Bot::where('race', $testRace)
    ->where('is_active', true)
    ->where('hp', '>', 0);

if ($mineLocation) {
    echo "Поиск с mine_location_id = {$mineLocation->id}\n";
    echo "Проверка is_active на MineLocation: " . ($mineLocation->is_active ? 'ПРОЙДЕНА' : 'НЕ ПРОЙДЕНА') . "\n";
    
    // Здесь проблема! LocationPlayerCacheService проверяет is_active на MineLocation
    if ($mineLocation->is_active) {
        $directBotQuery->where('mine_location_id', $mineLocation->id);
    } else {
        echo "⚠ ПРОБЛЕМА: MineLocation неактивна, боты не будут найдены для атаки!\n";
        $directBotQuery->where('mine_location_id', -1); // Заведомо несуществующий ID
    }
} else {
    $directBotQuery->where('location', $testLocation)
                   ->whereNull('mine_location_id');
}

$directBots = $directBotQuery->get();

echo "Ботов найдено прямым запросом: " . $directBots->count() . "\n";
foreach ($directBots as $bot) {
    echo "  {$bot->name} (ID: {$bot->id}) - HP: {$bot->hp}/{$bot->max_hp}\n";
    echo "    Локация: {$bot->location}\n";
    echo "    Mine Location ID: " . ($bot->mine_location_id ?: 'NULL') . "\n";
}

echo "\n=== 5. Поиск ВСЕ ботов в локации (без фильтра активности MineLocation) ===\n";
$allBotsInLocation = Bot::where('race', $testRace)
    ->where('is_active', true)
    ->where('hp', '>', 0);

if ($mineLocation) {
    $allBotsInLocation->where('mine_location_id', $mineLocation->id);
} else {
    $allBotsInLocation->where('location', $testLocation);
}

$allBots = $allBotsInLocation->get();

echo "Всех ботов в локации (игнорируя is_active на MineLocation): " . $allBots->count() . "\n";
foreach ($allBots as $bot) {
    echo "  {$bot->name} (ID: {$bot->id}) - HP: {$bot->hp}/{$bot->max_hp}\n";
}

echo "\n=== ДИАГНОЗ ===\n";
if ($botsFromFactionService->count() > 0 && $botsViaLocationCache->count() == 0) {
    echo "🔍 НАЙДЕНА ПРОБЛЕМА:\n";
    echo "- FactionCountService находит ботов (показывает в счетчиках)\n";
    echo "- LocationPlayerCacheService не находит ботов (не дает атаковать)\n";
    
    if ($mineLocation && !$mineLocation->is_active) {
        echo "- ПРИЧИНА: MineLocation неактивна (is_active = false)\n";
        echo "- РЕШЕНИЕ: Активировать MineLocation или убрать проверку is_active\n";
    } else {
        echo "- ПРИЧИНА: Различие в логике фильтрации между сервисами\n";
        echo "- РЕШЕНИЕ: Синхронизировать логику поиска ботов\n";
    }
} else if ($botsFromFactionService->count() == 0) {
    echo "❌ Проблема: Боты не найдены вообще\n";
} else {
    echo "✅ Логика поиска ботов работает корректно\n";
}

echo "\n=== Завершено ===\n";
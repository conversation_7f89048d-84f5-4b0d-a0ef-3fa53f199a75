<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 ДИАГНОСТИКА ОТНОШЕНИЯ activeEffects()\n";
echo "=" . str_repeat("=", 40) . "\n\n";

$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n\n";

// 1. Прямой запрос к таблице active_effects
echo "1️⃣ Прямой запрос к таблице active_effects...\n";
$directEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->get();

echo "Найдено эффектов (прямой запрос): {$directEffects->count()}\n";
foreach ($directEffects as $effect) {
    echo "- ID: {$effect->id}\n";
    echo "  target_type: '{$effect->target_type}'\n";
    echo "  target_id: {$effect->target_id}\n";
    echo "  effect_type: '{$effect->effect_type}'\n";
    echo "  effect_name: '" . ($effect->effect_name ?? 'NULL') . "'\n";
    echo "  ends_at: {$effect->ends_at}\n";
    echo "  isActive(): " . ($effect->isActive() ? 'Да' : 'Нет') . "\n\n";
}

// 2. Через отношение activeEffects()
echo "2️⃣ Через отношение activeEffects()...\n";
$relationEffects = $user->activeEffects()->get();
echo "Найдено эффектов (через отношение): {$relationEffects->count()}\n";

foreach ($relationEffects as $effect) {
    echo "- ID: {$effect->id}\n";
    echo "  target_type: '{$effect->target_type}'\n";
    echo "  target_id: {$effect->target_id}\n";
    echo "  effect_type: '{$effect->effect_type}'\n";
    echo "  effect_name: '" . ($effect->effect_name ?? 'NULL') . "'\n";
    echo "  ends_at: {$effect->ends_at}\n";
    echo "  isActive(): " . ($effect->isActive() ? 'Да' : 'Нет') . "\n\n";
}

// 3. Проверяем определение отношения
echo "3️⃣ Проверка определения отношения...\n";
$relation = $user->activeEffects();
echo "Класс отношения: " . get_class($relation) . "\n";
echo "Запрос отношения: " . $relation->toSql() . "\n";
echo "Параметры: " . json_encode($relation->getBindings()) . "\n\n";

// 4. Проверяем с фильтрацией как в контроллере
echo "4️⃣ С фильтрацией isActive()...\n";
$allUserEffects = $user->activeEffects()->with('skill')->get();
echo "Всего эффектов (с skill): {$allUserEffects->count()}\n";

$userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());
echo "Активных после фильтрации: {$userEffects->count()}\n";

foreach ($userEffects as $effect) {
    echo "✅ Активный: {$effect->effect_type} (ID: {$effect->id})\n";
}

// 5. Проверяем морфологическое отношение
echo "\n5️⃣ Проверка морфологического отношения...\n";
echo "User model class: " . get_class($user) . "\n";
echo "Expected target_type: 'App\\Models\\User'\n";

// Проверяем, есть ли эффекты с другими target_type
$allEffectsForUser = ActiveEffect::where('target_id', $user->id)->get();
echo "Всего эффектов для user_id {$user->id}: {$allEffectsForUser->count()}\n";

foreach ($allEffectsForUser as $effect) {
    echo "- target_type: '{$effect->target_type}', target_id: {$effect->target_id}\n";
}

echo "\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА!\n";

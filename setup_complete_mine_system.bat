@echo off
title НАСТРОЙКА СИСТЕМЫ АВТОАТАК В РУДНИКАХ
color 0A

echo ================================================================
echo    АВТОМАТИЧЕСКАЯ НАСТРОЙКА СИСТЕМЫ АВТОАТАК МОБОВ В РУДНИКАХ
echo ================================================================
echo.
echo Эта система позволит мобам автоматически атаковать игроков,
echo которые добывают ресурсы в рудниках (получают дебаф "Замечен").
echo.
echo НАЖМИТЕ ЛЮБУЮ КЛАВИШУ ДЛЯ ПРОДОЛЖЕНИЯ...
pause > nul
cls

echo ================================================================
echo ШАГ 1: ПРОВЕРКА СИСТЕМЫ
echo ================================================================

echo Проверка подключения к базе данных...
php artisan tinker --execute="
try {
    DB::connection()->getPdo();
    echo '✅ Подключение к PostgreSQL успешно' . PHP_EOL;
} catch (Exception \$e) {
    echo '❌ Ошибка подключения к БД: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ КРИТИЧЕСКАЯ ОШИБКА: Нет подключения к базе данных!
    echo Проверьте настройки в .env файле
    pause
    exit /b 1
)

echo.
echo ================================================================
echo ШАГ 2: СОЗДАНИЕ ТАБЛИЦЫ MINE_MARKS
echo ================================================================

echo Попытка запуска миграции через Laravel...
php artisan migrate --path=database/migrations/2025_07_21_160000_create_mine_marks_table_fixed.php --force

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️  Миграция Laravel не удалась, пробуем прямой SQL...
    
    echo Выполнение SQL скрипта...
    psql -h localhost -U postgres -d EoeGame -f create_mine_marks_table.sql
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ⚠️  Не удалось создать таблицу автоматически
        echo Система будет работать в fallback режиме (через active_effects)
        echo.
        set FALLBACK_MODE=1
    )
)

echo.
echo ================================================================
echo ШАГ 3: ПРОВЕРКА РЕЗУЛЬТАТА
echo ================================================================

php artisan tinker --execute="
use Illuminate\Support\Facades\Schema;
if (Schema::hasTable('mine_marks')) {
    echo '✅ Таблица mine_marks создана успешно' . PHP_EOL;
    echo 'Колонки: ' . implode(', ', Schema::getColumnListing('mine_marks')) . PHP_EOL;
    echo '🎯 Система будет работать в полном режиме' . PHP_EOL;
} else {
    echo '⚠️  Таблица mine_marks не создана' . PHP_EOL;
    echo '🔄 Система будет работать в fallback режиме' . PHP_EOL;
}
"

echo.
echo ================================================================
echo ШАГ 4: ТЕСТИРОВАНИЕ СИСТЕМЫ
echo ================================================================

echo Тестирование создания дебафов и автоатак...
php setup_mine_detection_system.php

echo.
echo ================================================================
echo ШАГ 5: ПРОВЕРКА АВТОАТАК
echo ================================================================

echo Запуск тестовой автоатаки...
php artisan mine:auto-attack

echo.
echo ================================================================
echo НАСТРОЙКА ЗАВЕРШЕНА!
echo ================================================================
echo.

if defined FALLBACK_MODE (
    echo ⚠️  СИСТЕМА РАБОТАЕТ В FALLBACK РЕЖИМЕ
    echo    Дебафы будут сохраняться в таблице active_effects
    echo    Для полного режима создайте таблицу mine_marks вручную
    echo.
) else (
    echo ✅ СИСТЕМА ПОЛНОСТЬЮ ГОТОВА К РАБОТЕ!
    echo.
)

echo 📋 КАК ИСПОЛЬЗОВАТЬ:
echo ====================
echo 1. Запустите планировщик Laravel: php artisan schedule:work
echo 2. Или запускайте автоатаки вручную: php artisan mine:auto-attack
echo 3. В игре: идите в рудник и нажимайте "Добыть ресурс"
echo 4. На вас появится дебаф "Замечен" и мобы начнут атаковать!
echo.

echo 🔍 ПРИНЦИП РАБОТЫ:
echo ==================
echo • Игрок добывает ресурс → получает дебаф "Замечен" на 5 минут
echo • Каждые 15-30 секунд мобы атакуют замеченных игроков  
echo • Между атаками на одного игрока минимум 30 секунд
echo • Дебафы автоматически очищаются при истечении времени
echo.

echo 🛠️  КОМАНДЫ ДЛЯ ОТЛАДКИ:
echo ========================
echo • php artisan mine:auto-attack         - Ручной запуск автоатак
echo • php test_mine_detection_system.php  - Полное тестирование
echo • php setup_mine_detection_system.php - Диагностика системы
echo.

echo НАЖМИТЕ ЛЮБУЮ КЛАВИШУ ДЛЯ ЗАВЕРШЕНИЯ...
pause > nul

echo.
echo 🎉 ГОТОВО! Система автоатак мобов в рудниках настроена и готова к использованию!
<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Mob;
use Illuminate\Support\Facades\Schema;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 ПРОВЕРКА СТРУКТУРЫ ТАБЛИЦЫ МОБОВ\n";
echo "=" . str_repeat("=", 35) . "\n\n";

// 1. Проверяем структуру таблицы
echo "1️⃣ Структура таблицы mobs...\n";
$columns = Schema::getColumnListing('mobs');
echo "Столбцы в таблице mobs:\n";
foreach ($columns as $column) {
    echo "- {$column}\n";
}

// 2. Проверяем мобов в локации 137
echo "\n2️⃣ Мобы в локации 137...\n";
$mobs = Mob::where('location_id', 137)->get();
echo "Найдено мобов: {$mobs->count()}\n";

foreach ($mobs as $mob) {
    echo "- ID: {$mob->id}, Имя: {$mob->name}\n";
    echo "  Локация: {$mob->location_id}\n";
    
    // Проверяем доступные поля
    $attributes = $mob->getAttributes();
    foreach ($attributes as $key => $value) {
        if (in_array($key, ['health', 'max_health', 'current_health', 'hp', 'max_hp'])) {
            echo "  {$key}: {$value}\n";
        }
    }
    echo "\n";
}

echo "✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";

<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Services\battle\FactionCountService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Диагностика проблемы подсчета в рудниках ===\n\n";

// Получаем сервис
$factionCountService = app(\App\Services\battle\FactionCountService::class);

// Получаем все активные локации рудников
$mineLocations = MineLocation::where('is_active', true)->get();

echo "Найдено локаций рудников: " . $mineLocations->count() . "\n\n";

foreach ($mineLocations as $mineLocation) {
    echo "=== Локация: {$mineLocation->name} (ID: {$mineLocation->id}) ===\n";
    echo "Является подлокацией: " . ($mineLocation->isSubLocation() ? 'ДА' : 'НЕТ') . "\n";
    echo "parent_id: " . ($mineLocation->parent_id ?? 'NULL') . "\n";
    
    if ($mineLocation->baseLocation) {
        echo "Базовая локация: {$mineLocation->baseLocation->name}\n";
    } else {
        echo "Базовая локация: НЕ НАЙДЕНА\n";
    }
    
    // Проверяем, какой метод будет использован
    if ($mineLocation->isSubLocation()) {
        echo "Будет использован: getMineFactionCounts({$mineLocation->id})\n";
        
        // Проверяем ботов напрямую
        $directBots = Bot::where('mine_location_id', $mineLocation->id)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->get();
        echo "Ботов найдено напрямую: " . $directBots->count() . "\n";
        
        if ($directBots->count() > 0) {
            $botsByRaceClass = $directBots->groupBy(function ($bot) {
                return $bot->race . '_' . $bot->class;
            });
            
            foreach ($botsByRaceClass as $raceClass => $bots) {
                echo "  - {$raceClass}: " . $bots->count() . "\n";
            }
        }
        
        // Вызываем сервис
        $factionCounts = $factionCountService->getMineFactionCounts($mineLocation->id);
        
    } else {
        echo "Будет использован: getLocationFactionCounts('{$mineLocation->name}')\n";
        
        // Проверяем поиск локации в getStrictBotsCountInLocation
        $foundMineLocation = MineLocation::where('name', $mineLocation->name)->first();
        echo "Локация найдена в getStrictBotsCountInLocation: " . ($foundMineLocation ? 'ДА' : 'НЕТ') . "\n";
        
        if ($foundMineLocation) {
            // Проверяем ботов через условную логику
            $conditionalBots = Bot::where('mine_location_id', $foundMineLocation->id)
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->get();
            echo "Ботов найдено через условную логику: " . $conditionalBots->count() . "\n";
        } else {
            // Проверяем ботов через обычную логику
            $regularBots = Bot::where('location', $mineLocation->name)
                ->whereNull('mine_location_id')
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->get();
            echo "Ботов найдено через обычную логику: " . $regularBots->count() . "\n";
        }
        
        // Вызываем сервис
        $factionCounts = $factionCountService->getLocationFactionCounts($mineLocation->name);
    }
    
    // Показываем результат
    echo "Результат подсчета:\n";
    echo "  Solarius: Warriors={$factionCounts['total_counts']['solarius']['warriors']}, Mages={$factionCounts['total_counts']['solarius']['mages']}, Knights={$factionCounts['total_counts']['solarius']['knights']}\n";
    echo "  Lunarius: Warriors={$factionCounts['total_counts']['lunarius']['warriors']}, Mages={$factionCounts['total_counts']['lunarius']['mages']}, Knights={$factionCounts['total_counts']['lunarius']['knights']}\n";
    
    // Проверяем игроков
    if ($mineLocation->baseLocation) {
        $playersInBaseLocation = User::whereHas('statistics', function ($q) use ($mineLocation) {
            $q->where('current_location', $mineLocation->baseLocation->name);
        })
        ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
        ->count();
        echo "Игроков в базовой локации ({$mineLocation->baseLocation->name}): {$playersInBaseLocation}\n";
    }
    
    $playersInMineLocation = User::whereHas('statistics', function ($q) use ($mineLocation) {
        $q->where('current_location', $mineLocation->name);
    })
    ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
    ->count();
    echo "Игроков в локации рудника ({$mineLocation->name}): {$playersInMineLocation}\n";
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

echo "=== Общая статистика ботов по mine_location_id ===\n";
$botsByMineLocation = Bot::whereNotNull('mine_location_id')
    ->where('is_active', true)
    ->where('hp', '>', 0)
    ->groupBy('mine_location_id')
    ->selectRaw('mine_location_id, count(*) as bots_count')
    ->get();

foreach ($botsByMineLocation as $stat) {
    $mineLoc = MineLocation::find($stat->mine_location_id);
    $name = $mineLoc ? $mineLoc->name : 'НЕИЗВЕСТНО';
    echo "mine_location_id {$stat->mine_location_id} ({$name}): {$stat->bots_count} ботов\n";
}

echo "\n=== Боты без mine_location_id ===\n";
$botsWithoutMineLocationId = Bot::whereNull('mine_location_id')
    ->where('is_active', true)
    ->where('hp', '>', 0)
    ->count();
echo "Ботов без mine_location_id: {$botsWithoutMineLocationId}\n";

echo "\n=== Завершено ===\n";
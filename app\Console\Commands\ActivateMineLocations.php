<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;

class ActivateMineLocations extends Command
{
    protected $signature = 'fix:activate-mine-locations {--force : Принудительно активировать все локации}';
    protected $description = 'Активирует неактивные локации рудников для отображения пагинации';

    public function handle()
    {
        $this->info('=== АКТИВАЦИЯ ЛОКАЦИЙ РУДНИКОВ ===');

        // Находим неактивные основные локации с базовой локацией
        $inactiveLocations = MineLocation::whereNull('parent_id')
            ->where('is_active', false)
            ->whereHas('baseLocation')
            ->get();

        if ($inactiveLocations->count() == 0) {
            $this->info('Нет неактивных локаций для активации.');
            return;
        }

        $this->info("Найдено неактивных локаций: " . $inactiveLocations->count());

        foreach ($inactiveLocations as $location) {
            $this->info("  - {$location->name} (ID: {$location->id})");
        }

        // Спрашиваем подтверждение
        if (!$this->option('force')) {
            if (!$this->confirm('Активировать эти локации?')) {
                $this->info('Отменено.');
                return;
            }
        }

        // Активируем локации
        $activatedCount = 0;
        foreach ($inactiveLocations as $location) {
            $location->is_active = true;
            $location->save();
            
            $this->info("✓ Активирована: {$location->name}");
            $activatedCount++;
        }

        $this->info("\nАктивировано локаций: $activatedCount");

        // Проверяем результат
        $totalActive = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->count();

        $this->info("Всего активных локаций: $totalActive");

        if ($totalActive > 4) {
            $pages = ceil($totalActive / 4);
            $this->info("Пагинация будет работать: $pages страниц");
            $this->info("Проверьте: http://127.0.0.1:8000/battle/mines");
        } else {
            $this->warn("Все еще недостаточно локаций для пагинации");
        }

        return 0;
    }
}
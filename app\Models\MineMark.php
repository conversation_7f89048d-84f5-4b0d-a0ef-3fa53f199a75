<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MineMark extends Model
{
    /**
     * Название таблицы
     *
     * @var string
     */
    protected $table = 'mine_marks';

    /**
     * Атрибуты, которые можно массово присваивать
     *
     * @var array
     */
    protected $fillable = [
        'player_id',           // ID игрока с меткой
        'mine_location_id',    // ID локации рудника
        'location_id',         // ID основной локации
        'location_name',       // Название локации рудника
        'expires_at',          // Время истечения метки
        'is_active',           // Активна ли метка
        'last_attack_at',      // Время последней атаки
        'attack_count',        // Количество атак
    ];

    /**
     * Атрибуты, которые следует преобразовать в даты
     *
     * @var array
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'last_attack_at' => 'datetime',
        'is_active' => 'boolean',
        'attack_count' => 'integer',
    ];

    /**
     * Связь с игроком
     *
     * @return BelongsTo
     */
    public function player(): BelongsTo
    {
        return $this->belongsTo(User::class, 'player_id');
    }

    /**
     * Связь с локацией рудника
     *
     * @return BelongsTo
     */
    public function mineLocation(): BelongsTo
    {
        return $this->belongsTo(MineLocation::class, 'mine_location_id');
    }

    /**
     * Связь с основной локацией
     *
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'location_id');
    }

    /**
     * Создать новую метку рудника для игрока
     *
     * @param int $playerId ID игрока
     * @param int $mineLocationId ID локации рудника
     * @param int $locationId ID основной локации
     * @param string $locationName Название локации
     * @param int $duration Длительность в секундах
     * @return MineMark
     */
    public static function createMark(
        int $playerId,
        int $mineLocationId,
        int $locationId,
        string $locationName,
        int $duration = 300
    ): MineMark {
        return self::create([
            'player_id' => $playerId,
            'mine_location_id' => $mineLocationId,
            'location_id' => $locationId,
            'location_name' => $locationName,
            'expires_at' => now()->addSeconds($duration),
            'is_active' => true,
            'last_attack_at' => null,
            'attack_count' => 0,
        ]);
    }

    /**
     * Кэшировать метку в Redis с точным TTL
     *
     * @return void
     */
    public function cacheInRedisWithPreciseTTL(): void
    {
        $cacheKey = $this->getRedisCacheKey();
        $ttl = $this->getTTL();

        if ($ttl > 0) {
            Redis::setex($cacheKey, $ttl, json_encode([
                'id' => $this->id,
                'player_id' => $this->player_id,
                'mine_location_id' => $this->mine_location_id,
                'location_id' => $this->location_id,
                'location_name' => $this->location_name,
                'expires_at' => $this->expires_at->timestamp,
                'is_active' => $this->is_active,
                'attack_count' => $this->attack_count ?? 0,
            ]));
        }
    }

    /**
     * Удалить метку из кэша Redis
     *
     * @return void
     */
    public function removeFromCache(): void
    {
        $cacheKey = $this->getRedisCacheKey();
        Redis::del($cacheKey);
    }

    /**
     * Получить ключ кэша Redis для метки
     *
     * @return string
     */
    public function getRedisCacheKey(): string
    {
        return "mine_mark:{$this->mine_location_id}:{$this->player_id}";
    }

    /**
     * Получить TTL для кэша Redis
     *
     * @return int
     */
    public function getTTL(): int
    {
        return max(0, $this->expires_at->diffInSeconds(now()));
    }

    /**
     * Деактивировать метку
     *
     * @return bool
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        $this->removeFromCache();
        
        return $this->save();
    }

    /**
     * Обновить время последней атаки
     *
     * @return bool
     */
    public function updateLastAttack(): bool
    {
        $this->last_attack_at = now();
        $this->attack_count = ($this->attack_count ?? 0) + 1;
        
        // Обновляем кэш
        $this->cacheInRedisWithPreciseTTL();
        
        return $this->save();
    }

    /**
     * Проверить, активна ли метка
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->is_active && $this->expires_at > now();
    }

    /**
     * Очистить истекшие метки
     *
     * @return int Количество удаленных меток
     */
    public static function cleanupExpired(): int
    {
        $expiredMarks = self::where('expires_at', '<', now())
            ->orWhere('is_active', false)
            ->get();

        $deletedCount = 0;
        foreach ($expiredMarks as $mark) {
            $mark->removeFromCache();
            $mark->delete();
            $deletedCount++;
        }

        return $deletedCount;
    }

    /**
     * Получить всех активных игроков в локации рудника из кэша
     *
     * @param int $mineLocationId ID локации рудника
     * @return array
     */
    public static function getActivePlayersInMineLocation(int $mineLocationId): array
    {
        $pattern = "mine_mark:{$mineLocationId}:*";
        $keys = Redis::keys($pattern);
        
        $players = [];
        foreach ($keys as $key) {
            $data = Redis::get($key);
            if ($data) {
                $players[] = json_decode($data, true);
            }
        }
        
        return $players;
    }

    /**
     * Scope для активных меток
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope для меток в конкретной локации рудника
     */
    public function scopeInMineLocation($query, int $mineLocationId)
    {
        return $query->where('mine_location_id', $mineLocationId);
    }

    /**
     * Scope для меток конкретного игрока
     */
    public function scopeForPlayer($query, int $playerId)
    {
        return $query->where('player_id', $playerId);
    }
}
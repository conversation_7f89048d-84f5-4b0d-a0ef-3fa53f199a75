<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class CheckMinimumHP
{
    public function handle($request, Closure $next, $minHP = 1, $minHpPercent = null)
    {
        $user = Auth::user();

        if (!$user || !$user->profile) {
            return $next($request);
        }

        // Получаем актуальные значения HP/MP пользователя с учетом регенерации
        // Это важно: только так можно узнать, сколько у игрока реально здоровья на данный момент
        $actualResources = $user->profile->getActualResources();
        $actualHp = $actualResources['current_hp'];

        // Проверяем, не находимся ли мы уже на странице поражения
        $isDefeatPage = $request->route()->getName() === 'battle.defeat';

        // Проверяем, не является ли текущий запрос запросом на возрождение
        $isRespawnRequest = $request->route()->getName() === 'battle.respawn';

        // Проверяем, является ли запрос запросом к функциям персонажа, которые должны быть доступны даже при HP = 0
        $isAllowedFunction = false;

        // Если игрок находится в состоянии поражения (is_defeated = true), то разрешаем только страницу поражения и возрождение
        if (session('is_defeated') === true) {
            $allowedRoutes = [
                'battle.defeat',   // Страница поражения
                'battle.respawn'  // Возрождение
            ];
        } else {
            // В обычном состоянии разрешаем доступ к функциям персонажа
            $allowedRoutes = [
                'character.index',
                'character.stats',
                'character.skills',
                'inventory.index',
                'inventory.equipment',
                'inventory.consumables',
                'messages.index',
                'messages.view',
                'messages.send',
                'settings.index',
                'settings.update',
                'battle.defeat' // Страница поражения всегда доступна
            ];
        }

        foreach ($allowedRoutes as $route) {
            if (strpos($request->route()->getName(), $route) !== false) {
                $isAllowedFunction = true;
                break;
            }
        }

        // Проверяем флаг смерти в сессии и обрабатываем его
        if (session('player_dead') === true) {
            // Гарантируем, что HP игрока равно 0
            $user->profile->current_hp = 0;
            $user->profile->last_regeneration_at = now();
            $user->profile->save();

            // Очищаем цели и сбрасываем другие боевые состояния
            $user->current_target_id = null;
            $user->current_target_type = null;
            $user->save();

            // Если это запрос на возрождение, пропускаем его
            if ($isRespawnRequest) {
                return $next($request);
            }

            // Не сбрасываем флаг смерти здесь, это будет сделано в контроллере respawn

            // Перенаправляем на страницу поражения, если мы не на ней и не выполняем возрождение
            if (!$isDefeatPage && !$isRespawnRequest) {
                return Redirect::route('battle.defeat');
            }
        }

        // Если HP равно 0 и мы не на странице поражения и не выполняем возрождение и не используем разрешенные функции
        if ($actualHp <= 0 && !$isDefeatPage && !$isRespawnRequest && !$isAllowedFunction) {
            // Проверяем, есть ли флаг недавней победы ПЕРВЫМ делом
            $hasRecentVictory = session('recent_victory_time') && 
                               (time() - session('recent_victory_time') < 10);
            
            // Если игрок недавно победил, НЕ устанавливаем флаг смерти
            if ($hasRecentVictory) {
                Log::info("CheckMinimumHP пропускает установку флага смерти для победителя", [
                    'user_id' => $user->id,
                    'current_hp' => $actualHp,
                    'recent_victory_time' => session('recent_victory_time'),
                    'current_time' => time(),
                    'victory_elapsed' => time() - session('recent_victory_time')
                ]);
                return $next($request);
            }
            
            // Проверяем, не является ли это результатом успешной атаки игрока
            $isAttackRequest = $request->isMethod('post') && 
                              (str_contains($request->url(), '/attack-player') || 
                               str_contains($request->url(), '/attack-bot') || 
                               str_contains($request->url(), '/attack-any-player') ||
                               str_contains($request->url(), '/retaliate'));
            
            // Проверяем, не является ли это GET запросом после атаки (перенаправление после победы)
            $isPostAttackRedirect = $request->isMethod('get') && 
                                   str_contains($request->url(), '/battle/outposts/') &&
                                   $user->current_target_id &&
                                   session('recent_attack_time') &&
                                   (time() - session('recent_attack_time') < 3);
            
            // Если это запрос атаки и у игрока есть цель, пропускаем проверку HP
            if ($isAttackRequest && $user->current_target_id) {
                return $next($request);
            }
            
            // Если это перенаправление после атаки, пропускаем проверку HP
            if ($isPostAttackRedirect) {
                return $next($request);
            }
            
            // Устанавливаем флаг смерти в сессии
            session(['player_dead' => true]);
            session(['death_timestamp' => time()]);
            session(['is_defeated' => true]);

            // Если информация об убийце не установлена в сессии, устанавливаем неизвестного убийцу
            if (!session('killer_type') || !session('killer_id')) {
                if ($user->last_attacker_id && $user->last_attacker_type) {
                    session([
                        'killer_type' => $user->last_attacker_type,
                        'killer_id' => $user->last_attacker_id
                    ]);
                } else {
                    session([
                        'killer_type' => 'unknown',
                        'killer_id' => null
                    ]);
                }
            }

            // Перенаправляем на страницу поражения
            return redirect()->route('battle.defeat');
        }

        // Синхронизируем значение HP в базе с актуальным значением для предотвращения несогласованности
        if ($user->profile->current_hp !== $actualHp) {
            $user->profile->current_hp = $actualHp;
            $user->profile->last_regeneration_at = now();
            $user->profile->save();
        }

        // Проверяем, является ли текущий запрос запросом на вход в локацию или действием внутри локации
        $isLocationEntry = $request->isMethod('get');

        // Проверяем минимальное HP только по актуальному значению
        $hpCheckFailed = false;

        // Если указан процент минимального HP и это запрос на вход в локацию
        if ($minHpPercent !== null && $isLocationEntry) {
            // Проверяем текущую локацию пользователя
            $currentLocation = $user->statistics->current_location ?? null;
            $targetLocation = null;

            // Определяем целевую локацию из URL
            if (strpos($request->path(), 'battle/outposts/elven_haven') !== false) {
                $targetLocation = 'Эльфийская Гавань';
            } elseif (strpos($request->path(), 'battle/outposts/dawn_fort') !== false) {
                $targetLocation = 'Форт Рассвета';
            } elseif (strpos($request->path(), 'battle/outposts/sandy_stronghold') !== false) {
                $targetLocation = 'Песчаный Оплот';
            } elseif (strpos($request->path(), 'battle/mines/tarnmore_quarry') !== false) {
                $targetLocation = 'Тарнмор';
            } elseif (preg_match('/battle\/outposts\/(\d+)$/', $request->path(), $matches)) {
                // Для пользовательских аванпостов (battle/outposts/{id})
                $outpostId = $matches[1];
                $outpostLocation = \App\Models\OutpostLocation::find($outpostId);
                if ($outpostLocation) {
                    $targetLocation = $outpostLocation->name;
                }
            }

            // Проверяем, является ли запрос входом в новую локацию
            if ($targetLocation !== null && $currentLocation !== $targetLocation) {
                $hpPercent = $actualHp / max(1, $user->profile->max_hp);
                if ($hpPercent < ($minHpPercent / 100)) {
                    $hpCheckFailed = true;
                    \Log::info("Игрок {$user->name} не допущен в локацию {$targetLocation} из-за низкого HP", [
                        'user_id' => $user->id,
                        'current_hp' => $actualHp,
                        'max_hp' => $user->profile->max_hp,
                        'hp_percent' => $hpPercent * 100,
                        'required_percent' => $minHpPercent
                    ]);
                }
            }
        } else {
            // Стандартная проверка на минимальное абсолютное значение HP
            if ($actualHp < $minHP) {
                $hpCheckFailed = true;
            }
        }

        if ($hpCheckFailed) {
            // Проверяем, есть ли флаг недавней победы
            $hasRecentVictory = session('recent_victory_time') && 
                               (time() - session('recent_victory_time') < 10);
            
            // Если игрок недавно победил, пропускаем проверку HP
            if ($hasRecentVictory) {
                Log::info("CheckMinimumHP пропускает проверку HP для победителя", [
                    'user_id' => $user->id,
                    'hasRecentVictory' => $hasRecentVictory,
                    'recent_victory_time' => session('recent_victory_time'),
                    'current_time' => time()
                ]);
                return $next($request);
            }
            
            // Определяем, куда перенаправлять в зависимости от URL
            $redirectRoute = 'home';

            if (strpos($request->path(), 'battle/mines') !== false) {
                $redirectRoute = 'battle.mines.index';
            } elseif (strpos($request->path(), 'battle/outposts') !== false) {
                $redirectRoute = 'battle.outposts.index';
            }

            // Формируем сообщение об ошибке в зависимости от типа проверки
            $errorMessage = 'У вас слишком мало здоровья. Восстановитесь!';
            if ($minHpPercent !== null && $isLocationEntry) {
                $errorMessage = "Вам нужно восстановить здоровье выше {$minHpPercent}%, чтобы войти в боевую локацию!";
            }

            Log::warning("CheckMinimumHP перенаправляет игрока", [
                'user_id' => $user->id,
                'redirectRoute' => $redirectRoute,
                'actualHp' => $actualHp,
                'hasRecentVictory' => $hasRecentVictory,
                'recent_victory_time' => session('recent_victory_time'),
                'current_time' => time()
            ]);
            
            return Redirect::route($redirectRoute)
                ->with('error', $errorMessage);
        }

        return $next($request);
    }
}
<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\User;
use App\Services\battle\UserLocationService;

// Инициализация Laravel
$app = new Application(
    $_ENV['APP_BASE_PATH'] ?? dirname(__DIR__)
);

$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЯ КНОПКИ 'БИТЬ В ОТВЕТ' ===\n\n";

try {
    $locationService = app(UserLocationService::class);
    
    echo "1. Поиск тестовых игроков:\n";
    
    // Находим двух игроков для тестирования
    $testUsers = User::whereHas('statistics')
                    ->with(['statistics', 'profile'])
                    ->limit(2)
                    ->get();
    
    if ($testUsers->count() < 2) {
        echo "❌ Недостаточно игроков для тестирования (нужно минимум 2)\n";
        exit(1);
    }
    
    $attacker = $testUsers->first();
    $victim = $testUsers->last();
    
    echo "Атакующий: {$attacker->name} (ID: {$attacker->id})\n";
    echo "Жертва: {$victim->name} (ID: {$victim->id})\n";
    echo "Локация атакующего: {$attacker->statistics->current_location}\n";
    echo "Локация жертвы: {$victim->statistics->current_location}\n";
    
    echo "\n2. Тестирование сценария межлокационной атаки:\n";
    
    // Имитируем сценарий: атакующий атакует жертву, затем меняет локацию
    echo "Шаг 1: Устанавливаем last_attacker_id (имитируем атаку)\n";
    $victim->update([
        'last_attacker_id' => $attacker->id,
        'last_attacker_type' => 'player'
    ]);
    echo "✓ У жертвы установлен last_attacker_id = {$attacker->id}\n";
    
    // Проверяем, что они в одной локации
    $sameLocationBefore = $locationService->arePlayersInSameLocation($attacker, $victim);
    echo "Игроки в одной локации ДО смены: " . ($sameLocationBefore ? "Да" : "Нет") . "\n";
    
    echo "\nШаг 2: Атакующий меняет локацию\n";
    $originalLocation = $attacker->statistics->current_location;
    $newLocation = $originalLocation . "-тестовая_подлокация"; // Делаем искусственную смену локации
    
    // Вызываем метод обновления локации, который должен сбросить last_attacker_id
    $locationService->updateUserLocation($attacker, $newLocation);
    
    echo "✓ Атакующий перешел из '{$originalLocation}' в '{$newLocation}'\n";
    
    // Перезагружаем данные жертвы
    $victim->refresh();
    
    echo "\nШаг 3: Проверяем результат:\n";
    echo "last_attacker_id у жертвы ПОСЛЕ смены локации: " . ($victim->last_attacker_id ?? 'null') . "\n";
    echo "last_attacker_type у жертвы: " . ($victim->last_attacker_type ?? 'null') . "\n";
    
    $sameLocationAfter = $locationService->arePlayersInSameLocation($attacker, $victim);
    echo "Игроки в одной локации ПОСЛЕ смены: " . ($sameLocationAfter ? "Да" : "Нет") . "\n";
    
    echo "\n3. Результат тестирования:\n";
    
    if ($victim->last_attacker_id === null && $victim->last_attacker_type === null) {
        echo "✅ ИСПРАВЛЕНИЕ РАБОТАЕТ!\n";
        echo "✅ last_attacker_id корректно сброшен при смене локации\n";
        echo "✅ Кнопка 'Бить в ответ' больше не будет доступна между разными локациями\n";
    } else {
        echo "❌ ПРОБЛЕМА НЕ ИСПРАВЛЕНА!\n";
        echo "❌ last_attacker_id не был сброшен: {$victim->last_attacker_id}\n";
        echo "❌ Возможны межлокационные атаки через кнопку 'Бить в ответ'\n";
    }
    
    echo "\n4. Проверка обратного сценария:\n";
    
    // Возвращаем атакующего в исходную локацию
    echo "Возвращаем атакующего в исходную локацию...\n";
    $locationService->updateUserLocation($attacker, $originalLocation);
    
    // Снова устанавливаем last_attacker_id
    $victim->update([
        'last_attacker_id' => $attacker->id,
        'last_attacker_type' => 'player'
    ]);
    
    echo "Установили last_attacker_id снова...\n";
    
    $sameLocationFinal = $locationService->arePlayersInSameLocation($attacker, $victim);
    echo "Игроки снова в одной локации: " . ($sameLocationFinal ? "Да" : "Нет") . "\n";
    
    // Если они в одной локации, last_attacker_id НЕ должен сброситься
    $victim->refresh();
    echo "last_attacker_id после возврата в ту же локацию: " . ($victim->last_attacker_id ?? 'null') . "\n";
    
    if ($sameLocationFinal && $victim->last_attacker_id == $attacker->id) {
        echo "✅ КОРРЕКТНО: last_attacker_id сохраняется, когда игроки в одной локации\n";
    } else {
        echo "❌ ОШИБКА: last_attacker_id сбрасывается даже когда игроки в одной локации\n";
    }
    
    echo "\n=== ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===\n";
    echo "Проблема с кнопкой 'Бить в ответ' между разными локациями должна быть устранена.\n";
    
} catch (Exception $e) {
    echo "ОШИБКА: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
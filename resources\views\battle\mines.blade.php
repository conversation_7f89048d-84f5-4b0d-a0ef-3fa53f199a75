<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Рудники - Echoes of Eternity</title>

    @vite(['resources/css/app.css', 'resources/css/battle/mines-selection.css', 'resources/css/battle/mines-filters.css', 'resources/js/app.js', 'resources/js/battle/mines-selection.js', 'resources/js/battle/mines-filters.js', 'resources/js/battle/mines-dropdown-pagination.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Приветственное сообщение --}}
        <x-battle.welcome-message />

        {{-- Блок изображения локации --}}
        <div class="mb-2">
            @if (isset($error))
                <div class="bg-[#613f36] text-[#ffeac1] p-1 rounded border border-[#88634a] shadow-md mb-3">
                    {{ $error }}
                </div>
            @endif

            {{-- Заголовок рудников без изображения --}}
            <x-layout.mines-header :breadcrumbs="$breadcrumbs" title="Рудники" />
        </div>

        {{-- Блок фильтров --}}
        <x-battle.mines.filter-buttons />

        {{-- Модальные окна фильтров --}}
        <x-battle.mines.resource-filter-modal />
        <x-battle.mines.item-filter-modal />
        <x-battle.mines.filter-notification-modal />
        <x-battle.mines.error-modal />

        {{-- Основной контент - список рудников --}}
        <div class="mt-2 space-y-3" id="mines-list">
            {{-- Другие рудники --}}
            @foreach ($minesPaginator as $index => $mine)
                @if ($mine->is_custom ?? false)
                        <div class="bg-[#3b3a33] rounded-lg border border-[#a6925e] shadow-deep glow-border">
                            {{-- Заголовок карьера --}}
                            <button onclick="toggleDetails('details-custom_mine_{{ $index }}')"
                                class="w-full text-left text-[#e5b769] font-bold tracking-wide
                                                                                                                                                                                                              bg-gradient-to-b from-[#5a4d36] to-[#4a4a3d]
                                                                                                                                                                                                              border border-[#8b7548] shadow-md
                                                                                                                                                                                                              flex items-center justify-between
                                                                                                                                                                                                              rounded-lg overflow-hidden box-border">
                                <span class="flex items-center gap-2">
                                    {{ $mine->name }}
                                </span>
                                <span class="text-lg transition-transform duration-200" id="icon-custom_mine_{{ $index }}">▼</span>
                            </button>

                            {{-- Скрытый контент --}}
                            <div id="details-custom_mine_{{ $index }}" class="hidden bg-[#2f2d2b] border-t border-[#8b7548] p-4">
                                {{-- Изображение --}}
                                <div class="w-full bg-[#3b3a33] rounded-lg shadow-lg overflow-hidden mb-4">
                                    <img src="{{ asset($mine->image) }}" alt="Изображение локации" class="w-full h-24 opacity-90">
                                </div>

                                {{-- Статус фракций --}}
                                <div class="mb-4">
                                    <x-battle.faction-status :solWarriors="$solWarriors" :solMages="$solMages"
                                        :solKnights="$solKnights" :lunWarriors="$lunWarriors" :lunMages="$lunMages"
                                        :lunKnights="$lunKnights" />
                                </div>





                            {{-- Используем новый компонент для отображения ресурсов и предметов --}}
                            <x-battle.mines.mine-resources-dropdown
                                :mine="$mine"
                                :index="$index"
                                :resources="$mine->resources ?? collect()"
                                :possibleItems="$mine->possible_items ?? collect()"
                                :canEnterMines="$canEnterMines"
                            />


                        </div>
                    </div>
                @endif
            @endforeach
    </div>

    {{-- Пагинация рудников --}}
    @if(isset($minesPaginator))
        @if($minesPaginator->hasPages() || $minesPaginator->total() > 4)
            <div class="mt-6 mb-4">
                {{ $minesPaginator->links('vendor.pagination.simple-tailwind') }}
            </div>
        @endif
        
        {{-- Отладочная информация для проверки --}}
        @if(config('app.debug'))
            <div class="mt-4 mb-2 text-center">
                <span class="text-[#9a9483] text-sm">
                    Рудников: {{ $minesPaginator->total() }} | Страниц: {{ $minesPaginator->lastPage() }} | hasPages: {{ $minesPaginator->hasPages() ? 'Да' : 'Нет' }}
                </span>
            </div>
        @endif
    @endif

    </div>

    {{-- Навигационные кнопки --}}
    <x-layout.navigation-buttons />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />

</body>

</html>
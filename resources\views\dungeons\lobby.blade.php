{{-- ===============================
Шаблон лобби подземелья - промежуточная страница перед входом
=============================== --}}
<!DOCTYPE html>
<html lang="ru">
@php use Illuminate\Support\Facades\Auth; @endphp

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Лобби: {{ $dungeon->name }} - Echoes of Eternity</title>
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/global/csrf.js', 'resources/js/layout/footer-counters.js', 'resources/js/layout/server-time.js', 'resources/js/party/party-main.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col" data-page="party">
    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="false" :unreadMessagesCount="0" :hasBrokenItems="false"
                :brokenItemsCount="0" />
        </x-layout.hp-mp-bar>

        {{-- Хлебные крошки --}}
        <x-layout.breadcrumbs :breadcrumbs="$breadcrumbs" />

        {{-- Название локации --}}
        <x-layout.location-name title="Лобби: {{ $dungeon->name }}" />

        {{-- Flash-сообщения --}}
        <x-game-flash-messages />

        {{-- Основной контентный блок --}}
        <div class="mt-0 space-y-1">

            {{-- Информация о подземелье --}}
            <div
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-3">
                <div class="flex items-center space-x-3 mb-2">
                    @if($dungeon->image_path)
                        <img src="{{ asset($dungeon->image_path) }}" alt="{{ $dungeon->name }}"
                            class="w-12 h-12 rounded-lg border border-[#6c4539] object-cover">
                    @else
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-[#6c4539] to-[#2a1b12] rounded-lg border border-[#6c4539] flex items-center justify-center">
                            <span class="text-[#fceac4] text-lg">🏰</span>
                        </div>
                    @endif
                    <div>
                        <h3 class="text-[#fceac4] font-semibold text-sm">{{ $dungeon->name }}</h3>
                        <div class="text-xs text-[#a6925e] space-x-2">
                            <span>Уровень: {{ $dungeon->min_level }}+</span>
                            <span>Игроки: {{ $dungeon->min_players }}-{{ $dungeon->max_players }}</span>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Участники группы --}}
            <div
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-3">
                {{-- Заголовок с индикатором готовности --}}
                <div class="mb-3">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-[#e4d7b0] font-semibold text-sm md:text-sm sm:text-xs">
                            Участники группы ({{ $memberCount }}/{{ $dungeon->max_players }})
                        </h3>
                        <span class="text-[#e4d7b0] text-xs md:text-xs sm:text-xs font-bold">
                            Готовы:
                            @php
                                $readyCount = $partyMembers->where('pivot.is_ready', true)->count();
                            @endphp
                            {{ $readyCount }}/{{ $memberCount }}
                        </span>
                    </div>
                    {{-- Прогресс-бар готовности --}}
                    <div
                        class="w-full bg-[#3b3629] rounded-full h-2 md:h-2 sm:h-2 border border-[#6e3f35] shadow-inner mb-2">
                        @php
                            $readyPercentage = $memberCount > 0 ? ($readyCount / $memberCount) * 100 : 0;
                        @endphp
                        <div class="bg-gradient-to-r from-[#2f473c] to-[#1e2e27] h-2 md:h-2 sm:h-2 rounded-full transition-all duration-500 shadow-[0_0_8px_rgba(47,71,60,0.4)]"
                            style="width: {{ $readyPercentage }}%"></div>
                    </div>
                </div>

                {{-- Список участников --}}
                <div class="space-y-2">
                    @foreach($partyMembers as $member)
                        <x-dungeons.dungeon-member-card :member="$member" :isCurrentUser="$member->id === $user->id"
                            :isLeader="$activeParty->isLeader($member->id)" :canManage="$activeParty->isLeader($user->id)"
                            :dungeon="$dungeon" />
                    @endforeach
                </div>
            </div>

            {{-- Статус готовности и кнопки действий --}}
            <div
                class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-3">
                {{-- Кнопка готовности для текущего пользователя --}}
                @php
                    $currentUserMember = $partyMembers->firstWhere('id', $user->id);
                    $isCurrentUserReady = $currentUserMember ? $currentUserMember->pivot->is_ready : false;
                @endphp

                <form action="{{ route('dungeons.lobby.toggle-ready', $dungeon) }}" method="POST" class="w-full">
                    @csrf
                    <button type="submit"
                        class="w-full mb-3 py-3 px-4 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl
                               {{ $isCurrentUserReady
    ? 'bg-gradient-to-b from-[#6e3f35] to-[#59372d] hover:from-[#59372d] hover:to-[#3c221b] text-[#f8eac2] border border-[#6e3f35] hover:border-[#59372d] shadow-[0_0_10px_rgba(110,63,53,0.4)]'
    : 'bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#1e2e27] hover:to-[#243c2f] text-[#f8eac2] border border-[#2f473c] hover:border-[#1e2e27] shadow-[0_0_10px_rgba(47,71,60,0.4)]' }}">
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                @if($isCurrentUserReady)
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                @else
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                @endif
                            </svg>
                            <span>{{ $isCurrentUserReady ? 'НЕ ГОТОВ' : 'ГОТОВ' }}</span>
                        </div>
                    </button>
                </form>

                {{-- Кнопка начала подземелья (только для лидера) --}}
                @if($activeParty->isLeader($user->id))
                            <form action="{{ route('dungeons.enter', $dungeon) }}" method="POST">
                                @csrf
                                <button type="submit"
                                    class="w-full mb-3 py-3 px-4 rounded-lg font-semibold transition-all duration-300 shadow-lg
                                                           {{ $canStartDungeon
                    ? 'bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#1e2e27] hover:to-[#243c2f] text-[#f8eac2] border border-[#2f473c] hover:border-[#1e2e27] shadow-[0_0_10px_rgba(47,71,60,0.4)] hover:shadow-xl'
                    : 'bg-gradient-to-b from-[#6e3f35] to-[#59372d] text-[#f8eac2] border border-[#6e3f35] opacity-50 cursor-not-allowed shadow-[0_0_10px_rgba(110,63,53,0.2)]' }}"
                                    {{ !$canStartDungeon ? 'disabled' : '' }}>
                                    <div class="flex items-center justify-center space-x-2">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM8 15a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                                                clip-rule="evenodd" />
                                        </svg>
                                        <span>НАЧАТЬ ПОДЗЕМЕЛЬЕ</span>
                                    </div>
                                    @if(!$canStartDungeon)
                                        <div class="text-xs text-[#c1a96e] mt-1">
                                            @if(!empty($readinessErrors))
                                                @foreach($readinessErrors as $error)
                                                    <div>• {{ $error }}</div>
                                                @endforeach
                                            @elseif(!$allReady)
                                                Не все участники готовы
                                            @elseif($memberCount < $dungeon->min_players)
                                                Недостаточно участников (мин. {{ $dungeon->min_players }})
                                            @elseif($memberCount > $dungeon->max_players)
                                                Слишком много участников (макс. {{ $dungeon->max_players }})
                                            @endif
                                        </div>
                                    @endif
                                </button>
                            </form>
                @endif

                {{-- Предупреждения о состоянии группы --}}
                @if(!empty($readinessWarnings))
                    <div class="mb-3 p-2 bg-[#6e3f35]/20 border border-[#6e3f35] rounded-lg">
                        <div class="text-[#c1a96e] text-xs font-medium mb-1">⚠️ Предупреждения:</div>
                        @foreach($readinessWarnings as $warning)
                            <div class="text-[#f8eac2] text-xs">• {{ $warning }}</div>
                        @endforeach
                    </div>
                @endif

                {{-- Кнопка выхода из лобби --}}
                <a href="{{ route('dungeons.index') }}"
                    class="w-full bg-gradient-to-br from-[#3e342c] to-[#2a2721] text-[#e5b769] font-medium py-2 px-4 rounded-lg border border-[#514b3c] hover:border-[#a6925e] hover:from-[#4a3f35] hover:to-[#322d24] transition-all duration-300 flex items-center justify-center space-x-2">
                    <span>←</span>
                    <span>Выйти из лобби</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Навигационные кнопки --}}
    <x-layout.navigation-buttons />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />


</body>

</html>
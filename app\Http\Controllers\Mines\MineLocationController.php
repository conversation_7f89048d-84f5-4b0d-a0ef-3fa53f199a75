<?php

namespace App\Http\Controllers\Mines;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Mob;
use App\Models\User;
use App\Models\Skill;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\UserResource;
use App\Models\SpawnedResource;
use App\Services\BattleLogService;
use App\Services\ExperienceService;
use App\Services\RewardService;
use App\Services\CurrencyService;
use App\Services\SkillService;
use App\Contracts\DamageCalculator;
use App\Services\FlashMessageService;
use App\Services\LogFormattingService;
use App\Services\ObeliskService;
use App\Services\LocationResourceService;
use App\Services\battle\FactionCountService;
use App\Services\MineTargetResetService;
use App\Services\MineDetectionService;

use Illuminate\Support\Facades\Redis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MineLocationController extends Controller
{
    protected BattleLogService $battleLogService;
    protected ExperienceService $experienceService;
    protected DamageCalculator $damageCalculator;
    protected SkillService $skillService;
    protected RewardService $rewardService;
    protected LogFormattingService $logFormatter;
    protected ObeliskService $obeliskService;
    protected LocationResourceService $resourceService;
    protected CurrencyService $currencyService;
    protected FactionCountService $factionCountService;
    protected MineTargetResetService $mineTargetResetService;
    protected MineDetectionService $mineDetectionService;

    public function __construct(
        ExperienceService $experienceService,
        BattleLogService $battleLogService,
        DamageCalculator $damageCalculator,
        RewardService $rewardService,
        SkillService $skillService,
        LogFormattingService $logFormatter,
        ObeliskService $obeliskService,
        LocationResourceService $resourceService,
        CurrencyService $currencyService,
        FactionCountService $factionCountService,
        MineTargetResetService $mineTargetResetService,
        MineDetectionService $mineDetectionService
    ) {
        $this->experienceService = $experienceService;
        $this->battleLogService = $battleLogService;
        $this->damageCalculator = $damageCalculator;
        $this->rewardService = $rewardService;
        $this->skillService = $skillService;
        $this->logFormatter = $logFormatter;
        $this->obeliskService = $obeliskService;
        $this->resourceService = $resourceService;
        $this->currencyService = $currencyService;
        $this->factionCountService = $factionCountService;
        $this->mineTargetResetService = $mineTargetResetService;
        $this->mineDetectionService = $mineDetectionService;
    }

    /**
     * Отображение страницы локации рудника
     *
     * @param string $slug Идентификатор локации
     * @return \Illuminate\View\View
     */
    public function index($slug)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы просматривать эту страницу.');
        }

        // Проверка HP
        if ($user->profile->hp <= 0) {
            return redirect()->route('battle.mines.index')
                ->with('error', 'У вас слишком мало здоровья. Восстановитесь!');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем базовую локацию
        $location = $mineLocation->baseLocation;
        $locationName = $location->name;
        $locationId = $location->id;

        // Проверяем и сбрасываем цели при переходе между подлокациями рудника
        $previousMineLocation = $this->mineTargetResetService->getPreviousMineLocationFromSessionById();
        $targetWasReset = $this->mineTargetResetService->checkAndResetTargetsOnMineLocationChange(
            $user,
            $mineLocation,
            $previousMineLocation
        );

        // Сохраняем текущую подлокацию в сессии для следующего перехода
        $this->mineTargetResetService->storeMineLocationInSession($mineLocation);

        // Обновляем текущую локацию пользователя
        if ($user->statistics->current_location !== $locationName) {
            $user->statistics->current_location = $locationName;
            $user->statistics->save();
            Log::info("Обновлена локация пользователя", [
                'user_id' => $user->id,
                'location' => $locationName
            ]);
        }

        // Получаем мобов для локации
        // Если это подлокация рудника, ищем мобов по mine_location_id
        if ($mineLocation->isSubLocation()) {
            $mobsInLocation = Mob::where('mine_location_id', $mineLocation->id)
                ->where('hp', '>', 0)
                ->get();
        } else {
            // Иначе используем обычную логику
            $mobsInLocation = Mob::where(function ($query) use ($locationId, $locationName) {
                $query->where('location', $locationName)
                    ->orWhere('location_id', $locationId);
            })
                ->where('hp', '>', 0)
                ->get();
        }

        // Получаем ботов для локации
        // Боты создаются с полем location равным названию из таблицы locations
        $botsInLocation = \App\Models\Bot::where('location', $locationName)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->get();

        // Сброс last_attacker_id, если атакующий не в этой локации
        if ($user->last_attacker_id) {
            $lastAttacker = User::whereHas('statistics', function ($query) use ($locationName) {
                $query->where('current_location', $locationName);
            })->find($user->last_attacker_id);

            if (!$lastAttacker) {
                Log::info("Сброс last_attacker_id для пользователя {$user->id}, атакующий не в локации '{$locationName}'");
                $user->last_attacker_id = null;
                $user->save();
            }
        }

        // Получение ресурсов для локации
        $resourcesInLocation = $this->getLocationResources($locationId, $mineLocation);

        // Обработка цели пользователя
        [$targetResource, $targetPlayer, $targetMob, $targetBot] = $this->handleUserTarget($user, $locationName, $locationId);

        // Используем FactionCountService для подсчета игроков и ботов по фракциям и классам
        $userRace = $user->profile->race ?? null;
        $factionCounts = $this->factionCountService->getLocationFactionCounts($locationName, $userRace);

        // Извлекаем данные из результата FactionCountService (используем total_counts для суммы игроков и ботов)
        $solWarriors = $factionCounts['total_counts']['solarius']['warriors'];
        $solMages = $factionCounts['total_counts']['solarius']['mages'];
        $solKnights = $factionCounts['total_counts']['solarius']['knights'];
        $lunWarriors = $factionCounts['total_counts']['lunarius']['warriors'];
        $lunMages = $factionCounts['total_counts']['lunarius']['mages'];
        $lunKnights = $factionCounts['total_counts']['lunarius']['knights'];

        // Получение игроков в локации (для совместимости с существующим кодом)
        $playersData = $this->getPlayersInLocation($locationName);

        // Получение логов боя
        $battleLogKey = $this->getBattleLogKey($user);
        $battleLogs = $this->battleLogService->getLogs($battleLogKey);

        // Проверка и восстановление баффов
        app(SkillService::class)->checkAndRestoreUserStrength($user);

        // Загрузка активных эффектов
        $allUserEffects = $user->activeEffects()->with('skill')->get();
        $userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());

        // Удаление истекших эффектов
        $expiredEffectIds = $allUserEffects->filter(fn($effect) => !$effect->isActive())->pluck('id')->toArray();
        if (!empty($expiredEffectIds)) {
            \App\Models\ActiveEffect::whereIn('id', $expiredEffectIds)->delete();
            Log::info('Удалены истекшие эффекты из контроллера', ['effect_ids' => $expiredEffectIds]);
        }

        // Загрузка умений пользователя
        $userSkills = $user->skills()->with('skill')->get();

        // Подготовка данных для отображения - получаем все подлокации родителя или текущей локации
        if ($mineLocation->isSubLocation()) {
            // Если это подлокация, показываем все подлокации родительской локации
            $subLocations = $mineLocation->parent->sublocations()->where('is_active', true)->orderBy('order')->get();
        } else {
            // Если это основная локация, показываем её подлокации
            $subLocations = $mineLocation->sublocations()->where('is_active', true)->orderBy('order')->get();
        }

        // Формирование хлебных крошек
        $breadcrumbs = [
            ['label' => 'Главная', 'url' => route('home')],
            ['label' => 'Сражение', 'url' => route('battle.index')],
            ['label' => 'Рудники', 'url' => route('battle.mines.index')],
        ];

        // Если это подлокация, добавляем родительскую локацию
        if ($mineLocation->isSubLocation()) {
            $parentLocation = $mineLocation->parent;
            $breadcrumbs[] = ['label' => $parentLocation->name, 'url' => route('battle.mines.location', $parentLocation->slug)];
        }

        // Добавляем текущую локацию
        $breadcrumbs[] = ['label' => $mineLocation->name, 'url' => route('battle.mines.location', $mineLocation->slug)];

        // Формируем название локации с учетом иерархии
        $locationTitle = $mineLocation->isSubLocation()
            ? $mineLocation->parent->name . ' - ' . $mineLocation->name
            : $mineLocation->name;

        // Данные для отображения
        $viewData = [
            'userProfile' => $user->profile,
            'user' => $user,
            'battleLogs' => $battleLogs,
            'solWarriors' => $solWarriors,
            'solMages' => $solMages,
            'solKnights' => $solKnights,
            'lunWarriors' => $lunWarriors,
            'lunMages' => $lunMages,
            'lunKnights' => $lunKnights,
            'currentLocation' => $locationName,
            'targetResource' => $targetResource,
            'resourcesInLocation' => $resourcesInLocation,
            'target' => $targetPlayer,
            'targetMob' => $targetMob,
            'targetBot' => $targetBot,
            'targetType' => $user->current_target_type,
            'location' => $locationName,
            'mobsInLocation' => $mobsInLocation,
            'botsInLocation' => $botsInLocation, // Добавляем ботов в данные для представления
            'currentSubLocation' => $mineLocation->slug,
            'userEffects' => $userEffects,
            'userSkills' => $userSkills,
            'playersInLocation' => $playersData['playersInLocation'],
            'mineLocation' => $mineLocation,
            'subLocations' => $subLocations,
            'breadcrumbs' => $breadcrumbs,
            'locationTitle' => $locationTitle
        ];

        // Выбор шаблона в зависимости от наличия кастомного
        $viewTemplate = view()->exists("battle.mines.locations.{$mineLocation->slug}")
            ? "battle.mines.locations.{$mineLocation->slug}"
            : 'battle.mines.locations.default';

        return view($viewTemplate, $viewData);
    }

    /**
     * Получение ресурсов для локации
     *
     * @param int $locationId ID локации
     * @param \App\Models\MineLocation $mineLocation Объект локации рудника
     * @return \Illuminate\Support\Collection
     */
    protected function getLocationResources(int $locationId, $mineLocation = null)
    {
        // Если это подлокация рудника, используем специальный метод
        if ($mineLocation && $mineLocation->isSubLocation()) {
            return $this->resourceService->getActiveResourcesForMineLocation($mineLocation->id);
        }

        // Иначе используем обычную логику
        return $this->resourceService->getActiveResourcesForLocation($locationId);
    }

    /**
     * Обработка цели пользователя
     *
     * @param User $user Пользователь
     * @param string $locationName Название локации
     * @param int $locationId ID локации
     * @param Mob|null $targetMob Моб-цель (опционально)
     * @return array
     */
    protected function handleUserTarget(User $user, string $locationName, int $locationId, ?Mob $targetMob = null)
    {
        $targetResource = null;
        $targetPlayer = null;
        $targetBot = null;

        if ($user->current_target_id && $user->current_target_type) {
            if ($user->current_target_type === 'resource') {
                // Используем MineTargetResetService для проверки доступности ресурса
                $mineLocation = MineLocation::where('slug', request()->route('slug'))
                    ->where('is_active', true)
                    ->first();
                
                if ($mineLocation) {
                    $targetResourceAvailable = $this->mineTargetResetService->isTargetAvailableInMineLocation(
                        'resource', 
                        $user->current_target_id, 
                        $mineLocation
                    );
                    
                    if ($targetResourceAvailable) {
                        $targetResource = SpawnedResource::with('resource')
                            ->where('id', $user->current_target_id)
                            ->where('is_active', true)
                            ->where('durability', '>', 0)
                            ->first();
                    } else {
                        $targetResource = null;
                    }
                } else {
                    // Fallback к старой логике если локация рудника не найдена
                    $targetResource = SpawnedResource::with('resource')
                        ->where('id', $user->current_target_id)
                        ->where('location_id', $locationId)
                        ->where('is_active', true)
                        ->where('durability', '>', 0)
                        ->first();
                }

                if (!$targetResource) {
                    $user->current_target_id = null;
                    $user->current_target_type = null;
                    $user->save();
                }
            } elseif ($user->current_target_type === 'player') {
                $targetPlayer = User::whereHas('statistics', function ($query) use ($locationName) {
                    $query->where('current_location', $locationName);
                })->find($user->current_target_id);

                if (!$targetPlayer) {
                    $user->current_target_id = null;
                    $user->current_target_type = null;
                    $user->save();
                }
            } elseif ($user->current_target_type === 'mob') {
                // Используем MineTargetResetService для проверки доступности моба
                $mineLocation = MineLocation::where('slug', request()->route('slug'))
                    ->where('is_active', true)
                    ->first();
                
                if ($mineLocation) {
                    $targetMobAvailable = $this->mineTargetResetService->isTargetAvailableInMineLocation(
                        'mob', 
                        $user->current_target_id, 
                        $mineLocation
                    );
                    
                    if ($targetMobAvailable) {
                        $targetMob = Mob::where('id', $user->current_target_id)
                            ->where('hp', '>', 0)
                            ->first();
                    } else {
                        $targetMob = null;
                    }
                } else {
                    // Fallback к старой логике если локация рудника не найдена
                    $targetMob = Mob::where('id', $user->current_target_id)
                        ->where(function ($query) use ($locationId, $locationName) {
                            $query->where('location_id', $locationId)
                                ->orWhere('location', $locationName);
                        })
                        ->where('hp', '>', 0)
                        ->first();
                }

                if (!$targetMob) {
                    $user->current_target_id = null;
                    $user->current_target_type = null;
                    $user->save();
                }
            } elseif ($user->current_target_type === 'bot') {
                // Используем MineTargetResetService для проверки доступности бота
                $mineLocation = MineLocation::where('slug', request()->route('slug'))
                    ->where('is_active', true)
                    ->first();
                
                if ($mineLocation) {
                    $targetBotAvailable = $this->mineTargetResetService->isTargetAvailableInMineLocation(
                        'bot', 
                        $user->current_target_id, 
                        $mineLocation
                    );
                    
                    if ($targetBotAvailable) {
                        $targetBot = \App\Models\Bot::where('id', $user->current_target_id)
                            ->where('is_active', true)
                            ->where('hp', '>', 0)
                            ->first();
                    } else {
                        $targetBot = null;
                    }
                } else {
                    // Fallback к старой логике если локация рудника не найдена
                    $targetBot = \App\Models\Bot::where('id', $user->current_target_id)
                        ->where('location', $locationName)
                        ->where('is_active', true)
                        ->where('hp', '>', 0)
                        ->first();
                }

                if (!$targetBot) {
                    $user->current_target_id = null;
                    $user->current_target_type = null;
                    $user->save();
                }
            }
        }

        return [$targetResource, $targetPlayer, $targetMob, $targetBot];
    }

    /**
     * Получение игроков в локации
     *
     * @param string $locationName Название локации
     * @return array
     */
    protected function getPlayersInLocation(string $locationName)
    {
        $user = Auth::user();

        // Используем централизованный сервис для работы с локациями
        $locationService = app(\App\Services\battle\UserLocationService::class);

        $playersInLocation = $locationService->getPlayersInLocation($locationName, $user->id);

        $solWarriors = $playersInLocation->filter(fn($player) => $player->profile->fraction === 'sol' && $player->profile->class === 'warrior');
        $solMages = $playersInLocation->filter(fn($player) => $player->profile->fraction === 'sol' && $player->profile->class === 'mage');
        $solKnights = $playersInLocation->filter(fn($player) => $player->profile->fraction === 'sol' && $player->profile->class === 'knight');
        $lunWarriors = $playersInLocation->filter(fn($player) => $player->profile->fraction === 'lun' && $player->profile->class === 'warrior');
        $lunMages = $playersInLocation->filter(fn($player) => $player->profile->fraction === 'lun' && $player->profile->class === 'mage');
        $lunKnights = $playersInLocation->filter(fn($player) => $player->profile->fraction === 'lun' && $player->profile->class === 'knight');

        return [
            'playersInLocation' => $playersInLocation,
            'solWarriors' => $solWarriors,
            'solMages' => $solMages,
            'solKnights' => $solKnights,
            'lunWarriors' => $lunWarriors,
            'lunMages' => $lunMages,
            'lunKnights' => $lunKnights
        ];
    }

    /**
     * Получение ключа логов боя
     *
     * @param User $user Пользователь
     * @return string
     */
    protected function getBattleLogKey(User $user)
    {
        return "battle_log:{$user->id}";
    }

    /**
     * Выбор игрока в качестве цели
     *
     * @param int $id ID игрока
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectPlayer($id)
    {
        $user = auth()->user();

        // Проверяем авторизацию
        if (!$user) {
            return back()->with('error', 'Вы должны быть авторизованы, чтобы выбрать цель');
        }

        // Находим игрока в той же локации
        $target = User::whereHas('statistics', function ($query) use ($user) {
            $query->where('current_location', $user->statistics->current_location);
        })->find($id);

        if (!$target) {
            return back()->with('error', 'Игрок не найден в вашей локации');
        }

        // Обновляем текущую цель пользователя
        $user->current_target_id = $target->id;
        $user->current_target_type = 'player';
        $user->save();

        // Логгирование
        Log::info("Пользователь выбрал цель - игрока", [
            'user_id' => $user->id,
            'target_id' => $target->id,
            'location' => $user->statistics->current_location
        ]);

        return back()->with('success', "Вы выбрали целью игрока {$target->name}");
    }

    /**
     * Выбор моба в качестве цели
     *
     * @param int $id ID моба
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectMob($id)
    {
        $user = Auth::user();

        // Проверяем авторизацию
        if (!$user) {
            return back()->with('error', 'Вы должны быть авторизованы, чтобы выбрать цель');
        }

        // Получаем текущую локацию пользователя
        $currentLocation = $user->statistics->current_location;
        $locationId = Location::where('name', $currentLocation)->value('id');

        // Находим моба в локации, учитывая точное название локации
        $mob = Mob::where('id', $id)
            ->where(function ($query) use ($locationId, $currentLocation) {
                $query->where('location', $currentLocation) // Моб должен быть точно в этой локации
                    ->orWhere(function ($q) use ($locationId, $currentLocation) {
                        // Или иметь правильный location_id и либо NULL, либо точное название в поле location
                        $q->where('location_id', $locationId)
                            ->where(function ($subQ) use ($currentLocation) {
                            $subQ->where('location', $currentLocation)
                                ->orWhereNull('location');
                        });
                    });
            })
            ->where('hp', '>', 0)
            ->first();

        if (!$mob) {
            return back()->with('error', 'Моб не найден в вашей локации или уже мёртв');
        }

        // Обновляем текущую цель пользователя
        $user->current_target_id = $mob->id;
        $user->current_target_type = 'mob';
        $user->save();

        // Логгирование
        Log::info("Пользователь выбрал цель - моба", [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'location' => $currentLocation
        ]);

        return back()->with('success', "Вы выбрали целью {$mob->name}");
    }

    /**
     * Выбор ресурса в качестве цели
     *
     * @param int $id ID ресурса
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectResource($id, $slug)
    {
        $user = auth()->user();

        // Проверяем авторизацию
        if (!$user) {
            return back()->with('error', 'Вы должны быть авторизованы, чтобы выбрать ресурс');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем основную локацию
        $location = $mineLocation->baseLocation;
        $locationId = $location->id;

        // Находим ресурс в локации
        $resource = SpawnedResource::with('resource')
            ->where('id', $id)
            ->where('location_id', $locationId)
            ->where('is_active', true)
            ->where('durability', '>', 0)
            ->first();

        if (!$resource) {
            return back()->with('error', 'Ресурс не найден или уже истощен');
        }

        // Обновляем текущую цель пользователя
        $user->current_target_id = $resource->id;
        $user->current_target_type = 'resource';
        $user->save();

        // Логгирование
        Log::info("Пользователь выбрал цель - ресурс", [
            'user_id' => $user->id,
            'resource_id' => $resource->id,
            'resource_name' => $resource->resource->name ?? 'Неизвестный ресурс',
            'location_id' => $locationId
        ]);

        return back()->with('success', "Вы выбрали ресурс {$resource->resource->name} в качестве цели");
    }

    /**
     * Добыча ресурса
     *
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function hitResource($slug)
    {
        $user = Auth::user();

        // Проверяем авторизацию
        if (!$user) {
            return back()->with('error', 'Вы должны быть авторизованы, чтобы добывать ресурсы');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем основную локацию
        $location = $mineLocation->baseLocation;
        $locationId = $location->id;

        // Проверяем, выбран ли ресурс как цель
        if ($user->current_target_type !== 'resource' || !$user->current_target_id) {
            return back()->with('error', 'Вы должны сначала выбрать ресурс целью');
        }

        // Находим выбранный ресурс
        $resource = SpawnedResource::with('resource')
            ->where('id', $user->current_target_id)
            ->where('location_id', $locationId)
            ->where('is_active', true)
            ->where('durability', '>', 0)
            ->first();

        if (!$resource) {
            $user->current_target_id = null;
            $user->current_target_type = null;
            $user->save();
            return back()->with('error', 'Ресурс не найден или истощен. Выберите другой ресурс.');
        }

        // Рассчитываем силу удара и добавляем случайность
        $basePower = 1; // Базовая сила добычи
        $miningPower = $user->profile->mining_power ?? $basePower;

        // Добавляем случайность (75-125% от базовой силы)
        $randomFactor = rand(75, 125) / 100;
        $finalPower = max(1, round($miningPower * $randomFactor));

        // Уменьшаем прочность ресурса
        $this->resourceService->decreaseDurability($resource, $finalPower);

        // Обновляем состояние ресурса после добычи
        $remainingDurability = $resource->durability;
        $isResourceDepleted = $remainingDurability <= 0;

        // Формируем сообщение для лога боя
        $message = $isResourceDepleted
            ? "Вы добыли ресурс {$resource->resource->name} полностью!"
            : "Вы нанесли {$finalPower} урона по ресурсу {$resource->resource->name}. Осталось прочности: {$remainingDurability}";

        // Добавляем ресурс пользователю, если он полностью добыт
        if ($isResourceDepleted) {
            // Рассчитываем количество полученного ресурса (базовая награда + случайный бонус)
            $baseAmount = $resource->resource->base_amount ?? 1;
            $randomBonus = rand(0, round($baseAmount * 0.5)); // Случайный бонус до 50% от базового количества
            $amount = $baseAmount + $randomBonus;

            // Добавляем ресурс игроку
            UserResource::updateOrCreate(
                ['user_id' => $user->id, 'resource_id' => $resource->resource_id],
                ['amount' => \DB::raw("amount + {$amount}")]
            );

            // Добавляем бонусный опыт за добычу ресурса
            $experienceGain = $resource->resource->experience_reward ?? 5;
            $this->experienceService->addExperience($user, $experienceGain);

            // Сбрасываем текущую цель, так как ресурс добыт
            $user->current_target_id = null;
            $user->current_target_type = null;
            $user->save();

            // Дополняем сообщение информацией о полученной награде
            $message .= " Вы получили {$amount} единиц ресурса и {$experienceGain} опыта!";

            // Добавляем лог о получении ресурса
            Log::info("Пользователь добыл ресурс", [
                'user_id' => $user->id,
                'resource_id' => $resource->resource_id,
                'resource_name' => $resource->resource->name,
                'amount' => $amount,
                'experience' => $experienceGain
            ]);
        }

        // Применяем дебаф обнаружения за добычу ресурса в рудниках
        $detectionEffect = $this->mineDetectionService->applyDetectionDebuff($user, $mineLocation);
        
        if ($detectionEffect) {
            $detectionMessage = " Вас заметили! На вас наложен дебаф 'Обнаружен' на " . 
                floor(MineDetectionService::DETECTION_DURATION / 60) . " минут.";
            $message .= $detectionMessage;

            // Логируем применение дебафа
            Log::info('Применен дебаф обнаружения при добыче ресурса', [
                'user_id' => $user->id,
                'mine_location_id' => $mineLocation->id,
                'effect_id' => $detectionEffect->id
            ]);
        }

        // Добавляем сообщение в лог боя
        $battleLogKey = $this->getBattleLogKey($user);
        $this->battleLogService->addLog($battleLogKey, $message);

        return back()->with('success', $message);
    }
}
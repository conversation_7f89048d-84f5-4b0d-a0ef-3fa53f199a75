<?php

/**
 * ПОЛНОЕ ИСПРАВЛЕНИЕ ВСЕХ ПРОБЛЕМ С БОТАМИ В РУДНИКАХ
 * Исправляет несоответствия между админкой и компонентом статуса фракций
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use App\Services\battle\UserLocationService;
use App\Services\battle\FactionCountService;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 ПОЛНОЕ ИСПРАВЛЕНИЕ ВСЕХ ПРОБЛЕМ С БОТАМИ\n";
echo "==========================================\n\n";

$targetLocation = 'xzxzxzx';
$attackingBots = ['nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм'];

echo "Целевая подлокация: {$targetLocation}\n";
echo "Атакующие боты: " . implode(', ', $attackingBots) . "\n\n";

// Найдем подлокацию
$mineLocation = MineLocation::where('name', $targetLocation)->first();
if (!$mineLocation) {
    echo "❌ Подлокация не найдена!\n";
    exit;
}

echo "✅ Подлокация: {$mineLocation->name} (ID: {$mineLocation->id})\n\n";

// 1. ЧТО ПОКАЗЫВАЕТ АДМИНКА СЕЙЧАС
echo "1. АДМИНКА - ТЕКУЩЕЕ СОСТОЯНИЕ\n";
echo "==============================\n";

$adminBots = Bot::where('location', $targetLocation)
    ->where('created_by_admin', true)
    ->get();

echo "Ботов в админке: {$adminBots->count()}\n";
foreach ($adminBots as $bot) {
    echo "  ✅ {$bot->name} (race: {$bot->race}, class: {$bot->class})\n";
}

// 2. ЧТО ПОКАЗЫВАЕТ КОМПОНЕНТ СТАТУСА ФРАКЦИЙ
echo "\n2. КОМПОНЕНТ СТАТУСА ФРАКЦИЙ\n";
echo "============================\n";

$userLocationService = new UserLocationService();

// Считаем как компонент
$solariumWarriors = $userLocationService->getBotsCountInLocation($targetLocation, 'solarius', 'warrior');
$solariumMages = $userLocationService->getBotsCountInLocation($targetLocation, 'solarius', 'mage');
$solariumPriests = $userLocationService->getBotsCountInLocation($targetLocation, 'solarius', 'priest');

$lunariumWarriors = $userLocationService->getBotsCountInLocation($targetLocation, 'lunarius', 'warrior');
$lunariumMages = $userLocationService->getBotsCountInLocation($targetLocation, 'lunarius', 'mage');
$lunariumPriests = $userLocationService->getBotsCountInLocation($targetLocation, 'lunarius', 'priest');

$totalSolarium = $solariumWarriors + $solariumMages + $solariumPriests;
$totalLunarium = $lunariumWarriors + $lunariumMages + $lunariumPriests;
$totalBots = $totalSolarium + $totalLunarium;

echo "Солариус: {$totalSolarium} (воины: {$solariumWarriors}, маги: {$solariumMages}, жрецы: {$solariumPriests})\n";
echo "Лунариус: {$totalLunarium} (воины: {$lunariumWarriors}, маги: {$lunariumMages}, жрецы: {$lunariumPriests})\n";
echo "Всего ботов в компоненте: {$totalBots}\n";

// 3. ПОИСК ВСЕХ БОТОВ В БАЗЕ ДАННЫХ
echo "\n3. ПОИСК ВСЕХ БОТОВ В БАЗЕ ДАННЫХ\n";
echo "=================================\n";

$allBots = [];

// Поиск по именам
foreach ($attackingBots as $botName) {
    $bot = Bot::where('name', $botName)->first();
    if ($bot) {
        $allBots[] = $bot;
        echo "🤖 {$bot->name}: location='{$bot->location}', mine_location_id={$bot->mine_location_id}\n";
    }
}

// Поиск по ID локации
$botsWithId = Bot::where('location', (string) $mineLocation->id)->get();
foreach ($botsWithId as $bot) {
    if (!in_array($bot->name, array_column($allBots, 'name'))) {
        $allBots[] = $bot;
        echo "🤖 {$bot->name}: location='{$bot->location}' (ID вместо названия)\n";
    }
}

// Поиск по mine_location_id
$botsWithMineId = Bot::where('mine_location_id', $mineLocation->id)->get();
foreach ($botsWithMineId as $bot) {
    if (!in_array($bot->name, array_column($allBots, 'name'))) {
        $allBots[] = $bot;
        echo "🤖 {$bot->name}: mine_location_id={$bot->mine_location_id}, location='{$bot->location}'\n";
    }
}

echo "Всего найдено уникальных ботов: " . count($allBots) . "\n";

// 4. ИСПРАВЛЕНИЕ ВСЕХ БОТОВ
echo "\n4. ИСПРАВЛЕНИЕ ВСЕХ БОТОВ\n";
echo "=========================\n";

$fixedCount = 0;

foreach ($allBots as $bot) {
    $needsFix = false;
    $oldLocation = $bot->location;
    $oldMineId = $bot->mine_location_id;
    
    // Проверяем, нужно ли исправлять
    if ($bot->location !== $mineLocation->name) {
        $needsFix = true;
    }
    
    if ($bot->mine_location_id !== $mineLocation->id) {
        $needsFix = true;
    }
    
    if ($needsFix) {
        $bot->location = $mineLocation->name;
        $bot->mine_location_id = $mineLocation->id;
        $bot->next_action_time = null;
        $bot->save();
        
        echo "✅ {$bot->name}: location '{$oldLocation}' → '{$bot->location}', mine_location_id {$oldMineId} → {$bot->mine_location_id}\n";
        $fixedCount++;
    } else {
        echo "✅ {$bot->name}: уже правильно настроен\n";
    }
}

echo "Исправлено ботов: {$fixedCount}\n";

// 5. ПРОВЕРКА ПОСЛЕ ИСПРАВЛЕНИЯ
echo "\n5. ПРОВЕРКА ПОСЛЕ ИСПРАВЛЕНИЯ\n";
echo "=============================\n";

// Проверяем админку
$adminBotsAfter = Bot::where('location', $targetLocation)
    ->where('created_by_admin', true)
    ->get();

echo "Ботов в админке теперь: {$adminBotsAfter->count()}\n";
foreach ($adminBotsAfter as $bot) {
    $active = $bot->is_active && $bot->hp > 0 ? '✅' : '❌';
    echo "  {$active} {$bot->name} (race: {$bot->race}, class: {$bot->class}, HP: {$bot->hp}/{$bot->max_hp})\n";
}

// Проверяем компонент
$solariumWarriorsAfter = $userLocationService->getBotsCountInLocation($targetLocation, 'solarius', 'warrior');
$solariumMagesAfter = $userLocationService->getBotsCountInLocation($targetLocation, 'solarius', 'mage');
$solariumPriestsAfter = $userLocationService->getBotsCountInLocation($targetLocation, 'solarius', 'priest');

$lunariumWarriorsAfter = $userLocationService->getBotsCountInLocation($targetLocation, 'lunarius', 'warrior');
$lunariumMagesAfter = $userLocationService->getBotsCountInLocation($targetLocation, 'lunarius', 'mage');
$lunariumPriestsAfter = $userLocationService->getBotsCountInLocation($targetLocation, 'lunarius', 'priest');

$totalSolariumAfter = $solariumWarriorsAfter + $solariumMagesAfter + $solariumPriestsAfter;
$totalLunariumAfter = $lunariumWarriorsAfter + $lunariumMagesAfter + $lunariumPriestsAfter;
$totalBotsAfter = $totalSolariumAfter + $totalLunariumAfter;

echo "\nКомпонент статуса фракций теперь показывает:\n";
echo "Солариус: {$totalSolariumAfter} (воины: {$solariumWarriorsAfter}, маги: {$solariumMagesAfter}, жрецы: {$solariumPriestsAfter})\n";
echo "Лунариус: {$totalLunariumAfter} (воины: {$lunariumWarriorsAfter}, маги: {$lunariumMagesAfter}, жрецы: {$lunariumPriestsAfter})\n";
echo "Всего ботов в компоненте: {$totalBotsAfter}\n";

// 6. СРАВНЕНИЕ РЕЗУЛЬТАТОВ
echo "\n6. СРАВНЕНИЕ РЕЗУЛЬТАТОВ\n";
echo "=======================\n";

$adminCount = $adminBotsAfter->count();
$componentCount = $totalBotsAfter;

echo "Админка показывает: {$adminCount} ботов\n";
echo "Компонент показывает: {$componentCount} ботов\n";

if ($adminCount === $componentCount) {
    echo "✅ РЕЗУЛЬТАТЫ СОВПАДАЮТ!\n";
} else {
    echo "❌ РЕЗУЛЬТАТЫ НЕ СОВПАДАЮТ!\n";
    echo "Разница: " . abs($adminCount - $componentCount) . " ботов\n";
    
    // Дополнительная диагностика
    echo "\nДополнительная диагностика:\n";
    
    // Проверяем активность ботов
    $activeBotsInAdmin = $adminBotsAfter->where('is_active', true)->where('hp', '>', 0)->count();
    echo "Активных ботов в админке: {$activeBotsInAdmin}\n";
    
    // Проверяем все боты в компоненте (без фильтров)
    $allBotsInComponent = $userLocationService->getBotsCountInLocation($targetLocation);
    echo "Всех ботов в компоненте: {$allBotsInComponent}\n";
}

// 7. ПРОВЕРКА КОНКРЕТНЫХ БОТОВ
echo "\n7. ПРОВЕРКА КОНКРЕТНЫХ БОТОВ\n";
echo "============================\n";

foreach ($attackingBots as $botName) {
    $bot = Bot::where('name', $botName)->first();
    if ($bot) {
        $inAdmin = $bot->location === $targetLocation ? '✅' : '❌';
        $correctData = ($bot->location === $targetLocation && $bot->mine_location_id === $mineLocation->id) ? '✅' : '❌';
        
        echo "🤖 {$botName}:\n";
        echo "   В админке: {$inAdmin} (location: '{$bot->location}')\n";
        echo "   Корректные данные: {$correctData} (mine_location_id: {$bot->mine_location_id})\n";
        echo "   Активен: " . ($bot->is_active && $bot->hp > 0 ? '✅' : '❌') . "\n";
        echo "\n";
    }
}

// 8. ИТОГОВЫЙ ОТЧЕТ
echo "8. ИТОГОВЫЙ ОТЧЕТ\n";
echo "================\n";

if ($adminCount === $componentCount && $adminCount >= count($attackingBots)) {
    echo "🎉 ВСЕ ПРОБЛЕМЫ РЕШЕНЫ!\n";
    echo "✅ Все боты отображаются в админке\n";
    echo "✅ Компонент показывает правильное количество\n";
    echo "✅ Боты атакуют только в своей подлокации\n";
} else {
    echo "⚠️ ПРОБЛЕМЫ ОСТАЛИСЬ:\n";
    if ($adminCount !== $componentCount) {
        echo "- Несоответствие между админкой и компонентом\n";
    }
    if ($adminCount < count($attackingBots)) {
        echo "- Не все атакующие боты отображаются в админке\n";
    }
    echo "Запустите скрипт повторно или выполните ручную проверку\n";
}

echo "\n📊 СТАТИСТИКА:\n";
echo "Исправлено ботов: {$fixedCount}\n";
echo "Ботов в админке: {$adminCount}\n";
echo "Ботов в компоненте: {$componentCount}\n";
echo "Время: " . now()->format('H:i:s d.m.Y') . "\n";
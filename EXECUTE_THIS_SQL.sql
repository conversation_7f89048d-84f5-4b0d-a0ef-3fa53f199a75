-- ОБЯЗАТЕЛЬНО ВЫПОЛНИТЕ ЭТОТ SQL В POSTGRESQL!
-- Добавляем поля для отслеживания поражений в таблицу user_profiles

ALTER TABLE user_profiles 
ADD COLUMN is_defeated BOOLEAN DEFAULT FALSE,
ADD COLUMN defeated_by_type VARCHAR(255) NULL,
ADD COLUMN defeated_by_id BIGINT NULL,
ADD COLUMN defeated_at TIMESTAMP NULL;

-- Добавляем индексы для оптимизации
CREATE INDEX idx_user_profiles_is_defeated ON user_profiles (is_defeated);
CREATE INDEX idx_user_profiles_defeated_by ON user_profiles (defeated_by_type, defeated_by_id);
CREATE INDEX idx_user_profiles_defeated_at ON user_profiles (defeated_at);

-- Проверяем, что столбцы созданы
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND column_name IN ('is_defeated', 'defeated_by_type', 'defeated_by_id', 'defeated_at');
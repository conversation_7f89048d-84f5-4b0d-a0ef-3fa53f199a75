<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Services\BotBehaviorService;

class DiagnoseMineBotsLocation extends Command
{
    protected $signature = 'diagnose:mine-bots-location {--fix : Исправить неправильные локации ботов}';
    protected $description = 'Диагностика локаций ботов в рудниках и их соответствия подлокациям';

    public function handle()
    {
        $this->info('🔍 Диагностика локаций ботов в рудниках');
        $this->info('==========================================');
        
        // Проверка ботов
        $bots = Bot::where('location', 'LIKE', '%рудник%')
            ->orWhereNotNull('mine_location_id')
            ->orderBy('location')
            ->get();
            
        $this->info("Найдено ботов в рудниках: " . $bots->count());
        $this->newLine();
        
        $problemBots = [];
        $locationGroups = [];
        
        foreach($bots as $bot) {
            $locationGroups[$bot->location][] = $bot;
            
            // Проверяем соответствие локации и mine_location_id
            if ($bot->mine_location_id) {
                $mineLocation = MineLocation::find($bot->mine_location_id);
                if ($mineLocation && $bot->location !== $mineLocation->name) {
                    $problemBots[] = [
                        'bot' => $bot,
                        'expected_location' => $mineLocation->name,
                        'current_location' => $bot->location
                    ];
                }
            }
        }
        
        // Отображаем ботов по локациям
        foreach($locationGroups as $location => $locationBots) {
            $this->warn("📍 Локация: {$location}");
            foreach($locationBots as $bot) {
                $status = $bot->is_active ? '✅' : '❌';
                $this->line("  🤖 {$status} {$bot->name} ({$bot->race}) - Mine ID: {$bot->mine_location_id}");
            }
            $this->newLine();
        }
        
        // Отображаем проблемные боты
        if (!empty($problemBots)) {
            $this->error("❌ НАЙДЕНЫ ПРОБЛЕМНЫЕ БОТЫ:");
            foreach($problemBots as $problem) {
                $this->error("  🤖 {$problem['bot']->name}: текущая локация '{$problem['current_location']}', должна быть '{$problem['expected_location']}'");
            }
            $this->newLine();
            
            // Предложение исправления
            if ($this->option('fix')) {
                $this->info("🔧 Исправляем проблемные боты...");
                foreach($problemBots as $problem) {
                    $bot = $problem['bot'];
                    $bot->location = $problem['expected_location'];
                    $bot->save();
                    $this->info("  ✅ Исправлен бот {$bot->name}: {$problem['current_location']} → {$problem['expected_location']}");
                }
            } else {
                $this->warn("💡 Для исправления запустите: php artisan diagnose:mine-bots-location --fix");
            }
        } else {
            $this->info("✅ Все боты имеют правильные локации");
        }
        
        $this->newLine();
        
        // Проверка игроков
        $users = User::whereHas('statistics', function($q) {
            $q->where('current_location', 'LIKE', '%рудник%');
        })->with('statistics', 'profile')->get();
        
        $this->info("Найдено игроков в рудниках: " . $users->count());
        $this->newLine();
        
        $playerGroups = [];
        foreach($users as $user) {
            $playerGroups[$user->statistics->current_location][] = $user;
        }
        
        foreach($playerGroups as $location => $locationPlayers) {
            $this->warn("📍 Локация: {$location}");
            foreach($locationPlayers as $player) {
                $status = $player->is_online ? '🟢' : '🔴';
                $this->line("  👤 {$status} {$player->name} ({$player->profile->race})");
            }
            $this->newLine();
        }
        
        // Проверка логики атак
        $this->info("🎯 Проверка логики атак:");
        $this->newLine();
        
        $botBehaviorService = app(BotBehaviorService::class);
        $attackTests = 0;
        $correctBehavior = 0;
        
        foreach($locationGroups as $location => $locationBots) {
            if (isset($playerGroups[$location])) {
                $this->warn("⚔️ Тестирование в локации: {$location}");
                
                foreach($locationBots as $bot) {
                    foreach($playerGroups[$location] as $player) {
                        $attackTests++;
                        
                        $shouldAttack = $botBehaviorService->shouldBotAttack($bot, $player);
                        $botRace = $bot->race ?? 'unknown';
                        $playerRace = $player->profile->race ?? 'unknown';
                        
                        $areEnemies = $this->areEnemyRaces($botRace, $playerRace);
                        $expectedBehavior = $areEnemies;
                        
                        if ($shouldAttack === $expectedBehavior) {
                            $correctBehavior++;
                            $result = $shouldAttack ? '⚔️ АТАКУЕТ' : '🤝 НЕ АТАКУЕТ';
                            $this->line("  ✅ {$bot->name} ({$botRace}) → {$player->name} ({$playerRace}): {$result}");
                        } else {
                            $result = $shouldAttack ? '⚔️ АТАКУЕТ' : '🤝 НЕ АТАКУЕТ';
                            $this->error("  ❌ {$bot->name} ({$botRace}) → {$player->name} ({$playerRace}): {$result} (НЕПРАВИЛЬНО!)");
                        }
                    }
                }
                $this->newLine();
            }
        }
        
        // Итоги
        $this->info("📊 ИТОГИ ДИАГНОСТИКИ:");
        $this->info("Всего ботов: " . $bots->count());
        $this->info("Всего игроков: " . $users->count());
        $this->info("Проблемных ботов: " . count($problemBots));
        $this->info("Тестов атак: {$attackTests}");
        $this->info("Правильное поведение: {$correctBehavior}/{$attackTests}");
        
        $successRate = $attackTests > 0 ? round(($correctBehavior / $attackTests) * 100, 2) : 0;
        $this->info("Успешность: {$successRate}%");
        
        if ($successRate >= 95) {
            $this->info("🎉 Система работает отлично!");
        } elseif ($successRate >= 80) {
            $this->warn("⚠️ Система работает удовлетворительно");
        } else {
            $this->error("❌ Система требует исправления");
        }
        
        return 0;
    }
    
    private function areEnemyRaces(string $race1, string $race2): bool
    {
        $raceRelations = [
            'solarius' => ['lunarius'],
            'lunarius' => ['solarius'],
        ];

        $enemyRaces = $raceRelations[$race1] ?? [];
        return in_array($race2, $enemyRaces, true);
    }
}
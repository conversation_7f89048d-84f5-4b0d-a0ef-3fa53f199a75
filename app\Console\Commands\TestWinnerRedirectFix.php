<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Location;
use App\Models\UserProfile;
use App\Models\UserStatistic;
use App\Services\PlayerHealthService;
use Illuminate\Support\Facades\Log;

class TestWinnerRedirectFix extends Command
{
    protected $signature = 'test:winner-redirect-fix';
    protected $description = 'Тестирует исправление проблемы с перенаправлением победителя на страницу поражения';

    public function handle()
    {
        $this->info('🧪 Тестирование исправления проблемы с перенаправлением победителя...');
        
        // Создаем тестовых пользователей
        $this->info('📝 Создание тестовых пользователей...');
        
        $winner = $this->createTestUser('winner_test_user', 'Тестовый Победитель');
        $loser = $this->createTestUser('loser_test_user', 'Тестовый Проигравший');
        
        // Устанавливаем тестовую локацию
        $location = Location::where('name', 'Эльфийская Гавань')->first();
        if (!$location) {
            $location = Location::where('is_active', true)->first();
        }
        
        if (!$location) {
            $this->error('❌ Не найдена активная локация для теста');
            return;
        }
        
        $winner->statistics->update(['current_location' => $location->name]);
        $loser->statistics->update(['current_location' => $location->name]);
        
        $this->info("📍 Локация для теста: {$location->name}");
        
        // Тест 1: Нормальная ситуация - победитель здоров
        $this->info('🎯 Тест 1: Нормальная ситуация - победитель здоров');
        
        $winner->profile->update(['hp' => 100, 'max_hp' => 100]);
        $loser->profile->update(['hp' => 1, 'max_hp' => 100]);
        
        $this->info("Winner HP: {$winner->profile->hp}, Loser HP: {$loser->profile->hp}");
        
        // Симулируем смерть проигравшего
        $loser->profile->update(['hp' => 0]);
        
        // Проверяем актуальные ресурсы победителя
        $winnerResources = $winner->profile->getActualResources();
        $this->info("Winner actual HP: {$winnerResources['current_hp']}");
        
        if ($winnerResources['current_hp'] > 0) {
            $this->info('✅ Тест 1 пройден: Победитель здоров, должен остаться в локации');
        } else {
            $this->error('❌ Тест 1 провален: Победитель не должен быть мертв');
        }
        
        // Тест 2: Проблемная ситуация - победитель тоже мертв
        $this->info('🎯 Тест 2: Проблемная ситуация - победитель тоже мертв');
        
        $winner->profile->update(['hp' => 0, 'max_hp' => 100]);
        $loser->profile->update(['hp' => 0, 'max_hp' => 100]);
        
        // Устанавливаем информацию об убийце для победителя
        $winner->update([
            'last_attacker_id' => $loser->id,
            'last_attacker_type' => 'player'
        ]);
        
        $this->info("Winner HP: {$winner->profile->hp}, Loser HP: {$loser->profile->hp}");
        
        // Проверяем актуальные ресурсы победителя
        $winnerResources = $winner->profile->getActualResources();
        $this->info("Winner actual HP: {$winnerResources['current_hp']}");
        
        if ($winnerResources['current_hp'] <= 0) {
            $this->info('✅ Тест 2 пройден: Победитель мертв, должен быть перенаправлен на поражение');
            
            // Проверяем, что информация об убийце установлена
            if ($winner->last_attacker_id && $winner->last_attacker_type) {
                $this->info("✅ Информация об убийце: {$winner->last_attacker_type} ID {$winner->last_attacker_id}");
            } else {
                $this->warn('⚠️ Информация об убийце не установлена');
            }
        } else {
            $this->error('❌ Тест 2 провален: Победитель должен быть мертв');
        }
        
        // Тест 3: Симуляция одновременной смерти
        $this->info('🎯 Тест 3: Симуляция одновременной смерти (отравление, дебафф)');
        
        $winner->profile->update(['hp' => 1, 'max_hp' => 100]);
        $loser->profile->update(['hp' => 1, 'max_hp' => 100]);
        
        // Симулируем урон, который убивает обеих
        $winner->profile->update(['hp' => 0]);
        $loser->profile->update(['hp' => 0]);
        
        $this->info("Winner HP: {$winner->profile->hp}, Loser HP: {$loser->profile->hp}");
        
        // Проверяем актуальные ресурсы
        $winnerResources = $winner->profile->getActualResources();
        $loserResources = $loser->profile->getActualResources();
        
        $this->info("Winner actual HP: {$winnerResources['current_hp']}, Loser actual HP: {$loserResources['current_hp']}");
        
        if ($winnerResources['current_hp'] <= 0 && $loserResources['current_hp'] <= 0) {
            $this->info('✅ Тест 3 пройден: Оба игрока мертвы, оба должны быть перенаправлены на поражение');
        } else {
            $this->error('❌ Тест 3 провален: Оба игрока должны быть мертвы');
        }
        
        // Очистка
        $this->info('🧹 Очистка тестовых данных...');
        $this->cleanupTestUser($winner);
        $this->cleanupTestUser($loser);
        
        $this->info('✅ Тестирование завершено');
        
        // Выводим резюме исправления
        $this->info('');
        $this->info('📋 Резюме исправления:');
        $this->info('1. Добавлена проверка HP победителя перед перенаправлением');
        $this->info('2. Если у победителя HP <= 0, он перенаправляется на страницу поражения');
        $this->info('3. Исправлено в методах: attackPlayer, attackAnyPlayer, retaliate');
        $this->info('4. Добавлено логирование для отладки');
        $this->info('5. Установка флага поражения и информации об убийце');
    }
    
    private function createTestUser($name, $displayName)
    {
        $user = User::firstOrCreate([
            'name' => $name,
            'email' => $name . '@test.local',
        ], [
            'password' => bcrypt('password'),
            'role' => 'player',
        ]);
        
        // Создаем профиль если не существует
        if (!$user->profile) {
            UserProfile::create([
                'user_id' => $user->id,
                'hp' => 100,
                'max_hp' => 100,
                'mp' => 50,
                'max_mp' => 50,
                'strength' => 10,
                'agility' => 10,
                'intelligence' => 10,
                'race' => 'human',
                'class' => 'warrior',
                'level' => 1,
                'experience' => 0,
                'bronze' => 100,
                'silver' => 10,
                'gold' => 1,
            ]);
        }
        
        // Создаем статистику если не существует
        if (!$user->statistics) {
            UserStatistic::create([
                'user_id' => $user->id,
                'current_location' => 'Эльфийская Гавань',
            ]);
        }
        
        $this->info("✅ Создан тестовый пользователь: {$displayName} (ID: {$user->id})");
        
        return $user->fresh();
    }
    
    private function cleanupTestUser($user)
    {
        if ($user->profile) {
            $user->profile->delete();
        }
        if ($user->statistics) {
            $user->statistics->delete();
        }
        $user->delete();
    }
}
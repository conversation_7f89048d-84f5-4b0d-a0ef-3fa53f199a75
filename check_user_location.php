<?php

/**
 * Проверка локации конкретного игрока и ботов, которые его атакуют
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\UserStatistic;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔍 ПРОВЕРКА ЛОКАЦИИ ИГРОКА И АТАКУЮЩИХ БОТОВ\n";
echo "==========================================\n\n";

// Если передан аргумент с именем игрока
$userName = $argv[1] ?? null;

if (!$userName) {
    echo "Использование: php check_user_location.php [имя_игрока]\n";
    echo "Или найдем всех онлайн игроков:\n\n";
    
    $onlineUsers = User::where('is_online', true)
        ->with(['profile', 'statistics'])
        ->get();
    
    foreach ($onlineUsers as $user) {
        echo "👤 {$user->name}\n";
    }
    
    echo "\nВыберите имя игрока для детальной проверки\n";
    exit;
}

// Находим игрока
$user = User::where('name', $userName)->with(['profile', 'statistics'])->first();

if (!$user) {
    echo "❌ Игрок '{$userName}' не найден\n";
    exit;
}

echo "👤 ИГРОК: {$user->name}\n";
echo "===================\n";

$userLocation = $user->statistics->current_location ?? 'unknown';
$userRace = $user->profile->race ?? 'unknown';
$userHp = $user->profile->current_hp ?? 0;
$isOnline = $user->is_online ? 'ДА' : 'НЕТ';

echo "Локация: {$userLocation}\n";
echo "Раса: {$userRace}\n";
echo "HP: {$userHp}\n";
echo "Онлайн: {$isOnline}\n";
echo "Последняя активность: " . ($user->last_activity_timestamp ? date('H:i:s d.m.Y', $user->last_activity_timestamp) : 'неизвестно') . "\n\n";

// Проверяем, является ли локация подлокацией рудника
$mineLocation = MineLocation::where('name', $userLocation)->first();

if ($mineLocation) {
    echo "🏭 ПОДЛОКАЦИЯ РУДНИКА: {$mineLocation->name}\n";
    echo "============================\n";
    echo "ID: {$mineLocation->id}\n";
    echo "Базовая локация: " . ($mineLocation->baseLocation->name ?? 'неизвестно') . "\n";
    echo "Активна: " . ($mineLocation->is_active ? 'ДА' : 'НЕТ') . "\n\n";
} else {
    echo "🏛️ ОБЫЧНАЯ ЛОКАЦИЯ: {$userLocation}\n";
    echo "===================\n\n";
}

// Находим всех ботов в этой локации
echo "🤖 БОТЫ В ЛОКАЦИИ '{$userLocation}'\n";
echo "==================================\n";

$botsInLocation = Bot::where('location', $userLocation)
    ->where('is_active', true)
    ->get();

if ($botsInLocation->count() === 0) {
    echo "✅ Ботов в локации нет\n";
} else {
    foreach ($botsInLocation as $bot) {
        $enemyRace = $bot->race === 'solarius' ? 'lunarius' : 'solarius';
        $shouldAttack = ($userRace === $enemyRace) ? '⚔️ ДА' : '🤝 НЕТ';
        $cooldown = $bot->next_action_time ? $bot->next_action_time->format('H:i:s') : 'нет';
        
        echo "🤖 {$bot->name} ({$bot->race}, HP: {$bot->hp}/{$bot->max_hp})\n";
        echo "   Должен атаковать {$userRace}: {$shouldAttack}\n";
        echo "   Кулдаун до: {$cooldown}\n";
        echo "   Mine Location ID: " . ($bot->mine_location_id ?? 'null') . "\n";
        echo "   Создан админом: " . ($bot->created_by_admin ? 'ДА' : 'НЕТ') . "\n";
        echo "\n";
    }
}

// Проверяем ботов в других локациях, которые могут атаковать неправильно
echo "⚠️  ПРОВЕРКА БОТОВ В ДРУГИХ ЛОКАЦИЯХ\n";
echo "===================================\n";

$problemBots = Bot::where('location', '!=', $userLocation)
    ->where('is_active', true)
    ->where('created_by_admin', true)
    ->get();

$foundProblems = false;

foreach ($problemBots as $bot) {
    $enemyRace = $bot->race === 'solarius' ? 'lunarius' : 'solarius';
    
    if ($userRace === $enemyRace) {
        // Этот бот должен атаковать игрока, но находится в другой локации
        // Проверяем, не атакует ли он случайно
        
        echo "🔍 {$bot->name} ({$bot->race}) в локации '{$bot->location}'\n";
        echo "   Может атаковать {$userRace}, но находится в другой локации\n";
        
        // Проверяем, правильно ли настроена его локация
        if (is_numeric($bot->location)) {
            echo "   ❌ ПРОБЛЕМА: location = ID ({$bot->location}) вместо названия\n";
            $foundProblems = true;
        }
        
        echo "\n";
    }
}

if (!$foundProblems) {
    echo "✅ Проблемных ботов не найдено\n";
}

// Проверяем логи атак (если есть)
echo "\n📊 СТАТИСТИКА АТАК\n";
echo "=================\n";

// Проверяем последние логи боевых действий
$recentCombat = DB::table('combat_activities')
    ->where('defender_id', $user->id)
    ->where('created_at', '>=', now()->subMinutes(10))
    ->orderBy('created_at', 'desc')
    ->limit(10)
    ->get();

if ($recentCombat->count() > 0) {
    echo "Последние атаки на {$user->name}:\n";
    foreach ($recentCombat as $combat) {
        $attacker = User::find($combat->attacker_id);
        $attackerName = $attacker ? $attacker->name : "ID:{$combat->attacker_id}";
        $time = \Carbon\Carbon::parse($combat->created_at)->format('H:i:s');
        
        echo "   ⚔️ {$time}: {$attackerName} → {$combat->damage} урона\n";
    }
} else {
    echo "Атак за последние 10 минут не найдено\n";
}

echo "\n🎯 РЕКОМЕНДАЦИИ\n";
echo "==============\n";

if ($foundProblems) {
    echo "1. Запустите экстренное исправление: php emergency_bot_fix.php\n";
    echo "2. Или выполните: php artisan bots:fix-mine-locations\n";
    echo "3. Сбросьте кулдауны: php artisan cache:clear\n";
} else {
    echo "✅ Серьезных проблем не обнаружено\n";
    echo "Возможные причины атак:\n";
    echo "1. Боты атакуют по расписанию (каждые 18-40 секунд)\n";
    echo "2. Вы находитесь в PvP зоне\n";
    echo "3. Кэш не обновился после изменения локации\n";
}

echo "\n🏁 ПРОВЕРКА ЗАВЕРШЕНА\n";
echo "===================\n";
echo "Время: " . now()->format('H:i:s d.m.Y') . "\n";
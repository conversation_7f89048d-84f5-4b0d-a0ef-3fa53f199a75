<?php

require __DIR__ . '/vendor/autoload.php';

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Services\MineDetectionService;
use App\Jobs\MineAutoAttackJob;

echo "🎯 ПОЛНЫЙ ТЕСТ СИСТЕМЫ РУДНИКОВ\n";
echo "================================\n\n";

// Проверяем пользователя admin
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}
echo "✅ Пользователь найден: {$user->name} (ID: {$user->id})\n\n";

// 1. Проверяем таблицы
echo "📋 ПРОВЕРКА ТАБЛИЦ:\n";
$hasMineMarks = \Illuminate\Support\Facades\Schema::hasTable('mine_marks');
$hasActiveEffects = \Illuminate\Support\Facades\Schema::hasTable('active_effects');
echo "   mine_marks: " . ($hasMineMarks ? '✅ Есть' : '❌ Нет') . "\n";
echo "   active_effects: " . ($hasActiveEffects ? '✅ Есть' : '❌ Нет') . "\n";

// 2. Проверяем кастомные рудники
echo "\n⛏️ ПРОВЕРКА КАСТОМНЫХ РУДНИКОВ:\n";
$mineLocations = MineLocation::whereNull('parent_id')->where('is_active', true)->get();
echo "   Найдено активных рудников: {$mineLocations->count()}\n";

foreach ($mineLocations as $mine) {
    echo "   - {$mine->name} (ID: {$mine->id})\n";
    
    // Проверяем мобов в этом руднике
    $mobs = Mob::where('location_id', $mine->location_id)
        ->where('mob_type', 'mine')
        ->where('hp', '>', 0)
        ->get();
    
    echo "     Мобов: {$mobs->count()}\n";
    foreach ($mobs as $mob) {
        echo "       • {$mob->name} (HP: {$mob->hp}, Location: {$mob->location_id})\n";
    }
}

if ($mineLocations->count() == 0) {
    echo "❌ Нет активных кастомных рудников!\n";
    exit(1);
}

// 3. Тестируем создание дебафа
echo "\n🎯 ТЕСТ СОЗДАНИЯ ДЕБАФА:\n";
$testMine = $mineLocations->first();
$mineDetectionService = app(MineDetectionService::class);

try {
    // Удаляем старые метки/эффекты
    MineMark::where('player_id', $user->id)->delete();
    ActiveEffect::where('target_type', 'App\\Models\\User')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection')
        ->delete();

    $mark = $mineDetectionService->applyDetectionDebuff($user, $testMine);
    if ($mark) {
        echo "✅ Метка создана в mine_marks (ID: {$mark->id})\n";
    } else {
        echo "❌ Ошибка создания метки!\n";
    }

    // Проверяем активные эффекты
    $effects = ActiveEffect::where('target_type', 'App\\Models\\User')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection')
        ->get();

    if ($effects->count() > 0) {
        echo "✅ Эффект создан в active_effects для отображения\n";
        foreach ($effects as $effect) {
            echo "   - {$effect->effect_name} (до {$effect->ends_at})\n";
        }
    } else {
        echo "❌ Эффект НЕ создан в active_effects!\n";
    }

} catch (\Exception $e) {
    echo "❌ Ошибка при создании дебафа: " . $e->getMessage() . "\n";
}

// 4. Проверяем планировщик
echo "\n⏰ ПРОВЕРКА ПЛАНИРОВЩИКА:\n";
$schedulerPid = null;
if (file_exists('storage/scheduler.pid')) {
    $schedulerPid = trim(file_get_contents('storage/scheduler.pid'));
}

if ($schedulerPid) {
    $isRunning = shell_exec("ps -p $schedulerPid") !== null;
    echo "   PID планировщика: {$schedulerPid}\n";
    echo "   Статус: " . ($isRunning ? '✅ Работает' : '❌ Не работает') . "\n";
} else {
    echo "❌ PID планировщика не найден в storage/scheduler.pid\n";
}

// Проверяем логи планировщика
$schedulerLog = 'storage/logs/scheduler.log';
if (file_exists($schedulerLog)) {
    $logContent = file_get_contents($schedulerLog);
    $hasMineAttack = strpos($logContent, 'mine:auto-attack') !== false;
    echo "   Лог планировщика: " . ($hasMineAttack ? '✅ Есть mine:auto-attack' : '❌ Нет mine:auto-attack') . "\n";
} else {
    echo "❌ Лог планировщика не найден: {$schedulerLog}\n";
}

// 5. Ручная проверка задачи
echo "\n🤖 РУЧНОЙ ЗАПУСК MineAutoAttackJob:\n";
try {
    // Создаем тестовую метку если её нет
    if (!$mark) {
        $mark = MineMark::create([
            'player_id' => $user->id,
            'mine_location_id' => $testMine->id,
            'location_id' => $testMine->location_id ?? 1,
            'location_name' => $testMine->name,
            'expires_at' => now()->addMinutes(5),
            'is_active' => true,
            'attack_count' => 0
        ]);
        echo "✅ Создана тестовая метка\n";
    }

    // Запускаем задачу синхронно для теста
    $job = new MineAutoAttackJob();
    $job->handle(
        app(App\Services\MineDetectionService::class),
        app(App\Services\BattleLogService::class),
        app(App\Services\PlayerHealthService::class),
        app(App\Services\CombatFormulaService::class),
        app(App\Services\LogFormattingService::class)
    );
    echo "✅ MineAutoAttackJob выполнен без ошибок\n";

} catch (\Exception $e) {
    echo "❌ Ошибка при выполнении MineAutoAttackJob: " . $e->getMessage() . "\n";
}

// 6. Результаты
echo "\n📊 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:\n";

$activeMarks = MineMark::where('is_active', true)
    ->where('expires_at', '>', now())
    ->count();

$activeEffects = ActiveEffect::where('effect_type', 'mine_detection')
    ->where('ends_at', '>', now())
    ->count();

echo "   Активных меток в mine_marks: {$activeMarks}\n";
echo "   Активных эффектов для отображения: {$activeEffects}\n";

// Проверяем логи атак
$attackLog = 'storage/logs/mine-auto-attacks.log';
if (file_exists($attackLog)) {
    $attackContent = file_get_contents($attackLog);
    $attackCount = substr_count($attackContent, 'Успешная атака');
    echo "   Записей атак в логе: {$attackCount}\n";
} else {
    echo "   Лог атак не найден: {$attackLog}\n";
}

echo "\n🎯 РЕКОМЕНДАЦИИ:\n";
if (!$hasMineMarks) {
    echo "   1. Запустите миграцию: run_mine_migrations.bat\n";
}
if ($schedulerPid && !$isRunning) {
    echo "   2. Перезапустите планировщик: ./start_scheduler_simple.sh\n";
}
if ($activeMarks > 0 && $activeEffects == 0) {
    echo "   3. Проверьте создание эффектов в active_effects\n";
}
if ($mineLocations->first()->location_id) {
    $mobsInFirstMine = Mob::where('location_id', $mineLocations->first()->location_id)
        ->where('mob_type', 'mine')
        ->count();
    if ($mobsInFirstMine == 0) {
        echo "   4. Добавьте мобов с mob_type='mine' в локацию рудника\n";
    }
}

echo "\n✅ ТЕСТ ЗАВЕРШЕН!\n";

// Очистка тестовых данных
if (isset($mark) && $mark->exists) {
    $mark->delete();
}
ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();
echo "🧹 Тестовые данные очищены\n";
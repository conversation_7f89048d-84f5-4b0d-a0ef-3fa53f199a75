@props([
    'itemName' => '',
    'iconPath' => null,
    'quantity' => 1,
    'source' => '',
    'type' => 'success',
    'inventoryFull' => false,
    'lostQuantity' => 0
])

@php
    // Определяем классы для разных типов сообщений
    $messageClasses = [
        'success' => [
            'bg' => 'bg-[#36513f]',
            'text' => 'text-[#c8ffdb]',
            'border' => 'border-[#4a7759]',
            'icon_bg' => 'bg-[#2e8b57]'
        ],
        'warning' => [
            'bg' => 'bg-[#5e553a]',
            'text' => 'text-[#ffe7bd]',
            'border' => 'border-[#7d7248]',
            'icon_bg' => 'bg-[#b8860b]'
        ],
        'error' => [
            'bg' => 'bg-[#613f36]',
            'text' => 'text-[#ffeac1]',
            'border' => 'border-[#88634a]',
            'icon_bg' => 'bg-[#8b0000]'
        ],
    ];
    
    // Устанавливаем тип сообщения в зависимости от флага инвентаря
    if ($inventoryFull) {
        $type = 'warning';
    }
    
    // Получаем классы в зависимости от типа
    $classes = $messageClasses[$type] ?? $messageClasses['success'];
@endphp

<div class="inventory-notification {{ $classes['bg'] }} {{ $classes['text'] }} p-2 rounded-md border {{ $classes['border'] }} shadow-md mb-2 animate-fade-in overflow-hidden">
    <div class="flex items-start">
        {{-- Иконка предмета --}}
        <div class="flex-shrink-0 mr-2">
            <div class="{{ $classes['icon_bg'] }} rounded-full p-1 border border-[#a6925e]">
                @if ($iconPath)
                    <img src="{{ asset($iconPath) }}" alt="{{ $itemName }}" class="w-8 h-8 object-contain" style="image-rendering: crisp-edges;">
                @else
                    <div class="w-8 h-8 flex items-center justify-center text-white">
                        @if ($type === 'success')
                            ✅
                        @elseif ($type === 'warning')
                            ⚠️
                        @elseif ($type === 'error')
                            ❌
                        @endif
                    </div>
                @endif
            </div>
        </div>
        
        {{-- Информация о предмете --}}
        <div class="flex-1">
            <div class="font-medium">
                {{ $itemName }} {{ $quantity > 1 ? "x{$quantity}" : "" }}
            </div>
            
            <div class="text-xs opacity-80 mt-0.5">
                @if ($source)
                    {{ $source }}
                @endif
                
                @if ($inventoryFull)
                    <div class="mt-1 text-[#ffb74d] font-semibold">
                        Инвентарь полон.
                    </div>
                @endif
            </div>
        </div>
        
        {{-- Индикатор типа уведомления (справа) --}}
        <div class="flex-shrink-0 ml-2">
            @if ($inventoryFull)
                <div class="text-[#ffb74d] text-xl">⚠️</div>
            @elseif ($type === 'success')
                <div class="text-[#4caf50] text-xl">✅</div>
            @elseif ($type === 'error')
                <div class="text-[#f44336] text-xl">❌</div>
            @endif
        </div>
    </div>
    
    {{-- Декоративный элемент снизу --}}
    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-30"></div>
</div>

<style>
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.3s ease-out forwards;
    }
    
    .inventory-notification {
        position: relative;
    }
</style> 
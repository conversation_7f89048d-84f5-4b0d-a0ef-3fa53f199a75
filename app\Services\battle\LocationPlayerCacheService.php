<?php

namespace App\Services\battle;

use App\Models\User;
use App\Models\Bot;
use App\Services\battle\LuaScripts\PlayerBattleOperations;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Сервис для кеширования игроков в локациях с оптимизацией производительности
 * Использует Redis для быстрого доступа к данным игроков в боевых локациях
 */
class LocationPlayerCacheService
{
    /**
     * Время жизни кеша в секундах
     */
    private const CACHE_TTL = 30;

    /**
     * Время жизни кеша для ботов в секундах (дольше, так как они меняются реже)
     */
    private const BOT_CACHE_TTL = 60;

    /**
     * Префикс для ключей кеша игроков
     */
    private const PLAYER_CACHE_PREFIX = 'location_players:';

    /**
     * Префикс для ключей кеша ботов
     */
    private const BOT_CACHE_PREFIX = 'location_bots:';

    /**
     * Получает кешированный список игроков в локации с фильтрацией по расе
     *
     * @param string $locationName Название локации
     * @param string $race Раса для фильтрации ('solarius' или 'lunarius')
     * @param int|null $excludeUserId ID пользователя для исключения
     * @return \Illuminate\Support\Collection
     */
    public function getCachedPlayersInLocation(string $locationName, string $race, ?int $excludeUserId = null)
    {
        $cacheKey = $this->getPlayerCacheKey($locationName, $race);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($locationName, $race, $excludeUserId) {
            return $this->fetchPlayersFromDatabase($locationName, $race, $excludeUserId);
        });
    }

    /**
     * Получает кешированный список ботов в локации с фильтрацией по расе
     *
     * @param string $locationName Название локации
     * @param string $race Раса для фильтрации ('solarius' или 'lunarius')
     * @param int|null $excludeBotId ID бота для исключения
     * @return \Illuminate\Support\Collection
     */
    public function getCachedBotsInLocation(string $locationName, string $race, ?int $excludeBotId = null)
    {
        $cacheKey = $this->getBotCacheKey($locationName, $race);

        // ИСПРАВЛЕНО: Включаем кеш обратно, проблема была в логике поиска, а не в кэшировании
        return Cache::remember($cacheKey, self::BOT_CACHE_TTL, function () use ($locationName, $race, $excludeBotId) {
            return $this->fetchBotsFromDatabase($locationName, $race, $excludeBotId);
        });
    }

    /**
     * Получает всех врагов (игроков + ботов) в локации для указанной расы
     *
     * @param string $locationName Название локации
     * @param string $userRace Раса текущего игрока
     * @param int|null $excludeUserId ID пользователя для исключения
     * @param int|null $excludeBotId ID бота для исключения
     * @return \Illuminate\Support\Collection
     */
    public function getCachedEnemiesInLocation(string $locationName, string $userRace, ?int $excludeUserId = null, ?int $excludeBotId = null)
    {
        // Определяем вражескую расу
        $enemyRace = ($userRace === 'solarius') ? 'lunarius' : 'solarius';

        // Получаем игроков и ботов параллельно
        $enemyPlayers = $this->getCachedPlayersInLocation($locationName, $enemyRace, $excludeUserId);
        $enemyBots = $this->getCachedBotsInLocation($locationName, $enemyRace, $excludeBotId);

        // Объединяем коллекции
        return $enemyPlayers->merge($enemyBots);
    }

    /**
     * Очищает кеш игроков для указанной локации
     *
     * @param string $locationName Название локации
     * @return void
     */
    public function clearLocationCache(string $locationName): void
    {
        // Очищаем кеш для обеих рас
        Cache::forget($this->getPlayerCacheKey($locationName, 'solarius'));
        Cache::forget($this->getPlayerCacheKey($locationName, 'lunarius'));
        Cache::forget($this->getBotCacheKey($locationName, 'solarius'));
        Cache::forget($this->getBotCacheKey($locationName, 'lunarius'));

        Log::info('Очищен кеш локации', ['location' => $locationName]);
    }

    /**
     * Обновляет кеш игроков в локации при изменении данных
     *
     * @param string $locationName Название локации
     * @param string $race Раса игрока
     * @return void
     */
    public function refreshLocationCache(string $locationName, string $race): void
    {
        $cacheKey = $this->getPlayerCacheKey($locationName, $race);
        Cache::forget($cacheKey);

        // Предварительно загружаем новые данные
        $this->getCachedPlayersInLocation($locationName, $race);
    }

    /**
     * Получает ключ кеша для игроков
     *
     * @param string $locationName Название локации
     * @param string $race Раса
     * @return string
     */
    private function getPlayerCacheKey(string $locationName, string $race): string
    {
        return self::PLAYER_CACHE_PREFIX . md5($locationName) . ':' . $race;
    }

    /**
     * Получает ключ кеша для ботов
     *
     * @param string $locationName Название локации
     * @param string $race Раса
     * @return string
     */
    private function getBotCacheKey(string $locationName, string $race): string
    {
        return self::BOT_CACHE_PREFIX . md5($locationName) . ':' . $race;
    }

    /**
     * Получает игроков из базы данных с оптимизированным запросом
     *
     * @param string $locationName Название локации
     * @param string $race Раса для фильтрации
     * @param int|null $excludeUserId ID пользователя для исключения
     * @return \Illuminate\Support\Collection
     */
    private function fetchPlayersFromDatabase(string $locationName, string $race, ?int $excludeUserId = null)
    {
        $query = User::select(['users.id', 'users.name', 'users.last_activity_timestamp'])
            ->join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
            ->join('user_statistics', 'users.id', '=', 'user_statistics.user_id')
            ->where('user_profiles.race', $race)
            ->where('user_statistics.current_location', $locationName)
            ->where('users.last_activity_timestamp', '>=', now()->subMinutes(10)->timestamp)
            ->whereRaw('COALESCE(user_profiles.current_hp, user_profiles.hp) > 0');

        if ($excludeUserId) {
            $query->where('users.id', '!=', $excludeUserId);
        }

        return $query->get();
    }

    /**
     * Получает ботов из базы данных с оптимизированным запросом
     * ИСПРАВЛЕНИЕ: Учитываем ботов как по полю location, так и по mine_location_id
     *
     * @param string $locationName Название локации
     * @param string $race Раса для фильтрации
     * @param int|null $excludeBotId ID бота для исключения
     * @return \Illuminate\Support\Collection
     */
    private function fetchBotsFromDatabase(string $locationName, string $race, ?int $excludeBotId = null)
    {
        // Ищем MineLocation по имени (может быть подлокация или базовая локация)
        // ИСПРАВЛЕНИЕ: Убираем проверку is_active для синхронизации с FactionCountService
        $mineLocation = \App\Models\MineLocation::where('name', $locationName)
            ->first();

        $allBots = collect();

        if ($mineLocation) {
            // ИСПРАВЛЕНО: Строгая изоляция - ищем ботов только в конкретной локации
            if ($mineLocation->isSubLocation()) {
                // Если это подлокация рудника - ищем ботов только с mine_location_id
                $query = Bot::select(['id', 'name', 'hp', 'max_hp', 'race', 'location', 'mine_location_id'])
                    ->where('race', $race)
                    ->where('mine_location_id', $mineLocation->id)
                    ->where('is_active', true)
                    ->where('hp', '>', 0)
                    ->whereNull('death_time') // Добавляем фильтр как в FactionCountService
                    ->whereNotNull('location') // Добавляем фильтр как в FactionCountService
                    ->where('location', '!=', ''); // Добавляем фильтр как в FactionCountService

                if ($excludeBotId) {
                    $query->where('id', '!=', $excludeBotId);
                }

                $allBots = $query->get();
                
                \Log::debug("LocationPlayerCacheService: поиск ботов в подлокации рудника (строгая изоляция + фильтры FactionCountService)", [
                    'location_name' => $locationName,
                    'mine_location_id' => $mineLocation->id,
                    'race' => $race,
                    'bots_found' => $allBots->count(),
                    'applied_filters' => [
                        'mine_location_id' => $mineLocation->id,
                        'race' => $race,
                        'is_active' => true,
                        'hp > 0',
                        'death_time IS NULL',
                        'location IS NOT NULL',
                        'location != empty'
                    ]
                ]);
            } else {
                // ИСПРАВЛЕНИЕ: Если это базовая локация рудника - ищем ботов С mine_location_id этой локации
                // Логика как в FactionCountService::getStrictBotsCountInLocation
                $query = Bot::select(['id', 'name', 'hp', 'max_hp', 'race', 'location', 'mine_location_id'])
                    ->where('race', $race)
                    ->where('mine_location_id', $mineLocation->id) // ИСПРАВЛЕНО: Ищем ботов С mine_location_id
                    ->where('is_active', true)
                    ->where('hp', '>', 0)
                    ->whereNull('death_time') // Добавляем фильтр как в FactionCountService
                    ->whereNotNull('location') // Добавляем фильтр как в FactionCountService
                    ->where('location', '!=', ''); // Добавляем фильтр как в FactionCountService

                if ($excludeBotId) {
                    $query->where('id', '!=', $excludeBotId);
                }

                $allBots = $query->get();
                
                \Log::debug("LocationPlayerCacheService: поиск ботов в локации рудника (ИСПРАВЛЕНО: синхронизировано с FactionCountService)", [
                    'location_name' => $locationName,
                    'mine_location_id' => $mineLocation->id,
                    'race' => $race,
                    'bots_found' => $allBots->count(),
                    'fix_applied' => 'Используем mine_location_id вместо whereNull(mine_location_id)'
                ]);
            }
        } else {
            // Обычная локация (не рудник) - ищем по полю location
            $query = Bot::select(['id', 'name', 'hp', 'max_hp', 'race', 'location', 'mine_location_id'])
                ->where('race', $race)
                ->where('location', $locationName)
                ->whereNull('mine_location_id') // Только боты не в рудниках
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->whereNull('death_time') // Добавляем фильтр как в FactionCountService
                ->whereNotNull('location') // Добавляем фильтр как в FactionCountService
                ->where('location', '!=', ''); // Добавляем фильтр как в FactionCountService

            if ($excludeBotId) {
                $query->where('id', '!=', $excludeBotId);
            }

            $allBots = $query->get();
            
            \Log::debug("LocationPlayerCacheService: поиск ботов в обычной локации", [
                'location_name' => $locationName,
                'race' => $race,
                'bots_found' => $allBots->count()
            ]);
        }

        return $allBots;
    }

    /**
     * Создает Lua-скрипт для атомарного обновления данных игрока в Redis
     *
     * @return string
     */
    public function getPlayerUpdateLuaScript(): string
    {
        return '
            local player_key = KEYS[1]
            local location = ARGV[1]
            local race = ARGV[2]
            local hp = tonumber(ARGV[3])
            
            -- Обновляем данные игрока
            redis.call("HSET", player_key, "location", location, "race", race, "hp", hp, "updated_at", redis.call("TIME")[1])
            
            -- Очищаем кеш локации для обеих рас
            local location_hash = redis.call("SHA1HEX", location)
            redis.call("DEL", "location_players:" .. location_hash .. ":solarius")
            redis.call("DEL", "location_players:" .. location_hash .. ":lunarius")
            
            return "OK"
        ';
    }

    /**
     * Выполняет атомарное обновление данных игрока через Lua-скрипт
     *
     * @param int $userId ID игрока
     * @param string $location Локация
     * @param string $race Раса
     * @param int $hp Текущее HP
     * @return void
     */
    public function atomicUpdatePlayer(int $userId, string $location, string $race, int $hp): void
    {
        $script = $this->getPlayerUpdateLuaScript();
        $playerKey = "player:{$userId}";

        Redis::eval($script, 1, $playerKey, $location, $race, $hp);
    }

    /**
     * Получает игроков в локации с использованием оптимизированного Lua-скрипта
     *
     * @param string $locationName Название локации
     * @param string $race Раса для фильтрации
     * @param int|null $excludeUserId ID пользователя для исключения
     * @return \Illuminate\Support\Collection
     */
    public function getCachedPlayersWithLua(string $locationName, string $race, ?int $excludeUserId = null)
    {
        $cacheKey = $this->getPlayerCacheKey($locationName, $race);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($locationName, $race, $excludeUserId) {
            // Используем Lua-скрипт для быстрого поиска
            $players = PlayerBattleOperations::getPlayersInLocation($locationName, $race, $excludeUserId);

            // Преобразуем результат в коллекцию объектов User
            $userIds = collect($players)->pluck('id')->toArray();

            if (empty($userIds)) {
                return collect();
            }

            // Получаем полные данные игроков из БД одним запросом
            return User::whereIn('id', $userIds)
                ->select(['id', 'name', 'last_activity_timestamp'])
                ->get();
        });
    }

    /**
     * Синхронизирует данные игрока из БД в Redis для быстрого доступа
     *
     * @param User $user Игрок
     * @return bool
     */
    public function syncPlayerToRedis(User $user): bool
    {
        $playerData = [
            'location' => $user->statistics->current_location ?? '',
            'race' => $user->profile->race ?? '',
            'hp' => $user->profile->current_hp ?? $user->profile->hp ?? 0,
            'max_hp' => $user->profile->max_hp ?? 100,
            'last_activity' => $user->last_activity_timestamp ?? time()
        ];

        return PlayerBattleOperations::syncPlayerToRedis($user->id, $playerData);
    }

    /**
     * ЗАЩИТА ОТ РЕГРЕССИЙ: Валидирует синхронность с FactionCountService
     * 
     * @param string $locationName Название локации
     * @param string $race Раса для проверки
     * @return array Результат валидации
     */
    public function validateSyncWithFactionService(string $locationName, string $race): array
    {
        // Получаем ботов через текущий сервис
        $this->clearLocationCache($locationName);
        $cacheBots = $this->getCachedBotsInLocation($locationName, $race);
        
        // Получаем ботов через FactionCountService логику
        $mineLocation = \App\Models\MineLocation::where('name', $locationName)->first();
        
        if ($mineLocation) {
            $factionBots = Bot::where('race', $race)
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->whereNull('death_time')
                ->whereNotNull('location')
                ->where('location', '!=', '')
                ->where('mine_location_id', $mineLocation->id)
                ->get();
        } else {
            $factionBots = Bot::where('race', $race)
                ->where('location', $locationName)
                ->whereNull('mine_location_id')
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->whereNull('death_time')
                ->whereNotNull('location')
                ->where('location', '!=', '')
                ->get();
        }
        
        $isSync = $cacheBots->count() === $factionBots->count();
        
        $result = [
            'is_synchronized' => $isSync,
            'location' => $locationName,
            'race' => $race,
            'cache_service_count' => $cacheBots->count(),
            'faction_service_count' => $factionBots->count(),
            'difference' => $factionBots->count() - $cacheBots->count(),
            'cache_bot_ids' => $cacheBots->pluck('id')->sort()->values()->toArray(),
            'faction_bot_ids' => $factionBots->pluck('id')->sort()->values()->toArray()
        ];
        
        if (!$isSync) {
            \Log::warning('LocationPlayerCacheService: Обнаружена рассинхронизация с FactionCountService', $result);
        }
        
        return $result;
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Mob;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\Resource;
use App\Models\ResourceDrop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MobController extends Controller
{
    /**
     * Отображает список всех мобов в админке
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Получаем текущего авторизованного пользователя
        $user = Auth::user();

        // Получаем ID локации из запроса для фильтрации
        $locationId = $request->input('location_id');

        // Запрос для получения мобов
        $mobsQuery = Mob::orderBy('location_id')->orderBy('name');

        // Применяем фильтр по локации, если он задан
        $mobsQuery->when($locationId, function ($query, $locationId) {
            return $query->where('location_id', $locationId);
        });

        // Получаем отфильтрованных мобов с пагинацией
        $mobs = $mobsQuery->paginate(15)->withQueryString(); // Добавляем withQueryString для сохранения фильтра при пагинации

        // Получаем все локации для выпадающего списка фильтра
        $locations = Location::orderBy('name')->get();

        // Добавляем подземелья как виртуальные локации для фильтра
        $dungeons = \App\Models\Dungeon::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Создаем коллекцию с обычными локациями и подземельями
        $allLocations = collect();
        
        // Добавляем обычные локации
        foreach ($locations as $location) {
            $allLocations->push($location);
        }
        
        // Добавляем подземелья как виртуальные локации
        foreach ($dungeons as $dungeon) {
            $virtualLocation = new \stdClass();
            $virtualLocation->id = 'dungeon_' . $dungeon->id;
            $virtualLocation->name = 'Подземелье: ' . $dungeon->name;
            $virtualLocation->location_type = 'dungeon';
            $allLocations->push($virtualLocation);
        }

        // Передаем данные в шаблон
        return $this->prepareUserProfileView($user, 'admin.mobs.index')
            ->with('mobs', $mobs)
            ->with('locations', $allLocations)
            ->with('selectedLocationId', $locationId); // Передаем выбранный ID локации
    }

    /**
     * Показывает форму создания нового моба
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $user = Auth::user();
        // Получаем только активные локации, отсортированные по типу и названию
        $locations = Location::where('is_active', true)
            ->orderBy('location_type')
            ->orderBy('name')
            ->get();

        // Добавляем подземелья как виртуальные локации
        $dungeons = \App\Models\Dungeon::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Создаем коллекцию с обычными локациями и подземельями
        $allLocations = collect();
        
        // Добавляем обычные локации
        foreach ($locations as $location) {
            $allLocations->push($location);
        }
        
        // Добавляем подземелья как виртуальные локации
        foreach ($dungeons as $dungeon) {
            $virtualLocation = new \stdClass();
            $virtualLocation->id = 'dungeon_' . $dungeon->id;
            $virtualLocation->name = 'Подземелье: ' . $dungeon->name;
            $virtualLocation->location_type = 'dungeon';
            $allLocations->push($virtualLocation);
        }

        // Добавляем подлокации рудников
        $mineLocations = MineLocation::where('is_active', true)
            ->orderBy('name')
            ->get();

        foreach ($mineLocations as $mineLocation) {
            $virtualMineLocation = new \stdClass();
            $virtualMineLocation->id = 'mine_' . $mineLocation->id;
            $virtualMineLocation->name = $mineLocation->name . ' (рудник)';
            $virtualMineLocation->location_type = 'mine';
            $allLocations->push($virtualMineLocation);
        }

        return $this->prepareUserProfileView($user, 'admin.mobs.create')
            ->with('locations', $allLocations);
    }

    /**
     * Сохраняет нового моба в базу данных
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Валидация данных
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'location_id' => 'required|string',
            'description' => 'nullable|string',
            'max_hp' => 'required|integer|min:1',
            'strength' => 'required|integer|min:0',
            'defense' => 'required|integer|min:0',
            'agility' => 'nullable|integer|min:0',
            'vitality' => 'nullable|integer|min:0',
            'intelligence' => 'nullable|integer|min:0',
            'experience_reward' => 'required|integer|min:0',
            'icon' => 'nullable|string',
            'respawn_time' => 'required|integer|min:1',
        ]);

        // Добавляем уникальный slug для моба
        $validated['slug'] = $this->generateUniqueSlug($validated['name']);

        // Устанавливаем HP равным max_HP при создании
        $validated['hp'] = $validated['max_hp'];

        // Обрабатываем выбор локации (обычная локация, подземелье или рудничная подлокация)
        if (str_starts_with($validated['location_id'], 'mine_')) {
            // Это рудничная подлокация
            $mineLocationId = str_replace('mine_', '', $validated['location_id']);
            $mineLocation = MineLocation::find($mineLocationId);
            
            if ($mineLocation) {
                $validated['location'] = $mineLocation->name;
                $validated['location_id'] = $mineLocation->location_id; // ID основной локации рудника
                $validated['mine_location_id'] = $mineLocation->id; // ID подлокации рудника
                $validated['mob_type'] = 'mine';
            } else {
                return redirect()->back()->withErrors(['location_id' => 'Рудничная подлокация не найдена'])->withInput();
            }
        } elseif (str_starts_with($validated['location_id'], 'dungeon_')) {
            // Это подземелье
            $dungeonId = str_replace('dungeon_', '', $validated['location_id']);
            $dungeon = \App\Models\Dungeon::find($dungeonId);
            
            if ($dungeon) {
                $validated['location'] = 'Подземелье: ' . $dungeon->name;
                
                // Ищем или создаем виртуальную локацию для подземелья
                $dungeonLocation = Location::firstOrCreate([
                    'name' => 'Подземелье: ' . $dungeon->name,
                    'location_type' => 'dungeon'
                ], [
                    'description' => 'Боевая локация подземелья ' . $dungeon->name,
                    'min_level' => $dungeon->min_level,
                    'max_level' => null,
                    'route_name' => 'dungeons.battle',
                    'is_active' => true,
                    'order' => 1000 + $dungeon->id,
                    'gs_requirement' => $dungeon->recommended_gs
                ]);
                
                $validated['location_id'] = $dungeonLocation->id;
                $validated['mob_type'] = 'dungeon';
            } else {
                return redirect()->back()->withErrors(['location_id' => 'Подземелье не найдено'])->withInput();
            }
        } else {
            // Это обычная локация
            $location = Location::find($validated['location_id']);
            if ($location) {
                // Проверяем, является ли локация рудником
                if ($location->location_type === 'mine') {
                    // Для рудников используем название из таблицы mine_locations
                    $mineLocation = MineLocation::where('location_id', $location->id)
                        ->whereNull('parent_id') // Берем только основную локацию рудника, не подлокацию
                        ->first();

                    if ($mineLocation) {
                        $validated['location'] = $mineLocation->name;
                    } else {
                        $validated['location'] = $location->name;
                    }

                    // ИСПРАВЛЕНИЕ: Автоматически устанавливаем mob_type = 'mine' для рудничных мобов
                    $validated['mob_type'] = 'mine';
                } else {
                    $validated['location'] = $location->name;
                    // Для не-рудничных локаций устанавливаем mob_type = 'normal'
                    $validated['mob_type'] = 'normal';
                }
            } else {
                return redirect()->back()->withErrors(['location_id' => 'Локация не найдена'])->withInput();
            }
        }

        // Сохраняем моба
        $mob = Mob::create($validated);

        Log::info('Создан новый моб в админке', [
            'mob_id' => $mob->id,
            'name' => $mob->name,
            'location_id' => $mob->location_id,
            'location' => $mob->location
        ]);

        return redirect()->route('admin.mobs.index')
            ->with('success', 'Моб успешно создан!');
    }

    /**
     * Показывает информацию о мобе
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $user = Auth::user();

        // Временно загружаем ТОЛЬКО currencyDrops для отладки
        $mob = Mob::with('currencyDrops')->findOrFail($id);

        // Логируем, что загрузилось
        Log::info('Mob data with currency drops for ID ' . $id, [
            'mob_exists' => !empty($mob),
            'currency_drops_count' => $mob->currencyDrops ? $mob->currencyDrops->count() : 'null', // Проверяем count
            'currency_drops_data' => $mob->currencyDrops ? $mob->currencyDrops->toArray() : 'null' // Смотрим сами данные
        ]);

        // Загружаем остальные связи отдельно, чтобы страница работала
        $mob->load(['location', 'resourceDrops.resource', 'itemDrops.item']);

        return $this->prepareUserProfileView($user, 'admin.mobs.show')
            ->with('mob', $mob);
    }

    /**
     * Показывает форму редактирования моба
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $user = Auth::user();
        $mob = Mob::with('location')->findOrFail($id);
        // Получаем только активные локации, отсортированные по типу и названию
        $locations = Location::where('is_active', true)
            ->orderBy('location_type')
            ->orderBy('name')
            ->get();

        // Добавляем подземелья как виртуальные локации
        $dungeons = \App\Models\Dungeon::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Создаем коллекцию с обычными локациями и подземельями
        $allLocations = collect();
        
        // Добавляем обычные локации
        foreach ($locations as $location) {
            $allLocations->push($location);
        }
        
        // Добавляем подземелья как виртуальные локации
        foreach ($dungeons as $dungeon) {
            $virtualLocation = new \stdClass();
            $virtualLocation->id = 'dungeon_' . $dungeon->id;
            $virtualLocation->name = 'Подземелье: ' . $dungeon->name;
            $virtualLocation->location_type = 'dungeon';
            $allLocations->push($virtualLocation);
        }

        // Добавляем подлокации рудников
        $mineLocations = MineLocation::where('is_active', true)
            ->orderBy('name')
            ->get();

        foreach ($mineLocations as $mineLocation) {
            $virtualMineLocation = new \stdClass();
            $virtualMineLocation->id = 'mine_' . $mineLocation->id;
            $virtualMineLocation->name = $mineLocation->name . ' (рудник)';
            $virtualMineLocation->location_type = 'mine';
            $allLocations->push($virtualMineLocation);
        }

        return $this->prepareUserProfileView($user, 'admin.mobs.edit')
            ->with('mob', $mob)
            ->with('locations', $allLocations);
    }

    /**
     * Обновляет информацию о мобе
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Валидация данных
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'location_id' => 'required|string',
            'description' => 'nullable|string',
            'max_hp' => 'required|integer|min:1',
            'strength' => 'required|integer|min:0',
            'defense' => 'required|integer|min:0',
            'agility' => 'nullable|integer|min:0',
            'vitality' => 'nullable|integer|min:0',
            'intelligence' => 'nullable|integer|min:0',
            'experience_reward' => 'required|integer|min:0',
            'icon' => 'nullable|string',
            'respawn_time' => 'required|integer|min:1',
        ]);

        // Обрабатываем выбор локации (обычная локация, подземелье или рудничная подлокация)
        if (str_starts_with($validated['location_id'], 'mine_')) {
            // Это рудничная подлокация
            $mineLocationId = str_replace('mine_', '', $validated['location_id']);
            $mineLocation = MineLocation::find($mineLocationId);
            
            if ($mineLocation) {
                $validated['location'] = $mineLocation->name;
                $validated['location_id'] = $mineLocation->location_id; // ID основной локации рудника
                $validated['mine_location_id'] = $mineLocation->id; // ID подлокации рудника
                $validated['mob_type'] = 'mine';
            } else {
                return redirect()->back()->withErrors(['location_id' => 'Рудничная подлокация не найдена'])->withInput();
            }
        } elseif (str_starts_with($validated['location_id'], 'dungeon_')) {
            // Это подземелье
            $dungeonId = str_replace('dungeon_', '', $validated['location_id']);
            $dungeon = \App\Models\Dungeon::find($dungeonId);
            
            if ($dungeon) {
                $validated['location'] = 'Подземелье: ' . $dungeon->name;
                
                // Ищем или создаем виртуальную локацию для подземелья
                $dungeonLocation = Location::firstOrCreate([
                    'name' => 'Подземелье: ' . $dungeon->name,
                    'location_type' => 'dungeon'
                ], [
                    'description' => 'Боевая локация подземелья ' . $dungeon->name,
                    'min_level' => $dungeon->min_level,
                    'max_level' => null,
                    'route_name' => 'dungeons.battle',
                    'is_active' => true,
                    'order' => 1000 + $dungeon->id,
                    'gs_requirement' => $dungeon->recommended_gs
                ]);
                
                $validated['location_id'] = $dungeonLocation->id;
                $validated['mob_type'] = 'dungeon';
            } else {
                return redirect()->back()->withErrors(['location_id' => 'Подземелье не найдено'])->withInput();
            }
        } else {
            // Это обычная локация
            $location = Location::find($validated['location_id']);
            if ($location) {
                // Проверяем, является ли локация рудником
                if ($location->location_type === 'mine') {
                    // Для рудников используем название из таблицы mine_locations
                    $mineLocation = MineLocation::where('location_id', $location->id)
                        ->whereNull('parent_id') // Берем только основную локацию рудника, не подлокацию
                        ->first();

                    if ($mineLocation) {
                        $validated['location'] = $mineLocation->name;
                    } else {
                        $validated['location'] = $location->name;
                    }

                    // ИСПРАВЛЕНИЕ: Автоматически устанавливаем mob_type = 'mine' для рудничных мобов
                    $validated['mob_type'] = 'mine';
                } else {
                    $validated['location'] = $location->name;
                    // Для не-рудничных локаций устанавливаем mob_type = 'normal'
                    $validated['mob_type'] = 'normal';
                }
            } else {
                return redirect()->back()->withErrors(['location_id' => 'Локация не найдена'])->withInput();
            }
        }

        // Находим моба
        $mob = Mob::findOrFail($id);

        // Обновляем slug, если имя изменилось
        if ($mob->name !== $validated['name']) {
            $validated['slug'] = $this->generateUniqueSlug($validated['name'], $mob->id);
        }

        // Обновляем моба
        $mob->update($validated);

        Log::info('Обновлен моб в админке', [
            'mob_id' => $mob->id,
            'name' => $mob->name
        ]);

        return redirect()->route('admin.mobs.index')
            ->with('success', 'Моб успешно обновлен!');
    }

    /**
     * Удаляет моба
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $mob = Mob::findOrFail($id);

        // Удаляем связанные записи
        $mob->resourceDrops()->delete();
        $mob->itemDrops()->delete();
        $mob->currencyDrops()->delete();

        // Удаляем самого моба
        $mob->delete();

        Log::info('Удален моб в админке', [
            'mob_id' => $id,
            'name' => $mob->name
        ]);

        return redirect()->route('admin.mobs.index')
            ->with('success', 'Моб успешно удален!');
    }

    /**
     * Тестирует дроп ресурсов моба
     *
     * @param int $mobId
     * @param int $iterations
     * @return \Illuminate\View\View
     */
    public function testResourceDrop($mobId, $iterations = 100)
    {
        $user = Auth::user();
        $mob = Mob::with('resourceDrops.resource')->findOrFail($mobId);

        // Результаты тестирования
        $results = [];
        // Статистика по дропу для каждого ресурса
        $dropStats = [];

        // Инициализируем статистику для всех возможных ресурсов
        foreach ($mob->resourceDrops as $drop) {
            $dropStats[$drop->resource_id] = [
                'resource_name' => $drop->resource->name,
                'expected_chance' => $drop->drop_chance,
                'min_quantity' => $drop->min_quantity,
                'max_quantity' => $drop->max_quantity,
                'actual_drops' => 0,
                'total_quantity' => 0,
                'actual_chance' => 0,
                'avg_quantity' => 0,
            ];
        }

        // Выполняем симуляцию дропа указанное число раз
        for ($i = 0; $i < $iterations; $i++) {
            $droppedItem = $mob->getRandomDroppedResource();

            if ($droppedItem) {
                $resourceId = $droppedItem['resource_id'];
                $quantity = $droppedItem['quantity'];

                $results[] = [
                    'iteration' => $i + 1,
                    'resource' => $droppedItem['resource'],
                    'quantity' => $quantity
                ];

                // Обновляем статистику
                $dropStats[$resourceId]['actual_drops']++;
                $dropStats[$resourceId]['total_quantity'] += $quantity;
            } else {
                $results[] = [
                    'iteration' => $i + 1,
                    'resource' => 'Ничего не выпало',
                    'quantity' => 0
                ];
            }
        }

        // Вычисляем фактические шансы и среднее количество
        foreach ($dropStats as $resourceId => &$stats) {
            $stats['actual_chance'] = ($stats['actual_drops'] / $iterations) * 100;
            $stats['avg_quantity'] = $stats['actual_drops'] > 0
                ? $stats['total_quantity'] / $stats['actual_drops']
                : 0;
        }

        Log::info('Тестирование дропа ресурсов', [
            'mob_id' => $mobId,
            'mob_name' => $mob->name,
            'iterations' => $iterations,
            'drop_stats' => $dropStats
        ]);

        return $this->prepareUserProfileView($user, 'admin.mobs.test_resource_drop')
            ->with('mob', $mob)
            ->with('results', $results)
            ->with('dropStats', $dropStats)
            ->with('iterations', $iterations);
    }

    /**
     * Универсальный метод для отображения профиля.
     *
     * @param $user Пользователь (авторизованный или другой)
     * @param string $view Шаблон для отображения
     * @return \Illuminate\View\View
     */
    protected function prepareUserProfileView($user, string $view)
    {
        // Получаем профиль пользователя
        $userProfile = $user->profile;

        // Проверяем, есть ли профиль
        if (!$userProfile) {
            abort(404, 'Профиль не найден.');
        }

        // Расчёт GS (Game Score или другая логика)
        $gs = method_exists($userProfile, 'calculateGS') ? $userProfile->calculateGS() : null;

        // Получение статистики пользователя
        $userStatistics = $user->statistics;

        // Передача данных в шаблон
        return view($view, [
            'userProfile' => $userProfile,
            'user' => $user,
            'gs' => $gs,
            'userStatistics' => $userStatistics,
        ]);
    }

    /**
     * Генерирует уникальный slug для моба
     *
     * @param string $name Имя моба
     * @param int|null $excludeId ID моба для исключения из проверки (при обновлении)
     * @return string
     */
    protected function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        // Проверяем уникальность slug
        while (true) {
            $query = Mob::where('slug', $slug);
            
            // Исключаем текущий моб при обновлении
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
            
            // Если slug уникален, возвращаем его
            if (!$query->exists()) {
                break;
            }
            
            // Если не уникален, добавляем счетчик
            $slug = $baseSlug . '-' . $counter;
            $counter++;
            
            // Защита от бесконечного цикла
            if ($counter > 100) {
                $slug = $baseSlug . '-' . time();
                break;
            }
        }

        return $slug;
    }
}
<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Services\battle\FactionCountService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Отладка подсчета фракций ===\n\n";

// Получаем сервис
$factionCountService = app(FactionCountService::class);

// Пример локации для тестирования
$testLocation = 'Шахта Новичков';

echo "Тестируем локацию: {$testLocation}\n\n";

// Проверяем ботов в локации
echo "=== Боты в локации ===\n";
$botsInLocation = Bot::where('location', $testLocation)
    ->where('is_active', true)
    ->where('hp', '>', 0)
    ->get();

echo "Всего активных ботов: " . $botsInLocation->count() . "\n";

$botsByRace = $botsInLocation->groupBy('race');
foreach ($botsByRace as $race => $bots) {
    echo "Раса {$race}: " . $bots->count() . " ботов\n";
    
    $botsByClass = $bots->groupBy('class');
    foreach ($botsByClass as $class => $classBots) {
        echo "  - {$class}: " . $classBots->count() . "\n";
    }
}

echo "\n=== Игроки в локации ===\n";
$playersInLocation = User::whereHas('statistics', function ($q) use ($testLocation) {
    $q->where('current_location', $testLocation);
})
->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
->with(['profile', 'statistics'])
->get();

echo "Всего онлайн игроков: " . $playersInLocation->count() . "\n";

$playersByRace = $playersInLocation->groupBy(function ($player) {
    return $player->profile->race ?? 'unknown';
});

foreach ($playersByRace as $race => $players) {
    echo "Раса {$race}: " . $players->count() . " игроков\n";
    
    $playersByClass = $players->groupBy(function ($player) {
        return $player->profile->class ?? 'unknown';
    });
    
    foreach ($playersByClass as $class => $classPlayers) {
        echo "  - {$class}: " . $classPlayers->count() . "\n";
    }
}

echo "\n=== Результат FactionCountService ===\n";
$factionCounts = $factionCountService->getLocationFactionCounts($testLocation);

echo "Результат подсчета:\n";
echo "Solarius игроки: " . json_encode($factionCounts['player_counts']['solarius']) . "\n";
echo "Lunarius игроки: " . json_encode($factionCounts['player_counts']['lunarius']) . "\n";
echo "Solarius боты: " . json_encode($factionCounts['bot_counts']['solarius']) . "\n";
echo "Lunarius боты: " . json_encode($factionCounts['bot_counts']['lunarius']) . "\n";
echo "Общий подсчет Solarius: " . json_encode($factionCounts['total_counts']['solarius']) . "\n";
echo "Общий подсчет Lunarius: " . json_encode($factionCounts['total_counts']['lunarius']) . "\n";

// Проверяем неподдерживаемые классы ботов
echo "\n=== Неподдерживаемые классы ботов ===\n";
$unsupportedClasses = Bot::whereNotIn('class', ['warrior', 'mage', 'priest'])
    ->where('is_active', true)
    ->where('hp', '>', 0)
    ->distinct('class')
    ->pluck('class')
    ->toArray();

if (!empty($unsupportedClasses)) {
    echo "Найдены боты с неподдерживаемыми классами: " . implode(', ', $unsupportedClasses) . "\n";
    foreach ($unsupportedClasses as $class) {
        $count = Bot::where('class', $class)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->count();
        echo "  - {$class}: {$count} ботов\n";
    }
} else {
    echo "Все классы ботов поддерживаются\n";
}

// Проверяем неподдерживаемые расы ботов
echo "\n=== Неподдерживаемые расы ботов ===\n";
$unsupportedRaces = Bot::whereNotIn('race', ['solarius', 'lunarius'])
    ->where('is_active', true)
    ->where('hp', '>', 0)
    ->distinct('race')
    ->pluck('race')
    ->toArray();

if (!empty($unsupportedRaces)) {
    echo "Найдены боты с неподдерживаемыми расами: " . implode(', ', $unsupportedRaces) . "\n";
    foreach ($unsupportedRaces as $race) {
        $count = Bot::where('race', $race)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->count();
        echo "  - {$race}: {$count} ботов\n";
    }
} else {
    echo "Все расы ботов поддерживаются\n";
}

// Проверяем игроков с неподдерживаемыми расами
echo "\n=== Неподдерживаемые расы игроков ===\n";
$unsupportedPlayerRaces = \DB::table('user_profiles')
    ->whereNotIn('race', ['solarius', 'lunarius'])
    ->whereNotNull('race')
    ->distinct('race')
    ->pluck('race')
    ->toArray();

if (!empty($unsupportedPlayerRaces)) {
    echo "Найдены игроки с неподдерживаемыми расами: " . implode(', ', $unsupportedPlayerRaces) . "\n";
    foreach ($unsupportedPlayerRaces as $race) {
        $count = \DB::table('user_profiles')->where('race', $race)->count();
        echo "  - {$race}: {$count} игроков\n";
    }
} else {
    echo "Все расы игроков поддерживаются\n";
}

echo "\n=== Завершено ===\n";
<?php

namespace App\Services\Mine;

use App\Models\Bot;
use App\Models\User;
use App\Services\RedisKeyService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * Сервис кэширования для ботов рудников
 * Адаптирован с системы ботов аванпостов для работы в рудниках
 * Использует Redis для оптимизации производительности
 */
class MineBotCacheService
{
    protected array $config;
    protected RedisKeyService $redisKeyService;
    protected array $ttlConfig;
    protected string $prefix;

    public function __construct(RedisKeyService $redisKeyService)
    {
        $this->config = config('mine_bots', []);
        $this->redisKeyService = $redisKeyService;
        
        // Получаем TTL конфигурацию из mine_bots или используем значения по умолчанию
        $this->ttlConfig = $this->config['redis']['ttl'] ?? [
            'bot_state' => 300,
            'player_state' => 180,
            'attack_cooldown' => 60,
            'healing_cooldown' => 30,
        ];
        
        // Получаем префикс из конфигурации mine_bots
        $this->prefix = $this->config['redis']['prefix'] ?? 'mine_bots:';
    }

    /**
     * Кэширует состояние бота
     */
    public function cacheBotState(Bot $bot): bool
    {
        try {
            $key = $this->redisKeyService->getBotDataKey($bot->id);
            $ttl = $this->ttlConfig['bot_state'];

            $data = [
                'id' => $bot->id,
                'name' => $bot->name,
                'race' => $bot->race,
                'class' => $bot->class,
                'level' => $bot->level,
                'hp' => $bot->hp,
                'max_hp' => $bot->max_hp,
                'mp' => $bot->mp,
                'max_mp' => $bot->max_mp,
                'strength' => $bot->strength,
                'intelligence' => $bot->intelligence,
                'dexterity' => $bot->dexterity,
                'armor' => $bot->armor,
                'magic_resistance' => $bot->magic_resistance,
                'location' => $bot->location,
                'mine_location_id' => $bot->mine_location_id,
                'is_active' => $bot->is_active,
                'current_target_id' => $bot->current_target_id,
                'current_target_type' => $bot->current_target_type,
                'last_attack_time' => $bot->last_attack_time?->timestamp,
                'next_action_time' => $bot->next_action_time?->timestamp,
                'cached_at' => now()->timestamp,
            ];

            Redis::setex($key, $ttl, json_encode($data));

            return true;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка кэширования состояния бота {$bot->id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получает состояние бота из кэша
     */
    public function getBotState(int $botId): ?array
    {
        try {
            $key = $this->getBotStateKey($botId);
            $data = Redis::get($key);

            if (!$data) {
                return null;
            }

            return json_decode($data, true);

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка получения состояния бота {$botId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Кэширует ботов в локации
     */
    public function cacheLocationBots(string $location): bool
    {
        try {
            $key = $this->getLocationBotsKey($location);
            $ttl = $this->ttlConfig['bot_state'];

            $bots = Bot::where('location', $location)
                ->where('is_active', true)
                ->where('created_by_admin', true)
                ->select(['id', 'name', 'race', 'class', 'level', 'hp', 'max_hp', 'mp', 'max_mp'])
                ->get();

            $data = [
                'location' => $location,
                'bots' => $bots->toArray(),
                'count' => $bots->count(),
                'cached_at' => now()->timestamp,
            ];

            Redis::setex($key, $ttl, json_encode($data));

            return true;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка кэширования ботов локации {$location}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Кэширует игроков в локации
     */
    public function cacheLocationPlayers(string $location): bool
    {
        try {
            $key = $this->getLocationPlayersKey($location);
            $ttl = $this->ttlConfig['player_state'];

            // Определяем онлайн-статус по активности в последние 20 минут
            $onlineThreshold = now()->timestamp - (20 * 60);

            $players = User::whereHas('statistics', function ($query) use ($location) {
                $query->where('current_location', $location);
            })
                ->where(function ($query) use ($onlineThreshold) {
                    $query->where('is_online', true)
                        ->orWhere('last_activity_timestamp', '>', $onlineThreshold);
                })
                ->with(['profile:user_id,race,current_hp,max_hp', 'statistics:user_id,current_location'])
                ->select(['id', 'name', 'is_online', 'last_activity_timestamp'])
                ->get();

            $data = [
                'location' => $location,
                'players' => $players->map(function ($player) {
                    return [
                        'id' => $player->id,
                        'name' => $player->name,
                        'race' => $player->profile->race ?? null,
                        'hp' => $player->profile->current_hp ?? 0,
                        'max_hp' => $player->profile->max_hp ?? 100,
                        'is_online' => $player->is_online,
                        'location' => $player->statistics->current_location ?? null,
                    ];
                })->toArray(),
                'count' => $players->count(),
                'cached_at' => now()->timestamp,
            ];

            Redis::setex($key, $ttl, json_encode($data));

            return true;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка кэширования игроков локации {$location}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получает кэшированных игроков локации
     */
    public function getLocationPlayers(string $location): ?array
    {
        try {
            $key = $this->getLocationPlayersKey($location);
            $data = Redis::get($key);

            if (!$data) {
                return null;
            }

            return json_decode($data, true);

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка получения игроков локации {$location}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Устанавливает кулдаун атаки для бота
     */
    public function setAttackCooldown(int $botId, int $seconds): bool
    {
        try {
            $key = $this->getAttackCooldownKey($botId);
            Redis::setex($key, $seconds, now()->timestamp);
            return true;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка установки кулдауна атаки для бота {$botId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверяет, есть ли кулдаун атаки у бота
     */
    public function hasAttackCooldown(int $botId): bool
    {
        try {
            $key = $this->getAttackCooldownKey($botId);
            return Redis::exists($key) > 0;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка проверки кулдауна атаки для бота {$botId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Устанавливает кулдаун лечения для бота
     */
    public function setHealingCooldown(int $botId, int $seconds): bool
    {
        try {
            $key = $this->getHealingCooldownKey($botId);
            Redis::setex($key, $seconds, now()->timestamp);
            return true;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка установки кулдауна лечения для бота {$botId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверяет, есть ли кулдаун лечения у бота
     */
    public function hasHealingCooldown(int $botId): bool
    {
        try {
            $key = $this->getHealingCooldownKey($botId);
            return Redis::exists($key) > 0;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка проверки кулдауна лечения для бота {$botId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Очищает весь кэш для локации
     */
    public function clearLocationCache(string $location): bool
    {
        try {
            $keys = [
                $this->getLocationBotsKey($location),
                $this->getLocationPlayersKey($location),
            ];

            foreach ($keys as $key) {
                Redis::del($key);
            }

            return true;

        } catch (\Exception $e) {
            Log::error("MineBotCacheService: Ошибка очистки кэша локации {$location}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Генерирует ключ для состояния бота
     */
    protected function getBotStateKey(int $botId): string
    {
        return $this->prefix . "bot:{$botId}:state";
    }

    /**
     * Генерирует ключ для ботов локации
     */
    protected function getLocationBotsKey(string $location): string
    {
        return $this->prefix . "location:" . md5($location) . ":bots";
    }

    /**
     * Генерирует ключ для игроков локации
     */
    protected function getLocationPlayersKey(string $location): string
    {
        return $this->prefix . "location:" . md5($location) . ":players";
    }

    /**
     * Генерирует ключ для кулдауна атаки
     */
    protected function getAttackCooldownKey(int $botId): string
    {
        return $this->prefix . "bot:{$botId}:attack_cooldown";
    }

    /**
     * Генерирует ключ для кулдауна лечения
     */
    protected function getHealingCooldownKey(int $botId): string
    {
        return $this->prefix . "bot:{$botId}:healing_cooldown";
    }
}

<!DOCTYPE html>
<html lang="en">
@php use Illuminate\Support\Facades\Auth; @endphp {{-- Используем фасад Auth --}}

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>вввв - {{ Auth::check() ? Auth::user()->name : 'Гость' }}</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    {{-- Основной контейнер страницы --}}
    <div
        class="container max-w-md mx-auto px-1 py-0 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasBrokenItems="$hasBrokenItems ?? false" :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Сообщения --}}
        <div class="text-center flex justify-center space-x-1">
            @if (session('welcome_message'))
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    {{ session('welcome_message') }}
                </div>
            @endif
        </div>

        

        {{-- Блок изображения локации --}}
        <div class="mb-1">
            {{-- Отображение активных эффектов --}}
            <x-layout.active-effects :userEffects="$userEffects" />

            {{-- Определяем, оглушен ли пользователь --}}
            @php
                $isStunned = $userEffects->contains(function ($effect) {
                    return $effect->skill_id == 14 && $effect->isActive();
                });
            @endphp

            {{-- Заголовок аванпоста с унифицированным компонентом --}}
            {{-- Используем стандартное фоновое изображение для названий всех аванпостов --}}
            <x-layout.outpost-header :breadcrumbs="$breadcrumbs" :title="$locationName ?? 'вввв'"
                :useCustomLocationName="false" />
        </div>
        {{-- Блок для флеш-сообщений --}}
        <x-game-flash-messages />
        <!-- Блок фракций и деревень -->
        <div class="mt-1">
            <!-- Статус фракций -->
            <x-battle.faction-status :solWarriors="$solWarriors" :solMages="$solMages" :solKnights="$solKnights" :lunWarriors="$lunWarriors"
                :lunMages="$lunMages" :lunKnights="$lunKnights" />

            <!-- Деревни и обелиск -->
            <div class="grid grid-cols-3 gap-2 w-full">
                @php
                    $userRace = auth()->user()->profile->race;
                    // Получаем все деревни для текущей локации
                    $villagesCount = count($villages ?? []);
                    // Определяем, сколько деревень будет до и после обелиска
                    $villagesBefore = floor($villagesCount / 2);
                    $villagesAfter = $villagesCount - $villagesBefore;
                @endphp

                <!-- Деревни перед обелиском -->
                @foreach ($villages ?? [] as $index => $village)
                    @if ($index < $villagesBefore)
                        <x-battle.outposts.village-block :village="$village" :userRace="$userRace" :villageName="$village->name"
                            routePrefix="battle.outposts" :locationId="$outpostLocation->id" />
                    @endif
                @endforeach

                <!-- Обелиск -->
                <x-battle.outposts.obelisk-block :obelisk="$obelisk ?? null" :user="$user ?? null" routePrefix="battle.outposts" />

                <!-- Деревни после обелиска -->
                @foreach ($villages ?? [] as $index => $village)
                    @if ($index >= $villagesBefore && $index < $villagesBefore + $villagesAfter)
                        <x-battle.outposts.village-block :village="$village" :userRace="$userRace" :villageName="$village->name"
                            routePrefix="battle.outposts" :locationId="$outpostLocation->id" />
                    @endif
                @endforeach
            </div>

            <!-- Блок действий с целью -->
            <x-battle.target-block :target="$target ?? null" :isStunned="$isStunned ?? false" :lastAttacker="$lastAttacker ?? null" :lastAttackerResources="$lastAttackerResources ?? null"
                routePrefix="battle.outposts" />

            <!-- Панель быстрого использования зелий -->
            <div class="mt-2 mb-2">
                <x-user.quick-potion-bar class="flex justify-center items-center" />
            </div>

            <!-- Панель умений -->
            <x-battle.skills-panel routePrefix="battle.outposts" :isStunned="$isStunned ?? false" />

            {{-- Блок участников группы --}}
            <x-party.members-bar :partyMembers="$partyMembers ?? collect()" :currentUserId="$user->id ?? null" />

            {{-- Логи боя --}}
            <x-battle.battle-logs :battleLogs="$battleLogs" />

        </div>

        {{-- Нижние кнопки навигации --}}
        <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
            {{-- Кнопка Рюкзак --}}
            <a href="{{ route('inventory.index') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Рюкзак
            </a>
            {{-- Кнопка Персонаж --}}
            <a href="{{ route('user.profile') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Персонаж
            </a>
            {{-- Кнопка Группа --}}
            <a href="{{ route('party.index') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Группа
            </a>
            {{-- Кнопка Гильдия --}}
            <a href="{{ route('guilds.index') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Гильдия
            </a>
        </div>

        {{-- Футер --}}
        <x-layout.footer :onlineCount="$onlineCount" />
        {{-- Стили для анимаций --}}
        <style>
            @keyframes messagePulse {
                0% {
                    filter: drop-shadow(0 0 0 rgba(231, 76, 60, 0));
                }

                50% {
                    filter: drop-shadow(0 0 5px rgba(231, 76, 60, 0.7));
                }

                100% {
                    filter: drop-shadow(0 0 0 rgba(231, 76, 60, 0));
                }
            }

            @keyframes fadeUp {
                0% {
                    opacity: 0;
                    transform: translateY(0);
                }

                20% {
                    opacity: 1;
                }

                80% {
                    opacity: 1;
                }

                100% {
                    opacity: 0;
                    transform: translateY(-15px);
                }
            }

            .animate-fade-up {
                animation: fadeUp 2s ease-out forwards;
            }

            #progressBar {
                transition: width 0.1s linear;
            }
        </style>

        {{-- Подключение скриптов --}}
        @vite(['resources/js/attackLimiter.js'])
</body>

</html>
<?php

/**
 * ПОИСК СКРЫТЫХ БОТОВ, КОТОРЫЕ АТАКУЮТ, НО НЕ ПОКАЗЫВАЮТСЯ В АДМИНКЕ
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔍 ПОИСК СКРЫТЫХ БОТОВ\n";
echo "======================\n\n";

// Получаем подлокацию из аргумента
$locationName = $argv[1] ?? 'xzxzxzx';
echo "Ищем ботов в подлокации: {$locationName}\n\n";

// 1. Боты, которые показываются в админке (правильные)
echo "1. БОТЫ В АДМИНКЕ (location = '{$locationName}')\n";
echo "==============================================\n";

$adminBots = Bot::where('location', $locationName)
    ->where('created_by_admin', true)
    ->get();

echo "Найдено в админке: {$adminBots->count()}\n";
foreach ($adminBots as $bot) {
    echo "✅ {$bot->name} (ID: {$bot->id}, race: {$bot->race})\n";
}

// 2. Ищем ботов по именам атакующих
echo "\n2. ПОИСК БОТОВ ПО ИМЕНАМ АТАКУЮЩИХ\n";
echo "==================================\n";

$attackerNames = ['nvvng2', 'cccccc2', 'nvvng', 'иииииииииииии2222'];
$foundBots = [];

foreach ($attackerNames as $name) {
    $bot = Bot::where('name', $name)->first();
    if ($bot) {
        $foundBots[] = $bot;
        $status = $bot->location === $locationName ? '✅ ПРАВИЛЬНО' : '❌ НЕПРАВИЛЬНО';
        echo "🤖 {$bot->name}: location = '{$bot->location}' {$status}\n";
        echo "    ID: {$bot->id}, mine_location_id: {$bot->mine_location_id}\n";
        echo "    Активен: " . ($bot->is_active ? 'ДА' : 'НЕТ') . "\n";
        echo "    Создан админом: " . ($bot->created_by_admin ? 'ДА' : 'НЕТ') . "\n";
        echo "\n";
    } else {
        echo "❌ Бот '{$name}' не найден в базе\n";
    }
}

// 3. Ищем подлокацию рудника
echo "3. ИНФОРМАЦИЯ О ПОДЛОКАЦИИ\n";
echo "=========================\n";

$mineLocation = MineLocation::where('name', $locationName)->first();
if ($mineLocation) {
    echo "📍 Подлокация найдена:\n";
    echo "    ID: {$mineLocation->id}\n";
    echo "    Название: {$mineLocation->name}\n";
    echo "    Активна: " . ($mineLocation->is_active ? 'ДА' : 'НЕТ') . "\n";
    echo "    Базовая локация: " . ($mineLocation->baseLocation->name ?? 'неизвестно') . "\n";
    
    // Ищем всех ботов с ID этой подлокации
    $botsWithId = Bot::where('location', (string) $mineLocation->id)->get();
    echo "    Ботов с ID локации: {$botsWithId->count()}\n";
    
    foreach ($botsWithId as $bot) {
        echo "    🤖 {$bot->name} (location = '{$bot->location}' вместо '{$mineLocation->name}')\n";
    }
} else {
    echo "❌ Подлокация '{$locationName}' не найдена\n";
}

// 4. Поиск всех ботов, которые могут атаковать в этой локации
echo "\n4. ВСЕ БОТЫ, КОТОРЫЕ МОГУТ АТАКОВАТЬ В ЛОКАЦИИ\n";
echo "=============================================\n";

$allPossibleBots = Bot::where(function ($query) use ($locationName, $mineLocation) {
    $query->where('location', $locationName);
    if ($mineLocation) {
        $query->orWhere('location', (string) $mineLocation->id);
    }
})->where('created_by_admin', true)->get();

echo "Всего ботов, связанных с локацией: {$allPossibleBots->count()}\n";

foreach ($allPossibleBots as $bot) {
    $isCorrect = $bot->location === $locationName ? '✅' : '❌';
    echo "{$isCorrect} {$bot->name} (location: '{$bot->location}', active: " . ($bot->is_active ? 'ДА' : 'НЕТ') . ")\n";
}

// 5. Проверяем компонент статуса фракции
echo "\n5. ПРОВЕРКА СЧЕТЧИКА ФРАКЦИЙ\n";
echo "===========================\n";

// Получаем игроков в локации
$playersInLocation = User::whereHas('statistics', function ($query) use ($locationName) {
    $query->where('current_location', $locationName);
})->with('profile')->get();

echo "Игроков в локации: {$playersInLocation->count()}\n";

$solariumCount = $playersInLocation->filter(fn($p) => $p->profile->race === 'solarius')->count();
$lunariumCount = $playersInLocation->filter(fn($p) => $p->profile->race === 'lunarius')->count();

echo "☀️ Solarius: {$solariumCount}\n";
echo "🌙 Lunarius: {$lunariumCount}\n";

// 6. НЕМЕДЛЕННОЕ ИСПРАВЛЕНИЕ
echo "\n6. НЕМЕДЛЕННОЕ ИСПРАВЛЕНИЕ\n";
echo "=========================\n";

if ($mineLocation) {
    $fixedCount = 0;
    
    // Исправляем всех ботов с ID локации
    $botsToFix = Bot::where('location', (string) $mineLocation->id)->get();
    
    foreach ($botsToFix as $bot) {
        $oldLocation = $bot->location;
        $bot->location = $mineLocation->name;
        $bot->mine_location_id = $mineLocation->id;
        $bot->next_action_time = null;
        $bot->save();
        
        echo "✅ ИСПРАВЛЕН: {$bot->name} ('{$oldLocation}' → '{$bot->location}')\n";
        $fixedCount++;
    }
    
    if ($fixedCount > 0) {
        echo "\n🎉 ИСПРАВЛЕНО БОТОВ: {$fixedCount}\n";
        echo "Теперь в админке должны отображаться все боты!\n";
    } else {
        echo "\n✅ Исправлений не требуется\n";
    }
} else {
    echo "❌ Не могу исправить - подлокация не найдена\n";
}

// 7. Проверяем результат
echo "\n7. ПРОВЕРКА РЕЗУЛЬТАТА\n";
echo "=====================\n";

$finalCheck = Bot::where('location', $locationName)
    ->where('created_by_admin', true)
    ->get();

echo "Ботов в админке после исправления: {$finalCheck->count()}\n";
foreach ($finalCheck as $bot) {
    echo "✅ {$bot->name} (активен: " . ($bot->is_active ? 'ДА' : 'НЕТ') . ")\n";
}

echo "\n🏁 ПОИСК ЗАВЕРШЕН\n";
echo "=================\n";
echo "Время: " . now()->format('H:i:s d.m.Y') . "\n";
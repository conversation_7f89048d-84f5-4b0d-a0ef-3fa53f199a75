<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Проверяем, существует ли таблица, если нет - создаем
        if (!Schema::hasTable('mine_marks')) {
            Schema::create('mine_marks', function (Blueprint $table) {
                $table->id();
                
                // Основные поля
                $table->unsignedBigInteger('player_id')->comment('ID игрока с меткой');
                $table->unsignedBigInteger('mine_location_id')->comment('ID локации рудника');
                $table->unsignedBigInteger('location_id')->comment('ID основной локации');
                $table->string('location_name')->comment('Название локации рудника');
                
                // Временные поля
                $table->timestamp('expires_at')->comment('Время истечения метки');
                $table->timestamp('last_attack_at')->nullable()->comment('Время последней атаки');
                
                // Статусные поля
                $table->boolean('is_active')->default(true)->comment('Активна ли метка');
                $table->unsignedInteger('attack_count')->default(0)->comment('Количество атак');
                
                $table->timestamps();
                
                // Индексы для оптимизации
                $table->index(['player_id', 'mine_location_id'], 'mine_marks_player_location_idx');
                $table->index(['mine_location_id', 'is_active', 'expires_at'], 'mine_marks_location_active_idx');
                $table->index(['is_active', 'expires_at'], 'mine_marks_active_expires_idx');
                $table->index(['location_id'], 'mine_marks_location_id_idx');
                $table->index(['expires_at'], 'mine_marks_expires_idx');
                
                // Внешние ключи (проверяем существование таблиц)
                if (Schema::hasTable('users')) {
                    $table->foreign('player_id')->references('id')->on('users')->onDelete('cascade');
                }
                
                if (Schema::hasTable('mine_locations')) {
                    $table->foreign('mine_location_id')->references('id')->on('mine_locations')->onDelete('cascade');
                }
                
                if (Schema::hasTable('locations')) {
                    $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
                }
            });
            
            echo "Таблица mine_marks создана успешно\n";
        } else {
            echo "Таблица mine_marks уже существует\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mine_marks');
    }
};
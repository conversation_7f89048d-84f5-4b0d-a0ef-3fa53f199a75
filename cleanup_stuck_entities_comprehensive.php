<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Support\Facades\Schema;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Комплексная очистка застрявших сущностей ===\n\n";

$dryRun = true; // Установить в false для реального выполнения

echo "Режим: " . ($dryRun ? "ТЕСТОВЫЙ (изменения не будут применены)" : "РЕАЛЬНЫЙ (изменения будут применены)") . "\n\n";

echo "=== 1. Поиск мертвых игроков в счетчиках ===\n";

// Находим игроков с HP <= 0, которые все еще онлайн
$deadPlayersOnline = User::where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
    ->whereHas('profile', function ($q) {
        $q->where('current_hp', '<=', 0);
        
        // Добавляем проверку is_defeated только если поле существует
        if (Schema::hasColumn('user_profiles', 'is_defeated')) {
            $q->orWhere('is_defeated', true);
        }
    })
    ->with(['profile', 'statistics'])
    ->get();

echo "Найдено мертвых/поверженных игроков онлайн: " . $deadPlayersOnline->count() . "\n";

foreach ($deadPlayersOnline as $player) {
    $race = $player->profile->race ?? 'unknown';
    $class = $player->profile->class ?? 'unknown';
    $hp = $player->profile->current_hp ?? 'N/A';
    $maxHp = $player->profile->max_hp ?? 'N/A';
    $isDefeated = 'N/A';
    if (isset($player->profile->is_defeated)) {
        $isDefeated = $player->profile->is_defeated ? 'ДА' : 'НЕТ';
    }
    $location = $player->statistics->current_location ?? 'Неизвестно';
    
    echo "  ПРОБЛЕМА: {$player->name} - {$race} {$class} в {$location}\n";
    echo "    HP: {$hp}/{$maxHp}, поражен: {$isDefeated}\n";
    echo "    Последняя активность: " . date('Y-m-d H:i:s', $player->last_activity_timestamp) . "\n";
    
    if (!$dryRun) {
        // Обновляем время последней активности, чтобы исключить из онлайн счетчиков
        $player->update(['last_activity_timestamp' => now()->subHours(1)->timestamp]);
        echo "    ИСПРАВЛЕНО: установлена устаревшая активность\n";
    }
    echo "\n";
}

echo "=== 2. Поиск проблематичных ботов ===\n";

$problematicBots = Bot::where('is_active', true)
    ->where(function ($q) {
        $q->where('hp', '<=', 0)
          ->orWhereNotNull('death_time')
          ->orWhereNull('location')
          ->orWhere('location', '');
    })
    ->get();

echo "Найдено проблематичных активных ботов: " . $problematicBots->count() . "\n";

foreach ($problematicBots as $bot) {
    $problems = [];
    if ($bot->hp <= 0) $problems[] = "HP <= 0";
    if ($bot->death_time) $problems[] = "death_time установлен";
    if (!$bot->location || $bot->location === '') $problems[] = "нет локации";
    
    echo "  ПРОБЛЕМА: {$bot->name} - {$bot->race} {$bot->class}\n";
    echo "    Проблемы: " . implode(', ', $problems) . "\n";
    echo "    HP: {$bot->hp}/{$bot->max_hp}\n";
    echo "    Локация: " . ($bot->location ?: 'NULL/ПУСТАЯ') . "\n";
    echo "    Mine Location ID: " . ($bot->mine_location_id ?: 'NULL') . "\n";
    
    if (!$dryRun) {
        // Деактивируем проблематичных ботов
        $bot->update(['is_active' => false]);
        echo "    ИСПРАВЛЕНО: бот деактивирован\n";
    }
    echo "\n";
}

echo "=== 3. Поиск ботов без соответствующей локации ===\n";

// Находим ботов в несуществующих локациях
$botsInInvalidLocations = Bot::where('is_active', true)
    ->where('hp', '>', 0)
    ->whereNotNull('location')
    ->where('location', '!=', '')
    ->whereDoesntHave('mineLocation') // Нет связи с MineLocation
    ->whereRaw("location NOT IN (SELECT name FROM locations)") // И не существует в основных локациях
    ->get();

echo "Найдено ботов в несуществующих локациях: " . $botsInInvalidLocations->count() . "\n";

foreach ($botsInInvalidLocations as $bot) {
    echo "  ПРОБЛЕМА: {$bot->name} в несуществующей локации '{$bot->location}'\n";
    echo "    Race: {$bot->race}, Class: {$bot->class}\n";
    echo "    HP: {$bot->hp}/{$bot->max_hp}\n";
    
    if (!$dryRun) {
        // Деактивируем ботов в несуществующих локациях
        $bot->update(['is_active' => false]);
        echo "    ИСПРАВЛЕНО: бот деактивирован\n";
    }
    echo "\n";
}

echo "=== 4. Поиск игроков в несуществующих локациях ===\n";

$playersInInvalidLocations = User::whereHas('statistics', function ($q) {
        $q->whereNotNull('current_location')
          ->where('current_location', '!=', '')
          ->whereRaw("current_location NOT IN (SELECT name FROM locations)")
          ->whereRaw("current_location NOT IN (SELECT name FROM mine_locations)");
    })
    ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
    ->with(['profile', 'statistics'])
    ->get();

echo "Найдено игроков в несуществующих локациях: " . $playersInInvalidLocations->count() . "\n";

foreach ($playersInInvalidLocations as $player) {
    $location = $player->statistics->current_location;
    $race = $player->profile->race ?? 'unknown';
    $class = $player->profile->class ?? 'unknown';
    
    echo "  ПРОБЛЕМА: {$player->name} - {$race} {$class} в несуществующей локации '{$location}'\n";
    
    if (!$dryRun) {
        // Перемещаем в безопасную локацию
        $player->statistics->update(['current_location' => 'Городская площадь']);
        echo "    ИСПРАВЛЕНО: перемещен в Городскую площадь\n";
    }
    echo "\n";
}

echo "=== 5. Специальная проверка локации 'аааааааааааа' ===\n";

$playersInLocationA = User::whereHas('statistics', function ($q) {
    $q->where('current_location', 'аааааааааааа');
})
->with(['profile', 'statistics'])
->get();

echo "Всего игроков в локации 'аааааааааааа': " . $playersInLocationA->count() . "\n";

foreach ($playersInLocationA as $player) {
    $race = $player->profile->race ?? 'unknown';
    $class = $player->profile->class ?? 'unknown';
    $hp = $player->profile->current_hp ?? 'N/A';
    $maxHp = $player->profile->max_hp ?? 'N/A';
    $isDefeated = 'N/A';
    if (isset($player->profile->is_defeated)) {
        $isDefeated = $player->profile->is_defeated ? 'ДА' : 'НЕТ';
    }
    $isOnline = $player->last_activity_timestamp >= (now()->subMinutes(5)->timestamp);
    $canAttack = $hp > 0 && $isOnline;
    
    // Добавляем проверку is_defeated если поле существует
    if (isset($player->profile->is_defeated)) {
        $canAttack = $canAttack && !$player->profile->is_defeated;
    }
    
    echo "  {$player->name} - {$race} {$class}\n";
    echo "    HP: {$hp}/{$maxHp}, поражен: {$isDefeated}, онлайн: " . ($isOnline ? 'ДА' : 'НЕТ') . "\n";
    echo "    Может быть атакован: " . ($canAttack ? 'ДА' : 'НЕТ') . "\n";
    
    if (!$canAttack && $isOnline) {
        echo "    ПРОБЛЕМА: показывается в счетчиках, но не может быть атакован\n";
        if (!$dryRun) {
            // Обновляем время активности для исключения из счетчиков
            $player->update(['last_activity_timestamp' => now()->subHours(1)->timestamp]);
            echo "    ИСПРАВЛЕНО: удален из онлайн счетчиков\n";
        }
    }
    echo "\n";
}

echo "=== ИТОГИ ===\n";
echo "Мертвых игроков онлайн: " . $deadPlayersOnline->count() . "\n";
echo "Проблематичных ботов: " . $problematicBots->count() . "\n";
echo "Ботов в несуществующих локациях: " . $botsInInvalidLocations->count() . "\n";
echo "Игроков в несуществующих локациях: " . $playersInInvalidLocations->count() . "\n";

if ($dryRun) {
    echo "\nЧтобы применить исправления, измените \$dryRun = false в скрипте\n";
} else {
    echo "\nВсе исправления применены!\n";
}

echo "\n=== Очистка завершена ===\n";
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class Bot extends Model
{
    use HasFactory;

    /**
     * Атрибуты, которые можно массово присваивать.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'race',
        'class',
        'level',
        'hp',
        'max_hp',
        'mp',
        'max_mp',
        'strength',
        'intelligence',
        'dexterity',
        'armor',
        'magic_resistance',
        'location',
        'mine_location_id',
        'is_active',
        'current_target_id',
        'current_target_type',
        'last_attack_time',
        'last_attacker_id',
        'last_attacker_type',
        'attack_interval',
        'target_change_interval',
        'last_hp_regen_time',
        'last_mp_regen_time',
        'can_use_skills',
        'intelligent_targeting',
        'can_retreat',
        'respawn_time',
        'next_action_time',
        'last_regeneration_at',
        'death_time',
        'respawn_interval',
        'is_peaceful',
        'created_by_admin',
        'base_damage',
    ];

    /**
     * Атрибуты, для которых следует выполнить приведение типов.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'last_attack_time' => 'datetime',
        'respawn_time' => 'datetime',
        'last_hp_regen_time' => 'datetime',
        'last_mp_regen_time' => 'datetime',
        'next_action_time' => 'datetime',
        'last_regeneration_at' => 'datetime',
        'death_time' => 'datetime',
        'can_use_skills' => 'boolean',
        'intelligent_targeting' => 'boolean',
        'can_retreat' => 'boolean',
        'is_peaceful' => 'boolean',
        'created_by_admin' => 'boolean',
    ];

    /**
     * Полиморфное отношение к текущей цели бота
     * Используется для eager loading в пакетных операциях
     */
    public function currentTarget()
    {
        return $this->morphTo('current_target', 'current_target_type', 'current_target_id');
    }

    /**
     * Связь с навыками бота
     */
    public function skills()
    {
        return $this->hasMany(BotSkill::class, 'bot_id');
    }

    /**
     * Получает доступные навыки бота (не на кулдауне)
     */
    public function getAvailableSkills()
    {
        return $this->skills()
            ->with('skill')
            ->whereHas('skill', function ($query) {
                $query->where('class', $this->class)
                    ->orWhereNull('class');
            })
            ->get()
            ->filter(function ($botSkill) {
                return $botSkill->isAvailable();
            });
    }

    /**
     * Проверяет, есть ли у бота конкретный навык
     */
    public function hasSkill(int $skillId): bool
    {
        return $this->skills()->where('skill_id', $skillId)->exists();
    }

    /**
     * Добавляет навык боту
     */
    public function addSkill(int $skillId, int $chance = 100): BotSkill
    {
        return $this->skills()->updateOrCreate(
            ['skill_id' => $skillId],
            ['chance' => $chance]
        );
    }

    /**
     * Получение текущей цели бота (legacy метод)
     */
    public function getTarget()
    {
        if ($this->current_target_type === 'player') {
            return User::find($this->current_target_id);
        } elseif ($this->current_target_type === 'bot') {
            return self::find($this->current_target_id);
        } elseif ($this->current_target_type === 'mob') {
            return Mob::find($this->current_target_id);
        }

        return null;
    }

    /**
     * Определение, находится ли бот в зоне ПВП
     *
     * Возвращает true, если бот находится в локации, где разрешены PvP-атаки
     * Включает динамическое определение всех рудников и аванпостов из базы данных
     */
    public function isInPvpZone()
    {
        // Список локаций, где разрешено PvP для ботов (legacy поддержка)
        $pvpLocations = [
            'Эльфийская Гавань', // Классическая PvP-зона
            'Тарнмор - Каньон',  // Ещё одна PvP-зона
            'Песчаный Оплот',    // PvP-зона для автоатаки ботов
            'Форт Рассвета',     // Добавляем Форт Рассвета как PvP-зону
            'Первая Тестовая',   // Тестовая локация для отладки боевой системы ботов
            'Тестовый Аванпост', // Тестовая локация для отладки автоатаки ботов
            'awdawd',            // Тестовая локация для проверки атак ботов
        ];

        // Проверяем точное совпадение с известными PvP локациями
        if (in_array($this->location, $pvpLocations)) {
            return true;
        }

        // ИСПРАВЛЕНИЕ: Проверяем, является ли локация аванпостом
        if ($this->isOutpostLocation()) {
            return true;
        }

        // Проверяем, является ли локация рудником через различные способы
        if ($this->isMineLocation()) {
            return true;
        }

        return false;
    }

    /**
     * Проверка, является ли локация аванпостом
     * Использует динамическое определение из базы данных
     */
    public function isOutpostLocation()
    {
        try {
            // Проверяем, есть ли локация с таким названием и типом 'outpost'
            return \App\Models\Location::where('name', $this->location)
                ->where('location_type', 'outpost')
                ->where('is_active', true)
                ->exists();
        } catch (\Exception $e) {
            \Log::error('Bot: Ошибка при проверке аванпоста: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверяет, является ли текущая локация бота рудником
     *
     * @return bool
     */
    private function isMineLocation(): bool
    {
        // Кеширование результата для производительности
        static $mineLocationsCache = null;

        if ($mineLocationsCache === null) {
            $mineLocationsCache = $this->getAllMineLocationNames();
        }

        // Проверяем точное совпадение с локациями рудников из БД
        if (in_array($this->location, $mineLocationsCache)) {
            return true;
        }

        // Проверяем, содержит ли название локации слово "рудник" (регистронезависимо)
        if (stripos($this->location, 'рудник') !== false) {
            return true;
        }

        return false;
    }

    /**
     * Получает все названия локаций рудников из базы данных
     *
     * @return array
     */
    private function getAllMineLocationNames(): array
    {
        try {
            $mineLocations = [];

            // Получаем локации типа 'mine' из основной таблицы locations
            $mainMineLocations = \App\Models\Location::where('location_type', 'mine')
                ->where('is_active', true)
                ->pluck('name')
                ->toArray();

            $mineLocations = array_merge($mineLocations, $mainMineLocations);

            // Получаем локации из таблицы mine_locations, связанные с основными локациями
            $customMineLocations = \App\Models\MineLocation::where('is_active', true)
                ->with('baseLocation')
                ->get()
                ->filter(fn($mineLocation) => $mineLocation->baseLocation && $mineLocation->baseLocation->is_active)
                ->pluck('baseLocation.name')
                ->toArray();

            $mineLocations = array_merge($mineLocations, $customMineLocations);

            // Убираем дубликаты и пустые значения
            return array_filter(array_unique($mineLocations));
        } catch (\Exception $e) {
            \Log::error('Bot: Ошибка при получении локаций рудников: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Получение списка возможных целей для бота с оптимизацией и кешированием
     */
    public function getPotentialTargets()
    {
        // --- Русский комментарий: Используем кеширование для снижения нагрузки на БД ---
        $cacheKey = "location_players_{$this->location}_{$this->race}";

        $playerTargets = Cache::remember($cacheKey, 30, function () {
            // --- Русский комментарий: Определяем противоположную фракцию для поиска врагов ---
            $enemyFaction = ($this->race === 'solarius') ? 'lunarius' : 'solarius';

            // --- Русский комментарий: Проверяем все возможные варианты написания локации ---
            $possibleLocations = [
                $this->location,
                ucfirst($this->location),
                strtolower($this->location),
                strtoupper($this->location),
                ucwords($this->location),
                str_replace(' ', '-', $this->location),
                str_replace(' ', '_', $this->location)
            ];

            // Добавляем варианты с разными регистрами
            $possibleLocations[] = ucfirst(strtolower($this->location));
            $possibleLocations[] = str_replace('-', ' ', $this->location);
            $possibleLocations[] = str_replace('_', ' ', $this->location);

            // Удаляем дубликаты
            $possibleLocations = array_unique($possibleLocations);

            Log::info("[BotDebug] Проверяем возможные варианты написания локации", [
                'possible_locations' => $possibleLocations
            ]);

            // --- Русский комментарий: Ищем игроков противоположной фракции с eager loading ---
            return User::with(['profile', 'statistics'])
                ->whereHas('profile', function ($query) use ($enemyFaction) {
                    $query->whereRaw('LOWER(race) = ?', [strtolower($enemyFaction)])
                        ->where('current_hp', '>', 0);
                })
                ->whereHas('statistics', function ($query) use ($possibleLocations) {
                    $query->whereIn('current_location', $possibleLocations);
                })
                ->get();
        });

        // --- Русский комментарий: Определяем переменные для отладочного логирования ---
        $enemyFaction = ($this->race === 'solarius') ? 'lunarius' : 'solarius';
        $possibleLocations = [
            $this->location,
            ucfirst($this->location),
            strtolower($this->location),
            strtoupper($this->location),
            ucwords($this->location),
            str_replace(' ', '-', $this->location),
            str_replace(' ', '_', $this->location),
            ucfirst(strtolower($this->location)),
            str_replace('-', ' ', $this->location),
            str_replace('_', ' ', $this->location)
        ];
        $possibleLocations = array_unique($possibleLocations);

        // --- Русский комментарий: Ищем союзников (игроков своей фракции) для отладки ---
        $alliedPlayers = Cache::remember("location_allies_{$this->location}_{$this->race}", 30, function () use ($possibleLocations) {
            return User::with(['profile', 'statistics'])
                ->whereHas('profile', function ($query) {
                    $query->whereRaw('LOWER(race) = ?', [strtolower($this->race)])
                        ->where('current_hp', '>', 0);
                })
                ->whereHas('statistics', function ($query) use ($possibleLocations) {
                    $query->whereIn('current_location', $possibleLocations);
                })
                ->get();
        });

        // --- Русский комментарий: Получаем всех игроков в локации для отладки ---
        $allPlayers = Cache::remember("location_all_players_{$this->location}", 30, function () use ($possibleLocations) {
            return User::with(['profile', 'statistics'])
                ->whereHas('statistics', function ($query) use ($possibleLocations) {
                    $query->whereIn('current_location', $possibleLocations);
                })
                ->get();
        });

        Log::info("[BotDebug] Все игроки в локации '{$this->location}'", [
            'count' => $allPlayers->count(),
            'players' => $allPlayers->map(fn($p) => [
                'id' => $p->id,
                'name' => $p->name,
                'race' => $p->profile->race ?? null,
                'hp' => $p->profile->current_hp ?? null,
                'location' => $p->statistics->current_location ?? null,
            ])
        ]);

        // --- Русский комментарий: Логируем подробную информацию о найденных игроках ---
        Log::info("[BotDebug] Бот {$this->id} ({$this->name}) ищет цели в локации '{$this->location}'.", [
            'bot_race' => $this->race,
            'enemy_faction' => $enemyFaction,
            'location' => $this->location,
            'allied_players' => $alliedPlayers->map(fn($p) => [
                'id' => $p->id,
                'name' => $p->name,
                'race' => $p->profile->race ?? null,
                'hp' => $p->profile->current_hp ?? null,
                'location' => $p->statistics->current_location ?? null,
            ]),
            'enemy_players' => $playerTargets->map(fn($p) => [
                'id' => $p->id,
                'name' => $p->name,
                'race' => $p->profile->race ?? null,
                'hp' => $p->profile->current_hp ?? null,
                'location' => $p->statistics->current_location ?? null,
            ]),
        ]);

        // --- Русский комментарий: Возвращаем только найденных игроков вражеской фракции ---
        return [
            'players' => $playerTargets,
            'bots' => collect() // Ботов не ищем
        ];
    }

    /**
     * Выбор случайной цели для бота
     */
    /**
     * Выбор случайной цели для бота с умным распределением фокуса
     * Не более 3 ботов могут одновременно таргетить одного игрока.
     * Поведение менее предсказуемое (рандомизация, fallback).
     */
    public function selectRandomTarget()
    {
        // --- Получаем потенциальные цели (только враги) ---
        $targets = $this->getPotentialTargets();
        $allTargets = collect([]);

        // --- Добавляем игроков-врагов в список кандидатов ---
        foreach ($targets['players'] as $player) {
            $allTargets->push([
                'id' => $player->id,
                'type' => 'player',
                'object' => $player
            ]);
        }

        // --- Логируем список всех кандидатов на атаку ---
        Log::info("[BotDebug] Кандидаты на атаку для бота {$this->id} ({$this->name})", [
            'candidates' => $allTargets->map(function ($t) {
                return [
                    'id' => $t['id'],
                    'name' => $t['object']->name,
                    'race' => $t['object']->profile->race ?? null,
                    'hp' => $t['object']->profile->current_hp ?? null,
                    'location' => $t['object']->statistics->current_location ?? null,
                ];
            })
        ]);

        // --- Ограничиваем фокус: не более 3 ботов на одного игрока ---
        $maxBotsPerPlayer = 3;
        $location = $this->location;
        // Получаем всех активных ботов в локации
        $allBots = self::where('location', $location)
            ->where('is_active', true)
            ->get();
        // Считаем сколько ботов уже таргетят каждого игрока
        $targetCounts = [];
        foreach ($allBots as $bot) {
            if ($bot->current_target_type === 'player' && $bot->current_target_id) {
                $targetCounts[$bot->current_target_id] = ($targetCounts[$bot->current_target_id] ?? 0) + 1;
            }
        }
        // Фильтруем кандидатов: исключаем игроков, которых уже фокусят >= лимита
        $filtered = collect();
        foreach ($allTargets as $candidate) {
            if ($candidate['type'] === 'player') {
                $count = $targetCounts[$candidate['id']] ?? 0;
                if ($count < $maxBotsPerPlayer) {
                    $filtered->push($candidate);
                }
            } else {
                $filtered->push($candidate); // если будут враги-боты — не ограничиваем
            }
        }
        // --- Если после фильтрации никого не осталось — fallback к исходному списку ---
        $finalCandidates = $filtered->count() > 0 ? $filtered : $allTargets;
        // --- Рандомизация: иногда (10% шанс) игнорируем лимит для большей хаотичности ---
        if ($finalCandidates->count() < $allTargets->count() && rand(1, 10) === 1) {
            $finalCandidates = $allTargets;
        }
        // --- Выбор случайной цели из финального списка ---
        if ($finalCandidates->count() > 0) {
            $randomTarget = $finalCandidates->random();
            $this->current_target_id = $randomTarget['id'];
            $this->current_target_type = $randomTarget['type'];
            $this->save();
            Log::info("[BotDebug] Бот {$this->id} ({$this->name}) выбрал умную цель: игрок ID {$randomTarget['id']}, фокус на нем: " . ($targetCounts[$randomTarget['id']] ?? 0));
            return $randomTarget['object'];
        }
        // --- Если врагов не найдено, логируем причину ---
        Log::info("[BotDebug] Бот {$this->id} ({$this->name}) не нашел игроков вражеской фракции для атаки.");
        return null;
    }


    /**
     * Проверка, может ли бот атаковать других игроков
     */
    public function canAttackPlayers()
    {
        // Проверяем, находится ли бот в PvP зоне
        if (!$this->isInPvpZone()) {
            Log::info("Бот {$this->id} ({$this->name}) не может атаковать, т.к. не в PvP зоне");
            return false;
        }

        // Проверяем, не является ли бот временно мирным (например, после возрождения)
        if ($this->isPeaceful()) {
            Log::info("Бот {$this->id} ({$this->name}) временно мирный");
            return false;
        }

        // Всегда разрешаем боту атаковать игроков в PvP зоне
        Log::info("Бот {$this->id} ({$this->name}) может атаковать игроков");
        return true;
    }

    /**
     * Проверка, является ли бот временно мирным
     */
    public function isPeaceful()
    {
        // Если бот недавно возродился, он не может атаковать некоторое время
        if ($this->respawn_time) {
            $respawnTime = Carbon::parse($this->respawn_time);
            $now = Carbon::now();
            $peacefulDuration = 5; // Уменьшаем до 5 секунд мирного времени после возрождения (было 20)

            // Проверяем, что время возрождения не в будущем
            if ($respawnTime->isAfter($now)) {
                Log::warning("Бот {$this->name} (ID: {$this->id}) имеет время возрождения в будущем. Исправляем на текущее время.");
                $this->respawn_time = $now;
                $this->save();
                return false;
            }

            // Используем diffInSeconds с флагом absolute=true для получения положительного значения
            $secondsSinceRespawn = $respawnTime->diffInSeconds($now, false);

            if ($secondsSinceRespawn < $peacefulDuration) {
                Log::info("Бот {$this->name} (ID: {$this->id}) временно мирный после возрождения. Прошло {$secondsSinceRespawn} сек. из {$peacefulDuration}");
                return true;
            }
        }

        return false;
    }

    /**
     * Возрождение бота после смерти
     *
     * Восстанавливает здоровье и ману бота, очищает информацию о цели,
     * устанавливает время возрождения и активирует бота.
     *
     * @param bool $forceRespawn Принудительное воскрешение без учета времени
     * @return Bot
     */
    public function respawn($forceRespawn = false)
    {
        // Восстановление здоровья и маны
        $this->hp = $this->max_hp;
        $this->mp = $this->max_mp;

        // Очистка информации о цели
        $this->current_target_id = null;
        $this->current_target_type = null;

        // Установка времени возрождения (текущее время)
        $now = Carbon::now();
        $this->respawn_time = $now;
        $this->last_regeneration_at = $now; // Обновляем время последней регенерации
        $this->last_attack_time = $now->copy()->subSeconds(10); // Устанавливаем время последней атаки в прошлом

        // Сбрасываем время смерти
        $this->death_time = null;

        // Активация бота
        $this->is_active = true;

        // Логируем возрождение
        Log::info("Бот {$this->name} (ID: {$this->id}) возрожден в локации {$this->location}");

        $this->save();

        return $this;
    }

    /**
     * Проверяет, прошло ли достаточно времени для воскрешения бота
     *
     * Если время смерти не задано, считаем что бот может возродиться.
     * Если задан интервал возрождения, проверяем прошло ли достаточно времени с момента смерти.
     *
     * @return bool Возвращает true, если бот может быть воскрешен
     */
    public function canRespawn()
    {
        // Если бот активен, воскрешение не требуется
        if ($this->is_active) {
            return false;
        }

        // Если время смерти не задано, можно возрождать
        if (!$this->death_time) {
            return true;
        }

        // Получаем интервал воскрешения (или значение по умолчанию)
        $respawnInterval = $this->respawn_interval ?? 300; // По умолчанию 5 минут (300 секунд)

        // Вычисляем прошедшее время с момента смерти
        $deathTime = Carbon::parse($this->death_time);
        $now = Carbon::now();

        // Проверяем, что время смерти было в прошлом
        if ($deathTime->isAfter($now)) {
            return false; // Время смерти в будущем - что-то не так
        }

        // Вычисляем количество секунд, прошедших с момента смерти
        $secondsSinceDeath = $deathTime->diffInSeconds($now, false);

        // Проверяем, прошло ли достаточно времени
        return $secondsSinceDeath >= $respawnInterval;
    }

    /**
     * Обработка смерти бота
     *
     * Устанавливает HP в 0, деактивирует бота и записывает время смерти
     *
     * @return void
     */
    public function die()
    {
        $this->hp = 0;
        $this->is_active = false;
        $this->death_time = Carbon::now();
        $this->current_target_id = null;
        $this->current_target_type = null;
        $this->save();

        Log::info("Бот {$this->name} (ID: {$this->id}) погиб и будет воскрешен через {$this->respawn_interval} секунд");
    }

    /**
     * Получение расовой принадлежности целей для атаки
     */
    public function getEnemyRace()
    {
        return $this->race === 'solarius' ? 'lunarius' : 'solarius';
    }

    /**
     * Принудительная синхронизация бота с Redis
     * Используется для ручной синхронизации после изменений
     *
     * @return bool Успех операции
     */
    public function syncToRedis(): bool
    {
        try {
            // Определяем ID локации
            $locationId = $this->extractLocationId($this->location);

            if (!$locationId) {
                Log::warning("Bot::syncToRedis: Не удалось определить ID локации для бота {$this->id}, location: {$this->location}");
                return false;
            }

            $botsKey = "mine_location:{$locationId}:bots";

            // Подготавливаем данные бота для Redis
            $botData = [
                "bot:{$this->id}:id" => $this->id,
                "bot:{$this->id}:name" => $this->name,
                "bot:{$this->id}:race" => $this->race,
                "bot:{$this->id}:class" => $this->class,
                "bot:{$this->id}:level" => $this->level,
                "bot:{$this->id}:hp" => $this->hp,
                "bot:{$this->id}:max_hp" => $this->max_hp,
                "bot:{$this->id}:mp" => $this->mp,
                "bot:{$this->id}:max_mp" => $this->max_mp,
                "bot:{$this->id}:strength" => $this->strength,
                "bot:{$this->id}:intelligence" => $this->intelligence,
                "bot:{$this->id}:dexterity" => $this->dexterity,
                "bot:{$this->id}:armor" => $this->armor,
                "bot:{$this->id}:magic_resistance" => $this->magic_resistance,
                "bot:{$this->id}:is_active" => $this->is_active ? 1 : 0,
                "bot:{$this->id}:location" => $this->location,
                "bot:{$this->id}:last_sync" => now()->timestamp,
            ];

            // Используем pipeline для эффективной записи
            $pipeline = \Illuminate\Support\Facades\Redis::pipeline();

            foreach ($botData as $field => $value) {
                $pipeline->hset($botsKey, $field, $value);
            }

            // Устанавливаем TTL для ключа ботов (24 часа)
            $pipeline->expire($botsKey, 86400);

            $pipeline->exec();

            Log::info("Bot::syncToRedis: Успешно синхронизирован бот {$this->name} с Redis", [
                'bot_id' => $this->id,
                'location_id' => $locationId,
                'strength' => $this->strength,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Bot::syncToRedis: Ошибка синхронизации бота {$this->id} с Redis: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Извлекает ID локации из строки location
     *
     * @param string $location
     * @return int|null
     */
    protected function extractLocationId(string $location): ?int
    {
        // ИСПРАВЛЕНИЕ: Если у бота есть mine_location_id, используем его для Redis
        if ($this->mine_location_id) {
            return $this->mine_location_id;
        }

        // Пытаемся найти ID локации в строке
        if (is_numeric($location)) {
            return (int) $location;
        }

        // Ищем числа в строке
        if (preg_match('/(\d+)/', $location, $matches)) {
            return (int) $matches[1];
        }

        // Сначала пытаемся найти подлокацию рудника по имени
        try {
            $mineLocation = \App\Models\MineLocation::where('name', $location)->first();
            if ($mineLocation) {
                return $mineLocation->id;
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки поиска
        }

        // Затем пытаемся найти локацию в основной таблице locations
        try {
            $mainLocation = \App\Models\Location::where('name', $location)->first();
            if ($mainLocation) {
                return $mainLocation->id;
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки поиска
        }

        // Как последняя попытка - ищем похожую локацию в mine_locations (для совместимости)
        try {
            $mineLocation = \App\Models\MineLocation::where('name', 'LIKE', '%' . trim(str_replace('(Рудник)', '', $location)) . '%')->first();
            if ($mineLocation) {
                return $mineLocation->id;
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки поиска
        }

        return null;
    }

    /**
     * Получение списка потенциальных игроков-целей в локации с кешированием
     */
    public function getPlayerTargets()
    {
        $enemyRace = $this->getEnemyRace();
        $cacheKey = "player_targets_{$this->location}_{$enemyRace}";

        return Cache::remember($cacheKey, 30, function () use ($enemyRace) {
            return User::with(['profile', 'statistics'])
                ->whereHas('profile', function ($query) use ($enemyRace) {
                    $query->whereRaw('LOWER(race) = ?', [strtolower($enemyRace)])
                        ->where('current_hp', '>', 0);
                })
                ->whereHas('statistics', function ($query) {
                    $query->where('current_location', $this->location);
                })
                ->get();
        });
    }

    /**
     * Проверка, находится ли цель в пределах досягаемости для атаки
     */
    public function isTargetInRange($target)
    {
        // По умолчанию считаем, что все цели в локации в пределах досягаемости
        return true;
    }

    /**
     * Рассчитывает базовый урон бота на основе его силы
     * Формула: урон = сила бота (простая формула согласно требованиям)
     *
     * @return int Базовый урон бота
     */
    public function calculateBaseDamage(): int
    {
        // Согласно требованиям: "Урон бота это Сила вот и все"
        return $this->strength;
    }

    /**
     * Обновляет базовый урон бота и сохраняет в базе данных
     * Используется при создании/редактировании бота через админ-панель
     *
     * @return void
     */
    public function updateBaseDamage(): void
    {
        $this->base_damage = $this->calculateBaseDamage();
        $this->save();
    }

    /**
     * Получение оставшегося времени до следующей возможной атаки
     */
    public function getRemainingAttackCooldown()
    {
        if (!$this->last_attack_time) {
            return 0;
        }

        $lastAttack = Carbon::parse($this->last_attack_time);
        $now = Carbon::now();

        // Предположим, что интервал атаки составляет 5 секунд
        $attackInterval = 5;

        $secondsSinceLastAttack = $now->diffInSeconds($lastAttack);
        $remainingCooldown = max(0, $attackInterval - $secondsSinceLastAttack);

        return $remainingCooldown;
    }

    /**
     * Получение актуальных ресурсов бота (HP/MP) с учетом времени последней регенерации
     *
     * Этот метод рассчитывает текущее HP и MP бота на лету, исходя из времени последней регенерации и скорости восстановления.
     * Используется для всех боевых расчетов, отображения и логики, чтобы всегда работать с реальным состоянием бота.
     * Не сохраняет значения в базу, а только возвращает их для использования в логике.
     * После любого изменения HP/MP обязательно обновляйте last_regeneration_at!
     *
     * @param Carbon|null $now Текущее время (если не указано, берется текущее системное)
     * @return array Массив с ключами: 'current_hp', 'current_mp', 'is_hp_full', 'is_mp_full', 'seconds_elapsed'
     */
    public function getActualResources(?Carbon $now = null): array
    {
        // Если время не передано, используем текущее системное
        $now = $now ?: Carbon::now(); // Carbon - удобная библиотека для работы с датой и временем

        // Получаем время последней регенерации из базы (или текущее, если его нет)
        $lastRegen = $this->last_regeneration_at ? Carbon::parse($this->last_regeneration_at) : $now;

        // Считаем, сколько секунд прошло с последней регенерации
        $secondsElapsed = $lastRegen->diffInSeconds($now);

        // Рассчитываем скорость восстановления на основе уровня бота
        $baseRecovery = max(1, $this->level * 0.5); // Базовая скорость восстановления

        // Если прошло мало времени, возвращаем текущие значения
        if ($secondsElapsed < 1) {
            return [
                'current_hp' => $this->hp,
                'current_mp' => $this->mp,
                'is_hp_full' => $this->hp >= $this->max_hp,
                'is_mp_full' => $this->mp >= $this->max_mp,
                'seconds_elapsed' => $secondsElapsed,
            ];
        }

        // Считаем, сколько HP и MP должно было восстановиться за прошедшее время
        $hpRegenPerSecond = $baseRecovery / 180; // HP восстанавливается медленнее
        $mpRegenPerSecond = $baseRecovery / 60;  // MP восстанавливается быстрее

        $hpToRegen = $hpRegenPerSecond * $secondsElapsed; // Сколько HP восстановилось бы
        $mpToRegen = $mpRegenPerSecond * $secondsElapsed; // Сколько MP восстановилось бы

        // Новые значения HP/MP не могут превышать максимальные значения
        $actualHp = min($this->max_hp, $this->hp + $hpToRegen);
        $actualMp = min($this->max_mp, $this->mp + $mpToRegen);

        // Проверяем, заполнены ли HP/MP полностью
        $isHpFull = $actualHp >= $this->max_hp;
        $isMpFull = $actualMp >= $this->max_mp;

        // Возвращаем массив с актуальными значениями (без сохранения в базу)
        return [
            'current_hp' => (int) round($actualHp),
            'current_mp' => (int) round($actualMp),
            'is_hp_full' => $isHpFull,
            'is_mp_full' => $isMpFull,
            'seconds_elapsed' => $secondsElapsed,
        ];
    }
}

<?php

require __DIR__ . '/vendor/autoload.php';

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Models\MineLocation;

echo "🎯 ТЕСТ ОТОБРАЖЕНИЯ ДЕБАФА РУДНИКОВ\n";
echo "=================================\n\n";

// Проверяем пользователя admin
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "✅ Пользователь найден: {$user->name} (ID: {$user->id})\n\n";

// Проверяем активные эффекты в active_effects
echo "📋 АКТИВНЫЕ ЭФФЕКТЫ В ТАБЛИЦЕ active_effects:\n";
$activeEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('ends_at', '>', now())
    ->get();

if ($activeEffects->count() > 0) {
    foreach ($activeEffects as $effect) {
        echo "   - {$effect->effect_type}: {$effect->effect_name} (до {$effect->ends_at})\n";
    }
} else {
    echo "   ❌ Нет активных эффектов в active_effects\n";
}

echo "\n📋 АКТИВНЫЕ МЕТКИ В ТАБЛИЦЕ mine_marks:\n";
$mineMarks = MineMark::where('player_id', $user->id)
    ->where('is_active', true)
    ->where('expires_at', '>', now())
    ->get();

if ($mineMarks->count() > 0) {
    foreach ($mineMarks as $mark) {
        echo "   - Замечен в {$mark->location_name} (до {$mark->expires_at})\n";
        echo "     Mine Location ID: {$mark->mine_location_id}\n";
        echo "     Атак: {$mark->attack_count}\n";
    }
} else {
    echo "   ❌ Нет активных меток в mine_marks\n";
}

echo "\n🔧 СОЗДАЕМ ТЕСТОВУЮ МЕТКУ:\n";
$mineLocation = MineLocation::first();
if (!$mineLocation) {
    echo "❌ Нет локаций рудников\n";
    exit(1);
}

$testMark = MineMark::create([
    'player_id' => $user->id,
    'mine_location_id' => $mineLocation->id,
    'location_id' => $mineLocation->location_id ?? 1,
    'location_name' => $mineLocation->name,
    'expires_at' => now()->addMinutes(5),
    'is_active' => true,
    'attack_count' => 0
]);

echo "✅ Создана метка ID: {$testMark->id}\n";

// Проверяем отображение через модель User
echo "\n👤 АКТИВНЫЕ ЭФФЕКТЫ ЧЕРЕЗ User::activeEffects():\n";
$userEffects = $user->activeEffects()
    ->where('ends_at', '>', now())
    ->get();

if ($userEffects->count() > 0) {
    foreach ($userEffects as $effect) {
        echo "   - {$effect->effect_type}: {$effect->effect_name}\n";
    }
} else {
    echo "   ❌ Нет активных эффектов через User::activeEffects()\n";
}

echo "\n🎯 ПРОБЛЕМА:\n";
echo "   MineMark создается в таблице mine_marks\n";
echo "   Но User::activeEffects() смотрит в active_effects\n";
echo "   Нужно либо:\n";
echo "   1. Создавать запись в active_effects при создании MineMark\n";
echo "   2. Или добавить метод User::getMineMarks() в отображение\n";

// Очищаем тестовую метку
$testMark->delete();
echo "\n🧹 Тестовая метка удалена\n";
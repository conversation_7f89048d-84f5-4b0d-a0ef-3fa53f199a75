<?php

/**
 * Тестовый скрипт для проверки системы автоатак мобов в рудниках
 * 
 * Этот скрипт проверяет:
 * 1. Создание меток "Замечен" при добыче ресурсов
 * 2. Автоатаки мобов на замеченных игроков
 * 3. Очистку истекших меток
 * 
 * Запуск: php test_mine_detection_system.php
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Services\MineDetectionService;
use App\Jobs\MineAutoAttackJob;

echo "🧪 ТЕСТИРОВАНИЕ СИСТЕМЫ АВТОАТАК МОБОВ В РУДНИКАХ\n";
echo "================================================\n\n";

try {
    // 1. Проверяем наличие необходимых данных
    echo "1️⃣ Проверка данных в базе...\n";
    
    $user = User::with('profile')->first();
    if (!$user) {
        throw new Exception("❌ Не найдено пользователей в базе данных");
    }
    echo "   ✅ Найден пользователь: {$user->name} (ID: {$user->id})\n";
    
    $mineLocation = MineLocation::first();
    if (!$mineLocation) {
        throw new Exception("❌ Не найдено локаций рудников в базе данных");
    }
    echo "   ✅ Найдена локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    
    $mobs = Mob::where('location_id', $mineLocation->location_id)
        ->where('mine_location_id', $mineLocation->id)
        ->get();
    echo "   ℹ️  Найдено мобов в руднике: " . $mobs->count() . "\n";
    
    // 2. Тестируем MineDetectionService
    echo "\n2️⃣ Тестирование MineDetectionService...\n";
    
    $mineDetectionService = app(MineDetectionService::class);
    
    // Очищаем старые метки
    $cleaned = $mineDetectionService->cleanupExpiredMarks();
    echo "   🧹 Очищено старых меток: {$cleaned}\n";
    
    // Проверяем наличие активных меток до создания
    $hasMarkBefore = $mineDetectionService->hasActiveMark(
        $user->id, 
        $mineLocation->location_id, 
        $mineLocation->id
    );
    echo "   📊 Активная метка до создания: " . ($hasMarkBefore ? 'Да' : 'Нет') . "\n";
    
    // Создаем метку обнаружения
    echo "   🎯 Создание метки обнаружения...\n";
    $mark = $mineDetectionService->createMark($user, $mineLocation, 120); // 2 минуты
    echo "   ✅ Создана метка с ID: {$mark->id}\n";
    echo "   ⏰ Истекает в: {$mark->expires_at}\n";
    
    // Проверяем наличие активных меток после создания
    $hasMarkAfter = $mineDetectionService->hasActiveMark(
        $user->id, 
        $mineLocation->location_id, 
        $mineLocation->id
    );
    echo "   📊 Активная метка после создания: " . ($hasMarkAfter ? 'Да' : 'Нет') . "\n";
    
    // Получаем всех замеченных игроков
    $markedPlayers = $mineDetectionService->getMarkedPlayersInMine(
        $mineLocation->location_id, 
        $mineLocation->id
    );
    echo "   👥 Замеченных игроков в руднике: " . count($markedPlayers) . "\n";
    
    // 3. Тестируем MineAutoAttackJob
    echo "\n3️⃣ Тестирование MineAutoAttackJob...\n";
    
    if (count($markedPlayers) > 0) {
        echo "   🚀 Запуск задачи автоатак...\n";
        
        // Запускаем джоб синхронно для тестирования
        $job = new MineAutoAttackJob();
        $job->handle(
            $mineDetectionService,
            app(\App\Services\BattleLogService::class),
            app(\App\Services\PlayerHealthService::class),
            app(\App\Services\CombatFormulaService::class),
            app(\App\Services\LogFormattingService::class)
        );
        
        echo "   ✅ Джоб выполнен успешно\n";
        
        // Проверяем обновление времени последней атаки
        $markAfterAttack = $mineDetectionService->getActiveMark($user->id, $mineLocation->id);
        if ($markAfterAttack && $markAfterAttack->last_attack_at) {
            echo "   🎯 Время последней атаки обновлено: {$markAfterAttack->last_attack_at}\n";
        }
    } else {
        echo "   ⚠️  Нет замеченных игроков для тестирования атак\n";
    }
    
    // 4. Тестируем консольную команду
    echo "\n4️⃣ Тестирование консольной команды...\n";
    echo "   🖥️  Запуск команды: php artisan mine:auto-attack\n";
    
    $exitCode = Artisan::call('mine:auto-attack');
    if ($exitCode === 0) {
        echo "   ✅ Команда выполнена успешно\n";
    } else {
        echo "   ❌ Команда завершилась с кодом: {$exitCode}\n";
    }
    
    // 5. Проверяем интеграцию с hitResource
    echo "\n5️⃣ Проверка интеграции с hitResource...\n";
    
    // Симулируем добычу ресурса (вызов метода из MinesController)
    echo "   ⛏️  Симуляция добычи ресурса...\n";
    $detectionMark = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
    
    if ($detectionMark) {
        echo "   ✅ Дебаф успешно применен при добыче ресурса\n";
        echo "   🆔 ID метки: {$detectionMark->id}\n";
    } else {
        echo "   ❌ Не удалось применить дебаф при добыче ресурса\n";
    }
    
    // 6. Финальная проверка системы
    echo "\n6️⃣ Финальная проверка системы...\n";
    
    $allMarks = $mineDetectionService->getMarkedPlayersInMine(
        $mineLocation->location_id, 
        $mineLocation->id
    );
    echo "   📊 Всего активных меток в руднике: " . count($allMarks) . "\n";
    
    foreach ($allMarks as $playerData) {
        echo "   👤 Игрок: {$playerData['player_name']} (ID: {$playerData['player_id']})\n";
        echo "      ⏰ Истекает через: " . ($playerData['remaining_seconds'] ?? 0) . " секунд\n";
        echo "      🎯 Количество атак: " . ($playerData['attack_count'] ?? 0) . "\n";
    }
    
    // 7. Тест очистки
    echo "\n7️⃣ Тестирование очистки меток...\n";
    
    // Создаем истекшую метку для тестирования очистки
    $expiredMark = $mineDetectionService->createMark($user, $mineLocation, -10); // Истекшая метка
    echo "   📋 Создана истекшая метка для тестирования очистки\n";
    
    $cleanedFinal = $mineDetectionService->cleanupExpiredMarks();
    echo "   🧹 Очищено истекших меток: {$cleanedFinal}\n";
    
    echo "\n✅ ВСЕ ТЕСТЫ ЗАВЕРШЕНЫ УСПЕШНО!\n\n";
    
    echo "📋 РЕЗЮМЕ ТЕСТИРОВАНИЯ:\n";
    echo "=====================\n";
    echo "✅ MineDetectionService работает корректно\n";
    echo "✅ Метки создаются и отслеживаются правильно\n";
    echo "✅ MineAutoAttackJob выполняется без ошибок\n";
    echo "✅ Консольная команда mine:auto-attack функциональна\n";
    echo "✅ Интеграция с добычей ресурсов работает\n";
    echo "✅ Система очистки меток функционирует\n\n";
    
    echo "🚀 СИСТЕМА ГОТОВА К РАБОТЕ!\n";
    echo "💡 Для запуска в продакшн используйте планировщик Laravel:\n";
    echo "   php artisan schedule:work\n\n";
    
} catch (Exception $e) {
    echo "\n❌ ОШИБКА ПРИ ТЕСТИРОВАНИИ: " . $e->getMessage() . "\n";
    echo "📍 Файл: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "📜 Трассировка:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
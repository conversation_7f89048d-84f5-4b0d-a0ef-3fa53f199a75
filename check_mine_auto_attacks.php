<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\MineMark;
use App\Services\MineDetectionService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "⚔️ ПРОВЕРКА СИСТЕМЫ АВТОАТАК В РУДНИКАХ\n";
echo "=" . str_repeat("=", 45) . "\n\n";

// 1. Проверяем пользователя и локацию
$user = User::where('name', 'admin')->first();
$mineLocation = MineLocation::where('name', 'аааааааааааа')->first();

if (!$user || !$mineLocation) {
    echo "❌ Пользователь или локация не найдены!\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
echo "✅ Локация: {$mineLocation->name} (ID: {$mineLocation->id})\n";
echo "   Основная локация: {$mineLocation->location_id}\n\n";

// 2. Проверяем активные метки
echo "1️⃣ Проверка активных меток...\n";
$activeMarks = MineMark::where('is_active', true)
    ->where('expires_at', '>', now())
    ->with(['player', 'mineLocation'])
    ->get();

echo "Активных меток: {$activeMarks->count()}\n";
foreach ($activeMarks as $mark) {
    $playerName = $mark->player->name ?? 'Unknown';
    $mineName = $mark->mineLocation->name ?? 'Unknown';
    $timeRemaining = $mark->expires_at->diffInSeconds(now());
    echo "- Игрок: {$playerName}, Рудник: {$mineName}, Осталось: {$timeRemaining}с\n";
}

// 3. Проверяем мобов в локации
echo "\n2️⃣ Проверка мобов в локации...\n";
$mobsInLocation = Mob::where('location_id', $mineLocation->location_id)
    ->where('hp', '>', 0)
    ->get();

echo "Мобов в локации {$mineLocation->location_id}: {$mobsInLocation->count()}\n";
foreach ($mobsInLocation as $mob) {
    echo "- {$mob->name} (ID: {$mob->id}), HP: {$mob->hp}/{$mob->max_hp}\n";
}

// 4. Проверяем сервис обнаружения
echo "\n3️⃣ Проверка сервиса обнаружения...\n";
$mineDetectionService = app(MineDetectionService::class);

$markedPlayers = $mineDetectionService->getMarkedPlayersInMine(
    $mineLocation->location_id,
    $mineLocation->id
);

echo "Замеченных игроков в руднике: " . count($markedPlayers) . "\n";
foreach ($markedPlayers as $playerData) {
    echo "- Игрок ID: {$playerData['player_id']}, Имя: {$playerData['player_name']}\n";
    echo "  Метка истекает: {$playerData['expires_at']}\n";
}

// 5. Проверяем, может ли моб атаковать игрока
echo "\n4️⃣ Проверка возможности атаки...\n";
if ($mobsInLocation->count() > 0 && count($markedPlayers) > 0) {
    $mob = $mobsInLocation->first();
    $playerData = $markedPlayers[0];

    echo "Моб: {$mob->name} (ID: {$mob->id})\n";
    echo "Игрок: {$playerData['player_name']} (ID: {$playerData['player_id']})\n";
    echo "Моб может атаковать: " . ($mob->hp > 0 ? 'Да' : 'Нет') . "\n";
    echo "Игрок замечен: Да\n";

    // Проверяем, есть ли у игрока цель
    $targetPlayer = User::find($playerData['player_id']);
    if ($targetPlayer) {
        echo "Текущая цель игрока: " . ($targetPlayer->current_target_type ?? 'Нет') . "\n";
        echo "ID цели: " . ($targetPlayer->current_target_id ?? 'Нет') . "\n";
    }
} else {
    echo "❌ Нет мобов или замеченных игроков для атаки\n";
}

// 6. Проверяем планировщик задач
echo "\n5️⃣ Информация о планировщике...\n";
echo "Для автоатак должен быть запущен планировщик:\n";
echo "   php artisan schedule:work\n";
echo "Или настроен cron для:\n";
echo "   php artisan schedule:run\n";

echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";

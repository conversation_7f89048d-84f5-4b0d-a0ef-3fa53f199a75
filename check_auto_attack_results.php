<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Services\BattleLogService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 ПРОВЕРКА РЕЗУЛЬТАТОВ АВТОАТАКИ\n";
echo "=" . str_repeat("=", 35) . "\n\n";

// 1. Проверяем пользователя
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";

// 2. Проверяем HP пользователя
$userProfile = $user->profile;
if ($userProfile) {
    echo "💚 HP: {$userProfile->current_hp}/{$userProfile->max_hp}\n";
    echo "💙 MP: {$userProfile->current_mp}/{$userProfile->max_mp}\n";
} else {
    echo "❌ Профиль пользователя не найден!\n";
}

// 3. Проверяем активные метки
echo "\n1️⃣ Проверка активных меток...\n";
$activeMarks = MineMark::where('player_id', $user->id)
    ->where('is_active', true)
    ->where('expires_at', '>', now())
    ->get();

echo "Активных меток: {$activeMarks->count()}\n";
foreach ($activeMarks as $mark) {
    echo "- ID: {$mark->id}, Рудник: {$mark->mine_location_id}\n";
    echo "  Последняя атака: " . ($mark->last_attack_at ?? 'Никогда') . "\n";
    echo "  Количество атак: {$mark->attack_count}\n";
    echo "  Истекает: {$mark->expires_at}\n\n";
}

// 4. Проверяем активные эффекты
echo "2️⃣ Проверка активных эффектов...\n";
$activeEffects = ActiveEffect::where('target_type', 'player')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->get();

echo "Эффектов mine_detection: {$activeEffects->count()}\n";
foreach ($activeEffects as $effect) {
    $isActive = $effect->isActive();
    echo "- ID: {$effect->id}, Активен: " . ($isActive ? 'Да' : 'Нет') . "\n";
    echo "  Оставшееся время: {$effect->remaining_duration}с\n";
    echo "  Истекает: {$effect->ends_at}\n\n";
}

// 5. Проверяем журнал боя
echo "3️⃣ Проверка журнала боя...\n";
$battleLogService = app(BattleLogService::class);
$battleLogKey = "battle_log_location_137"; // Локация аааааааааааа

try {
    $battleLogs = $battleLogService->getLogs($battleLogKey, 10);
    echo "Записей в журнале боя: " . count($battleLogs) . "\n";
    
    if (count($battleLogs) > 0) {
        echo "Последние записи:\n";
        foreach (array_slice($battleLogs, -5) as $log) {
            echo "- " . date('H:i:s', $log['timestamp']) . ": {$log['message']}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Ошибка при получении журнала боя: {$e->getMessage()}\n";
}

// 6. Проверяем цель игрока
echo "\n4️⃣ Проверка цели игрока...\n";
echo "Текущая цель: " . ($user->current_target_type ?? 'Нет') . "\n";
echo "ID цели: " . ($user->current_target_id ?? 'Нет') . "\n";

if ($user->current_target_type && $user->current_target_id) {
    if ($user->current_target_type === 'mob') {
        $mob = \App\Models\Mob::find($user->current_target_id);
        if ($mob) {
            echo "Цель: {$mob->name} (HP: {$mob->hp}/{$mob->max_hp})\n";
        }
    }
}

echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";

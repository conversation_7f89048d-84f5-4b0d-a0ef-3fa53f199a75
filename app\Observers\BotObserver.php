<?php

namespace App\Observers;

use App\Models\Bot;
use App\Services\Redis\RedisMineService;
use App\Services\Redis\RedisMineBotService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

/**
 * Observer для модели Bot
 * Автоматически синхронизирует изменения ботов с Redis
 */
class BotObserver
{
    /**
     * Сервис для работы с Redis в рудниках
     */
    protected RedisMineService $redisMineService;

    /**
     * Сервис для работы с ботами в Redis
     */
    protected RedisMineBotService $redisMineBotService;

    public function __construct(
        RedisMineService $redisMineService,
        RedisMineBotService $redisMineBotService
    ) {
        $this->redisMineService = $redisMineService;
        $this->redisMineBotService = $redisMineBotService;
    }

    /**
     * Обработка события "created" модели Bot
     * Синхронизирует нового бота с Redis
     *
     * @param Bot $bot
     * @return void
     */
    public function created(Bot $bot): void
    {
        Log::info("BotObserver: Создан новый бот {$bot->name} (ID: {$bot->id})");
        $this->syncBotToRedis($bot);
    }

    /**
     * Обработка события "updated" модели Bot
     * Синхронизирует изменения бота с Redis
     *
     * @param Bot $bot
     * @return void
     */
    public function updated(Bot $bot): void
    {
        // Проверяем, изменились ли важные атрибуты
        $importantFields = [
            'strength',
            'intelligence',
            'dexterity',
            'armor',
            'magic_resistance',
            'hp',
            'max_hp',
            'mp',
            'max_mp',
            'level',
            'is_active',
            'location'
        ];

        $hasImportantChanges = false;
        $changedFields = [];

        foreach ($importantFields as $field) {
            if ($bot->isDirty($field) || $bot->wasChanged($field)) {
                $hasImportantChanges = true;
                $changedFields[] = $field;
            }
        }

        if ($hasImportantChanges) {
            Log::info("BotObserver: Обнаружены изменения бота {$bot->name} (ID: {$bot->id})", [
                'changed_fields' => $changedFields,
                'old_strength' => $bot->getOriginal('strength'),
                'new_strength' => $bot->strength,
            ]);

            $this->syncBotToRedis($bot);
        }
    }

    /**
     * Обработка события "deleted" модели Bot
     * Удаляет бота из Redis
     *
     * @param Bot $bot
     * @return void
     */
    public function deleted(Bot $bot): void
    {
        Log::info("BotObserver: Удален бот {$bot->name} (ID: {$bot->id})");
        $this->removeBotFromRedis($bot);
    }

    /**
     * Синхронизирует данные бота с Redis
     *
     * @param Bot $bot
     * @return void
     */
    protected function syncBotToRedis(Bot $bot): void
    {
        try {
            // Определяем локацию бота для синхронизации
            $locationId = $this->extractLocationId($bot->location, $bot);

            if (!$locationId) {
                Log::warning("BotObserver: Не удалось определить ID локации для бота {$bot->id}, location: {$bot->location}");
                return;
            }

            // Синхронизируем конкретного бота
            $this->syncSpecificBotToRedis($bot, $locationId);

            Log::info("BotObserver: Успешно синхронизирован бот {$bot->name} с Redis", [
                'bot_id' => $bot->id,
                'location_id' => $locationId,
                'strength' => $bot->strength,
            ]);

        } catch (\Exception $e) {
            Log::error("BotObserver: Ошибка синхронизации бота {$bot->id} с Redis: " . $e->getMessage());
        }
    }

    /**
     * Синхронизирует конкретного бота с Redis
     *
     * @param Bot $bot
     * @param int $locationId
     * @return void
     */
    protected function syncSpecificBotToRedis(Bot $bot, int $locationId): void
    {
        // Используем ключ без префикса, так как Redis автоматически добавляет префикс
        $botsKey = "mine_location:{$locationId}:bots";

        // Подготавливаем данные бота для Redis
        $botData = [
            "bot:{$bot->id}:id" => $bot->id,
            "bot:{$bot->id}:name" => $bot->name,
            "bot:{$bot->id}:race" => $bot->race,
            "bot:{$bot->id}:class" => $bot->class,
            "bot:{$bot->id}:level" => $bot->level,
            "bot:{$bot->id}:hp" => $bot->hp,
            "bot:{$bot->id}:max_hp" => $bot->max_hp,
            "bot:{$bot->id}:mp" => $bot->mp,
            "bot:{$bot->id}:max_mp" => $bot->max_mp,
            "bot:{$bot->id}:strength" => $bot->strength,
            "bot:{$bot->id}:intelligence" => $bot->intelligence,
            "bot:{$bot->id}:dexterity" => $bot->dexterity,
            "bot:{$bot->id}:armor" => $bot->armor,
            "bot:{$bot->id}:magic_resistance" => $bot->magic_resistance,
            "bot:{$bot->id}:is_active" => $bot->is_active ? 1 : 0,
            "bot:{$bot->id}:location" => $bot->location,
            "bot:{$bot->id}:last_sync" => now()->timestamp,
        ];

        // Используем pipeline для эффективной записи
        $pipeline = Redis::pipeline();

        foreach ($botData as $field => $value) {
            $pipeline->hset($botsKey, $field, $value);
        }

        // Устанавливаем TTL для ключа ботов (24 часа)
        $pipeline->expire($botsKey, 86400);

        $pipeline->exec();
    }

    /**
     * Удаляет бота из Redis
     *
     * @param Bot $bot
     * @return void
     */
    protected function removeBotFromRedis(Bot $bot): void
    {
        try {
            $locationId = $this->extractLocationId($bot->location, $bot);

            if (!$locationId) {
                return;
            }

            $botsKey = "mine_location:{$locationId}:bots";

            // Получаем все поля бота для удаления
            $botFields = [
                "bot:{$bot->id}:id",
                "bot:{$bot->id}:name",
                "bot:{$bot->id}:race",
                "bot:{$bot->id}:class",
                "bot:{$bot->id}:level",
                "bot:{$bot->id}:hp",
                "bot:{$bot->id}:max_hp",
                "bot:{$bot->id}:mp",
                "bot:{$bot->id}:max_mp",
                "bot:{$bot->id}:strength",
                "bot:{$bot->id}:intelligence",
                "bot:{$bot->id}:dexterity",
                "bot:{$bot->id}:armor",
                "bot:{$bot->id}:magic_resistance",
                "bot:{$bot->id}:is_active",
                "bot:{$bot->id}:location",
                "bot:{$bot->id}:last_sync"
            ];

            // Удаляем все поля бота из Redis
            Redis::hdel($botsKey, ...$botFields);

            Log::info("BotObserver: Удален бот {$bot->id} из Redis");

        } catch (\Exception $e) {
            Log::error("BotObserver: Ошибка удаления бота {$bot->id} из Redis: " . $e->getMessage());
        }
    }

    /**
     * Извлекает ID локации из строки location
     *
     * @param string $location
     * @return int|null
     */
    protected function extractLocationId(string $location, Bot $bot = null): ?int
    {
        // ИСПРАВЛЕНИЕ: Если у бота есть mine_location_id, используем его для Redis
        if ($bot && $bot->mine_location_id) {
            return $bot->mine_location_id;
        }

        // Пытаемся найти ID локации в строке
        if (is_numeric($location)) {
            return (int) $location;
        }

        // Ищем числа в строке
        if (preg_match('/(\d+)/', $location, $matches)) {
            return (int) $matches[1];
        }

        // Сначала пытаемся найти подлокацию рудника по имени
        try {
            $mineLocation = \App\Models\MineLocation::where('name', $location)->first();
            if ($mineLocation) {
                return $mineLocation->id;
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки поиска
        }

        // Затем пытаемся найти локацию в основной таблице locations
        try {
            $mainLocation = \App\Models\Location::where('name', $location)->first();
            if ($mainLocation) {
                return $mainLocation->id;
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки поиска
        }

        // Как последняя попытка - ищем похожую локацию в mine_locations (для совместимости)
        try {
            $mineLocation = \App\Models\MineLocation::where('name', 'LIKE', '%' . trim(str_replace('(Рудник)', '', $location)) . '%')->first();
            if ($mineLocation) {
                return $mineLocation->id;
            }
        } catch (\Exception $e) {
            // Игнорируем ошибки поиска
        }

        return null;
    }
}

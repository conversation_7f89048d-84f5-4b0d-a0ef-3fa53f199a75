<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\SpawnedResource;
use App\Models\Mob;
use App\Models\Bot;
use Illuminate\Contracts\Auth\Authenticatable;

/**
 * Сервис для управления сбросом целей в рудниках при переходах между подлокациями
 */
class MineTargetResetService
{
    /**
     * Проверить и сбросить цели игрока при переходе между подлокациями рудника
     *
     * @param Authenticatable $user Пользователь
     * @param MineLocation $newMineLocation Новая подлокация рудника
     * @param MineLocation|null $previousMineLocation Предыдущая подлокация рудника (если известна)
     * @return bool Был ли выполнен сброс целей
     */
    public function checkAndResetTargetsOnMineLocationChange(
        Authenticatable $user,
        MineLocation $newMineLocation,
        ?MineLocation $previousMineLocation = null
    ): bool {
        // Если у пользователя нет активной цели, сброс не нужен
        if (!$user->current_target_type || !$user->current_target_id) {
            Log::debug("MineTargetReset: У пользователя {$user->id} нет активной цели", [
                'user_id' => $user->id,
                'new_location' => $newMineLocation->name,
                'new_location_id' => $newMineLocation->id
            ]);
            return false;
        }

        // Определяем предыдущую подлокацию из сессии, если не передана
        if (!$previousMineLocation) {
            $previousMineLocation = $this->getPreviousMineLocationFromSession($user);
        }

        // Если не удалось определить предыдущую локацию, сбрасываем цель для безопасности
        if (!$previousMineLocation) {
            Log::info("MineTargetReset: Не удалось определить предыдущую подлокацию, сбрасываем цель", [
                'user_id' => $user->id,
                'current_target_type' => $user->current_target_type,
                'current_target_id' => $user->current_target_id,
                'new_location' => $newMineLocation->name
            ]);
            return $this->resetUserTargets($user, 'Предыдущая подлокация не определена');
        }

        // Если это переход в ту же подлокацию, сброс не нужен
        if ($newMineLocation->id === $previousMineLocation->id) {
            Log::debug("MineTargetReset: Переход в ту же подлокацию, сброс не нужен", [
                'user_id' => $user->id,
                'location_id' => $newMineLocation->id,
                'location_name' => $newMineLocation->name
            ]);
            return false;
        }

        // Проверяем, доступна ли текущая цель в новой подлокации
        $targetAvailableInNewLocation = $this->isTargetAvailableInMineLocation(
            $user->current_target_type,
            $user->current_target_id,
            $newMineLocation
        );

        if (!$targetAvailableInNewLocation) {
            Log::info("MineTargetReset: Цель недоступна в новой подлокации, выполняем сброс", [
                'user_id' => $user->id,
                'target_type' => $user->current_target_type,
                'target_id' => $user->current_target_id,
                'previous_location' => $previousMineLocation->name,
                'new_location' => $newMineLocation->name
            ]);
            
            return $this->resetUserTargets(
                $user,
                "Переход из '{$previousMineLocation->name}' в '{$newMineLocation->name}'"
            );
        }

        Log::debug("MineTargetReset: Цель доступна в новой подлокации, сброс не нужен", [
            'user_id' => $user->id,
            'target_type' => $user->current_target_type,
            'target_id' => $user->current_target_id,
            'location' => $newMineLocation->name
        ]);

        return false;
    }

    /**
     * Проверить, доступна ли цель в указанной подлокации рудника
     *
     * @param string $targetType Тип цели (resource, mob, bot, player)
     * @param int $targetId ID цели
     * @param MineLocation $mineLocation Подлокация рудника
     * @return bool Доступна ли цель в данной подлокации
     */
    public function isTargetAvailableInMineLocation(string $targetType, int $targetId, MineLocation $mineLocation): bool
    {
        switch ($targetType) {
            case 'resource':
                return $this->isResourceAvailableInMineLocation($targetId, $mineLocation);
            
            case 'mob':
                return $this->isMobAvailableInMineLocation($targetId, $mineLocation);
            
            case 'bot':
                return $this->isBotAvailableInMineLocation($targetId, $mineLocation);
            
            case 'player':
                // ИСПРАВЛЕНО: Проверяем, находится ли игрок в той же подлокации
                return $this->isPlayerAvailableInMineLocation($targetId, $mineLocation);
            
            default:
                Log::warning("MineTargetReset: Неизвестный тип цели", [
                    'target_type' => $targetType,
                    'target_id' => $targetId
                ]);
                return false;
        }
    }

    /**
     * Проверить доступность ресурса в подлокации рудника
     */
    protected function isResourceAvailableInMineLocation(int $resourceId, MineLocation $mineLocation): bool
    {
        if ($mineLocation->isSubLocation()) {
            // Для подлокации проверяем ресурсы с mine_location_id
            $resource = SpawnedResource::where('id', $resourceId)
                ->where('mine_location_id', $mineLocation->id)
                ->where('is_active', true)
                ->where('durability', '>', 0)
                ->first();
        } else {
            // Для основной локации проверяем ресурсы с location_id и mine_location_id = NULL
            $baseLocation = $mineLocation->baseLocation;
            $resource = SpawnedResource::where('id', $resourceId)
                ->where('location_id', $baseLocation->id)
                ->whereNull('mine_location_id')
                ->where('is_active', true)
                ->where('durability', '>', 0)
                ->first();
        }

        return $resource !== null;
    }

    /**
     * Проверить доступность моба в подлокации рудника
     */
    protected function isMobAvailableInMineLocation(int $mobId, MineLocation $mineLocation): bool
    {
        if ($mineLocation->isSubLocation()) {
            // Для подлокации проверяем мобов с mine_location_id
            $mob = Mob::where('id', $mobId)
                ->where('mine_location_id', $mineLocation->id)
                ->where('hp', '>', 0)
                ->first();
        } else {
            // Для основной локации проверяем мобов по названию локации
            $mob = Mob::where('id', $mobId)
                ->where('location', $mineLocation->name)
                ->where('hp', '>', 0)
                ->first();
        }

        return $mob !== null;
    }

    /**
     * Проверить доступность бота в подлокации рудника
     */
    protected function isBotAvailableInMineLocation(int $botId, MineLocation $mineLocation): bool
    {
        if ($mineLocation->isSubLocation()) {
            // Для подлокации проверяем ботов с mine_location_id
            $bot = Bot::where('id', $botId)
                ->where('mine_location_id', $mineLocation->id)
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->first();
        } else {
            // Для основной локации проверяем ботов по названию локации
            $bot = Bot::where('id', $botId)
                ->where('location', $mineLocation->name)
                ->whereNull('mine_location_id')
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->first();
        }

        return $bot !== null;
    }

    /**
     * Проверить доступность игрока в подлокации рудника
     */
    protected function isPlayerAvailableInMineLocation(int $playerId, MineLocation $mineLocation): bool
    {
        $player = User::find($playerId);
        
        if (!$player || !$player->statistics) {
            return false;
        }

        // Получаем текущую локацию игрока
        $playerCurrentLocation = $player->statistics->current_location;
        
        // Если игрок в той же подлокации - цель доступна
        if ($playerCurrentLocation === $mineLocation->name) {
            return true;
        }

        // Если это основная локация рудника, проверяем, находится ли игрок в любой подлокации этого рудника
        if (!$mineLocation->isSubLocation()) {
            $baseLocationName = $mineLocation->name;
            $sublocations = MineLocation::where('parent_id', $mineLocation->id)
                ->where('is_active', true)
                ->pluck('name')
                ->toArray();
            
            return in_array($playerCurrentLocation, $sublocations) || $playerCurrentLocation === $baseLocationName;
        }

        return false;
    }

    /**
     * Сбросить цели пользователя с логированием причины
     */
    protected function resetUserTargets(Authenticatable $user, string $reason): bool
    {
        try {
            Log::info("MineTargetReset: Сбрасываем цели пользователя", [
                'user_id' => $user->id,
                'reason' => $reason,
                'old_target_type' => $user->current_target_type,
                'old_target_id' => $user->current_target_id
            ]);

            $user->update([
                'current_target_id' => null,
                'current_target_type' => null
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("MineTargetReset: Ошибка при сбросе целей", [
                'user_id' => $user->id,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Получить предыдущую подлокацию рудника из сессии
     */
    protected function getPreviousMineLocationFromSession(Authenticatable $user): ?MineLocation
    {
        // Попытаемся определить предыдущую локацию из статистики пользователя
        $currentLocationName = $user->statistics->current_location ?? null;
        
        if (!$currentLocationName) {
            return null;
        }

        // Ищем подлокацию рудника по названию
        $mineLocation = MineLocation::where('name', $currentLocationName)
            ->where('is_active', true)
            ->first();

        return $mineLocation;
    }

    /**
     * Сохранить текущую подлокацию рудника в сессии для отслеживания переходов
     */
    public function storeMineLocationInSession(MineLocation $mineLocation): void
    {
        session(['current_mine_location_id' => $mineLocation->id]);
        
        Log::debug("MineTargetReset: Сохранена текущая подлокация в сессии", [
            'location_id' => $mineLocation->id,
            'location_name' => $mineLocation->name
        ]);
    }

    /**
     * Получить предыдущую подлокацию рудника из сессии по ID
     */
    public function getPreviousMineLocationFromSessionById(): ?MineLocation
    {
        $locationId = session('current_mine_location_id');
        
        if (!$locationId) {
            return null;
        }

        return MineLocation::where('id', $locationId)
            ->where('is_active', true)
            ->first();
    }
}

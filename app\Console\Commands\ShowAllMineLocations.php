<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;

class ShowAllMineLocations extends Command
{
    protected $signature = 'fix:show-all-mine-locations {--revert : Вернуть к показу только основных локаций}';
    protected $description = 'Временно изменяет фильтр, чтобы показать все локации рудников включая подлокации';

    public function handle()
    {
        $controllerPath = app_path('Http/Controllers/Mines/MinesController.php');
        
        if (!file_exists($controllerPath)) {
            $this->error('Контроллер не найден');
            return 1;
        }

        $content = file_get_contents($controllerPath);

        if ($this->option('revert')) {
            // Возвращаем к оригинальному фильтру
            $content = str_replace(
                '// $customMineLocations = MineLocation::whereNull(\'parent_id\')',
                '$customMineLocations = MineLocation::whereNull(\'parent_id\')',
                $content
            );
            
            $content = str_replace(
                '$customMineLocations = MineLocation::where(\'is_active\', true)',
                '// $customMineLocations = MineLocation::where(\'is_active\', true)',
                $content
            );
            
            $this->info('Фильтр возвращен к показу только основных локаций');
        } else {
            // Изменяем фильтр, чтобы показать все локации
            $content = str_replace(
                '$customMineLocations = MineLocation::whereNull(\'parent_id\')',
                '// $customMineLocations = MineLocation::whereNull(\'parent_id\')',
                $content
            );
            
            $content = str_replace(
                '// $customMineLocations = MineLocation::where(\'is_active\', true)',
                '$customMineLocations = MineLocation::where(\'is_active\', true)',
                $content
            );
            
            // Если замена не произошла, добавляем новый фильтр
            if (strpos($content, '// $customMineLocations = MineLocation::whereNull(\'parent_id\')') === false) {
                $content = str_replace(
                    '$customMineLocations = MineLocation::whereNull(\'parent_id\')',
                    '// Показываем все локации (включая подлокации) для демонстрации пагинации
        $customMineLocations = MineLocation::where(\'is_active\', true)',
                    $content
                );
            }
            
            $this->info('Фильтр изменен для показа всех локаций');
        }

        file_put_contents($controllerPath, $content);

        // Проверяем результат
        $totalLocations = MineLocation::where('is_active', true)->count();
        $mainLocations = MineLocation::whereNull('parent_id')->where('is_active', true)->count();

        $this->info("Всего активных локаций: $totalLocations");
        $this->info("Основных локаций: $mainLocations");

        if ($this->option('revert')) {
            $this->info("Будет отображаться: $mainLocations локаций");
        } else {
            $this->info("Будет отображаться: $totalLocations локаций");
        }

        $this->info('Проверьте: http://127.0.0.1:8000/battle/mines');

        return 0;
    }
}
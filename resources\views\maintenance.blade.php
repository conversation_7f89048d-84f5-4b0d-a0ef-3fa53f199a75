<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Технические работы - Echoes of Eternity</title>
    @vite(['resources/css/app.css', 'resources/css/maintenance.css'])
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
</head>

<body class="maintenance-body">
    {{-- Основной контейнер страницы --}}
    <div class="maintenance-container">
        {{-- Затемненный фон с эффектом размытия --}}
        <div class="maintenance-backdrop"></div>

        {{-- Контейнер модального окна --}}
        <div class="maintenance-modal">
            {{-- Заголовок с анимированной шестеренкой --}}
            <div class="maintenance-header">
                {{-- SVG иконка шестеренки --}}
                <div class="maintenance-icon">
                    <svg class="gear-animation" width="64" height="64" viewBox="0 0 24 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l1.86-1.41c.2-.15.25-.42.13-.64l-1.86-3.23c-.12-.22-.39-.3-.61-.22l-2.17.87c-.5-.38-1.03-.7-1.62-.94L14.4 2.81c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41L9.25 5.35C8.66 5.59 8.12 5.92 7.63 6.29L5.46 5.42c-.22-.08-.49 0-.61.22L2.99 8.87c-.12.22-.07.49.13.64L4.98 11c-.04.32-.07.65-.07.97c0 .32.03.65.07.97l-1.86 1.41c-.2.15-.25.42-.13.64l1.86 3.23c.12.22.39.3.61.22l2.17-.87c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.17.87c.22.08.49 0 .61-.22l1.86-3.23c.12-.22.07-.49-.13-.64L19.43 13Z"
                            fill="currentColor" />
                    </svg>
                </div>

                <h1 class="maintenance-title">
                    @if(isset($is_authenticated) && $is_authenticated)
                        Завершен набор игроков на закрытый бета-тест!
                    @else
                        Завершен набор игроков на закрытый бета-тест!
                    @endif
                </h1>
            </div>

            {{-- Основное содержимое --}}
            <div class="maintenance-content">
                {{-- Персонализированное приветствие --}}
                @if(isset($user_name) && $user_name)
                    <div class="maintenance-greeting">
                        <p class="greeting-text">
                            <strong>Уважаемый игрок {{ $user_name }},</strong>
                        </p>
                    </div>
                @endif

                {{-- Сообщение о технических работах --}}
                <div class="maintenance-message">
                    <p>{{ $message }}</p>

                    @if(isset($user_name) && $user_name)
                        <div class="user-help-info">
                            <p class="help-text">
                                💡 <strong>Важно:</strong> Благодарим вас за участие в закрытом бета-тестировании.
                                Ваше игровое имя: <strong>{{ $user_name }}</strong>
                            </p>
                        </div>
                    @endif
                </div>

                {{-- Предполагаемое время завершения --}}
                @if($estimated_completion)
                    <div class="maintenance-completion">
                        <div class="completion-label">Предполагаемое время завершения:</div>
                        <div class="completion-time">
                            {{ \Carbon\Carbon::parse($estimated_completion)->format('d.m.Y в H:i') }}
                        </div>
                    </div>
                @endif

                {{-- Дополнительная информация --}}
                <div class="maintenance-info">
                    <div class="info-item">
                        <svg class="info-icon" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"
                                fill="currentColor" />
                        </svg>
                        <span>Все данные сохранены и будут доступны после завершения работ</span>
                    </div>

                    <div class="info-item">
                        <svg class="info-icon" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                                fill="currentColor" />
                        </svg>
                        <span>Мы работаем над улучшением игрового опыта</span>
                    </div>
                </div>

                {{-- Кнопка обновления страницы --}}
                <div class="maintenance-actions">
                    <button onclick="location.reload()" class="refresh-button">
                        <svg class="refresh-icon" width="20" height="20" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
                                fill="currentColor" />
                        </svg>
                        Обновить страницу
                    </button>
                </div>
            </div>

            {{-- Футер с дополнительной информацией --}}
            <div class="maintenance-footer">
                <p class="footer-text">
                    Следите за новостями в нашей группе Telegram или у разработчика — @De_6a, чтобы всегда быть в курсе
                    обновлений и получать дополнительную информацию.
                </p>
                <div class="footer-time">
                    Текущее время сервера: <span id="server-time">{{ now()->format('d.m.Y H:i:s') }}</span>
                </div>
            </div>
        </div>
    </div>

    {{-- JavaScript для обновления времени --}}
    <script>
        // Обновляем время сервера каждую секунду
        function updateServerTime() {
            const timeElement = document.getElementById('server-time');
            if (timeElement) {
                const now = new Date();
                const options = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                };
                timeElement.textContent = now.toLocaleDateString('ru-RU', options).replace(',', '');
            }
        }

        // Запускаем обновление времени
        setInterval(updateServerTime, 1000);

        // Автоматическое обновление страницы каждые 30 секунд
        let autoRefreshInterval;

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                location.reload();
            }, 30000); // 30 секунд
        }

        // Останавливаем автообновление при активности пользователя
        function resetAutoRefresh() {
            clearInterval(autoRefreshInterval);
            startAutoRefresh();
        }

        // Слушаем события пользователя
        document.addEventListener('click', resetAutoRefresh);
        document.addEventListener('keypress', resetAutoRefresh);
        document.addEventListener('mousemove', resetAutoRefresh);

        // Запускаем автообновление
        startAutoRefresh();

        // Добавляем эффект появления модального окна
        document.addEventListener('DOMContentLoaded', function () {
            const modal = document.querySelector('.maintenance-modal');
            if (modal) {
                setTimeout(() => {
                    modal.classList.add('show');
                }, 100);
            }
        });
    </script>
</body>

</html>
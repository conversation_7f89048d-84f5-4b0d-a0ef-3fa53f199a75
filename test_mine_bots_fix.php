<?php

/**
 * Тестовый скрипт для проверки исправления проблем с ботами в рудниках
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЙ СИСТЕМЫ БОТОВ В РУДНИКАХ\n";
echo "===============================================\n\n";

// 1. Исправляем локации ботов
echo "1. ИСПРАВЛЕНИЕ ЛОКАЦИЙ БОТОВ\n";
echo "----------------------------\n";

$mineLocations = MineLocation::where('is_active', true)->get();
$totalFixed = 0;

foreach ($mineLocations as $mine) {
    // Ищем ботов с location = ID вместо названия
    $botsWithIdLocation = Bot::where('location', (string) $mine->id)
        ->where('created_by_admin', true)
        ->get();

    if ($botsWithIdLocation->count() > 0) {
        echo "📍 {$mine->name} (ID: {$mine->id}): найдено {$botsWithIdLocation->count()} ботов с неправильной локацией\n";
        
        foreach ($botsWithIdLocation as $bot) {
            $oldLocation = $bot->location;
            $bot->location = $mine->name;
            $bot->mine_location_id = $mine->id;
            $bot->save();
            
            echo "   ✅ {$bot->name}: '{$oldLocation}' → '{$bot->location}'\n";
            $totalFixed++;
        }
    }
}

echo "Исправлено локаций: {$totalFixed}\n\n";

// 2. Проверяем привязки mine_location_id
echo "2. ПРОВЕРКА ПРИВЯЗОК MINE_LOCATION_ID\n";
echo "------------------------------------\n";

$totalIdFixed = 0;
foreach ($mineLocations as $mine) {
    $botsWithoutId = Bot::where('location', $mine->name)
        ->where('created_by_admin', true)
        ->where(function ($query) use ($mine) {
            $query->whereNull('mine_location_id')
                ->orWhere('mine_location_id', '!=', $mine->id);
        })
        ->get();

    if ($botsWithoutId->count() > 0) {
        echo "📍 {$mine->name}: найдено {$botsWithoutId->count()} ботов без правильной привязки\n";
        
        foreach ($botsWithoutId as $bot) {
            $oldId = $bot->mine_location_id;
            $bot->mine_location_id = $mine->id;
            $bot->save();
            
            echo "   ✅ {$bot->name}: mine_location_id {$oldId} → {$mine->id}\n";
            $totalIdFixed++;
        }
    }
}

echo "Исправлено привязок: {$totalIdFixed}\n\n";

// 3. Сброс кулдаунов
echo "3. СБРОС КУЛДАУНОВ\n";
echo "-------------------\n";

$botsWithCooldown = Bot::where('created_by_admin', true)
    ->whereNotNull('next_action_time')
    ->count();

if ($botsWithCooldown > 0) {
    Bot::where('created_by_admin', true)->update(['next_action_time' => null]);
    echo "✅ Сброшены кулдауны у {$botsWithCooldown} ботов\n";
} else {
    echo "✅ Кулдауны уже сброшены\n";
}

echo "\n";

// 4. Проверка изоляции подлокаций
echo "4. ПРОВЕРКА ИЗОЛЯЦИИ ПОДЛОКАЦИЙ\n";
echo "-------------------------------\n";

foreach ($mineLocations as $mine) {
    $bots = Bot::where('location', $mine->name)
        ->where('is_active', true)
        ->where('created_by_admin', true)
        ->get();

    $players = User::whereHas('statistics', function ($query) use ($mine) {
        $query->where('current_location', $mine->name);
    })->with(['profile', 'statistics'])->get();

    echo "📍 {$mine->name}:\n";
    echo "   🤖 Ботов: {$bots->count()}\n";
    echo "   👥 Игроков: {$players->count()}\n";

    // Проверяем корректность поиска целей
    foreach ($bots as $bot) {
        $enemyRace = $bot->race === 'solarius' ? 'lunarius' : 'solarius';
        
        $validTargets = User::whereHas('profile', function ($query) use ($enemyRace) {
            $query->where('race', $enemyRace)
                ->where('current_hp', '>', 0);
        })->whereHas('statistics', function ($query) use ($mine) {
            $query->where('current_location', $mine->name);
        })->count();

        echo "   🎯 {$bot->name} ({$bot->race}): {$validTargets} целей в своей подлокации\n";
    }
    
    echo "\n";
}

// 5. Финальная проверка
echo "5. ФИНАЛЬНАЯ ПРОВЕРКА\n";
echo "--------------------\n";

$totalIssues = 0;

// Проверяем ботов с неправильными локациями
$botsWithWrongLocation = Bot::where('created_by_admin', true)
    ->whereRaw('location REGEXP \'^[0-9]+$\'')
    ->count();

if ($botsWithWrongLocation > 0) {
    echo "❌ Ботов с ID локации: {$botsWithWrongLocation}\n";
    $totalIssues += $botsWithWrongLocation;
}

// Проверяем ботов без правильных привязок
$botsWithoutValidId = 0;
foreach ($mineLocations as $mine) {
    $count = Bot::where('location', $mine->name)
        ->where('created_by_admin', true)
        ->where(function ($query) use ($mine) {
            $query->whereNull('mine_location_id')
                ->orWhere('mine_location_id', '!=', $mine->id);
        })
        ->count();
    $botsWithoutValidId += $count;
}

if ($botsWithoutValidId > 0) {
    echo "❌ Ботов без правильной привязки: {$botsWithoutValidId}\n";
    $totalIssues += $botsWithoutValidId;
}

if ($totalIssues === 0) {
    echo "✅ ВСЕ ПРОБЛЕМЫ ИСПРАВЛЕНЫ!\n";
    echo "   - Боты корректно привязаны к подлокациям\n";
    echo "   - Изоляция подлокаций работает правильно\n";
    echo "   - Кулдауны сброшены\n";
} else {
    echo "❌ Осталось проблем: {$totalIssues}\n";
}

echo "\n🎉 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО\n";
<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use App\Services\battle\FactionCountService;
use App\Services\battle\LocationPlayerCacheService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== ТЕСТ: СИНХРОНИЗАЦИЯ СЕРВИСОВ ПОИСКА БОТОВ ===\n\n";

$testLocation = 'аааааааааааа';

echo "Тестируемая локация: {$testLocation}\n\n";

// 1. Получаем локацию рудника
$mineLocation = MineLocation::where('name', $testLocation)->first();
if (!$mineLocation) {
    echo "❌ ОШИБКА: Локация рудника не найдена\n";
    exit(1);
}

echo "✓ Локация найдена: ID {$mineLocation->id}, активна: " . ($mineLocation->is_active ? 'ДА' : 'НЕТ') . "\n\n";

// 2. Тестируем поиск для обеих рас
$races = ['solarius', 'lunarius'];

foreach ($races as $race) {
    echo "ТЕСТ ДЛЯ РАСЫ: {$race}\n";
    echo str_repeat('-', 30) . "\n";
    
    // FactionCountService
    $factionService = app(FactionCountService::class);
    $factionBots = Bot::where('race', $race)
        ->where('is_active', true)
        ->where('hp', '>', 0)
        ->whereNull('death_time')
        ->whereNotNull('location')
        ->where('location', '!=', '')
        ->where('mine_location_id', $mineLocation->id)
        ->get();
    
    // LocationPlayerCacheService
    $locationCacheService = app(LocationPlayerCacheService::class);
    
    // Очищаем кеш для чистого теста
    $locationCacheService->clearLocationCache($testLocation);
    
    $cacheBots = $locationCacheService->getCachedBotsInLocation($testLocation, $race);
    
    $factionCount = $factionBots->count();
    $cacheCount = $cacheBots->count();
    
    echo "FactionCountService: {$factionCount} ботов\n";
    echo "LocationPlayerCacheService: {$cacheCount} ботов\n";
    
    if ($factionCount === $cacheCount) {
        echo "✅ СИНХРОНИЗАЦИЯ: OK\n";
        
        // Проверяем, что найдены одни и те же боты
        $factionBotIds = $factionBots->pluck('id')->sort()->values();
        $cacheBotIds = $cacheBots->pluck('id')->sort()->values();
        
        if ($factionBotIds->toArray() === $cacheBotIds->toArray()) {
            echo "✅ ИДЕНТИЧНОСТЬ БОТОВ: OK\n";
        } else {
            echo "❌ ИДЕНТИЧНОСТЬ БОТОВ: FAIL\n";
            echo "   FactionCountService IDs: " . $factionBotIds->implode(', ') . "\n";
            echo "   LocationPlayerCacheService IDs: " . $cacheBotIds->implode(', ') . "\n";
        }
        
        // Показываем найденных ботов
        if ($factionCount > 0) {
            echo "   Найденные боты:\n";
            foreach ($factionBots as $bot) {
                echo "     - {$bot->name} (ID: {$bot->id}, {$bot->class})\n";
            }
        }
    } else {
        echo "❌ РАССИНХРОНИЗАЦИЯ: FAIL\n";
        echo "   Разница: " . ($factionCount - $cacheCount) . " ботов\n";
        
        if ($factionCount > 0) {
            echo "   FactionCountService нашел:\n";
            foreach ($factionBots as $bot) {
                echo "     - {$bot->name} (ID: {$bot->id})\n";
            }
        }
        
        if ($cacheCount > 0) {
            echo "   LocationPlayerCacheService нашел:\n";
            foreach ($cacheBots as $bot) {
                echo "     - {$bot->name} (ID: {$bot->id})\n";
            }
        }
    }
    
    echo "\n";
}

// 3. Проверяем конкретно бота1
echo "ПРОВЕРКА БОТА1\n";
echo str_repeat('-', 30) . "\n";

$bot1 = Bot::where('name', 'бот1')->first();
if ($bot1) {
    echo "✓ Бот1 найден: {$bot1->name} (раса: {$bot1->race}, класс: {$bot1->class})\n";
    echo "  Локация: {$bot1->location}\n";
    echo "  Mine Location ID: {$bot1->mine_location_id}\n";
    
    // Проверяем, что бот1 теперь находится в кеше для атаки
    $locationCacheService = app(LocationPlayerCacheService::class);
    $locationCacheService->clearLocationCache($testLocation);
    
    $botsForAttack = $locationCacheService->getCachedBotsInLocation($testLocation, $bot1->race);
    $bot1InCache = $botsForAttack->firstWhere('id', $bot1->id);
    
    if ($bot1InCache) {
        echo "✅ Бот1 ДОСТУПЕН для атаки через LocationPlayerCacheService\n";
    } else {
        echo "❌ Бот1 НЕ ДОСТУПЕН для атаки через LocationPlayerCacheService\n";
    }
} else {
    echo "❌ Бот1 не найден\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";

// 4. Итоговая проверка команды "Бить любого"
echo "\n=== ИТОГОВАЯ ПРОВЕРКА: КОМАНДА 'БИТЬ ЛЮБОГО' ===\n";

// Симулируем игрока противоположной расы
$playerRace = 'solarius'; // Если игрок Solarius, он должен видеть ботов Lunarius
$enemyRace = ($playerRace === 'solarius') ? 'lunarius' : 'solarius';

$locationCacheService = app(LocationPlayerCacheService::class);
$locationCacheService->clearLocationCache($testLocation);

$enemiesForAttack = $locationCacheService->getCachedEnemiesInLocation($testLocation, $playerRace);

echo "Игрок расы: {$playerRace}\n";
echo "Ищем врагов расы: {$enemyRace}\n";
echo "Найдено врагов для атаки: " . $enemiesForAttack->count() . "\n";

if ($enemiesForAttack->count() > 0) {
    echo "✅ КОМАНДА 'БИТЬ ЛЮБОГО' БУДЕТ РАБОТАТЬ\n";
    echo "Доступные цели:\n";
    foreach ($enemiesForAttack as $enemy) {
        if (isset($enemy->race)) { // Это бот
            echo "  - БОТ: {$enemy->name} ({$enemy->race} {$enemy->class})\n";
        } else { // Это игрок
            echo "  - ИГРОК: {$enemy->name}\n";
        }
    }
} else {
    echo "❌ КОМАНДА 'БИТЬ ЛЮБОГО' НЕ БУДЕТ РАБОТАТЬ\n";
    echo "Причина: В локации нет врагов противоположной фракции\n";
}

echo "\nГОТОВО!\n";
<?php

/**
 * Простой тест отображения дебафа "Замечен!" в активных эффектах
 * Имитирует загрузку эффектов в CustomMineController
 */

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\ActiveEffect;
use App\Models\MineLocation;
use App\Services\MineDetectionService;

echo "🎯 ТЕСТ ОТОБРАЖЕНИЯ ДЕБАФА 'ЗАМЕЧЕН!' В АКТИВНЫХ ЭФФЕКТАХ\n";
echo "========================================================\n\n";

// Находим пользователя admin
$user = User::where('name', 'admin')->with('profile')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "👤 Пользователь: {$user->name} (ID: {$user->id})\n";
echo "   HP: {$user->profile->current_hp}/{$user->profile->max_hp}\n\n";

// Находим кастомный рудник
$mine = MineLocation::where('is_active', true)
    ->whereNull('parent_id')
    ->first();
    
if (!$mine) {
    echo "❌ Нет активных кастомных рудников!\n";
    exit(1);
}

echo "⛏️ Тестовый рудник: {$mine->name} (ID: {$mine->id})\n\n";

// Очищаем старые дебафы
echo "🧹 Очищаем старые дебафы...\n";
ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();

// Создаём дебаф через сервис
echo "🎯 Создаём дебаф через MineDetectionService...\n";
try {
    $service = app(MineDetectionService::class);
    $mark = $service->applyDetectionDebuff($user, $mine);
    
    if ($mark) {
        echo "✅ Дебаф создан успешно!\n\n";
    } else {
        echo "❌ Ошибка создания дебафа!\n";
        exit(1);
    }
} catch (\Exception $e) {
    echo "❌ Исключение: " . $e->getMessage() . "\n";
    exit(1);
}

// ТОЧНО ТАК ЖЕ КАК В CUSTOMMINECONTROLLER
echo "📋 Загружаем активные эффекты как в CustomMineController (строка 334):\n";

// Строка 334 из CustomMineController.php
$allUserEffects = $user->activeEffects()->with('skill')->get();
$userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());

echo "   Всего эффектов: {$allUserEffects->count()}\n";
echo "   Активных эффектов: {$userEffects->count()}\n\n";

if ($userEffects->count() == 0) {
    echo "❌ НЕТ АКТИВНЫХ ЭФФЕКТОВ!\n";
    echo "   Компонент покажет: 'Нет активных эффектов.'\n\n";
} else {
    echo "✅ ЕСТЬ АКТИВНЫЕ ЭФФЕКТЫ!\n";
    
    foreach ($userEffects as $effect) {
        echo "   📍 Эффект ID {$effect->id}:\n";
        echo "     - Тип: '{$effect->effect_type}'\n";
        echo "     - Название: '{$effect->effect_name}'\n";
        echo "     - Активен: " . ($effect->isActive() ? 'Да' : 'Нет') . "\n";
        echo "     - Время: {$effect->remaining_duration}с\n";
        echo "     - Skill загружен: " . ($effect->skill ? 'Да' : 'Нет') . "\n";
        
        // Проверяем условия для отображения в компоненте
        if ($effect->effect_type === 'mine_detection') {
            echo "     🎯 ЭТО MINE_DETECTION ЭФФЕКТ!\n";
            
            // Условия из компонента active-effects.blade.php:
            // @if($activeEffects->isEmpty()) -> НЕТ, есть эффекты
            // @if($effect->remaining_duration > 0) -> строка 22
            $timeCheck = $effect->remaining_duration > 0;
            echo "     - remaining_duration > 0: " . ($timeCheck ? '✅ Да' : '❌ Нет') . " ({$effect->remaining_duration})\n";
            
            // @if($effect->skill) -> строка 24 (НЕ для mine_detection)
            // @elseif($effect->effect_type == 'mine_detection') -> строка 28 ✅
            echo "     - Использует специальную иконку (строки 28-34)\n";
            echo "     - Иконка: assets/obelisk_mark.png\n";
            echo "     - Стили: w-4 h-4 text-red-400 animate-pulse\n";
            echo "     - Цвет времени: text-red-400\n";
            
            if ($timeCheck) {
                echo "     🎉 ВСЕ УСЛОВИЯ ВЫПОЛНЕНЫ! Эффект ДОЛЖЕН отображаться!\n";
            } else {
                echo "     ❌ Время истекло - эффект НЕ будет отображаться\n";
            }
        }
        echo "\n";
    }
}

// Проверяем, что передалось бы в компонент
echo "🎨 Что получит компонент active-effects:\n";
echo "   userEffects: коллекция из {$userEffects->count()} эффектов\n";

$mineDetectionEffects = $userEffects->where('effect_type', 'mine_detection');
echo "   mine_detection эффектов: {$mineDetectionEffects->count()}\n";

if ($mineDetectionEffects->count() > 0) {
    echo "   📱 В компоненте будет:\n";
    echo "   - НЕ показывать: 'Нет активных эффектов.' (строка 18)\n"; 
    echo "   - Показать div с эффектами (строка 20)\n";
    echo "   - Для mine_detection: специальная иконка (строки 28-34)\n";
    echo "   - Красный текст времени (строка 39)\n";
    echo "\n   🎯 ОЖИДАЕМЫЙ РЕЗУЛЬТАТ: Красная пульсирующая иконка с временем!\n";
} else {
    echo "   📱 В компоненте будет показано: 'Нет активных эффектов.'\n";
}

echo "\n";

// Имитируем HTML компонента
if ($userEffects->count() > 0) {
    echo "🌐 ИМИТАЦИЯ HTML КОМПОНЕНТА:\n";
    echo "<div class=\"active-effects\">\n";
    echo "  <div class=\"flex flex-row flex-wrap pl-0 ml-0 gap-1 items-start\">\n";
    
    foreach ($userEffects as $effect) {
        if ($effect->remaining_duration > 0) {
            echo "    <div class=\"flex flex-col items-center justify-center text-center w-4\">\n";
            
            if ($effect->effect_type == 'mine_detection') {
                echo "      <img src=\"assets/obelisk_mark.png\" alt=\"Замечен в рудниках\" \n";
                echo "           class=\"w-4 h-4 text-red-400 animate-pulse\" \n";
                echo "           title=\"{$effect->effect_name}\">\n";
                echo "      <span class=\"text-[10px] w-full text-red-400\">\n";
                echo "        " . round($effect->remaining_duration) . "с\n";
                echo "      </span>\n";
            }
            
            echo "    </div>\n";
        }
    }
    
    echo "  </div>\n";
    echo "</div>\n";
} else {
    echo "🌐 ИМИТАЦИЯ HTML КОМПОНЕНТА:\n";
    echo "<div class=\"active-effects\">\n";
    echo "  <p class=\"text-gray-400\">Нет активных эффектов.</p>\n";
    echo "</div>\n";
}

// Очищаем тестовые данные
echo "\n🧹 Очищаем тестовые данные...\n";
if (isset($mark)) {
    $mark->delete();
}
ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();
echo "✅ Данные очищены\n";

echo "\n📋 ВЫВОДЫ:\n";
echo "=========\n";

if ($userEffects->count() > 0 && $mineDetectionEffects->count() > 0) {
    echo "✅ Система создания эффектов работает\n";
    echo "✅ Эффекты загружаются с ->with('skill')\n";
    echo "✅ mine_detection эффекты присутствуют в активных\n";
    echo "✅ Компонент должен отображать красную пульсирующую иконку\n";
    echo "\n🎯 ЕСЛИ ДЕБАФ ВСЁ РАВНО НЕ ПОКАЗЫВАЕТСЯ:\n";
    echo "   1. Проверьте браузерную консоль на JS ошибки\n";
    echo "   2. Убедитесь, что файл assets/obelisk_mark.png существует\n"; 
    echo "   3. Проверьте CSS стили (Tailwind компилируется?)\n";
    echo "   4. Убедитесь, что компонент включен в layout mine.blade.php\n";
    echo "   5. Проверьте права доступа к файлам в public/assets/\n";
} else {
    echo "❌ Проблемы с созданием или загрузкой эффектов\n";
    echo "   Дебаф создается, но не попадает в активные эффекты\n";
    echo "   Нужно проверить метод isActive() в модели ActiveEffect\n";
}

echo "\n✅ ТЕСТ ЗАВЕРШЁН!\n";
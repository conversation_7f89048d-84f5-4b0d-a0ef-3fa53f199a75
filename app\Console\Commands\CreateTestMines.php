<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;
use App\Models\Location;

class CreateTestMines extends Command
{
    protected $signature = 'create:test-mines {--count=8 : Количество рудников для создания}';
    protected $description = 'Создает тестовые рудники для демонстрации пагинации';

    public function handle()
    {
        $count = $this->option('count');
        $this->info("Создание $count тестовых рудников...");

        // Получаем первую базовую локацию
        $baseLocation = Location::first();
        if (!$baseLocation) {
            $this->error('Не найдена базовая локация для создания тестовых рудников');
            $this->info('Создайте базовую локацию через админку: /admin/locations');
            return 1;
        }

        $this->info("Используем базовую локацию: {$baseLocation->name}");

        $createdCount = 0;
        for ($i = 1; $i <= $count; $i++) {
            $slug = "test-mine-$i";
            
            // Проверяем, существует ли уже такой рудник
            $existingMine = MineLocation::where('slug', $slug)->first();
            if ($existingMine) {
                $this->warn("Рудник с slug '$slug' уже существует, пропускаем");
                continue;
            }

            // Создаем рудник
            $mine = MineLocation::create([
                'name' => "Тестовый рудник $i",
                'slug' => $slug,
                'description' => "Тестовый рудник для демонстрации пагинации ($i из $count)",
                'base_location_id' => $baseLocation->id,
                'is_active' => true,
                'order' => $i,
                'parent_id' => null,
            ]);

            $this->info("✓ Создан рудник: {$mine->name} (ID: {$mine->id})");
            $createdCount++;
        }

        $this->info("\nСоздано рудников: $createdCount");

        // Проверяем общее количество
        $totalMines = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->count();
        
        $this->info("Общее количество активных рудников: $totalMines");

        // Проверяем пагинацию
        if ($totalMines > 4) {
            $pages = ceil($totalMines / 4);
            $this->info("Пагинация должна работать: $pages страниц");
            $this->info("Проверьте: http://127.0.0.1:8000/battle/mines");
        } else {
            $this->warn("Недостаточно рудников для пагинации (нужно больше 4)");
        }

        // Показываем все созданные рудники
        $this->info("\nСписок всех рудников:");
        $allMines = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        foreach ($allMines as $index => $mine) {
            $page = floor($index / 4) + 1;
            $this->info("  {$mine->name} (порядок: {$mine->order}, страница: $page)");
        }

        return 0;
    }
}
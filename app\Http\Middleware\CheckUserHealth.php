<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class CheckUserHealth
{
    /**
     * Список безопасных локаций, куда можно входить с 0 HP
     */
    protected $safeLocations = [
        'Таверна',
        'Город',
        'Рынок',
        'Кузница'
        // Добавьте другие безопасные локации
    ];

    /**
     * Middleware для проверки здоровья пользователя перед входом в локацию
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Получаем текущего пользователя
        $user = Auth::user();

        // Если пользователь не авторизован, пропускаем проверку
        if (!$user) {
            return $next($request);
        }

        // Проверяем, есть ли у пользователя профиль
        if (!$user->profile) {
            \Log::warning("Пользователь {$user->name} не имеет профиля", [
                'user_id' => $user->id,
                'current_url' => $request->fullUrl()
            ]);
            return Redirect::route('battle.mines.index')->with('error', 'Профиль пользователя не найден');
        }

        // Получаем актуальные значения HP/MP пользователя с учетом регенерации
        // Это важно: только так можно узнать, сколько у игрока реально здоровья на данный момент
        $actualResources = $user->profile->getActualResources();
        $actualHp = $actualResources['current_hp'];

        // Получаем текущую локацию пользователя и целевой маршрут
        $currentLocation = $user->statistics->current_location ?? null;
        $targetRoute = $request->route()->getName();

        // Обработка сессии смерти
        if (session('player_dead') === true) {
            // Проверяем, есть ли флаг недавней победы ПЕРВЫМ делом
            $hasRecentVictory = session('recent_victory_time') && 
                               (time() - session('recent_victory_time') < 10);
            
            // Если игрок недавно победил, сбрасываем флаг смерти и пропускаем проверку
            if ($hasRecentVictory) {
                session(['player_dead' => false]);
                session()->forget('death_timestamp');
                \Log::info("CheckUserHealth сбрасывает флаг смерти для победителя", [
                    'user_id' => $user->id,
                    'recent_victory_time' => session('recent_victory_time'),
                    'current_time' => time(),
                    'victory_elapsed' => time() - session('recent_victory_time')
                ]);
                return $next($request);
            }
            
            // Проверяем, не является ли это результатом успешной атаки игрока
            $isAttackRequest = $request->isMethod('post') && 
                              (str_contains($request->url(), '/attack-player') || 
                               str_contains($request->url(), '/attack-bot') || 
                               str_contains($request->url(), '/attack-any-player') ||
                               str_contains($request->url(), '/retaliate'));
            
            // Проверяем, не является ли это GET запросом после атаки (перенаправление после победы)
            $isPostAttackRedirect = $request->isMethod('get') && 
                                   str_contains($request->url(), '/battle/outposts/') &&
                                   $user->current_target_id &&
                                   session('recent_attack_time') &&
                                   (time() - session('recent_attack_time') < 3);
            
            // Если это запрос атаки и у игрока есть цель, пропускаем проверку смерти
            if ($isAttackRequest && $user->current_target_id) {
                return $next($request);
            }
            
            // Если это перенаправление после атаки, пропускаем проверку смерти
            if ($isPostAttackRedirect) {
                \Log::info("CheckUserHealth пропускает проверку смерти", [
                    'user_id' => $user->id,
                    'isPostAttackRedirect' => $isPostAttackRedirect,
                    'recent_attack_time' => session('recent_attack_time'),
                    'current_time' => time()
                ]);
                return $next($request);
            }
            
            $deathTimestamp = session('death_timestamp', 0);
            $elapsedTime = time() - $deathTimestamp;

            // Если прошло менее 5 секунд с момента смерти, гарантируем перенаправление
            if ($elapsedTime < 5) {
                // Устанавливаем принудительно HP в 0
                $user->profile->current_hp = 0;
                $user->profile->last_regeneration_at = now();
                $user->profile->save();

                // Определяем безопасную локацию
                $safeLocation = $this->getSafeLocation($user);
                if ($user->statistics) {
                    $user->statistics->current_location = $safeLocation;
                    $user->statistics->save();
                }

                // Логируем действие
                \Log::info("Игрок {$user->name} перенаправлен в безопасную локацию {$safeLocation} после смерти (session flag)");

                // Сбрасываем флаг смерти после перенаправления
                session(['player_dead' => false]);
                session()->forget('killed_by_bot');
                session()->forget('death_timestamp');

                // Перенаправляем в безопасную локацию
                return Redirect::route('battle.outposts.index')
                    ->with('warning', 'Вы были перемещены в безопасную зону после смерти в бою.');
            } else {
                // Если прошло больше времени, сбрасываем флаг
                session(['player_dead' => false]);
                session()->forget('killed_by_bot');
                session()->forget('death_timestamp');
            }
        }

        // Проверяем целевую локацию по маршруту
        $isUnsafeLocation = false;

        // Определяем опасные локации на основе маршрута
        $unsafeRoutePatterns = [
            'battle.outposts.elven_haven',
            'battle.mines.deep',
            'battle.quarry',
            'battle.canyon'
        ];

        foreach ($unsafeRoutePatterns as $pattern) {
            if (strpos($targetRoute, $pattern) !== false) {
                $isUnsafeLocation = true;
                break;
            }
        }

        // Проверяем здоровье пользователя только для опасных локаций
        // Если HP <= 0 (по актуальному значению), не пускаем в опасную локацию
        // Но пропускаем игроков с недавней победой
        $hasRecentVictory = session('recent_victory_time') && 
                           (time() - session('recent_victory_time') < 10);
        
        if ($actualHp <= 0 && $isUnsafeLocation && !in_array($currentLocation, $this->safeLocations) && !$hasRecentVictory) {
            // Логируем попытку входа с нулевым HP
            \Log::warning("Пользователь {$user->name} пытался войти в опасную локацию с 0 HP (актуальное)", [
                'user_id' => $user->id,
                'route' => $targetRoute,
                'current_url' => $request->fullUrl()
            ]);

            // Гарантируем, что HP установлено в 0
            $user->profile->current_hp = 0;
            $user->profile->last_regeneration_at = now();
            $user->profile->save();

            // Сбрасываем цели и атакующих
            $this->resetCombatTargets($user);

            // Определяем безопасную локацию
            $safeLocation = $this->getSafeLocation($user);
            if ($user->statistics) {
                $user->statistics->current_location = $safeLocation;
                $user->statistics->save();
            }

            // Перенаправляем на страницу аванпостов с сообщением
            Log::warning("CheckUserHealth перенаправляет на battle.outposts.index", [
                'user_id' => $user->id,
                'actualHp' => $actualHp,
                'hasRecentVictory' => $hasRecentVictory,
                'recent_victory_time' => session('recent_victory_time'),
                'current_time' => time()
            ]);
            
            return Redirect::route('battle.outposts.index')
                ->with('warning', 'Вы не можете войти в опасную зону с 0 HP. Восстановите здоровье.');
        }

        // Если все проверки пройдены, пропускаем запрос дальше
        return $next($request);
    }

    /**
     * Сбрасывает боевые цели и атакующих для пользователя
     *
     * @param \App\Models\User $user
     * @return void
     */
    protected function resetCombatTargets($user)
    {
        // Сбрасываем цели для всех игроков, атакующих этого игрока
        \App\Models\User::where('current_target_type', 'player')
            ->where('current_target_id', $user->id)
            ->update(['current_target_id' => null, 'current_target_type' => null]);

        // Очищаем last_attacker_id у всех, у кого текущий игрок является последним атакующим
        \App\Models\User::where('last_attacker_id', $user->id)
            ->update(['last_attacker_id' => null]);

        // Сбрасываем цели и атакующих у самого пользователя
        $user->last_attacker_id = null;
        $user->current_target_id = null;
        $user->current_target_type = null;
        $user->save();
    }

    /**
     * Определяет безопасную локацию для игрока на основе его расы
     *
     * @param \App\Models\User $user
     * @return string
     */
    protected function getSafeLocation($user)
    {
        // Определяем расу игрока
        $race = $user->profile->race ?? 'solarius';

        // Выбираем локацию в зависимости от расы
        if ($race === 'solarius') {
            return 'Город Соляриус';
        } elseif ($race === 'lunarius') {
            return 'Город Лунариус';
        }

        // По умолчанию возвращаем нейтральную локацию
        return 'Таверна';
    }
}
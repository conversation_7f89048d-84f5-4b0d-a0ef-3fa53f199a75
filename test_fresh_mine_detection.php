<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Services\MineDetectionService;
use App\Models\MineMark;
use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🆕 ТЕСТ СВЕЖЕГО ДЕБАФА И АВТОАТАК\n";
echo "=" . str_repeat("=", 35) . "\n\n";

// 1. Получаем данные
$user = User::where('name', 'admin')->first();
$mineLocation = MineLocation::where('name', 'аааааааааааа')->first();

if (!$user || !$mineLocation) {
    echo "❌ Пользователь или локация не найдены!\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
echo "✅ Локация: {$mineLocation->name} (ID: {$mineLocation->id})\n\n";

// 2. Очищаем старые данные
echo "🧹 Очистка старых данных...\n";
MineMark::where('player_id', $user->id)->delete();
ActiveEffect::where('target_type', 'player')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();
echo "✅ Старые данные очищены\n\n";

// 3. Создаем новый дебаф
echo "⚡ Создание свежего дебафа...\n";
$mineDetectionService = app(MineDetectionService::class);
$mark = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);

if ($mark) {
    echo "✅ Новая метка создана:\n";
    echo "   ID: {$mark->id}\n";
    echo "   Истекает: {$mark->expires_at}\n";
    echo "   Активна: " . ($mark->is_active ? 'Да' : 'Нет') . "\n";
    echo "   Время до истечения: " . $mark->expires_at->diffInSeconds(now()) . "с\n\n";
} else {
    echo "❌ Не удалось создать метку!\n";
    exit(1);
}

// 4. Проверяем эффект в UI
echo "🎨 Проверка отображения в UI...\n";
$allUserEffects = $user->activeEffects()->with('skill')->get();
$userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());

echo "Активных эффектов для UI: {$userEffects->count()}\n";
foreach ($userEffects as $effect) {
    if ($effect->effect_type == 'mine_detection') {
        echo "✅ Эффект mine_detection найден:\n";
        echo "   ID: {$effect->id}\n";
        echo "   Название: " . ($effect->effect_name ?? 'N/A') . "\n";
        echo "   Оставшееся время: {$effect->remaining_duration}с\n";
        echo "   🎯 БУДЕТ ОТОБРАЖАТЬСЯ В UI!\n\n";
    }
}

// 5. Проверяем автоатаки
echo "⚔️ Проверка системы автоатак...\n";
$markedPlayers = $mineDetectionService->getMarkedPlayersInMine(
    $mineLocation->location_id,
    $mineLocation->id
);

echo "Замеченных игроков: " . count($markedPlayers) . "\n";
if (count($markedPlayers) > 0) {
    $playerData = $markedPlayers[0];
    echo "- Игрок: {$playerData['player_name']} (ID: {$playerData['player_id']})\n";
    echo "- Метка истекает: " . date('Y-m-d H:i:s', $playerData['expires_at']) . "\n";
    echo "- Время до истечения: " . ($playerData['expires_at'] - time()) . "с\n";
    
    echo "\n🤖 Для запуска автоатак выполните:\n";
    echo "   php artisan schedule:work\n";
    echo "   или\n";
    echo "   php artisan mine:auto-attack\n";
} else {
    echo "❌ Нет замеченных игроков для автоатак\n";
}

echo "\n✅ ТЕСТ ЗАВЕРШЕН!\n";

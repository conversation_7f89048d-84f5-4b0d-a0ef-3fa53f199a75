<?php

namespace App\Services;

use App\Models\User;
use App\Models\Bot;
use App\Models\Location;
use App\Traits\OutpostLocationTrait;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Сервис для управления поведением ботов
 * Реализует умную систему атак с учетом баланса между игроками и ботами
 * ИСПРАВЛЕНИЕ: Добавлена агрессивная логика для боевых локаций (Аванпосты и Рудники)
 * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Боты в рудниках теперь атакуют враждебных игроков автоматически
 * ИСПРАВЛЕНИЕ ПОДЛОКАЦИЙ: Боты атакуют только в пределах своей точной подлокации рудника
 */
class BotBehaviorService
{
    use OutpostLocationTrait;
    /**
     * Базовая вероятность атаки бота в обычных локациях (%)
     *
     * @var int
     */
    protected $baseAttackProbability = 60;

    /**
     * Базовая вероятность атаки бота в боевых локациях (%)
     * ИСПРАВЛЕНИЕ: Увеличена для активного боя в Аванпостах
     *
     * @var int
     */
    protected $battleLocationAttackProbability = 85;

    /**
     * Время кеширования данных о локации (секунды)
     *
     * @var int
     */
    protected $locationCacheTime = 10;

    /**
     * Максимальное снижение вероятности атаки для одинокого игрока в обычных локациях (%)
     *
     * @var int
     */
    protected $maxProbabilityReduction = 40;

    /**
     * Максимальное снижение вероятности атаки для одинокого игрока в боевых локациях (%)
     * ИСПРАВЛЕНИЕ: Уменьшено для поддержания активного боя в Аванпостах
     *
     * @var int
     */
    protected $battleLocationMaxReduction = 15;

    /**
     * Проверяет, должен ли бот атаковать игрока
     * Учитывает множество факторов для баланса игры
     * ИСПРАВЛЕНИЕ: Добавлена принудительная логика для Аванпостов
     *
     * @param Bot $bot Бот, для которого проверяется возможность атаки
     * @param User $player Игрок, на которого может быть совершена атака
     * @param int|null $locationId ID локации (если не указан, берется из игрока)
     * @return bool True, если бот должен атаковать
     */
    public function shouldBotAttack(Bot $bot, User $player, ?int $locationId = null): bool
    {
        // Если ID локации не передан, берем из игрока
        if (!$locationId && isset($player->current_location_id)) {
            $locationId = $player->current_location_id;
        }

        // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Принудительные атаки в Аванпостах и Рудниках
        // Аванпосты и Рудники должны работать с 100% агрессивностью противоположных рас
        if ($this->isOutpostLocation($bot->location)) {
            Log::info("Принудительная атака в Аванпосте: Бот {$bot->name} ВСЕГДА атакует игрока {$player->name} в локации {$bot->location}");
            return true;
        }

        // НОВОЕ ИСПРАВЛЕНИЕ: Принудительные атаки в Рудниках
        // Рудники должны работать аналогично аванпостам с проверкой расы И точной подлокации
        if ($this->isMineLocation($bot->location)) {
            // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяем точное совпадение подлокации
            if (!$this->areInSameMineSubLocation($bot, $player)) {
                Log::debug("Рудник: Бот {$bot->name} и игрок {$player->name} находятся в разных подлокациях рудника");
                return false;
            }
            
            // Проверяем, является ли игрок противоположной расой
            $botRace = $bot->race ?? 'unknown';
            $playerRace = $player->profile->race ?? 'unknown';
            
            if ($this->areEnemyRaces($botRace, $playerRace)) {
                Log::info("Принудительная атака в Руднике: Бот {$bot->name} ({$botRace}) ВСЕГДА атакует враждебного игрока {$player->name} ({$playerRace}) в подлокации {$bot->location}");
                return true;
            } else {
                Log::debug("Рудник: Бот {$bot->name} ({$botRace}) НЕ атакует союзного игрока {$player->name} ({$playerRace}) в подлокации {$bot->location}");
                return false;
            }
        }

        // ИСПРАВЛЕНИЕ: Определяем тип локации для выбора стратегии
        $isBattleLocation = $this->isBattleLocation($bot->location);
        $baseProb = $isBattleLocation ? $this->battleLocationAttackProbability : $this->baseAttackProbability;

        // Если не удалось определить локацию, используем соответствующую базовую вероятность
        if (!$locationId) {
            return $this->rollDice($baseProb);
        }

        try {
            // Получаем статистику по игрокам в локации
            $stats = $this->getLocationPlayerStats($locationId);

            // ИСПРАВЛЕНИЕ: Рассчитываем вероятность с учетом типа локации
            $attackProbability = $this->calculateAttackProbability($bot, $player, $stats, $isBattleLocation);

            // Решаем, атаковать или нет
            $shouldAttack = $this->rollDice($attackProbability);

            // Логируем решение с указанием типа локации
            Log::debug("Расчет атаки бота: " . ($shouldAttack ? "АТАКУЕТ" : "ИГНОРИРУЕТ"), [
                'bot_id' => $bot->id,
                'player_id' => $player->id,
                'attack_probability' => $attackProbability,
                'location_id' => $locationId,
                'location_name' => $bot->location,
                'is_battle_location' => $isBattleLocation,
                'player_stats' => $stats
            ]);

            return $shouldAttack;
        } catch (\Exception $e) {
            Log::error("Ошибка при определении поведения бота: " . $e->getMessage(), [
                'bot_id' => $bot->id,
                'player_id' => $player->id,
                'location_id' => $locationId,
                'location_name' => $bot->location,
                'exception' => $e
            ]);

            // В случае ошибки используем соответствующую базовую вероятность
            return $this->rollDice($baseProb);
        }
    }

    /**
     * Получает статистику по игрокам в указанной локации
     *
     * @param int $locationId ID локации
     * @return array Статистика по игрокам в локации
     */
    protected function getLocationPlayerStats(int $locationId): array
    {
        $cacheKey = "location:{$locationId}:player_stats";

        // Пытаемся получить из кеша
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Собираем статистику
        $stats = $this->collectLocationStats($locationId);

        // Кешируем на короткое время для оптимизации
        Cache::put($cacheKey, $stats, now()->addSeconds($this->locationCacheTime));

        return $stats;
    }

    /**
     * Собирает статистику по игрокам в локации
     *
     * @param int $locationId ID локации
     * @return array Статистика
     */
    protected function collectLocationStats(int $locationId): array
    {
        // ИСПРАВЛЕНИЕ: Получаем название локации по ID
        $location = Location::find($locationId);
        if (!$location) {
            return ['total_players' => 0, 'race_counts' => []];
        }

        // ИСПРАВЛЕНИЕ: Для статистики используем основную локацию, чтобы учесть общую ситуацию
        // В рудниках это даст более сбалансированные расчеты вероятностей
        $allPlayers = User::whereHas('statistics', function ($q) use ($location) {
            $q->where('current_location', 'LIKE', $location->name . '%');
        })->where('is_online', true)->with('profile')->get();

        // Подсчитываем количество игроков разных рас
        $racePlayerCounts = [];
        foreach ($allPlayers as $player) {
            $race = $player->profile->race ?? 'unknown';
            if (!isset($racePlayerCounts[$race])) {
                $racePlayerCounts[$race] = 0;
            }
            $racePlayerCounts[$race]++;
        }

        // Определяем общее количество игроков каждой расы
        $totalPlayers = count($allPlayers);

        return [
            'total_players' => $totalPlayers,
            'race_counts' => $racePlayerCounts,
        ];
    }

    /**
     * Рассчитывает вероятность атаки бота на игрока
     * ИСПРАВЛЕНИЕ: Добавлен параметр для боевых локаций
     *
     * @param Bot $bot Бот
     * @param User $player Игрок
     * @param array $stats Статистика по локации
     * @param bool $isBattleLocation Является ли локация боевой
     * @return int Вероятность атаки (0-100)
     */
    protected function calculateAttackProbability(Bot $bot, User $player, array $stats, bool $isBattleLocation = false): int
    {
        // ИСПРАВЛЕНИЕ: Выбираем базовую вероятность в зависимости от типа локации
        $probability = $isBattleLocation ? $this->battleLocationAttackProbability : $this->baseAttackProbability;
        $maxReduction = $isBattleLocation ? $this->battleLocationMaxReduction : $this->maxProbabilityReduction;

        // Раса бота (раса игрока используется в логике врагов ниже)
        $botRace = $bot->race ?? 'unknown';

        // Общее количество игроков
        $totalPlayers = $stats['total_players'];

        // ИСПРАВЛЕНИЕ: Более мягкие штрафы для боевых локаций
        // 1. Если игрок один в локации - снижаем вероятность атаки
        if ($totalPlayers === 1) {
            if ($isBattleLocation) {
                // В боевых локациях штраф меньше - боты должны быть агрессивнее
                $playerLevel = $player->profile->level ?? 1;
                $levelFactor = min(1, $playerLevel / 15); // Более мягкий фактор уровня
                $reduction = $maxReduction * (1 - $levelFactor);
                $probability -= $reduction;
            } else {
                // В обычных локациях используем старую логику
                $playerLevel = $player->profile->level ?? 1;
                $levelFactor = min(1, $playerLevel / 10);
                $reduction = $maxReduction * (1 - $levelFactor);
                $probability -= $reduction;
            }
        }
        // 2. Если игроков 2-3, немного снижаем вероятность
        elseif ($totalPlayers <= 3) {
            $reductionAmount = $isBattleLocation ? round($maxReduction / 3) : round($maxReduction / 2);
            $probability -= $reductionAmount;
        }

        // 3. Если есть несколько игроков противоположной расы, увеличиваем вероятность атаки
        $enemyRaces = $this->getEnemyRaces($botRace);
        $enemyPlayers = 0;

        foreach ($enemyRaces as $race) {
            $enemyPlayers += $stats['race_counts'][$race] ?? 0;
        }

        if ($enemyPlayers > 1) {
            // Повышаем вероятность атаки на 5% за каждого вражеского игрока после первого
            $bonusMultiplier = $isBattleLocation ? 7 : 5; // В боевых локациях больший бонус
            $probability += min(25, $bonusMultiplier * ($enemyPlayers - 1));
        }

        // 4. Проверяем, нет ли перекоса в атаках ботов
        // Если игрок уже атакован другим ботом, уменьшаем вероятность атаки
        if ($player->last_attacker_id && $this->lastAttackerIsBot($player->last_attacker_id)) {
            $penalty = $isBattleLocation ? 8 : 15; // В боевых локациях меньший штраф
            $probability -= $penalty;
        }

        // 5. Уровень игрока влияет на вероятность атаки
        // Если уровень игрока намного выше уровня бота, повышаем вероятность
        $playerLevel = $player->profile->level ?? 1;
        $botLevel = $bot->level ?? 1;

        if ($playerLevel > $botLevel + 5) {
            // Игрок значительно сильнее - ботам надо объединяться
            $levelBonus = $isBattleLocation ? 20 : 15; // В боевых локациях больший бонус
            $probability += $levelBonus;
        } elseif ($playerLevel < $botLevel - 3) {
            // Игрок значительно слабее - боты не так агрессивны
            $levelPenalty = $isBattleLocation ? 5 : 10; // В боевых локациях меньший штраф
            $probability -= $levelPenalty;
        }

        // ИСПРАВЛЕНИЕ: Финальная корректировка с учетом типа локации
        $minProb = $isBattleLocation ? 25 : 5; // В боевых локациях минимум выше
        $maxProb = $isBattleLocation ? 98 : 95; // В боевых локациях максимум выше
        return max($minProb, min($maxProb, $probability));
    }

    /**
     * Проверяет, является ли последний атакующий ботом
     *
     * @param int $attackerId ID атакующего
     * @return bool
     */
    protected function lastAttackerIsBot(int $attackerId): bool
    {
        // Проверяем кеш сначала
        $cacheKey = "attacker:{$attackerId}:is_bot";

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $isBot = Bot::where('id', $attackerId)->exists();

        // Кешируем результат на короткое время
        Cache::put($cacheKey, $isBot, now()->addMinutes(1));

        return $isBot;
    }

    /**
     * Возвращает список вражеских рас для указанной расы
     *
     * @param string $race Раса
     * @return array Список вражеских рас
     */
    protected function getEnemyRaces(string $race): array
    {
        // Определяем противоположные расы для игры (Solarius vs Lunarius)
        $raceRelations = [
            'solarius' => ['lunarius'],
            'lunarius' => ['solarius'],
        ];

        return $raceRelations[$race] ?? [];
    }

    /**
     * Проверяет, являются ли две расы враждебными друг другу
     *
     * @param string $race1 Первая раса
     * @param string $race2 Вторая раса
     * @return bool True если расы враждебны
     */
    protected function areEnemyRaces(string $race1, string $race2): bool
    {
        $enemyRaces = $this->getEnemyRaces($race1);
        return in_array($race2, $enemyRaces, true);
    }

    /**
     * Проверяет, находятся ли бот и игрок в одной и той же подлокации рудника
     * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Боты должны атаковать только в пределах своей подлокации
     *
     * @param Bot $bot Бот
     * @param User $player Игрок  
     * @return bool True если в одной подлокации
     */
    protected function areInSameMineSubLocation(Bot $bot, User $player): bool
    {
        try {
            // Получаем текущую локацию игрока
            $playerLocation = $player->statistics->current_location ?? null;
            
            if (!$playerLocation) {
                Log::debug("Игрок {$player->name} не имеет текущей локации");
                return false;
            }

            // Получаем локацию бота
            $botLocation = $bot->location;
            
            if (!$botLocation) {
                Log::debug("Бот {$bot->name} не имеет локации");
                return false;
            }

            // ТОЧНОЕ совпадение подлокаций в рудниках
            $sameLocation = $playerLocation === $botLocation;
            
            Log::debug("Проверка подлокации рудника", [
                'bot_name' => $bot->name,
                'bot_location' => $botLocation,
                'bot_mine_location_id' => $bot->mine_location_id,
                'player_name' => $player->name,
                'player_location' => $playerLocation,
                'same_location' => $sameLocation
            ]);

            return $sameLocation;

        } catch (\Exception $e) {
            Log::error("Ошибка при проверке подлокации рудника", [
                'bot_id' => $bot->id,
                'player_id' => $player->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Публичный метод для проверки, является ли локация аванпостом
     * Делегирует вызов к методу из OutpostLocationTrait
     *
     * @param string $locationName Название локации
     * @return bool
     */
    public function isOutpostLocationPublic(string $locationName): bool
    {
        return $this->isOutpostLocation($locationName);
    }

    // Метод isOutpostLocation() теперь наследуется из OutpostLocationTrait

    // Метод isBattleLocation() теперь наследуется из OutpostLocationTrait

    /**
     * Симулирует бросок кубика с вероятностью успеха
     *
     * @param int $probability Вероятность успеха (0-100)
     * @return bool Успех или неудача
     */
    protected function rollDice(int $probability): bool
    {
        return mt_rand(1, 100) <= $probability;
    }

    /**
     * Выбирает игрока для атаки ботом среди всех игроков в локации
     * Основано на вероятностном распределении с учетом всех факторов
     * ИСПРАВЛЕНИЕ: Добавлена поддержка боевых локаций
     *
     * @param Bot $bot Бот, который выбирает цель
     * @param int $locationId ID локации
     * @return User|null Выбранный игрок или null, если нет подходящих
     */
    public function chooseBotTarget(Bot $bot, int $locationId): ?User
    {
        try {
            // ИСПРАВЛЕНИЕ: Получаем название локации по ID
            $location = Location::find($locationId);
            if (!$location) {
                return null;
            }

            // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Для рудников используем точную подлокацию бота
            $targetLocation = $bot->location; // Используем точную локацию бота
            
            // Получаем всех игроков в точной подлокации бота
            $players = User::whereHas('statistics', function ($q) use ($targetLocation) {
                $q->where('current_location', $targetLocation);
            })->where('is_online', true)->with('profile')->get();

            if ($players->isEmpty()) {
                return null;
            }

            // ИСПРАВЛЕНИЕ: Определяем тип локации
            $isBattleLocation = $this->isBattleLocation($bot->location);

            // Получаем статистику по локации
            $stats = $this->getLocationPlayerStats($locationId);

            // Создаем взвешенный список игроков
            $weightedPlayers = [];
            $totalWeight = 0;

            foreach ($players as $player) {
                // ИСПРАВЛЕНИЕ: Рассчитываем вероятность с учетом типа локации
                $attackProbability = $this->calculateAttackProbability($bot, $player, $stats, $isBattleLocation);

                // Вес игрока для выбора
                $weight = $attackProbability;

                // Добавляем в список
                $weightedPlayers[] = [
                    'player' => $player,
                    'weight' => $weight
                ];

                $totalWeight += $weight;
            }

            // Если общий вес равен 0, выбираем случайного игрока
            if ($totalWeight === 0) {
                return $players->random();
            }

            // Выбираем игрока на основе весов
            $randValue = mt_rand(1, $totalWeight);
            $currentSum = 0;

            foreach ($weightedPlayers as $item) {
                $currentSum += $item['weight'];
                if ($randValue <= $currentSum) {
                    return $item['player'];
                }
            }

            // Если по какой-то причине не выбрали - возвращаем первого игрока
            return $players->first();
        } catch (\Exception $e) {
            Log::error("Ошибка при выборе цели для бота: " . $e->getMessage(), [
                'bot_id' => $bot->id,
                'location_id' => $locationId,
                'exception' => $e
            ]);

            return null;
        }
    }
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;

class RedisKeyService
{
    private string $prefix;
    
    public function __construct()
    {
        $this->prefix = Config::get('database.redis.default.options.prefix', '');
    }
    
    /**
     * Получает префикс для Redis ключей
     */
    public function getPrefix(): string
    {
        return $this->prefix;
    }
    
    /**
     * Создает ключ для ботов в локации
     */
    public function getBotLocationKey(string $location): string
    {
        return $this->prefix . "location:{$location}:bots";
    }
    
    /**
     * Создает ключ для ботов в локации рудника
     */
    public function getMineBotLocationKey(int $mineLocationId): string
    {
        return $this->prefix . "mine_location:{$mineLocationId}:bots";
    }
    
    /**
     * Создает ключ для игроков в локации
     */
    public function getPlayersLocationKey(string $location): string
    {
        return $this->prefix . "location:{$location}:players";
    }
    
    /**
     * Создает ключ для игроков в локации рудника
     */
    public function getMinePlayersLocationKey(int $mineLocationId): string
    {
        return $this->prefix . "mine_location:{$mineLocationId}:players";
    }
    
    /**
     * Создает ключ для счетчиков фракций в локации
     */
    public function getFactionCountKey(string $location): string
    {
        return $this->prefix . "location:{$location}:faction_counts";
    }
    
    /**
     * Создает ключ для счетчиков фракций в рудниках
     */
    public function getMineFactionCountKey(int $mineLocationId): string
    {
        return $this->prefix . "mine_location:{$mineLocationId}:faction_counts";
    }
    
    /**
     * Создает ключ для онлайн статуса в локации
     */
    public function getOnlineStatusKey(string $location): string
    {
        return $this->prefix . "location:{$location}:online";
    }
    
    /**
     * Создает ключ для онлайн статуса в рудниках
     */
    public function getMineOnlineStatusKey(int $mineLocationId): string
    {
        return $this->prefix . "mine_location:{$mineLocationId}:online";
    }
    
    /**
     * Создает ключ для данных бота
     */
    public function getBotDataKey(int $botId): string
    {
        return $this->prefix . "bot:{$botId}:data";
    }
    
    /**
     * Создает ключ для целей бота
     */
    public function getBotTargetsKey(int $botId): string
    {
        return $this->prefix . "bot:{$botId}:targets";
    }
    
    /**
     * Создает ключ для временных меток бота
     */
    public function getBotTimestampKey(int $botId): string
    {
        return $this->prefix . "bot:{$botId}:timestamp";
    }
    
    /**
     * Создает ключ без префикса для Lua скриптов
     */
    public function getRawKey(string $key): string
    {
        return str_replace($this->prefix, '', $key);
    }
    
    /**
     * Проверяет, является ли ключ системным ботовым ключом
     */
    public function isBotKey(string $key): bool
    {
        return str_contains($key, ':bots') || str_contains($key, ':bot:');
    }
    
    /**
     * Проверяет, является ли ключ системным ключом локации
     */
    public function isLocationKey(string $key): bool
    {
        return str_contains($key, ':location:') || str_contains($key, 'mine_location:');
    }
}
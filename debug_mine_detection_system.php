<?php

/**
 * Полная диагностика системы обнаружения в кастомных рудниках
 * Проверяет всю цепочку: создание дебафа → отображение → автоатаки
 */

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Models\Mob;
use App\Models\SpawnedResource;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "🔍 ДИАГНОСТИКА СИСТЕМЫ ОБНАРУЖЕНИЯ В КАСТОМНЫХ РУДНИКАХ\n";
echo "======================================================\n\n";

// 1. ПРОВЕРЯЕМ ПОЛЬЗОВАТЕЛЯ
echo "👤 ПРОВЕРКА ПОЛЬЗОВАТЕЛЯ:\n";
$user = User::where('name', 'admin')->with('profile', 'statistics')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
echo "   HP: {$user->profile->current_hp}/{$user->profile->max_hp}\n";
echo "   Локация: " . ($user->statistics->current_location ?? 'Не определена') . "\n\n";

// 2. ПРОВЕРЯЕМ КАСТОМНЫЕ РУДНИКИ
echo "⛏️ ПРОВЕРКА КАСТОМНЫХ РУДНИКОВ:\n";
$mineLocations = MineLocation::where('is_active', true)
    ->whereNull('parent_id')
    ->with('baseLocation')
    ->get();

if ($mineLocations->isEmpty()) {
    echo "❌ Нет активных кастомных рудников!\n";
    exit(1);
}

echo "✅ Найдено кастомных рудников: {$mineLocations->count()}\n";
$testMine = $mineLocations->first();
foreach ($mineLocations->take(3) as $mine) {
    echo "   - {$mine->name} (ID: {$mine->id}, Slug: {$mine->slug})\n";
    echo "     Base Location ID: {$mine->location_id}\n";
    
    // Проверяем ресурсы в этом руднике
    $resources = SpawnedResource::where('location_id', $mine->location_id)
        ->where('is_active', true)
        ->count();
    echo "     Активных ресурсов: {$resources}\n";
    
    // Проверяем мобов в этом руднике
    $mobs = Mob::where('location_id', $mine->location_id)
        ->where('mob_type', 'mine')
        ->where('hp', '>', 0)
        ->count();
    echo "     Мобов с mob_type='mine': {$mobs}\n";
}
echo "\n";

// 3. ПРОВЕРЯЕМ ТАБЛИЦЫ
echo "🗃️ ПРОВЕРКА ТАБЛИЦ:\n";
$hasMineMarks = Schema::hasTable('mine_marks');
$hasActiveEffects = Schema::hasTable('active_effects');
echo "   mine_marks: " . ($hasMineMarks ? '✅ Есть' : '❌ Нет') . "\n";
echo "   active_effects: " . ($hasActiveEffects ? '✅ Есть' : '❌ Нет') . "\n\n";

// 4. ТЕСТИРУЕМ СОЗДАНИЕ ДЕБАФА
echo "🎯 ТЕСТ СОЗДАНИЯ ДЕБАФА:\n";

// Очищаем старые тестовые данные
MineMark::where('player_id', $user->id)->delete();
ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();

echo "   Очищены старые тестовые данные\n";

// Тестируем сервис
try {
    $mineDetectionService = app(MineDetectionService::class);
    
    echo "   Применяем дебаф для рудника: {$testMine->name}\n";
    $mark = $mineDetectionService->applyDetectionDebuff($user, $testMine);
    
    if ($mark) {
        echo "✅ MineMark создан (ID: {$mark->id})\n";
        echo "   Игрок: {$mark->player_id}\n";
        echo "   Рудник: {$mark->mine_location_id}\n"; 
        echo "   Истекает: {$mark->expires_at}\n";
        echo "   Активен: " . ($mark->is_active ? 'Да' : 'Нет') . "\n";
    } else {
        echo "❌ MineMark НЕ создан!\n";
    }
    
    // Проверяем создание ActiveEffect
    $activeEffect = ActiveEffect::where('target_type', 'App\\Models\\User')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection')
        ->first();
        
    if ($activeEffect) {
        echo "✅ ActiveEffect создан (ID: {$activeEffect->id})\n";
        echo "   Название: '{$activeEffect->effect_name}'\n";
        echo "   Тип: '{$activeEffect->effect_type}'\n";
        echo "   Истекает: {$activeEffect->ends_at}\n";
        echo "   Активен: " . ($activeEffect->isActive() ? 'Да' : 'Нет') . "\n";
        echo "   Оставшееся время: {$activeEffect->remaining_duration}с\n";
    } else {
        echo "❌ ActiveEffect НЕ создан!\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Ошибка при создании дебафа: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. ТЕСТИРУЕМ ЗАГРУЗКУ ЭФФЕКТОВ КАК В КОНТРОЛЛЕРЕ
echo "📋 ТЕСТ ЗАГРУЗКИ АКТИВНЫХ ЭФФЕКТОВ (CustomMineController):\n";

try {
    // Точно так же, как в CustomMineController строка 334
    $allUserEffects = $user->activeEffects()->with('skill')->get();
    $userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());
    
    echo "✅ Всего эффектов у пользователя: {$allUserEffects->count()}\n";
    echo "✅ Активных эффектов: {$userEffects->count()}\n";
    
    if ($userEffects->count() > 0) {
        echo "   Типы активных эффектов:\n";
        foreach ($userEffects as $effect) {
            echo "     - {$effect->effect_type}: '{$effect->effect_name}' (время: {$effect->remaining_duration}с)\n";
            
            // Проверяем специально mine_detection
            if ($effect->effect_type === 'mine_detection') {
                echo "       🎯 НАЙДЕН MINE_DETECTION ЭФФЕКТ!\n";
                echo "       - isActive(): " . ($effect->isActive() ? 'true' : 'false') . "\n";
                echo "       - remaining_duration: {$effect->remaining_duration}\n";
                echo "       - skill загружен: " . ($effect->skill ? 'да' : 'нет') . "\n";
                echo "       - effect_data: " . json_encode($effect->effect_data) . "\n";
            }
        }
    } else {
        echo "   Нет активных эффектов\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Ошибка при загрузке эффектов: " . $e->getMessage() . "\n";
}

echo "\n";

// 6. ТЕСТИРУЕМ КОМПОНЕНТ АКТИВНЫХ ЭФФЕКТОВ
echo "🎨 ТЕСТ КОМПОНЕНТА ACTIVE-EFFECTS:\n";

$mineDetectionEffects = $userEffects->where('effect_type', 'mine_detection');

if ($mineDetectionEffects->count() > 0) {
    echo "✅ Найдено mine_detection эффектов: {$mineDetectionEffects->count()}\n";
    
    foreach ($mineDetectionEffects as $effect) {
        echo "   Тестируем эффект ID {$effect->id}:\n";
        
        // Проверяем условия из компонента (строки 22, 28)
        $passesTimeCheck = $effect->remaining_duration > 0;
        $isActiveCheck = $effect->isActive();
        $isMineDetection = $effect->effect_type == 'mine_detection';
        
        echo "     - remaining_duration > 0: " . ($passesTimeCheck ? '✅ true' : '❌ false') . " ({$effect->remaining_duration})\n";
        echo "     - isActive(): " . ($isActiveCheck ? '✅ true' : '❌ false') . "\n";
        echo "     - effect_type == 'mine_detection': " . ($isMineDetection ? '✅ true' : '❌ false') . "\n";
        
        if ($passesTimeCheck && $isActiveCheck && $isMineDetection) {
            echo "   🎉 ВСЕ УСЛОВИЯ ВЫПОЛНЕНЫ! Эффект БУДЕТ отображаться!\n";
            echo "       Используется специальная иконка: assets/obelisk_mark.png\n";
            echo "       Цвет: text-red-400 (красный)\n";
            echo "       Анимация: animate-pulse\n";
        } else {
            echo "   ❌ НЕ ВСЕ УСЛОВИЯ ВЫПОЛНЕНЫ - эффект НЕ будет отображаться!\n";
        }
    }
} else {
    echo "❌ mine_detection эффекты не найдены\n";
}

echo "\n";

// 7. ПРОВЕРЯЕМ ПЛАНИРОВЩИК И АВТОАТАКИ
echo "⚔️ ПРОВЕРКА АВТОМАТИЧЕСКИХ АТАК:\n";

// Проверяем наличие активных меток для атак
$activeMarks = MineMark::where('is_active', true)
    ->where('expires_at', '>', now())
    ->count();
    
echo "   Активных меток для атак: {$activeMarks}\n";

// Проверяем мобов для атак в тестовом руднике
$availableMobs = Mob::where('location_id', $testMine->location_id)
    ->where('mob_type', 'mine')
    ->where('hp', '>', 0)
    ->get();
    
echo "   Доступных мобов для атак в '{$testMine->name}': {$availableMobs->count()}\n";

foreach ($availableMobs->take(3) as $mob) {
    echo "     - {$mob->name} (HP: {$mob->hp}, ID: {$mob->id})\n";
}

// Проверяем логи планировщика
$schedulerLogPath = 'storage/logs/scheduler.log';
if (file_exists($schedulerLogPath)) {
    $logContent = file_get_contents($schedulerLogPath);
    $hasMineAttacks = strpos($logContent, 'mine:auto-attack') !== false;
    echo "   Планировщик запускал mine:auto-attack: " . ($hasMineAttacks ? '✅ Да' : '❌ Нет') . "\n";
} else {
    echo "   Лог планировщика не найден: {$schedulerLogPath}\n";
}

echo "\n";

// 8. СИМУЛИРУЕМ АВТОАТАКУ (если есть активные метки и мобы)
if ($activeMarks > 0 && $availableMobs->count() > 0) {
    echo "🤖 СИМУЛЯЦИЯ АВТОАТАКИ:\n";
    
    try {
        $job = new \App\Jobs\MineAutoAttackJob();
        
        echo "   Запускаем MineAutoAttackJob...\n";
        
        $job->handle(
            app(App\Services\MineDetectionService::class),
            app(App\Services\BattleLogService::class), 
            app(App\Services\PlayerHealthService::class),
            app(App\Services\CombatFormulaService::class),
            app(App\Services\LogFormattingService::class)
        );
        
        echo "✅ MineAutoAttackJob выполнен без ошибок\n";
        
        // Проверяем, изменилось ли здоровье игрока
        $user->refresh();
        echo "   Здоровье после атаки: {$user->profile->current_hp}/{$user->profile->max_hp}\n";
        
    } catch (\Exception $e) {
        echo "❌ Ошибка при выполнении автоатаки: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// 9. ФИНАЛЬНАЯ ДИАГНОСТИКА
echo "📊 ФИНАЛЬНАЯ ДИАГНОСТИКА:\n";
echo "========================\n";

$diagnosis = [
    'Таблица mine_marks' => $hasMineMarks ? '✅' : '❌',
    'Создание MineMark' => isset($mark) && $mark ? '✅' : '❌', 
    'Создание ActiveEffect' => isset($activeEffect) && $activeEffect ? '✅' : '❌',
    'Загрузка эффектов с ->with(\'skill\')' => ($userEffects->count() > 0) ? '✅' : '⚠️',
    'mine_detection в активных' => $mineDetectionEffects->count() > 0 ? '✅' : '❌',
    'Мобы для атак' => $availableMobs->count() > 0 ? '✅' : '❌'
];

foreach ($diagnosis as $check => $status) {
    echo "   {$check}: {$status}\n";
}

echo "\n🔧 РЕКОМЕНДАЦИИ:\n";

if (!$hasMineMarks) {
    echo "❗ Запустите создание таблицы mine_marks:\n";
    echo "   run_mine_migrations.bat\n";
}

if (isset($activeEffect) && $activeEffect && $mineDetectionEffects->isEmpty()) {
    echo "❗ ActiveEffect создается, но не попадает в активные эффекты\n";
    echo "   Проверьте метод isActive() в модели ActiveEffect\n";
}

if ($availableMobs->count() == 0) {
    echo "❗ Добавьте мобов с mob_type='mine' в локации рудников\n";
    echo "   INSERT INTO mobs (name, location_id, mob_type, hp, ...) VALUES (...)\n";
}

echo "\n🧹 ОЧИСТКА ТЕСТОВЫХ ДАННЫХ:\n";

// Удаляем тестовые данные
if (isset($mark) && $mark->exists) {
    $mark->delete();
    echo "✅ Тестовая метка удалена\n";
}

ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();
echo "✅ Тестовые эффекты удалены\n";

echo "\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА!\n";
echo "\n🎯 ОСНОВНЫЕ ВЫВОДЫ:\n";

if (isset($activeEffect) && $activeEffect && $mineDetectionEffects->count() > 0) {
    echo "✅ Система создания дебафов работает\n";
    echo "✅ ActiveEffect создается и загружается с ->with('skill')\n"; 
    echo "✅ Компонент active-effects должен отображать дебаф\n";
    echo "\n📋 Если дебаф всё равно не показывается:\n";
    echo "   1. Проверьте браузерные логи на JS ошибки\n";
    echo "   2. Убедитесь, что компонент включен в layout\n";
    echo "   3. Проверьте CSS стили компонента\n";
} else {
    echo "❌ Проблемы с созданием или загрузкой дебафов\n";
    echo "   Нужно дополнительно исследовать сервис MineDetectionService\n";
}
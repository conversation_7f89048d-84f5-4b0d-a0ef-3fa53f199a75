<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Проверка данных ботов и игроков ===\n\n";

// Проверяем расы ботов
echo "Уникальные расы ботов:\n";
$botRaces = Bot::distinct('race')->pluck('race')->toArray();
foreach ($botRaces as $race) {
    $count = Bot::where('race', $race)->count();
    echo "- {$race}: {$count} ботов\n";
}

echo "\nУникальные классы ботов:\n";
$botClasses = Bot::distinct('class')->pluck('class')->toArray();
foreach ($botClasses as $class) {
    $count = Bot::where('class', $class)->count();
    echo "- {$class}: {$count} ботов\n";
}

// Проверяем расы игроков
echo "\nУникальные расы игроков (через profile):\n";
$userRaces = \DB::table('user_profiles')->distinct('race')->pluck('race')->toArray();
foreach ($userRaces as $race) {
    if ($race) {
        $count = \DB::table('user_profiles')->where('race', $race)->count();
        echo "- {$race}: {$count} игроков\n";
    }
}

echo "\nУникальные классы игроков (через profile):\n";
$userClasses = \DB::table('user_profiles')->distinct('class')->pluck('class')->toArray();
foreach ($userClasses as $class) {
    if ($class) {
        $count = \DB::table('user_profiles')->where('class', $class)->count();
        echo "- {$class}: {$count} игроков\n";
    }
}

// Проверяем активных ботов
echo "\nАктивные боты:\n";
$activeBots = Bot::where('is_active', true)->where('hp', '>', 0)->count();
$totalBots = Bot::count();
echo "Активных: {$activeBots} из {$totalBots} общих\n";

// Проверяем распределение ботов по расам
echo "\nРаспределение активных ботов по расам:\n";
foreach ($botRaces as $race) {
    $activeCount = Bot::where('race', $race)->where('is_active', true)->where('hp', '>', 0)->count();
    echo "- {$race}: {$activeCount} активных\n";
}
<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Очистка застрявших сущностей ===\n\n";

$dryRun = true; // Измените на false для реального выполнения
echo "РЕЖИМ: " . ($dryRun ? "ТЕСТОВЫЙ (изменения НЕ применяются)" : "РЕАЛЬНЫЙ (изменения применяются)") . "\n\n";

// 1. Очистка мертвых ботов, помеченных как активные
echo "=== 1. Поиск мертвых ботов с is_active = true ===\n";
$deadActiveBots = Bot::where('is_active', true)
    ->where('hp', '<=', 0)
    ->get();

echo "Найдено мертвых ботов помеченных как активные: " . $deadActiveBots->count() . "\n";

if ($deadActiveBots->count() > 0) {
    foreach ($deadActiveBots as $bot) {
        echo "  Бот: {$bot->name} ({$bot->race} {$bot->class}) в {$bot->location} - HP: {$bot->hp}\n";
    }
    
    if (!$dryRun) {
        $updated = Bot::where('is_active', true)
            ->where('hp', '<=', 0)
            ->update(['is_active' => false]);
        echo "  Деактивировано ботов: {$updated}\n";
    }
}

// 2. Очистка ботов с death_time но is_active = true
echo "\n=== 2. Поиск ботов с death_time но is_active = true ===\n";
$deadTimeBots = Bot::where('is_active', true)
    ->whereNotNull('death_time')
    ->get();

echo "Найдено ботов с death_time но активных: " . $deadTimeBots->count() . "\n";

if ($deadTimeBots->count() > 0) {
    foreach ($deadTimeBots as $bot) {
        echo "  Бот: {$bot->name} ({$bot->race} {$bot->class}) - death_time: {$bot->death_time}\n";
    }
    
    if (!$dryRun) {
        $updated = Bot::where('is_active', true)
            ->whereNotNull('death_time')
            ->update(['is_active' => false]);
        echo "  Деактивировано ботов: {$updated}\n";
    }
}

// 3. Очистка игроков с устаревшими данными активности (старше 30 минут)
echo "\n=== 3. Поиск игроков с устаревшими данными активности ===\n";
$staleThreshold = now()->subMinutes(30)->timestamp;
$stalePlayers = User::where('last_activity_timestamp', '<', $staleThreshold)
    ->where('last_activity_timestamp', '>', now()->subDays(7)->timestamp) // не старше недели
    ->whereHas('statistics')
    ->with(['profile', 'statistics'])
    ->get();

echo "Найдено игроков с устаревшей активностью: " . $stalePlayers->count() . "\n";

$lunariusMageStale = $stalePlayers->filter(function ($player) {
    return ($player->profile->race ?? '') === 'lunarius' && 
           ($player->profile->class ?? '') === 'mage';
});

echo "Из них Лунариус Магов: " . $lunariusMageStale->count() . "\n";

foreach ($lunariusMageStale as $player) {
    $lastActivity = date('Y-m-d H:i:s', $player->last_activity_timestamp);
    $currentLocation = $player->statistics->current_location ?? 'Неизвестно';
    echo "  {$player->name} - последняя активность: {$lastActivity}, локация: {$currentLocation}\n";
}

// 4. Проверка и очистка некорректных локаций
echo "\n=== 4. Поиск ботов с некорректными локациями ===\n";
$botsWithEmptyLocation = Bot::where('is_active', true)
    ->where(function ($q) {
        $q->whereNull('location')
          ->orWhere('location', '')
          ->orWhere('location', 'undefined')
          ->orWhere('location', 'null');
    })
    ->get();

echo "Найдено ботов с некорректными локациями: " . $botsWithEmptyLocation->count() . "\n";

foreach ($botsWithEmptyLocation as $bot) {
    echo "  Бот: {$bot->name} ({$bot->race} {$bot->class}) - локация: '{$bot->location}'\n";
}

if (!$dryRun && $botsWithEmptyLocation->count() > 0) {
    $updated = Bot::where('is_active', true)
        ->where(function ($q) {
            $q->whereNull('location')
              ->orWhere('location', '')
              ->orWhere('location', 'undefined')
              ->orWhere('location', 'null');
        })
        ->update(['is_active' => false]);
    echo "  Деактивировано ботов с некорректными локациями: {$updated}\n";
}

// 5. Специальная очистка для локации "аааааааааааа"
echo "\n=== 5. Проверка локации 'аааааааааааа' ===\n";

$playersInLocationA = User::whereHas('statistics', function ($q) {
    $q->where('current_location', 'аааааааааааа');
})
->where('last_activity_timestamp', '<', now()->subMinutes(10)->timestamp) // неактивные 10+ минут
->with(['profile', 'statistics'])
->get();

echo "Неактивных игроков в локации 'аааааааааааа': " . $playersInLocationA->count() . "\n";

foreach ($playersInLocationA as $player) {
    $race = $player->profile->race ?? 'unknown';
    $class = $player->profile->class ?? 'unknown';
    $lastActivity = date('Y-m-d H:i:s', $player->last_activity_timestamp);
    echo "  {$player->name} ({$race} {$class}) - последняя активность: {$lastActivity}\n";
}

// 6. Рекомендации по очистке
echo "\n=== РЕКОМЕНДАЦИИ ===\n";

if ($stalePlayers->count() > 0) {
    echo "1. Обновить систему онлайн статуса для корректного отслеживания активности\n";
}

if ($deadActiveBots->count() > 0 || $deadTimeBots->count() > 0) {
    echo "2. Исправить логику респавна ботов - мертвые боты остаются активными\n";
}

if ($botsWithEmptyLocation->count() > 0) {
    echo "3. Добавить валидацию локации при создании/обновлении ботов\n";
}

echo "4. Рассмотреть добавление автоматической очистки устаревших данных\n";

echo "\n=== КОМАНДЫ ДЛЯ РУЧНОЙ ОЧИСТКИ ===\n";
echo "-- Деактивация мертвых ботов:\n";
echo "UPDATE bots SET is_active = false WHERE is_active = true AND hp <= 0;\n\n";

echo "-- Деактивация ботов с death_time:\n";
echo "UPDATE bots SET is_active = false WHERE is_active = true AND death_time IS NOT NULL;\n\n";

echo "-- Деактивация ботов без локации:\n";
echo "UPDATE bots SET is_active = false WHERE is_active = true AND (location IS NULL OR location = '' OR location = 'undefined');\n\n";

if ($dryRun) {
    echo "\n!!! Для применения изменений запустите скрипт с \$dryRun = false !!!\n";
}

echo "\n=== ЗАВЕРШЕНО ===\n";
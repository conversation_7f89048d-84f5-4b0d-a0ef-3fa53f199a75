<?php

/**
 * ЭКСТРЕННЫЙ ПОИСК ВСЕХ СКРЫТЫХ БОТОВ В БАЗЕ ДАННЫХ
 * Находит всех ботов, которые атакуют, но не отображаются в админке
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use App\Models\User;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔍 ЭКСТРЕННЫЙ ПОИСК ВСЕХ СКРЫТЫХ БОТОВ\n";
echo "====================================\n\n";

$targetLocation = 'xzxzxzx';
$attackingBots = ['nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм'];

echo "Целевая подлокация: {$targetLocation}\n";
echo "Боты, которые атакуют: " . implode(', ', $attackingBots) . "\n\n";

// 1. Найдем подлокацию в базе
$mineLocation = MineLocation::where('name', $targetLocation)->first();
if (!$mineLocation) {
    echo "❌ Подлокация '{$targetLocation}' не найдена в mine_locations!\n";
    exit;
}

echo "✅ Подлокация найдена: {$mineLocation->name} (ID: {$mineLocation->id})\n";
echo "   Базовая локация: " . ($mineLocation->baseLocation->name ?? 'неизвестно') . "\n\n";

// 2. Проверим что показывает админка
echo "1. ЧТО ПОКАЗЫВАЕТ АДМИНКА\n";
echo "========================\n";

$adminBots = Bot::where('location', $targetLocation)
    ->where('created_by_admin', true)
    ->get();

echo "Ботов в админке: {$adminBots->count()}\n";
foreach ($adminBots as $bot) {
    echo "✅ {$bot->name} (ID: {$bot->id}, location: '{$bot->location}', mine_location_id: {$bot->mine_location_id})\n";
}

// 3. Найдем ВСЕХ ботов по именам
echo "\n2. ПОИСК ВСЕХ БОТОВ ПО ИМЕНАМ\n";
echo "============================\n";

$allFoundBots = [];
$hiddenBots = [];

foreach ($attackingBots as $botName) {
    $bot = Bot::where('name', $botName)->first();
    if ($bot) {
        $allFoundBots[] = $bot;
        $isInAdmin = $bot->location === $targetLocation;
        $status = $isInAdmin ? '✅ В АДМИНКЕ' : '❌ СКРЫТ';
        
        echo "🤖 {$bot->name}:\n";
        echo "   ID: {$bot->id}\n";
        echo "   location: '{$bot->location}'\n";
        echo "   mine_location_id: {$bot->mine_location_id}\n";
        echo "   is_active: " . ($bot->is_active ? 'true' : 'false') . "\n";
        echo "   created_by_admin: " . ($bot->created_by_admin ? 'true' : 'false') . "\n";
        echo "   Статус: {$status}\n";
        
        if (!$isInAdmin) {
            $hiddenBots[] = $bot;
        }
        echo "\n";
    } else {
        echo "❌ Бот '{$botName}' не найден в таблице bots\n\n";
    }
}

// 4. Найдем ботов с ID локации
echo "3. ПОИСК БОТОВ С ID ЛОКАЦИИ\n";
echo "===========================\n";

$botsWithIdLocation = Bot::where('location', (string) $mineLocation->id)
    ->where('created_by_admin', true)
    ->get();

echo "Ботов с ID локации ({$mineLocation->id}): {$botsWithIdLocation->count()}\n";
foreach ($botsWithIdLocation as $bot) {
    echo "🤖 {$bot->name} (location: '{$bot->location}' должно быть '{$mineLocation->name}')\n";
    if (!in_array($bot, $allFoundBots)) {
        $allFoundBots[] = $bot;
        $hiddenBots[] = $bot;
    }
}

// 5. Поиск по другим вариантам
echo "\n4. ПОИСК ПО ДРУГИМ ВАРИАНТАМ\n";
echo "============================\n";

// Поиск по базовой локации
$baseLocationName = $mineLocation->baseLocation->name ?? null;
if ($baseLocationName) {
    $botsWithBaseLoc = Bot::where('location', $baseLocationName)
        ->where('created_by_admin', true)
        ->get();
    
    echo "Ботов с базовой локацией '{$baseLocationName}': {$botsWithBaseLoc->count()}\n";
    foreach ($botsWithBaseLoc as $bot) {
        echo "🤖 {$bot->name} (в базовой локации вместо подлокации)\n";
    }
}

// Поиск по частичному совпадению
$partialMatch = Bot::where('location', 'like', '%' . substr($targetLocation, 0, 3) . '%')
    ->where('created_by_admin', true)
    ->get();

echo "Ботов с частичным совпадением локации: {$partialMatch->count()}\n";
foreach ($partialMatch as $bot) {
    echo "🤖 {$bot->name} (location: '{$bot->location}')\n";
}

// 6. ПОЛНЫЙ ПОИСК В БАЗЕ
echo "\n5. ПОЛНЫЙ ПОИСК В БАЗЕ ДАННЫХ\n";
echo "============================\n";

$allBots = Bot::where('created_by_admin', true)->get();
$suspiciousBots = [];

foreach ($allBots as $bot) {
    // Проверяем, может ли этот бот атаковать в нашей локации
    $couldAttack = false;
    
    // Если location содержит ID или похожие символы
    if (preg_match('/^[0-9]+$/', $bot->location) || 
        strpos($bot->location, $targetLocation) !== false ||
        in_array($bot->name, $attackingBots)) {
        $couldAttack = true;
    }
    
    if ($couldAttack && $bot->location !== $targetLocation) {
        $suspiciousBots[] = $bot;
    }
}

echo "Подозрительных ботов: " . count($suspiciousBots) . "\n";
foreach ($suspiciousBots as $bot) {
    echo "🚨 {$bot->name} (location: '{$bot->location}', mine_location_id: {$bot->mine_location_id})\n";
}

// 7. НЕМЕДЛЕННОЕ ИСПРАВЛЕНИЕ
echo "\n6. НЕМЕДЛЕННОЕ ИСПРАВЛЕНИЕ\n";
echo "=========================\n";

$fixedCount = 0;

// Исправляем всех найденных скрытых ботов
foreach ($hiddenBots as $bot) {
    $oldLocation = $bot->location;
    $bot->location = $mineLocation->name;
    $bot->mine_location_id = $mineLocation->id;
    $bot->next_action_time = null;
    $bot->save();
    
    echo "✅ ИСПРАВЛЕН: {$bot->name} ('{$oldLocation}' → '{$bot->location}')\n";
    $fixedCount++;
}

// Исправляем ботов с ID локации
foreach ($botsWithIdLocation as $bot) {
    if ($bot->location !== $mineLocation->name) {
        $oldLocation = $bot->location;
        $bot->location = $mineLocation->name;
        $bot->mine_location_id = $mineLocation->id;
        $bot->next_action_time = null;
        $bot->save();
        
        echo "✅ ИСПРАВЛЕН: {$bot->name} ('{$oldLocation}' → '{$bot->location}')\n";
        $fixedCount++;
    }
}

// Исправляем подозрительных ботов
foreach ($suspiciousBots as $bot) {
    if (in_array($bot->name, $attackingBots)) {
        $oldLocation = $bot->location;
        $bot->location = $mineLocation->name;
        $bot->mine_location_id = $mineLocation->id;
        $bot->next_action_time = null;
        $bot->save();
        
        echo "✅ ИСПРАВЛЕН: {$bot->name} ('{$oldLocation}' → '{$bot->location}')\n";
        $fixedCount++;
    }
}

// 8. ФИНАЛЬНАЯ ПРОВЕРКА
echo "\n7. ФИНАЛЬНАЯ ПРОВЕРКА\n";
echo "====================\n";

$finalAdminBots = Bot::where('location', $targetLocation)
    ->where('created_by_admin', true)
    ->get();

echo "Ботов в админке теперь: {$finalAdminBots->count()}\n";
foreach ($finalAdminBots as $bot) {
    $active = $bot->is_active ? '✅' : '❌';
    echo "{$active} {$bot->name} (HP: {$bot->hp}/{$bot->max_hp})\n";
}

// Проверяем, что все атакующие боты теперь в админке
echo "\nПроверка всех атакующих ботов:\n";
foreach ($attackingBots as $botName) {
    $bot = Bot::where('name', $botName)->first();
    if ($bot && $bot->location === $targetLocation) {
        echo "✅ {$botName}: теперь в админке\n";
    } else {
        echo "❌ {$botName}: все еще скрыт\n";
    }
}

echo "\n🎉 ИСПРАВЛЕНИЕ ЗАВЕРШЕНО\n";
echo "=======================\n";
echo "Исправлено ботов: {$fixedCount}\n";
echo "Время: " . now()->format('H:i:s d.m.Y') . "\n";
echo "Теперь в админке должны отображаться ВСЕ боты!\n";
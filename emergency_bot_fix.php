<?php

/**
 * ЭКСТРЕННОЕ ИСПРАВЛЕНИЕ ПРОБЛЕМЫ С АТАКАМИ БОТОВ
 * Немедленно исправляет боты, которые атакуют из неправильных локаций
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\UserStatistic;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🚨 ЭКСТРЕННОЕ ИСПРАВЛЕНИЕ ПРОБЛЕМЫ С АТАКАМИ БОТОВ\n";
echo "============================================\n\n";

// 1. Находим все проблемные боты
echo "1. ПОИСК ПРОБЛЕМНЫХ БОТОВ\n";
echo "------------------------\n";

$problemBots = [];
$mineLocations = MineLocation::where('is_active', true)->get();

foreach ($mineLocations as $mine) {
    // Ищем ботов с неправильными локациями
    $botsWithIdLocation = Bot::where('location', (string) $mine->id)
        ->where('created_by_admin', true)
        ->get();
    
    if ($botsWithIdLocation->count() > 0) {
        echo "❌ Рудник {$mine->name}: найдено {$botsWithIdLocation->count()} ботов с ID локации\n";
        
        foreach ($botsWithIdLocation as $bot) {
            $problemBots[] = [
                'bot' => $bot,
                'correct_location' => $mine->name,
                'mine_id' => $mine->id
            ];
        }
    }
}

// 2. Немедленное исправление
echo "\n2. НЕМЕДЛЕННОЕ ИСПРАВЛЕНИЕ\n";
echo "-------------------------\n";

$fixedCount = 0;
foreach ($problemBots as $problem) {
    $bot = $problem['bot'];
    $oldLocation = $bot->location;
    
    // Исправляем локацию
    $bot->location = $problem['correct_location'];
    $bot->mine_location_id = $problem['mine_id'];
    $bot->next_action_time = null; // Сбрасываем кулдаун
    $bot->save();
    
    echo "✅ {$bot->name}: '{$oldLocation}' → '{$bot->location}'\n";
    $fixedCount++;
}

if ($fixedCount === 0) {
    echo "✅ Проблемных ботов не найдено\n";
} else {
    echo "✅ Исправлено ботов: {$fixedCount}\n";
}

// 3. Проверяем текущее состояние игроков
echo "\n3. ПРОВЕРКА ТЕКУЩИХ ИГРОКОВ\n";
echo "---------------------------\n";

$onlineUsers = User::where('is_online', true)
    ->with(['profile', 'statistics'])
    ->get();

foreach ($onlineUsers as $user) {
    $location = $user->statistics->current_location ?? 'unknown';
    $race = $user->profile->race ?? 'unknown';
    
    echo "👤 {$user->name} ({$race}) в локации: {$location}\n";
    
    // Проверяем ботов в этой локации
    $botsInLocation = Bot::where('location', $location)
        ->where('is_active', true)
        ->where('created_by_admin', true)
        ->get();
    
    foreach ($botsInLocation as $bot) {
        $enemyRace = $bot->race === 'solarius' ? 'lunarius' : 'solarius';
        $shouldAttack = ($race === $enemyRace) ? 'ДА' : 'НЕТ';
        
        echo "   🤖 {$bot->name} ({$bot->race}) - должен атаковать: {$shouldAttack}\n";
    }
}

// 4. Сбрасываем все кулдауны
echo "\n4. СБРОС КУЛДАУНОВ\n";
echo "-----------------\n";

$cooldownCount = Bot::where('created_by_admin', true)
    ->whereNotNull('next_action_time')
    ->count();

Bot::where('created_by_admin', true)->update(['next_action_time' => null]);

echo "✅ Сброшены кулдауны у {$cooldownCount} ботов\n";

// 5. Проверяем изоляцию подлокаций
echo "\n5. ПРОВЕРКА ИЗОЛЯЦИИ ПОДЛОКАЦИЙ\n";
echo "-------------------------------\n";

$isolationIssues = 0;

foreach ($mineLocations as $mine) {
    $bots = Bot::where('location', $mine->name)
        ->where('is_active', true)
        ->where('created_by_admin', true)
        ->get();
    
    if ($bots->count() > 0) {
        echo "📍 {$mine->name}: {$bots->count()} ботов\n";
        
        foreach ($bots as $bot) {
            // Проверяем корректность локации
            if ($bot->location !== $mine->name) {
                echo "   ❌ {$bot->name}: неправильная локация '{$bot->location}'\n";
                $isolationIssues++;
            } else {
                echo "   ✅ {$bot->name}: корректная локация\n";
            }
        }
    }
}

// 6. Создаем лог для мониторинга
echo "\n6. СОЗДАНИЕ ЛОГА МОНИТОРИНГА\n";
echo "----------------------------\n";

$logData = [
    'timestamp' => now()->toDateTimeString(),
    'fixed_bots' => $fixedCount,
    'isolation_issues' => $isolationIssues,
    'mine_locations' => $mineLocations->count(),
    'online_users' => $onlineUsers->count()
];

file_put_contents(storage_path('logs/emergency_bot_fix.log'), 
    json_encode($logData, JSON_PRETTY_PRINT) . "\n", FILE_APPEND);

echo "✅ Лог создан: storage/logs/emergency_bot_fix.log\n";

// 7. Итоговая проверка
echo "\n7. ИТОГОВАЯ ПРОВЕРКА\n";
echo "-------------------\n";

// Проверяем, остались ли проблемные боты
$remainingIssues = 0;

foreach ($mineLocations as $mine) {
    $botsWithIdLocation = Bot::where('location', (string) $mine->id)
        ->where('created_by_admin', true)
        ->count();
    
    if ($botsWithIdLocation > 0) {
        $remainingIssues += $botsWithIdLocation;
    }
}

if ($remainingIssues === 0) {
    echo "🎉 ВСЕ ПРОБЛЕМЫ ИСПРАВЛЕНЫ!\n";
    echo "   - Боты больше не атакуют из неправильных локаций\n";
    echo "   - Изоляция подлокаций восстановлена\n";
    echo "   - Кулдауны сброшены\n";
} else {
    echo "⚠️  Осталось проблемных ботов: {$remainingIssues}\n";
    echo "   Требуется дополнительное исправление\n";
}

echo "\n🏁 ЭКСТРЕННОЕ ИСПРАВЛЕНИЕ ЗАВЕРШЕНО\n";
echo "==================================\n";
echo "Время: " . now()->format('H:i:s d.m.Y') . "\n";
echo "Исправлено ботов: {$fixedCount}\n";
echo "Онлайн игроков: {$onlineUsers->count()}\n";
echo "Активных рудников: {$mineLocations->count()}\n";
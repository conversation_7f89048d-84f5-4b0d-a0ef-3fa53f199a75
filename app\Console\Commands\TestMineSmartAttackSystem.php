<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\MineMark;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use App\Jobs\MineAutoAttackJob;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

class TestMineSmartAttackSystem extends Command
{
    protected $signature = 'test:mine-smart-attack {--dry-run : Только показать информацию без выполнения атак} {--stats : Показать подробную статистику}';
    protected $description = 'Тестирование умной системы атак мобов в рудниках';

    public function handle()
    {
        $this->info('🎯 ТЕСТИРОВАНИЕ УМНОЙ СИСТЕМЫ АТАК МОБОВ В РУДНИКАХ');
        $this->info('=' . str_repeat('=', 60));

        // Проверяем наличие таблицы mine_marks
        if (!Schema::hasTable('mine_marks')) {
            $this->error('❌ Таблица mine_marks не существует!');
            $this->info('💡 Запустите миграцию: php artisan migrate');
            return 1;
        }

        // 1. Проверяем наличие тестовых данных
        $this->checkTestData();

        // 2. Тестируем сервис распределения
        $this->testDistributionService();

        // 3. Тестируем умную атаку (если не dry-run)
        if (!$this->option('dry-run')) {
            $this->testSmartAttack();
        }

        // 4. Показываем статистику (если запрошена)
        if ($this->option('stats')) {
            $this->showDetailedStats();
        }

        $this->info('✅ Тестирование завершено!');
        return 0;
    }

    private function checkTestData()
    {
        $this->info('📊 ПРОВЕРКА ТЕСТОВЫХ ДАННЫХ:');
        
        // Проверяем пользователей
        $users = User::with('profile')->take(3)->get();
        $this->info("   👥 Пользователей: {$users->count()}");

        // Проверяем рудники
        $mineLocations = MineLocation::take(3)->get();
        $this->info("   ⛏️  Локаций рудников: {$mineLocations->count()}");

        // Проверяем мобов
        $mobs = Mob::where('mob_type', 'mine')->where('hp', '>', 0)->take(5)->get();
        $this->info("   👹 Живых мобов в рудниках: {$mobs->count()}");

        // Проверяем активные метки
        $activeMarks = MineMark::where('is_active', true)
            ->where('expires_at', '>', now())
            ->count();
        $this->info("   🎯 Активных меток 'Замечен': {$activeMarks}");

        if ($users->isEmpty() || $mineLocations->isEmpty() || $mobs->isEmpty()) {
            $this->warn('⚠️  Недостаточно тестовых данных для полного тестирования');
        }
    }

    private function testDistributionService()
    {
        $this->info('🧠 ТЕСТИРОВАНИЕ СЕРВИСА РАСПРЕДЕЛЕНИЯ:');

        $distributionService = app(MineTargetDistributionService::class);
        $mineDetectionService = app(MineDetectionService::class);

        // Получаем тестовые данные
        $user = User::with('profile')->first();
        $mineLocation = MineLocation::first();

        if (!$user || !$mineLocation) {
            $this->warn('   ⚠️  Нет данных для тестирования сервиса');
            return;
        }

        // Создаем тестовую метку если её нет
        $existingMark = MineMark::where('player_id', $user->id)
            ->where('mine_location_id', $mineLocation->id)
            ->where('is_active', true)
            ->first();

        if (!$existingMark) {
            $this->info('   📝 Создаем тестовую метку...');
            $mark = $mineDetectionService->createMark($user, $mineLocation, 300);
            $this->info("   ✅ Создана метка ID: {$mark->id}");
        }

        // Получаем замеченных игроков
        $markedPlayers = collect([
            [
                'player' => $user,
                'mine_location' => $mineLocation,
                'last_attack_at' => null
            ]
        ]);

        // Тестируем получение оптимальной пары
        $pair = $distributionService->getOptimalMobPlayerPair($markedPlayers, $mineLocation);

        if ($pair) {
            $this->info('   ✅ Сервис успешно создал пару моб-игрок:');
            $this->info("      🎯 Моб: {$pair['mob']->name} (ID: {$pair['mob']->id})");
            $this->info("      👤 Игрок: {$pair['player']->name} (ID: {$pair['player']->id})");
            
            $playerHp = round(($pair['player']->profile->current_hp / $pair['player']->profile->max_hp) * 100, 1);
            $this->info("      ❤️  HP игрока: {$playerHp}%");
        } else {
            $this->warn('   ⚠️  Сервис не смог создать пару моб-игрок');
        }

        // Показываем статистику распределения
        $stats = $distributionService->getDistributionStats($mineLocation);
        $this->info('   📊 Статистика распределения:');
        $this->info("      - Игроков с мобами: {$stats['total_players_with_mobs']}");
        $this->info("      - Всего назначений: {$stats['total_mob_assignments']}");
    }

    private function testSmartAttack()
    {
        $this->info('⚔️  ТЕСТИРОВАНИЕ УМНОЙ АТАКИ:');

        try {
            // Запускаем джоб умной атаки
            $job = new MineAutoAttackJob();
            
            $this->info('   🚀 Запуск MineAutoAttackJob с умной системой...');
            
            $job->handle(
                app(\App\Services\MineDetectionService::class),
                app(\App\Services\MineTargetDistributionService::class),
                app(\App\Services\BattleLogService::class),
                app(\App\Services\PlayerHealthService::class),
                app(\App\Services\CombatFormulaService::class),
                app(\App\Services\LogFormattingService::class)
            );
            
            $this->info('   ✅ Джоб выполнен успешно');
            
        } catch (\Exception $e) {
            $this->error('   ❌ Ошибка при выполнении джоба:');
            $this->error("      {$e->getMessage()}");
            
            if ($this->option('verbose')) {
                $this->error("      Трассировка: {$e->getTraceAsString()}");
            }
        }
    }

    private function showDetailedStats()
    {
        $this->info('📈 ПОДРОБНАЯ СТАТИСТИКА:');

        // Статистика по меткам
        $totalMarks = MineMark::count();
        $activeMarks = MineMark::where('is_active', true)
            ->where('expires_at', '>', now())
            ->count();
        $expiredMarks = MineMark::where('expires_at', '<=', now())->count();

        $this->info("   🎯 Метки 'Замечен':");
        $this->info("      - Всего: {$totalMarks}");
        $this->info("      - Активных: {$activeMarks}");
        $this->info("      - Истекших: {$expiredMarks}");

        // Статистика по мобам
        $totalMobs = Mob::where('mob_type', 'mine')->count();
        $aliveMobs = Mob::where('mob_type', 'mine')->where('hp', '>', 0)->count();
        $deadMobs = Mob::where('mob_type', 'mine')->where('hp', '<=', 0)->count();

        $this->info("   👹 Мобы в рудниках:");
        $this->info("      - Всего: {$totalMobs}");
        $this->info("      - Живых: {$aliveMobs}");
        $this->info("      - Мертвых: {$deadMobs}");

        // Статистика по локациям
        $mineLocations = MineLocation::count();
        $this->info("   ⛏️  Локаций рудников: {$mineLocations}");

        // Последние атаки
        $recentMarks = MineMark::where('last_attack_at', '>', now()->subMinutes(10))
            ->count();
        $this->info("   ⚔️  Атак за последние 10 минут: {$recentMarks}");
    }
}

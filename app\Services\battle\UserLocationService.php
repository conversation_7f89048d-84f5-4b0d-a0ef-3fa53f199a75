<?php

namespace App\Services\battle;

use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\Bot;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;

/**
 * Централизованный сервис для управления локациями игроков
 * Решает проблемы несогласованности названий и Redis ключей
 */
class UserLocationService
{
    /**
     * Обновляет локацию пользователя с полной синхронизацией
     *
     * @param User $user Пользователь
     * @param string $location Новая локация
     * @return bool Возвращает true, если локация была обновлена
     */
    public function updateUserLocation(User $user, string $location): bool
    {
        $currentLocation = optional($user->statistics)->current_location ?? 'Неизвестно';

        if ($currentLocation !== $location) {
            Log::info("Обновление локации пользователя", [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_race' => $user->profile->race ?? 'неизвестно',
                'user_class' => $user->profile->class ?? 'неизвестно',
                'старое_местоположение' => $currentLocation,
                'новое_местоположение' => $location,
                'timestamp' => now()->toDateTimeString()
            ]);

            // Нормализуем название локации
            $normalizedLocation = $this->normalizeLocationName($location);

            // Обновляем локацию в базе данных
            $updated = $user->statistics->update(['current_location' => $normalizedLocation]);

            if ($updated) {
                Log::info("Локация пользователя успешно обновлена в БД", [
                    'user_id' => $user->id,
                    'new_location' => $normalizedLocation,
                    'original_location' => $location
                ]);
            } else {
                Log::error("Не удалось обновить локацию пользователя в БД", [
                    'user_id' => $user->id,
                    'attempted_location' => $normalizedLocation
                ]);
            }

            $user->refresh();

            // Сбрасываем цели при смене локации
            $this->resetUserTargetsOnLocationChange($user, $normalizedLocation);

            // Очищаем кеш локации
            $this->clearLocationCache($currentLocation);
            $this->clearLocationCache($normalizedLocation);

            Log::info("Локация пользователя полностью обновлена", [
                'user_id' => $user->id,
                'location' => $normalizedLocation
            ]);

            return true;
        }

        return false;
    }

    /**
     * Нормализует название локации для единообразия
     *
     * @param string $location Исходное название локации
     * @return string Нормализованное название
     */
    public function normalizeLocationName(string $location): string
    {
        // Убираем лишние пробелы
        $normalized = trim($location);

        // Проверяем, есть ли такая локация в основной таблице
        $mainLocation = Location::where('name', $normalized)->first();
        if ($mainLocation) {
            return $mainLocation->name;
        }

        // Проверяем в таблице рудников
        $mineLocation = MineLocation::where('name', $normalized)->first();
        if ($mineLocation) {
            // ИСПРАВЛЕНИЕ: Для рудников возвращаем название самого рудника, а не базовой локации
            // Это обеспечивает единообразие с тем, как игроки находятся в локациях
            \Log::debug("UserLocationService: нормализация рудника", [
                'original' => $normalized,
                'mine_location' => $mineLocation->name,
                'base_location' => $mineLocation->baseLocation ? $mineLocation->baseLocation->name : null,
                'normalized_to' => $mineLocation->name
            ]);
            return $mineLocation->name;
        }

        // Если не найдено, возвращаем как есть, но очищенное
        \Log::debug("UserLocationService: локация не найдена, возвращаем как есть", [
            'original' => $location,
            'normalized' => $normalized
        ]);
        return $normalized;
    }

    /**
     * Получает ID локации по названию
     *
     * @param string $locationName Название локации
     * @return int|null ID локации или null если не найдена
     */
    public function getLocationId(string $locationName): ?int
    {
        $normalized = $this->normalizeLocationName($locationName);

        $location = Location::where('name', $normalized)->first();
        return $location ? $location->id : null;
    }

    /**
     * Формирует корректный Redis ключ для локации
     *
     * @param string $locationName Название локации
     * @param int $userId ID пользователя
     * @param string $prefix Префикс ключа
     * @return string Redis ключ
     */
    public function getLocationRedisKey(string $locationName, int $userId, string $prefix = 'location'): string
    {
        $locationId = $this->getLocationId($locationName);

        if ($locationId) {
            return "{$prefix}:{$locationId}:{$userId}";
        }

        // Fallback для неизвестных локаций
        $slug = strtolower(str_replace([' ', '-', '(', ')', '[', ']'], '_', $locationName));
        return "{$prefix}:{$slug}:{$userId}";
    }

    /**
     * Сбрасывает цели игроков при смене локации
     *
     * @param User $user Пользователь
     * @param string $newLocation Новая локация
     * @return void
     */
    protected function resetUserTargetsOnLocationChange(User $user, string $newLocation): void
    {
        // Сбрасываем цели для всех игроков, атакующих этого игрока
        $resetTargetsCount = User::where('current_target_type', 'player')
            ->where('current_target_id', $user->id)
            ->update(['current_target_id' => null, 'current_target_type' => null]);

        if ($resetTargetsCount > 0) {
            Log::info("Сброшены цели игроков, атакующих пользователя", [
                'user_id' => $user->id,
                'reset_count' => $resetTargetsCount
            ]);
        }

        // ИСПРАВЛЕНО: Проверяем и сбрасываем last_attacker_id у других игроков, если этот игрок был их атакующим
        // Когда игрок меняет локацию, он больше не может быть целью для "Бить в ответ" от игроков в других локациях
        $this->resetLastAttackerForOtherPlayers($user, $newLocation);

        Log::info("Обновлена информация о последнем атакующем при смене локации", [
            'user_id' => $user->id,
            'last_attacker_id' => $user->last_attacker_id,
            'last_attacker_type' => $user->last_attacker_type,
            'new_location' => $newLocation,
            'note' => 'last_attacker_id других игроков обновлен с учетом строгой изоляции локаций'
        ]);
    }

    /**
     * Очищает кеш локации
     *
     * @param string $locationName Название локации
     * @return void
     */
    public function clearLocationCache(string $locationName): void
    {
        $locationId = $this->getLocationId($locationName);

        if ($locationId) {
            // Очищаем кеш по ID локации
            Cache::forget("location:{$locationId}:player_stats");
            Cache::forget("location:{$locationId}:players_count");
            Cache::forget("location:{$locationId}:bots_count");
        }

        // Очищаем кеш по названию (fallback)
        $slug = strtolower(str_replace([' ', '-', '(', ')', '[', ']'], '_', $locationName));
        Cache::forget("location:{$slug}:player_stats");
        Cache::forget("location:{$slug}:players_count");
        Cache::forget("location:{$slug}:bots_count");
    }

    /**
     * Получает количество игроков в локации
     *
     * @param string $locationName Название локации
     * @param string|null $race Фильтр по расе (опционально)
     * @param string|null $class Фильтр по классу (опционально)
     * @return int Количество игроков
     */
    public function getPlayersCountInLocation(string $locationName, ?string $race = null, ?string $class = null): int
    {
        $normalizedLocation = $this->normalizeLocationName($locationName);

        $query = User::whereHas('statistics', function ($q) use ($normalizedLocation) {
            $q->where('current_location', $normalizedLocation);
        })
            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp);

        // ИСПРАВЛЕНИЕ: Базовая фильтрация - исключаем мертвых игроков
        $query->whereHas('profile', function ($q) use ($race, $class) {
            $q->where('current_hp', '>', 0);
            // TODO: Добавить ->where('is_defeated', false) после выполнения миграции

            // Дополнительная фильтрация по расе если указана
            if ($race && in_array($race, ['solarius', 'lunarius'])) {
                $q->where('race', $race);
            }

            // Дополнительная фильтрация по классу если указан
            if ($class) {
                $classMapping = [
                    'Воин' => 'warrior',
                    'Маг' => 'mage',
                    'Жрец' => 'priest'
                ];
                $englishClass = $classMapping[$class] ?? $class;
                $q->where('class', $englishClass);
            }
        });

        // Логируем неподдерживаемые расы отдельно
        if ($race && !in_array($race, ['solarius', 'lunarius'])) {
            \Log::warning("UserLocationService: неподдерживаемая раса для фильтрации", [
                'requested_race' => $race,
                'supported_races' => ['solarius', 'lunarius'],
                'location' => $normalizedLocation
            ]);
        }

        $count = $query->count();

        \Log::debug("UserLocationService: подсчет живых игроков в локации", [
            'location' => $normalizedLocation,
            'race_filter' => $race,
            'class_filter' => $class,
            'count' => $count,
            'applied_filters' => [
                'current_hp > 0',
                'онлайн за последние 5 минут'
            ]
        ]);

        return $count;
    }

    /**
     * Получает количество ботов в локации
     *
     * @param string $locationName Название локации
     * @param string|null $race Фильтр по расе (опционально)
     * @param string|null $class Фильтр по классу (опционально)
     * @return int Количество ботов
     */
    public function getBotsCountInLocation(string $locationName, ?string $race = null, ?string $class = null): int
    {
        $normalizedLocation = $this->normalizeLocationName($locationName);

        // Маппинг классов: русские названия -> английские (для ботов используются английские)
        $classMapping = [
            'Воин' => 'warrior',
            'Маг' => 'mage',
            'Жрец' => 'priest'
        ];

        $englishClass = $class ? ($classMapping[$class] ?? $class) : null;

        // Создаем единый запрос для подсчета ботов, исключая дубли
        $query = Bot::where('is_active', true)
            ->where('hp', '>', 0) // Исключаем мертвых ботов из подсчета
            ->whereNull('death_time') // Исключаем ботов с временем смерти
            ->whereNotNull('location') // Исключаем ботов без локации
            ->where('location', '!=', ''); // Исключаем ботов с пустой локацией

        if ($race) {
            $query->where('race', $race);
        }

        if ($englishClass) {
            $query->where('class', $englishClass);
        }

        // ИСПРАВЛЕНО: Строгая изоляция - ищем ботов только в точно той же локации
        $mineLocation = \App\Models\MineLocation::where('name', $normalizedLocation)->first();

        if ($mineLocation) {
            // Для рудника ищем ботов с конкретным mine_location_id
            $query->where('mine_location_id', $mineLocation->id);
        } else {
            // Для обычных локаций ищем ботов без mine_location_id
            $query->where('location', $normalizedLocation)
                  ->whereNull('mine_location_id');
        }

        $totalCount = $query->count();

        \Log::debug("UserLocationService: подсчет ботов в локации (исправлен дубль)", [
            'location' => $normalizedLocation,
            'race_filter' => $race,
            'class_filter' => $class,
            'english_class' => $englishClass,
            'total_count' => $totalCount,
            'mine_location_found' => $mineLocation ? $mineLocation->id : null
        ]);

        return $totalCount;
    }

    /**
     * Получает игроков в локации
     *
     * @param string $locationName Название локации
     * @param int|null $excludeUserId ID пользователя для исключения
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPlayersInLocation(string $locationName, ?int $excludeUserId = null)
    {
        $normalizedLocation = $this->normalizeLocationName($locationName);

        $query = User::whereHas('statistics', function ($q) use ($normalizedLocation) {
            $q->where('current_location', $normalizedLocation);
        })
            ->whereHas('profile', function ($q) {
                // ИСПРАВЛЕНИЕ: Исключаем мертвых игроков из счетчиков
                $q->where('current_hp', '>', 0);
                // TODO: Добавить ->where('is_defeated', false) после выполнения миграции
            })
            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp) // Унификация с EnemyFinderService
            ->with(['profile', 'statistics']);

        if ($excludeUserId) {
            $query->where('id', '!=', $excludeUserId);
        }

        \Log::debug("UserLocationService: поиск живых игроков в локации", [
            'location' => $normalizedLocation,
            'original_location' => $locationName,
            'exclude_user_id' => $excludeUserId,
            'query_conditions' => [
                'current_hp > 0',
                'онлайн за последние 5 минут'
            ]
        ]);

        return $query->get();
    }

    /**
     * Проверяет, находятся ли два игрока в одной локации
     * ИСПРАВЛЕНО: Строгая проверка - игроки должны быть в точно одной локации для PvP
     *
     * @param User $user1 Первый игрок
     * @param User $user2 Второй игрок
     * @return bool Находятся ли игроки в одной локации
     */
    public function arePlayersInSameLocation(User $user1, User $user2): bool
    {
        $location1 = $user1->statistics->current_location ?? 'Неизвестно';
        $location2 = $user2->statistics->current_location ?? 'Неизвестно';

        // Нормализуем локации
        $normalizedLocation1 = $this->normalizeLocationName($location1);
        $normalizedLocation2 = $this->normalizeLocationName($location2);

        // ИСПРАВЛЕНО: Только прямое совпадение для строгой изоляции PvP
        $directMatch = $normalizedLocation1 === $normalizedLocation2;

        \Log::debug("UserLocationService: проверка совпадения локаций игроков (строгая изоляция)", [
            'user1_id' => $user1->id,
            'user2_id' => $user2->id,
            'user1_location_original' => $location1,
            'user2_location_original' => $location2,
            'user1_location_normalized' => $normalizedLocation1,
            'user2_location_normalized' => $normalizedLocation2,
            'direct_match' => $directMatch,
            'strict_isolation' => true,
            'final_result' => $directMatch
        ]);

        return $directMatch;
    }

    /**
     * УДАЛЕНО: Метод areLocationsRelated удален для обеспечения строгой изоляции
     * Все PvP взаимодействия теперь требуют точного совпадения локаций
     * Это предотвращает атаки между базовыми локациями и подлокациями рудников
     */

    /**
     * ИСПРАВЛЕНИЕ: Сбрасывает last_attacker_id у других игроков при смене локации для строгой изоляции
     * Предотвращает использование кнопки "Бить в ответ" между разными локациями
     *
     * @param User $movedUser Игрок, который сменил локацию
     * @param string $newLocation Новая локация игрока
     * @return void
     */
    protected function resetLastAttackerForOtherPlayers(User $movedUser, string $newLocation): void
    {
        // Находим всех игроков, у которых этот игрок записан как last_attacker_id
        $affectedPlayers = User::where('last_attacker_id', $movedUser->id)
            ->where('last_attacker_type', 'player')
            ->whereHas('statistics')
            ->with('statistics')
            ->get();

        $resetCount = 0;

        foreach ($affectedPlayers as $player) {
            $playerLocation = $this->normalizeLocationName($player->statistics->current_location);
            
            // Если игроки больше не в одной локации - сбрасываем last_attacker_id для строгой изоляции
            if ($playerLocation !== $newLocation) {
                $player->update([
                    'last_attacker_id' => null,
                    'last_attacker_type' => null
                ]);
                
                $resetCount++;
                
                Log::info("Сброшен last_attacker_id для строгой изоляции локаций", [
                    'affected_player_id' => $player->id,
                    'affected_player_name' => $player->name,
                    'affected_player_location' => $playerLocation,
                    'moved_player_id' => $movedUser->id,
                    'moved_player_name' => $movedUser->name,
                    'moved_player_new_location' => $newLocation,
                    'reason' => 'strict_location_isolation'
                ]);
            }
        }

        if ($resetCount > 0) {
            Log::info("Сброшены last_attacker_id для поддержания строгой изоляции", [
                'moved_player_id' => $movedUser->id,
                'moved_player_name' => $movedUser->name,
                'new_location' => $newLocation,
                'reset_count' => $resetCount,
                'note' => 'Предотвращены межлокационные атаки через кнопку "Бить в ответ"'
            ]);
        }
    }
}

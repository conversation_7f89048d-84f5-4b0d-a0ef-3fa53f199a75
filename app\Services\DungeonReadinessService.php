<?php

namespace App\Services;

use App\Models\Dungeon;
use App\Models\Party;
use App\Models\User;
use App\Models\PartyMember;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Сервис для валидации готовности группы к началу подземелья
 * 
 * Обеспечивает централизованную проверку готовности участников группы
 * с валидацией их местоположения, статуса и активности
 */
class DungeonReadinessService
{
    /**
     * Максимальное время неактивности в минутах для сброса готовности
     */
    private const MAX_INACTIVE_MINUTES = 2;

    /**
     * Проверяет готовность всей группы к началу подземелья
     *
     * @param Dungeon $dungeon
     * @param Party $party
     * @return array
     */
    public function validatePartyReadiness(Dungeon $dungeon, Party $party): array
    {
        $result = [
            'ready' => false,
            'can_start' => false,
            'errors' => [],
            'warnings' => [],
            'members_status' => []
        ];

        try {
            // Получаем всех активных участников группы с блокировкой для консистентности
            $partyMembers = $party->activeUsers()->with(['profile'])->lockForUpdate()->get();
            
            if ($partyMembers->isEmpty()) {
                $result['errors'][] = 'В группе нет активных участников';
                return $result;
            }

            // Проверяем соответствие количества участников требованиям подземелья
            $memberCount = $partyMembers->count();
            if ($memberCount < $dungeon->min_players) {
                $result['errors'][] = "Недостаточно участников в группе ({$memberCount}/{$dungeon->min_players})";
            }
            
            if ($memberCount > $dungeon->max_players) {
                $result['errors'][] = "Слишком много участников в группе ({$memberCount}/{$dungeon->max_players})";
            }

            // Проверяем каждого участника
            $readyCount = 0;
            $validLocationCount = 0;
            $activeCount = 0;

            foreach ($partyMembers as $member) {
                $memberStatus = $this->validateMemberReadiness($member, $dungeon);
                $result['members_status'][] = $memberStatus;

                if ($memberStatus['is_ready']) {
                    $readyCount++;
                }
                
                if ($memberStatus['valid_location']) {
                    $validLocationCount++;
                }
                
                if ($memberStatus['is_active']) {
                    $activeCount++;
                }

                // Собираем ошибки и предупреждения
                if (!empty($memberStatus['errors'])) {
                    $result['errors'] = array_merge($result['errors'], $memberStatus['errors']);
                }
                
                if (!empty($memberStatus['warnings'])) {
                    $result['warnings'] = array_merge($result['warnings'], $memberStatus['warnings']);
                }
            }

            // Определяем общую готовность
            $result['ready'] = ($readyCount === $memberCount);
            $result['can_start'] = (
                $result['ready'] && 
                $validLocationCount === $memberCount && 
                $activeCount === $memberCount &&
                $memberCount >= $dungeon->min_players && 
                $memberCount <= $dungeon->max_players &&
                empty($result['errors'])
            );

            Log::info('[DungeonReadiness] Проверка готовности группы завершена', [
                'dungeon_id' => $dungeon->id,
                'party_id' => $party->id,
                'member_count' => $memberCount,
                'ready_count' => $readyCount,
                'valid_location_count' => $validLocationCount,
                'active_count' => $activeCount,
                'can_start' => $result['can_start'],
                'errors_count' => count($result['errors']),
                'warnings_count' => count($result['warnings'])
            ]);

        } catch (\Exception $e) {
            Log::error('[DungeonReadiness] Ошибка при проверке готовности группы', [
                'dungeon_id' => $dungeon->id,
                'party_id' => $party->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $result['errors'][] = 'Произошла ошибка при проверке готовности группы';
        }

        return $result;
    }

    /**
     * Проверяет готовность конкретного участника группы
     *
     * @param User $member
     * @param Dungeon $dungeon
     * @return array
     */
    public function validateMemberReadiness(User $member, Dungeon $dungeon): array
    {
        $result = [
            'user_id' => $member->id,
            'user_name' => $member->name,
            'is_ready' => false,
            'valid_location' => false,
            'is_active' => false,
            'errors' => [],
            'warnings' => []
        ];

        try {
            // Проверяем статус готовности в pivot таблице
            $result['is_ready'] = (bool) $member->pivot->is_ready;

            // Проверяем местоположение пользователя
            $result['valid_location'] = $this->isUserInCorrectLobby($member, $dungeon);
            
            if (!$result['valid_location']) {
                $result['errors'][] = "Игрок {$member->name} не находится в лобби подземелья";
                
                // Если игрок не в правильном лобби, сбрасываем его готовность
                if ($result['is_ready']) {
                    $this->resetMemberReadiness($member);
                    $result['is_ready'] = false;
                    $result['warnings'][] = "Готовность игрока {$member->name} сброшена из-за неправильного местоположения";
                }
            }

            // Проверяем активность пользователя
            $result['is_active'] = $this->isUserActive($member);
            
            if (!$result['is_active']) {
                $result['warnings'][] = "Игрок {$member->name} неактивен более " . self::MAX_INACTIVE_MINUTES . " минут";
                
                // Если игрок неактивен, сбрасываем его готовность
                if ($result['is_ready']) {
                    $this->resetMemberReadiness($member);
                    $result['is_ready'] = false;
                    $result['warnings'][] = "Готовность игрока {$member->name} сброшена из-за неактивности";
                }
            }

            // Проверяем уровень игрока
            if (!$dungeon->canPlayerEnterByLevel($member->profile->level ?? 1)) {
                $result['errors'][] = "У игрока {$member->name} недостаточный уровень для подземелья";
            }

        } catch (\Exception $e) {
            Log::error('[DungeonReadiness] Ошибка при проверке готовности участника', [
                'user_id' => $member->id,
                'dungeon_id' => $dungeon->id,
                'error' => $e->getMessage()
            ]);
            
            $result['errors'][] = "Ошибка при проверке игрока {$member->name}";
        }

        return $result;
    }

    /**
     * Проверяет, находится ли пользователь в правильном лобби подземелья
     *
     * @param User $user
     * @param Dungeon $dungeon
     * @return bool
     */
    private function isUserInCorrectLobby(User $user, Dungeon $dungeon): bool
    {
        return (
            $user->in_dungeon_id == $dungeon->id &&
            $user->in_dungeon_status === 'lobby'
        );
    }

    /**
     * Проверяет активность пользователя
     *
     * @param User $user
     * @return bool
     */
    private function isUserActive(User $user): bool
    {
        if (!$user->last_activity_timestamp) {
            return false;
        }

        $lastActivity = Carbon::createFromTimestamp($user->last_activity_timestamp);
        $inactiveMinutes = $lastActivity->diffInMinutes(now());
        
        return $inactiveMinutes <= self::MAX_INACTIVE_MINUTES;
    }

    /**
     * Сбрасывает готовность участника группы
     *
     * @param User $user
     * @return void
     */
    private function resetMemberReadiness(User $user): void
    {
        try {
            $activeParty = $user->activeParty;
            if (!$activeParty) {
                return;
            }

            $partyMember = $activeParty->members()
                ->where('user_id', $user->id)
                ->where('status', PartyMember::STATUS_ACTIVE)
                ->first();

            if ($partyMember && $partyMember->is_ready) {
                $partyMember->update(['is_ready' => false]);
                
                Log::info('[DungeonReadiness] Готовность участника сброшена', [
                    'user_id' => $user->id,
                    'party_id' => $activeParty->id,
                    'reason' => 'Автоматический сброс при валидации'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('[DungeonReadiness] Ошибка при сбросе готовности участника', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Сбрасывает готовность всех участников группы
     *
     * @param Party $party
     * @return void
     */
    public function resetAllMembersReadiness(Party $party): void
    {
        try {
            $party->members()
                ->where('status', PartyMember::STATUS_ACTIVE)
                ->where('is_ready', true)
                ->update(['is_ready' => false]);

            Log::info('[DungeonReadiness] Готовность всех участников группы сброшена', [
                'party_id' => $party->id
            ]);

        } catch (\Exception $e) {
            Log::error('[DungeonReadiness] Ошибка при сбросе готовности всех участников', [
                'party_id' => $party->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}

<?php

/**
 * Тестовый скрипт для проверки исправления атаки ботов в рудниках
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// Инициализируем Laravel приложение
$app = new Application(realpath(__DIR__));

$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// Теперь у нас есть доступ к Laravel
echo "🤖 Тестирование исправления атаки ботов в рудниках\n";
echo "==================================================\n\n";

try {
    // Находим тестовую подлокацию
    $testSubLocation = App\Models\MineLocation::where('name', 'testPod')
        ->where('is_active', true)
        ->first();

    if (!$testSubLocation) {
        echo "❌ Подлокация 'testPod' не найдена\n";
        echo "📝 Создайте подлокацию 'testPod' через админку\n";
        exit;
    }

    echo "📍 Найдена подлокация: {$testSubLocation->name} (ID: {$testSubLocation->id})\n";
    echo "   - Является подлокацией: " . ($testSubLocation->isSubLocation() ? 'Да' : 'Нет') . "\n";
    
    if ($testSubLocation->parent) {
        echo "   - Родительская локация: {$testSubLocation->parent->name}\n";
    }
    
    if ($testSubLocation->baseLocation) {
        echo "   - Базовая локация: {$testSubLocation->baseLocation->name}\n";
    }

    // Тестируем поиск ботов через LocationPlayerCacheService
    echo "\n🔧 Тестирование LocationPlayerCacheService\n";
    $cacheService = new App\Services\battle\LocationPlayerCacheService();
    
    // Проверяем ботов для обеих рас
    $races = ['solarius', 'lunarius'];
    
    foreach ($races as $race) {
        echo "\n🎯 Поиск ботов расы {$race} в локации 'testPod':\n";
        $bots = $cacheService->getCachedBotsInLocation('testPod', $race);
        
        echo "   - Найдено ботов: " . $bots->count() . "\n";
        
        foreach ($bots as $bot) {
            echo "     • {$bot->name} (ID: {$bot->id}, HP: {$bot->hp}, mine_location_id: " . ($bot->mine_location_id ?? 'NULL') . ")\n";
        }
    }

    // Тестируем прямой поиск в БД
    echo "\n🗃️  Тестирование прямого поиска ботов в БД:\n";
    
    // Все боты с mine_location_id равным testPod
    $directBots = App\Models\Bot::where('mine_location_id', $testSubLocation->id)
        ->where('is_active', true)
        ->where('hp', '>', 0)
        ->get();
        
    echo "   - Боты с mine_location_id = {$testSubLocation->id}: " . $directBots->count() . "\n";
    
    foreach ($directBots as $bot) {
        echo "     • {$bot->name} (ID: {$bot->id}, раса: {$bot->race}, HP: {$bot->hp})\n";
    }
    
    // Проверяем работу attackAnyPlayer
    echo "\n⚔️  Тестирование метода attackAnyPlayer:\n";
    
    // Находим тестового пользователя
    $testUser = App\Models\User::where('name', 'admin')->first();
    
    if ($testUser) {
        echo "   - Тестовый пользователь: {$testUser->name} (раса: " . ($testUser->profile->race ?? 'не определена') . ")\n";
        
        if ($testUser->profile->race) {
            $userRace = $testUser->profile->race;
            $enemyRace = ($userRace === 'solarius') ? 'lunarius' : 'solarius';
            
            echo "   - Ищем врагов расы: {$enemyRace}\n";
            
            // Получаем врагов через кэш-сервис
            $enemies = $cacheService->getCachedEnemiesInLocation('testPod', $userRace, $testUser->id);
            
            echo "   - Найдено врагов: " . $enemies->count() . "\n";
            
            foreach ($enemies as $enemy) {
                $type = $enemy instanceof App\Models\User ? 'Игрок' : 'Бот';
                echo "     • {$type}: {$enemy->name} (ID: {$enemy->id})\n";
            }
            
            if ($enemies->count() > 0) {
                echo "   ✅ Функция 'Бить любого' должна работать!\n";
            } else {
                echo "   ❌ Нет врагов для атаки. Создайте бота противоположной фракции в подлокации testPod\n";
            }
        } else {
            echo "   ❌ У пользователя admin не определена раса\n";
        }
    } else {
        echo "   ❌ Пользователь 'admin' не найден\n";
    }

    echo "\n✅ Исправления внесены:\n";
    echo "   1. ✅ Изменен текст кнопки с 'Выбрать случайную цель' на 'Бить любого'\n";
    echo "   2. ✅ Метод attackAnyPlayer теперь сразу атакует выбранную цель\n";
    echo "   3. ✅ Исправлен поиск ботов в подлокациях через LocationPlayerCacheService\n";
    echo "   4. ✅ Добавлена строгая изоляция по mine_location_id для подлокаций\n";

    echo "\n📋 Для полного функционирования:\n";
    echo "   • Убедитесь, что в подлокации 'testPod' есть боты с mine_location_id = {$testSubLocation->id}\n";
    echo "   • Боты должны иметь расу противоположную расе игрока\n";
    echo "   • Боты должны иметь is_active = true и hp > 0\n";

} catch (Exception $e) {
    echo "❌ Ошибка при тестировании: " . $e->getMessage() . "\n";
    echo "Трассировка: " . $e->getTraceAsString() . "\n";
}
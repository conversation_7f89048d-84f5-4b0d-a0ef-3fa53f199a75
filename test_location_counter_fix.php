<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Services\battle\FactionCountService;
use App\Services\battle\UserLocationService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Тест исправления счетчиков локации ===\n\n";

$testLocation = 'аааааааааааа';
echo "Тестируем локацию: {$testLocation}\n\n";

// Получаем сервисы
$factionCountService = app(FactionCountService::class);
$userLocationService = app(UserLocationService::class);

echo "=== ДО ИСПРАВЛЕНИЯ - Все игроки в локации ===\n";
$allPlayersBeforeFix = User::whereHas('statistics', function ($q) use ($testLocation) {
    $q->where('current_location', $testLocation);
})
->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
->with(['profile', 'statistics'])
->get();

echo "Всего игроков в локации (без фильтра HP/defeated): " . $allPlayersBeforeFix->count() . "\n";
foreach ($allPlayersBeforeFix as $player) {
    $race = $player->profile->race ?? 'unknown';
    $class = $player->profile->class ?? 'unknown';
    $hp = $player->profile->current_hp ?? 'N/A';
    $maxHp = $player->profile->max_hp ?? 'N/A';
    // Проверяем, есть ли поле is_defeated
    $isDefeated = 'N/A';
    if (isset($player->profile->is_defeated)) {
        $isDefeated = $player->profile->is_defeated ? 'ДА' : 'НЕТ';
    }
    
    echo "  {$player->name} - {$race} {$class} (HP: {$hp}/{$maxHp}, поражен: {$isDefeated})\n";
}

echo "\n=== ПОСЛЕ ИСПРАВЛЕНИЯ - Только живые игроки ===\n";
$alivePlayersAfterFix = $userLocationService->getPlayersInLocation($testLocation);

echo "Живых игроков в локации (с фильтром HP > 0 и defeated = false): " . $alivePlayersAfterFix->count() . "\n";
foreach ($alivePlayersAfterFix as $player) {
    $race = $player->profile->race ?? 'unknown';
    $class = $player->profile->class ?? 'unknown';
    $hp = $player->profile->current_hp ?? 'N/A';
    $maxHp = $player->profile->max_hp ?? 'N/A';
    // Проверяем, есть ли поле is_defeated
    $isDefeated = 'N/A';
    if (isset($player->profile->is_defeated)) {
        $isDefeated = $player->profile->is_defeated ? 'ДА' : 'НЕТ';
    }
    
    echo "  {$player->name} - {$race} {$class} (HP: {$hp}/{$maxHp}, поражен: {$isDefeated})\n";
}

echo "\n=== Результат FactionCountService ===\n";
$factionCounts = $factionCountService->getLocationFactionCounts($testLocation);

echo "Подсчет фракций:\n";
echo "Solarius игроки: " . json_encode($factionCounts['player_counts']['solarius']) . "\n";
echo "Lunarius игроки: " . json_encode($factionCounts['player_counts']['lunarius']) . "\n";
echo "Общий подсчет Solarius: " . json_encode($factionCounts['total_counts']['solarius']) . "\n";
echo "Общий подсчет Lunarius: " . json_encode($factionCounts['total_counts']['lunarius']) . "\n";

echo "\n=== Проверка ботов ===\n";
$botsInLocation = Bot::where('location', $testLocation)
    ->where('is_active', true)
    ->get();

echo "Всех активных ботов в локации: " . $botsInLocation->count() . "\n";
foreach ($botsInLocation as $bot) {
    $hasProblems = [];
    if ($bot->hp <= 0) $hasProblems[] = "HP <= 0";
    if ($bot->death_time) $hasProblems[] = "death_time установлен";
    if (!$bot->location || $bot->location === '') $hasProblems[] = "нет локации";
    
    $problemText = !empty($hasProblems) ? " [ПРОБЛЕМЫ: " . implode(', ', $hasProblems) . "]" : " [OK]";
    echo "  {$bot->name} - {$bot->race} {$bot->class} (HP: {$bot->hp}/{$bot->max_hp}){$problemText}\n";
}

$correctBotsInLocation = Bot::where('location', $testLocation)
    ->where('is_active', true)
    ->where('hp', '>', 0)
    ->whereNull('death_time')
    ->whereNotNull('location')
    ->where('location', '!=', '')
    ->get();

echo "\nКорректных ботов в локации (после фильтрации): " . $correctBotsInLocation->count() . "\n";

echo "\n=== Тест завершен ===\n";
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Party;
use App\Models\PartyMember;
use App\Models\PartyInvitation;
use App\Models\User;
use App\Services\PartyService;
use Illuminate\Support\Facades\Redis;

/**
 * Контроллер для управления группами игроков
 */
class PartyController extends Controller
{
    /**
     * API endpoint для получения актуальных данных о группе
     */
    public function getPartyData(Request $request)
    {
        $user = Auth::user();

        // Получаем активную группу пользователя
        $activeParty = $user->parties()
            ->wherePivot('status', PartyMember::STATUS_ACTIVE)
            ->with(['activeUsers.profile'])
            ->first();

        if (!$activeParty) {
            return response()->json([
                'success' => true,
                'members' => []
            ]);
        }

        // Получаем других участников группы (исключаем текущего пользователя)
        $otherMembers = $activeParty->activeUsers->filter(function ($member) use ($user) {
            return $member->id !== $user->id;
        });

        $membersData = $otherMembers->map(function ($member) {
            $memberResources = $member->profile->getActualResources();
            return [
                'id' => $member->id,
                'name' => $member->name,
                'current_hp' => $memberResources['current_hp'] ?? $member->profile->current_hp ?? 0,
                'max_hp' => $member->profile->max_hp ?? 100,
                'is_online' => $member->isOnline()
            ];
        });

        return response()->json([
            'success' => true,
            'members' => $membersData->values()
        ]);
    }

    /**
     * Отображение главной страницы групп
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userProfile = $user->profile;
        $actualResources = $userProfile->getActualResources();

        // Получаем активную группу пользователя
        $activeParty = $user->parties()
            ->wherePivot('status', PartyMember::STATUS_ACTIVE)
            ->with(['activeUsers.profile', 'leader.profile'])
            ->first();

        // Получаем приглашения в группы (новая логика WoW/L2 стиля)
        $invitations = $user->getActivePartyInvitations();

        // Получаем членство текущего пользователя в активной группе
        $userMembership = null;
        if ($activeParty) {
            $userMembership = $user->partyMemberships()
                ->where('party_id', $activeParty->id)
                ->where('status', PartyMember::STATUS_ACTIVE)
                ->first();
        }

        // Логика отображения согласно требованиям:
        // - Если игрок один (нет активной группы или группа состоит только из него) - показываем пустой интерфейс
        // - Если у игрока есть участники группы - показываем всех участников включая текущего пользователя
        $hasGroupMembers = false;
        $allMembers = collect();

        if ($activeParty) {
            $allMembers = $activeParty->activeUsers;
            $hasGroupMembers = $allMembers->count() > 1; // Больше одного участника
        }

        // Обработка flash-сообщений из параметров URL
        if ($request->get('action') === 'left') {
            session()->flash('party_action', 'Вы покинули группу');
        } elseif ($request->get('action') === 'kicked') {
            $kickedName = $request->get('name');
            $leaderName = $request->get('leader');
            
            // Проверяем, является ли текущий пользователь тем, кого исключили
            if ($kickedName === $user->name) {
                session()->flash('party_action', "Вас исключил из группы {$leaderName}");
            } else {
                session()->flash('party_action', "Участник {$kickedName} исключен из группы");
            }
        }

        return view('party.index', [
            'user' => $user,
            'userProfile' => $userProfile,
            'actualResources' => $actualResources,
            'activeParty' => $activeParty,
            'userMembership' => $userMembership,
            'hasGroupMembers' => $hasGroupMembers,
            'allMembers' => $allMembers,
            'invitations' => $invitations,
        ]);
    }

    /**
     * Создание новой группы
     */
    public function create(Request $request)
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'max_members' => 'integer|min:2|max:8',
            'is_public' => 'boolean',
            'auto_accept' => 'boolean',
        ]);

        $user = Auth::user();

        // Проверяем, не состоит ли пользователь уже в активной группе
        if ($user->hasActiveParty()) {
            return response()->json([
                'success' => false,
                'message' => 'Вы уже состоите в активной группе'
            ]);
        }

        DB::beginTransaction();

        try {
            // Создаем группу
            $party = Party::create([
                'leader_id' => $user->id,
                'name' => $request->name,
                'max_members' => $request->max_members ?? Party::DEFAULT_MAX_MEMBERS,
                'is_public' => $request->is_public ?? true,
                'auto_accept' => $request->auto_accept ?? false,
            ]);

            // Добавляем создателя как лидера группы
            PartyMember::create([
                'party_id' => $party->id,
                'user_id' => $user->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'joined_at' => now(),
                'is_ready' => true,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Группа успешно создана',
                'party_id' => $party->id
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при создании группы: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Присоединение к группе
     */
    public function join(Request $request, $partyId)
    {
        $user = Auth::user();
        $party = Party::findOrFail($partyId);

        // Проверяем, активна ли группа
        if (!$party->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Группа неактивна'
            ]);
        }

        // Проверяем, не состоит ли пользователь уже в активной группе
        if ($user->hasActiveParty()) {
            return response()->json([
                'success' => false,
                'message' => 'Вы уже состоите в активной группе'
            ]);
        }

        // Проверяем, может ли группа принять нового участника
        if (!$party->canAcceptNewMember()) {
            return response()->json([
                'success' => false,
                'message' => 'Группа заполнена'
            ]);
        }

        // Проверяем, не состоит ли пользователь уже в этой группе
        if ($party->isMember($user->id)) {
            return response()->json([
                'success' => false,
                'message' => 'Вы уже состоите в этой группе'
            ]);
        }

        DB::beginTransaction();

        try {
            $status = $party->auto_accept ? PartyMember::STATUS_ACTIVE : PartyMember::STATUS_INVITED;
            $joinedAt = $party->auto_accept ? now() : null;

            // Создаем запись об участии в группе
            PartyMember::create([
                'party_id' => $party->id,
                'user_id' => $user->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => $status,
                'joined_at' => $joinedAt,
                'is_ready' => false,
            ]);

            DB::commit();

            $message = $party->auto_accept
                ? 'Вы успешно присоединились к группе'
                : 'Заявка на вступление в группу отправлена';

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при присоединении к группе: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Покидание группы
     */
    public function leave(Request $request)
    {
        $user = Auth::user();

        $membership = $user->partyMemberships()
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->with('party')
            ->first();

        if (!$membership) {
            return response()->json([
                'success' => false,
                'message' => 'Вы не состоите в активной группе'
            ]);
        }

        DB::beginTransaction();

        try {
            $party = $membership->party;

            // Если пользователь - лидер группы
            if ($membership->isLeader()) {
                // Если в группе есть другие участники, передаем лидерство
                $otherMembers = $party->activeMembers()
                    ->where('user_id', '!=', $user->id)
                    ->get();

                if ($otherMembers->count() > 0) {
                    // Передаем лидерство первому участнику
                    $newLeader = $otherMembers->first();
                    $party->update(['leader_id' => $newLeader->user_id]);
                    $newLeader->update(['role' => PartyMember::ROLE_LEADER]);
                } else {
                    // Если других участников нет, расформировываем группу
                    $party->update(['status' => Party::STATUS_DISBANDED]);
                }
            }

            // Отмечаем участника как покинувшего группу
            $membership->markAsLeft();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Вы покинули группу',
                'redirect' => route('party.index') . '?action=left'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при покидании группы: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Принятие приглашения в группу
     */
    public function acceptInvitation(Request $request, $partyId)
    {
        $user = Auth::user();

        $membership = $user->partyMemberships()
            ->where('party_id', $partyId)
            ->where('status', PartyMember::STATUS_INVITED)
            ->with('party')
            ->first();

        if (!$membership) {
            return response()->json([
                'success' => false,
                'message' => 'Приглашение не найдено'
            ]);
        }

        // Проверяем состояние пользователя в группе
        if ($user->hasActiveParty()) {
            // Если пользователь в многопользовательской группе, блокируем принятие приглашения
            if ($user->isInMultiPlayerParty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Вы уже состоите в группе с другими игроками'
                ]);
            }
            // Если пользователь в соло-группе, разрешаем принятие (соло-группа будет покинута автоматически)
        }

        $party = $membership->party;

        // Проверяем, может ли группа принять нового участника
        if (!$party->canAcceptNewMember()) {
            return response()->json([
                'success' => false,
                'message' => 'Группа заполнена'
            ]);
        }

        try {
            $membership->acceptInvitation();

            return response()->json([
                'success' => true,
                'message' => 'Вы присоединились к группе'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при принятии приглашения: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Отклонение приглашения в группу
     */
    public function declineInvitation(Request $request, $partyId)
    {
        $user = Auth::user();

        $membership = $user->partyMemberships()
            ->where('party_id', $partyId)
            ->where('status', PartyMember::STATUS_INVITED)
            ->first();

        if (!$membership) {
            return response()->json([
                'success' => false,
                'message' => 'Приглашение не найдено'
            ]);
        }

        try {
            $membership->delete();

            return response()->json([
                'success' => true,
                'message' => 'Приглашение отклонено'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при отклонении приглашения: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Переключение статуса готовности
     */
    public function toggleReady(Request $request)
    {
        $user = Auth::user();

        $membership = $user->partyMemberships()
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->first();

        if (!$membership) {
            return redirect()->route('party.index')
                ->with('error', 'Вы не состоите в активной группе');
        }

        try {
            $membership->toggleReady();

            $message = $membership->is_ready ? 'Вы готовы к началу' : 'Вы отменили готовность';

            return redirect()->route('party.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->route('party.index')
                ->with('error', 'Ошибка при изменении статуса готовности: ' . $e->getMessage());
        }
    }

    /**
     * Исключение участника из группы (только для лидера)
     */
    public function kickMember(Request $request, $userId)
    {
        $leader = Auth::user();

        $leaderMembership = $leader->partyMemberships()
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->where('role', PartyMember::ROLE_LEADER)
            ->with('party')
            ->first();

        if (!$leaderMembership) {
            return response()->json([
                'success' => false,
                'message' => 'Вы не являетесь лидером группы'
            ]);
        }

        $memberToKick = $leaderMembership->party->members()
            ->where('user_id', $userId)
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->first();

        if (!$memberToKick) {
            return response()->json([
                'success' => false,
                'message' => 'Участник не найден в группе'
            ]);
        }

        if ($memberToKick->user_id === $leader->id) {
            return response()->json([
                'success' => false,
                'message' => 'Нельзя исключить самого себя'
            ]);
        }

        try {
            $memberToKick->markAsKicked();

            $kickedUser = User::find($userId);
            $kickedUserName = $kickedUser ? $kickedUser->name : 'Игрок';
            $leaderName = $leader->name;
            
            // Сохраняем флеш-сообщение в Redis для исключенного игрока
            if ($kickedUser) {
                // Создаем ключ для флеш-сообщения в Redis
                $flashKey = "user_flash_message:{$kickedUser->id}";
                
                // Сохраняем сообщение в Redis на 24 часа
                Redis::setex($flashKey, 86400, json_encode([
                    'type' => 'warning',
                    'message' => "Игрок {$leaderName} исключил вас из группы",
                    'icon' => '❌',
                    'timestamp' => now()->timestamp
                ]));
            }
            
            // Добавляем флеш-сообщение для лидера
            session()->flash('party_action', "Участник {$kickedUserName} исключен из группы");
            
            return response()->json([
                'success' => true,
                'message' => "Участник {$kickedUserName} исключен из группы",
                'redirect' => route('party.index')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при исключении участника: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Приглашение игрока в группу (только для лидера)
     */
    public function invitePlayer(Request $request, $userId)
    {
        $leader = Auth::user();
        $userToInvite = User::findOrFail($userId);

        $leaderMembership = $leader->partyMemberships()
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->where('role', PartyMember::ROLE_LEADER)
            ->with('party')
            ->first();

        if (!$leaderMembership) {
            return response()->json([
                'success' => false,
                'message' => 'Вы не являетесь лидером группы'
            ]);
        }

        $party = $leaderMembership->party;

        // Проверяем, может ли группа принять нового участника
        if (!$party->canAcceptNewMember()) {
            return response()->json([
                'success' => false,
                'message' => 'Группа заполнена'
            ]);
        }

        // Проверяем состояние приглашаемого игрока в группе
        if ($userToInvite->hasActiveParty()) {
            // Если игрок в многопользовательской группе, блокируем приглашение
            if ($userToInvite->isInMultiPlayerParty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Игрок уже состоит в группе с другими участниками'
                ]);
            }
            // Если игрок в соло-группе, разрешаем приглашение (соло-группа будет покинута при принятии)
        }

        // Проверяем, нет ли уже приглашения
        $existingInvitation = $party->members()
            ->where('user_id', $userId)
            ->where('status', PartyMember::STATUS_INVITED)
            ->exists();

        if ($existingInvitation) {
            return response()->json([
                'success' => false,
                'message' => 'Приглашение уже отправлено'
            ]);
        }

        try {
            PartyMember::create([
                'party_id' => $party->id,
                'user_id' => $userId,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_INVITED,
                'is_ready' => false,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Приглашение отправлено'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ошибка при отправке приглашения: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Передача лидерства (только для лидера)
     */
    public function transferLeadership(Request $request, $userId)
    {
        $currentLeader = Auth::user();

        $leaderMembership = $currentLeader->partyMemberships()
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->where('role', PartyMember::ROLE_LEADER)
            ->with('party')
            ->first();

        if (!$leaderMembership) {
            return response()->json([
                'success' => false,
                'message' => 'Вы не являетесь лидером группы'
            ]);
        }

        $newLeaderMembership = $leaderMembership->party->members()
            ->where('user_id', $userId)
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->first();

        if (!$newLeaderMembership) {
            return response()->json([
                'success' => false,
                'message' => 'Новый лидер не найден в группе'
            ]);
        }

        DB::beginTransaction();

        try {
            $party = $leaderMembership->party;

            // Обновляем лидера группы
            $party->update(['leader_id' => $userId]);

            // Обновляем роли участников
            $leaderMembership->update(['role' => PartyMember::ROLE_MEMBER]);
            $newLeaderMembership->update(['role' => PartyMember::ROLE_LEADER]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Лидерство передано'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при передаче лидерства: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Расформирование группы (только для лидера)
     */
    public function disband(Request $request)
    {
        $leader = Auth::user();

        $leaderMembership = $leader->partyMemberships()
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->where('role', PartyMember::ROLE_LEADER)
            ->with('party')
            ->first();

        if (!$leaderMembership) {
            return response()->json([
                'success' => false,
                'message' => 'Вы не являетесь лидером группы'
            ]);
        }

        DB::beginTransaction();

        try {
            $party = $leaderMembership->party;

            // Отмечаем всех участников как покинувших группу
            $party->members()
                ->where('status', PartyMember::STATUS_ACTIVE)
                ->update([
                    'status' => PartyMember::STATUS_LEFT,
                    'left_at' => now()
                ]);

            // Расформировываем группу
            $party->update(['status' => Party::STATUS_DISBANDED]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Группа расформирована'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при расформировании группы: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Отправить приглашение игроку в группу (новая логика WoW/L2)
     */
    public function sendInvitation(Request $request)
    {
        $request->validate([
            'player_identifier' => 'required|string|max:255',
            'message' => 'nullable|string|max:500',
        ]);

        $inviter = Auth::user();
        $playerIdentifier = $request->input('player_identifier');
        $message = $request->input('message');

        // Находим игрока по ID или имени
        $invitee = null;

        // Сначала пробуем найти по ID (если это число)
        if (is_numeric($playerIdentifier)) {
            $invitee = User::find($playerIdentifier);
        }

        // Если не найден по ID, ищем по имени
        if (!$invitee) {
            $invitee = User::where('name', $playerIdentifier)->first();
        }

        if (!$invitee) {
            return response()->json([
                'success' => false,
                'message' => 'Игрок с таким ID или именем не найден'
            ]);
        }

        // Нельзя пригласить самого себя
        if ($inviter->id === $invitee->id) {
            return response()->json([
                'success' => false,
                'message' => 'Нельзя пригласить самого себя'
            ]);
        }

        try {
            $partyService = new PartyService();
            $invitation = $partyService->sendInvitation($inviter, $invitee, $message);

            return response()->json([
                'success' => true,
                'message' => "Приглашение отправлено игроку {$invitee->name}",
                'invitation_id' => $invitation->id
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Принять приглашение в группу (новая логика)
     */
    public function acceptPartyInvitation(Request $request, $invitationId)
    {
        $user = Auth::user();
        $invitation = PartyInvitation::findOrFail($invitationId);

        try {
            $partyService = new PartyService();
            $party = $partyService->acceptInvitation($invitation);

            // Получаем имя приглашавшего для flash-сообщения
            $inviterName = $invitation->inviter->name;

            // Проверяем, это AJAX запрос или обычный
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "Вы вступили в группу к игроку {$inviterName}",
                    'party_id' => $party->id
                ]);
            }

            // Для обычных POST запросов добавляем flash-сообщение и редиректим
            session()->flash('success', "Вы вступили в группу к игроку {$inviterName}");
            return redirect()->route('party.index');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }

            // Для обычных POST запросов добавляем flash-сообщение об ошибке
            session()->flash('error', $e->getMessage());
            return redirect()->route('party.index');
        }
    }

    /**
     * Отклонить приглашение в группу (новая логика)
     */
    public function declinePartyInvitation(Request $request, $invitationId)
    {
        $user = Auth::user();
        $invitation = PartyInvitation::findOrFail($invitationId);

        try {
            $partyService = new PartyService();
            $partyService->declineInvitation($invitation);

            // Проверяем, это AJAX запрос или обычный
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Приглашение отклонено'
                ]);
            }

            // Для обычных POST запросов добавляем flash-сообщение и редиректим
            session()->flash('info', 'Приглашение отклонено');
            return redirect()->route('party.index');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }

            // Для обычных POST запросов добавляем flash-сообщение об ошибке
            session()->flash('error', $e->getMessage());
            return redirect()->route('party.index');
        }
    }

    /**
     * Поиск игроков для приглашения (по ID или имени)
     */
    public function searchPlayers(Request $request)
    {
        $request->validate([
            'query' => 'required|string|min:1|max:50',
        ]);

        $query = $request->input('query');
        $currentUser = Auth::user();

        // Поиск игроков по ID или имени
        $players = User::where(function ($q) use ($query) {
            // Если запрос - число, ищем по ID
            if (is_numeric($query)) {
                $q->where('id', $query);
            }
            // Также ищем по имени (частичное совпадение)
            $q->orWhere('name', 'ILIKE', "%{$query}%");
        })
            ->where('id', '!=', $currentUser->id) // Исключаем текущего пользователя
            ->with(['profile', 'onlineStatus'])
            ->limit(10)
            ->get();

        $results = $players->map(function ($player) use ($currentUser) {
            return [
                'id' => $player->id,
                'name' => $player->name,
                'level' => $player->profile->level ?? 1,
                'race' => $player->profile->race ?? 'solarius',
                'class' => $player->profile->class ?? 'воин',
                'is_online' => $player->onlineStatus?->is_online ?? false,
                'can_invite' => $currentUser->canInviteToParty($player),
                'has_active_party' => $player->hasActiveParty(),
                'is_in_solo_party' => $player->isInSoloParty(),
                'is_in_multi_party' => $player->isInMultiPlayerParty(),
            ];
        });

        return response()->json([
            'success' => true,
            'players' => $results
        ]);
    }

    /**
     * Получить активные приглашения пользователя
     */
    public function getActiveInvitations(Request $request)
    {
        $user = Auth::user();
        $invitations = $user->getActivePartyInvitations();

        $results = $invitations->map(function ($invitation) {
            return [
                'id' => $invitation->id,
                'inviter_name' => $invitation->inviter->name,
                'inviter_level' => $invitation->inviter->profile->level ?? 1,
                'message' => $invitation->message,
                'expires_at' => $invitation->expires_at->format('Y-m-d H:i:s'),
                'time_left' => $invitation->expires_at->diffInSeconds(now()),
            ];
        });

        return response()->json([
            'success' => true,
            'invitations' => $results
        ]);
    }

    /**
     * Принять приглашение в группу (новая логика)
     */
    public function acceptPartyInvitationNew(PartyInvitation $invitation)
    {
        $user = Auth::user();

        // Проверяем, что приглашение адресовано текущему пользователю
        if ($invitation->invitee_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Это приглашение не для вас'
            ]);
        }

        // Проверяем, что приглашение активно
        if (!$invitation->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Приглашение истекло или уже обработано'
            ]);
        }

        try {
            // Принимаем приглашение
            $result = $invitation->accept();

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Вы успешно присоединились к группе!'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Не удалось принять приглашение'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Произошла ошибка при принятии приглашения: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Отклонить приглашение в группу (новая логика)
     */
    public function declinePartyInvitationNew(PartyInvitation $invitation)
    {
        $user = Auth::user();

        // Проверяем, что приглашение адресовано текущему пользователю
        if ($invitation->invitee_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Это приглашение не для вас'
            ]);
        }

        // Проверяем, что приглашение активно
        if (!$invitation->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Приглашение уже обработано'
            ]);
        }

        try {
            // Отклоняем приглашение
            $invitation->decline();

            return response()->json([
                'success' => true,
                'message' => 'Приглашение отклонено'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Произошла ошибка при отклонении приглашения: ' . $e->getMessage()
            ]);
        }
    }
}

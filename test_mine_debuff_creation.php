<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Services\MineDetectionService;
use App\Models\ActiveEffect;
use App\Models\MineMark;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 ТЕСТИРОВАНИЕ СОЗДАНИЯ ДЕБАФА 'ЗАМЕЧЕН'\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 1. Получаем пользователя admin
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";

// 2. Получаем локацию рудника "аааааааааааа"
$mineLocation = MineLocation::where('name', 'аааааааааааа')->first();
if (!$mineLocation) {
    echo "❌ Локация рудника 'аааааааааааа' не найдена!\n";
    exit(1);
}

echo "✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n\n";

// 3. Очищаем старые метки и эффекты
echo "🧹 Очистка старых данных...\n";
MineMark::where('player_id', $user->id)->delete();
ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->delete();
echo "✅ Старые данные очищены\n\n";

// 4. Создаем новый дебаф через сервис
echo "⚡ Создание нового дебафа...\n";
$mineDetectionService = app(MineDetectionService::class);

try {
    $mark = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
    
    if ($mark) {
        echo "✅ MineMark создан:\n";
        echo "   ID: {$mark->id}\n";
        echo "   Игрок: {$mark->player_id}\n";
        echo "   Рудник: {$mark->mine_location_id}\n";
        echo "   Локация: {$mark->location_name}\n";
        echo "   Истекает: {$mark->expires_at}\n";
        echo "   Активен: " . ($mark->is_active ? 'Да' : 'Нет') . "\n\n";
    } else {
        echo "❌ MineMark НЕ создан!\n\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка при создании дебафа: {$e->getMessage()}\n\n";
}

// 5. Проверяем созданные эффекты
echo "🔍 Проверка созданных эффектов...\n";

// Проверяем MineMark
$createdMarks = MineMark::where('player_id', $user->id)->get();
echo "Создано меток: {$createdMarks->count()}\n";
foreach ($createdMarks as $mark) {
    $isActive = $mark->is_active && $mark->expires_at > now();
    echo "- Метка ID: {$mark->id}, Активна: " . ($isActive ? 'Да' : 'Нет') . "\n";
}

// Проверяем ActiveEffect
$createdEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
    ->where('target_id', $user->id)
    ->where('effect_type', 'mine_detection')
    ->get();

echo "Создано эффектов: {$createdEffects->count()}\n";
foreach ($createdEffects as $effect) {
    $isActive = $effect->isActive();
    echo "- Эффект ID: {$effect->id}, Тип: {$effect->effect_type}, Активен: " . ($isActive ? 'Да' : 'Нет') . "\n";
    echo "  Название: " . ($effect->effect_name ?? 'N/A') . "\n";
    echo "  Истекает: {$effect->ends_at}\n";
    echo "  Оставшееся время: {$effect->remaining_duration}с\n";
}

echo "\n6️⃣ Симуляция получения эффектов как в контроллере...\n";
$allUserEffects = $user->activeEffects()->with('skill')->get();
$userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());

echo "Всего эффектов: {$allUserEffects->count()}\n";
echo "Активных эффектов: {$userEffects->count()}\n";

foreach ($userEffects as $effect) {
    echo "✅ Активный эффект для UI: {$effect->effect_type} (ID: {$effect->id})\n";
    echo "   Оставшееся время: {$effect->remaining_duration}с\n";
    
    if ($effect->effect_type == 'mine_detection') {
        echo "   🎯 ЭТОТ ЭФФЕКТ ДОЛЖЕН ОТОБРАЖАТЬСЯ В UI!\n";
    }
}

echo "\n✅ ТЕСТ ЗАВЕРШЕН!\n";

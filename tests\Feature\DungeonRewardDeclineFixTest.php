<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Dungeon;
use App\Models\DungeonInstance;
use App\Models\DungeonRewardDistribution;
use App\Models\Party;
use App\Models\Item;
use App\Services\DungeonRewardDistributionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DungeonRewardDeclineFixTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user1;
    protected User $user2;
    protected Dungeon $dungeon;
    protected DungeonInstance $dungeonInstance;
    protected Party $party;
    protected Item $item;
    protected DungeonRewardDistributionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user1 = User::factory()->create();
        $this->user2 = User::factory()->create();
        
        $this->dungeon = Dungeon::factory()->create();
        $this->party = Party::factory()->create();
        $this->dungeonInstance = DungeonInstance::factory()->create([
            'dungeon_id' => $this->dungeon->id,
            'party_id' => $this->party->id,
        ]);
        
        $this->item = Item::factory()->create();
        $this->service = app(DungeonRewardDistributionService::class);
    }

    /** @test */
    public function declined_reward_shows_correct_status()
    {
        // Создаем награду
        $reward = DungeonRewardDistribution::create([
            'dungeon_instance_id' => $this->dungeonInstance->id,
            'party_id' => $this->party->id,
            'rewardable_type' => 'App\\Models\\Item',
            'rewardable_id' => $this->item->id,
            'quantity' => 1,
            'original_assignee_id' => $this->user1->id,
            'current_assignee_id' => $this->user1->id,
            'status' => DungeonRewardDistribution::STATUS_PENDING,
        ]);

        // Игрок отказывается от награды
        $result = $this->service->declineReward($reward, $this->user1);

        $this->assertTrue($result['success']);
        
        // Проверяем, что награда имеет статус declined
        $reward->refresh();
        $this->assertEquals(DungeonRewardDistribution::STATUS_DECLINED, $reward->status);
    }

    /** @test */
    public function declined_reward_without_requests_stays_declined()
    {
        // Создаем награду
        $reward = DungeonRewardDistribution::create([
            'dungeon_instance_id' => $this->dungeonInstance->id,
            'party_id' => $this->party->id,
            'rewardable_type' => 'App\\Models\\Item',
            'rewardable_id' => $this->item->id,
            'quantity' => 1,
            'original_assignee_id' => $this->user1->id,
            'current_assignee_id' => $this->user1->id,
            'status' => DungeonRewardDistribution::STATUS_PENDING,
        ]);

        // Игрок отказывается от награды (нет запросов от других)
        $result = $this->service->declineReward($reward, $this->user1);

        $this->assertTrue($result['success']);
        
        // Проверяем, что награда остается declined, а не становится distributed
        $reward->refresh();
        $this->assertEquals(DungeonRewardDistribution::STATUS_DECLINED, $reward->status);
        $this->assertNull($reward->requested_by);
    }

    /** @test */
    public function declined_reward_excluded_from_inventory_check()
    {
        // Создаем награду
        $reward = DungeonRewardDistribution::create([
            'dungeon_instance_id' => $this->dungeonInstance->id,
            'party_id' => $this->party->id,
            'rewardable_type' => 'App\\Models\\Item',
            'rewardable_id' => $this->item->id,
            'quantity' => 1,
            'original_assignee_id' => $this->user1->id,
            'current_assignee_id' => $this->user1->id,
            'status' => DungeonRewardDistribution::STATUS_DECLINED,
        ]);

        // Проверяем, что отказанная награда не учитывается при проверке инвентаря
        $result = $this->service->canCompleteForUser($this->user1, $this->dungeonInstance);

        $this->assertTrue($result['can_complete']);
        $this->assertEquals('', $result['message']);
    }

    /** @test */
    public function declined_reward_excluded_from_distribution()
    {
        // Создаем награду
        $reward = DungeonRewardDistribution::create([
            'dungeon_instance_id' => $this->dungeonInstance->id,
            'party_id' => $this->party->id,
            'rewardable_type' => 'App\\Models\\Item',
            'rewardable_id' => $this->item->id,
            'quantity' => 1,
            'original_assignee_id' => $this->user1->id,
            'current_assignee_id' => $this->user1->id,
            'status' => DungeonRewardDistribution::STATUS_DECLINED,
        ]);

        // Пытаемся распределить награды
        $result = $this->service->distributeRewardsToUser($this->user1, $this->dungeonInstance);

        $this->assertTrue($result['success']);
        
        // Проверяем, что отказанная награда не была распределена
        $this->assertEmpty($result['distributed_rewards']);
        $this->assertEmpty($result['errors']);
        
        // Проверяем, что статус награды остался declined
        $reward->refresh();
        $this->assertEquals(DungeonRewardDistribution::STATUS_DECLINED, $reward->status);
    }

    /** @test */
    public function declined_rewards_not_finalized_as_distributed()
    {
        // Создаем отказанную награду
        $reward = DungeonRewardDistribution::create([
            'dungeon_instance_id' => $this->dungeonInstance->id,
            'party_id' => $this->party->id,
            'rewardable_type' => 'App\\Models\\Item',
            'rewardable_id' => $this->item->id,
            'quantity' => 1,
            'original_assignee_id' => $this->user1->id,
            'current_assignee_id' => $this->user1->id,
            'status' => DungeonRewardDistribution::STATUS_DECLINED,
        ]);

        // Финализируем все награды
        $this->service->finalizeAllRewards($this->dungeonInstance);

        // Проверяем, что отказанная награда не стала distributed
        $reward->refresh();
        $this->assertEquals(DungeonRewardDistribution::STATUS_DECLINED, $reward->status);
    }

    /** @test */
    public function declined_reward_with_requests_gets_redistributed()
    {
        // Создаем награду с запросами
        $reward = DungeonRewardDistribution::create([
            'dungeon_instance_id' => $this->dungeonInstance->id,
            'party_id' => $this->party->id,
            'rewardable_type' => 'App\\Models\\Item',
            'rewardable_id' => $this->item->id,
            'quantity' => 1,
            'original_assignee_id' => $this->user1->id,
            'current_assignee_id' => $this->user1->id,
            'status' => DungeonRewardDistribution::STATUS_PENDING,
            'requested_by' => [$this->user2->id],
        ]);

        // Игрок отказывается от награды
        $result = $this->service->declineReward($reward, $this->user1);

        $this->assertTrue($result['success']);
        
        // Проверяем, что награда перераспределилась
        $reward->refresh();
        $this->assertEquals(DungeonRewardDistribution::STATUS_ACCEPTED, $reward->status);
        $this->assertEquals($this->user2->id, $reward->current_assignee_id);
        $this->assertNull($reward->requested_by);
    }
}
# Руководство по исправлению счетчиков локаций

## Описание проблемы
В рудниках и других локациях в счетчиках отображаются игроки/боты, которых нельзя атаковать через кнопку "Бить любого". Проблема состояла из двух частей:

1. **Игроки с 0 HP** показывались в счетчиках из-за отсутствия проверки здоровья в `UserLocationService`
2. **Боты показывались в счетчиках, но были недоступны для атаки** из-за несоответствия логики между `FactionCountService` (счетчики) и `LocationPlayerCacheService` (поиск целей)

## Возможная ошибка

Если при тестировании появляется ошибка:
```
SQLSTATE[42703]: Undefined column: 7 ОШИБКА: столбец "is_defeated" не существует
```

Это означает, что миграция для добавления полей поражения не была выполнена. Выполните команды из раздела "Выполнение миграции".

## Исправления

### 1. UserLocationService.php
**Файл:** `app/Services/battle/UserLocationService.php`

#### Изменения в методе `getPlayersInLocation()` (строка 348):
- Добавлена фильтрация по `current_hp > 0`
- Добавлена фильтрация по `is_defeated = false`
- Добавлено логирование для отладки

#### Изменения в методе `getPlayersCountInLocation()` (строка 220):
- Добавлена базовая фильтрация мертвых/поверженных игроков
- Оптимизирована логика фильтрации по расе и классу

#### Изменения в методе `getBotsCountInLocation()` (строка 299):
- Добавлена фильтрация ботов с `death_time`
- Добавлена проверка на наличие локации
- Исключены боты с пустой локацией

#### Изменение видимости метода `clearLocationCache()`:
- Изменен с `protected` на `public` для возможности очистки кеша извне

### 2. LocationPlayerCacheService.php
**Файл:** `app/Services/battle/LocationPlayerCacheService.php`

#### Проблема:
Логика поиска ботов для атаки отличалась от логики подсчета в счетчиках:
- `FactionCountService` не проверял `is_active` на `MineLocation` 
- `LocationPlayerCacheService` проверял `is_active` на `MineLocation`
- Различие в фильтрах: отсутствовали проверки `death_time`, `location` и т.д.

#### Исправления:
- Убрана проверка `->where('is_active', true)` на `MineLocation`
- Добавлены фильтры: `->whereNull('death_time')`, `->whereNotNull('location')`, `->where('location', '!=', '')`
- Синхронизирована логика поиска ботов между сервисами

### 3. Дополнительные файлы

#### test_location_counter_fix.php
Скрипт для тестирования исправлений, показывает разницу до и после применения фикса.

#### test_bot_targeting_fix.php  
Скрипт для тестирования исправления проблемы с таргетированием ботов.

#### debug_bot_targeting_issue.php
Диагностический скрипт для анализа различий между логикой счетчиков и поиска целей.

#### cleanup_stuck_entities_comprehensive.php
Скрипт для очистки застрявших сущностей:
- Находит мертвых игроков в онлайн счетчиках
- Находит проблематичных ботов
- Находит игроков/ботов в несуществующих локациях
- Имеет режим dry-run для безопасного тестирования

#### clear_location_counters_cache.php
Скрипт для очистки всех кешей связанных с локациями и счетчиками.

## Команды для тестирования

### 1. Проверка текущего состояния
```bash
# Тест исправления игроков (безопасно)
php test_location_counter_fix.php

# Тест исправления ботов для атаки (безопасно)
php test_bot_targeting_fix.php

# Диагностика различий в логике (подробно)
php debug_bot_targeting_issue.php

# Поиск проблематичных сущностей (тестовый режим)
php cleanup_stuck_entities_comprehensive.php
```

### 2. Выполнение миграции (если поле is_defeated отсутствует)
```bash
# Добавление полей поражения в user_profiles
php run_defeat_migration.php

# Активация полной фильтрации после миграции
php enable_defeated_field_filtering.php
```

### 3. Применение исправлений
```bash
# Очистка кеша счетчиков
php clear_location_counters_cache.php

# Очистка застрявших сущностей (установить $dryRun = false в файле)
# ВНИМАНИЕ: Изменит данные в БД!
php cleanup_stuck_entities_comprehensive.php
```

### 4. Проверка логов
```bash
# Просмотр логов для отладки счетчиков
tail -f storage/logs/laravel.log | grep "UserLocationService\|FactionCountService"
```

## Ключевые изменения в логике

### До исправления:
```php
// Только проверка онлайн статуса
->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
```

### После исправления:
```php
// Проверка онлайн статуса + проверка здоровья
->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
->whereHas('profile', function ($q) {
    $q->where('current_hp', '>', 0)
      ->where('is_defeated', false);
})
```

## Проверка результата

### Ожидаемый результат:
1. В счетчиках локаций показываются только живые игроки (HP > 0, is_defeated = false)
2. Мертвые игроки не отображаются в фракционных счетчиках
3. Проблематичные боты (мертвые, без локации) исключены из подсчета
4. Игроки в несуществующих локациях перемещены в безопасные локации

### Тестирование:
1. Зайти в локацию где был застрявший Лунариус Маг
2. Проверить счетчики фракций - мертвые игроки не должны отображаться
3. Попробовать атаковать - должны быть доступны только живые игроки противоположной расы

## Мониторинг

Для постоянного мониторинга состояния счетчиков можно использовать:

```bash
# Периодическая проверка (каждые 30 минут)
*/30 * * * * cd /path/to/project && php test_location_counter_fix.php >> /var/log/location_counters.log
```

## Отмена изменений

Если потребуется отменить изменения, восстановите оригинальные методы в `UserLocationService.php`:
- Уберите фильтрацию по `current_hp` и `is_defeated`
- Восстановите оригинальную логику подсчета

## Безопасность

Все скрипты имеют режим dry-run и подробное логирование. Перед применением реальных изменений всегда:
1. Сделайте резервную копию БД
2. Протестируйте в тестовом режиме
3. Проверьте логи на предмет ошибок
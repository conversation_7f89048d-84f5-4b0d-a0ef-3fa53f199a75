<?php

namespace App\Services\battle\outposts\ElvenHaven;

use App\Models\User;
use App\Models\Bot;
use App\Services\BattleLogService;
use App\Services\LogFormattingService;
use App\Services\ObeliskService;
use App\Services\battle\outposts\ElvenHaven\EnemyFinderService;

/**
 * Сервис для смены цели игрока в локации Эльфийская Гавань
 */
class ChangePlayerTargetService
{
    protected BattleLogService $battleLogService;
    protected LogFormattingService $logFormatter;
    protected ObeliskService $obeliskService;
    protected EnemyFinderService $enemyFinderService;

    /**
     * Конструктор сервиса для смены цели игрока
     * 
     * @param BattleLogService $battleLogService Сервис боевых логов
     * @param LogFormattingService $logFormatter Сервис форматирования логов
     * @param ObeliskService $obeliskService Сервис обелиска
     * @param EnemyFinderService $enemyFinderService Сервис для поиска случайного врага
     */
    public function __construct(
        BattleLogService $battleLogService,
        LogFormattingService $logFormatter,
        ObeliskService $obeliskService,
        EnemyFinderService $enemyFinderService
    ) {
        $this->battleLogService = $battleLogService;
        $this->logFormatter = $logFormatter;
        $this->obeliskService = $obeliskService;
        $this->enemyFinderService = $enemyFinderService;
    }

    /**
     * Смена текущей цели игрока на другого врага
     * 
     * @param User $user Пользователь, который меняет цель
     * @return mixed Результат операции (редирект)
     */
    public function changePlayerTarget(User $user)
    {
        $currentLocation = $user->statistics->current_location;

        // НЕ сбрасываем last_attacker_id при смене цели через кнопку "Сменить цель"
        // last_attacker_id должен сохраняться для возможности ответного удара
        // Он сбрасывается только когда атакующий покидает локацию или умирает

        // Получаем расу пользователя и определяем противоположную фракцию
        $userFaction = $user->profile->race;
        $enemyFaction = ($userFaction === 'solarius') ? 'lunarius' : 'solarius';

        // Проверяем наличие вражеских игроков в локации, ИСКЛЮЧАЯ текущую цель
        $enemyPlayersCount = User::where('id', '!=', $user->id)
            ->where(function ($query) use ($user) {
                if ($user->current_target_type === 'player' && $user->current_target_id) {
                    $query->where('id', '!=', $user->current_target_id); // Исключаем текущую цель
                }
            })
            ->whereHas('profile', function ($q) use ($enemyFaction) {
                $q->where('race', $enemyFaction)
                    ->where('hp', '>', 0);
            })
            ->whereHas('statistics', function ($q) use ($currentLocation) {
                $q->where('current_location', $currentLocation);
            })
            ->count();

        // Проверяем наличие вражеских ботов в локации, ИСКЛЮЧАЯ текущую цель
        $enemyBotsCount = Bot::where(function ($query) use ($user) {
            if ($user->current_target_type === 'bot' && $user->current_target_id) {
                $query->where('id', '!=', $user->current_target_id); // Исключаем текущую цель
            }
        })
            ->where('race', $enemyFaction)
            ->where('location', $currentLocation)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->count();

        // Если нет других врагов, кроме текущей цели - сбрасываем цель
        if ($enemyPlayersCount == 0 && $enemyBotsCount == 0) {
            // Сбрасываем текущую цель
            $user->current_target_id = null;
            $user->current_target_type = null;
            $user->save();

            return [
                'success' => false,
                'message' => 'Никого нет.',
                'redirect' => true
            ];
        }

        // Обработка для смены цели с текущего бота на другого игрока или бота
        if ($user->current_target_type === 'bot') {
            // Попытаемся найти игрока или бота для атаки
            $enemy = $this->enemyFinderService->findRandomEnemyInLocation($user);

            if ($enemy) {
                // Устанавливаем новую цель
                $user->current_target_id = $enemy->id;
                $user->current_target_type = $enemy instanceof User ? 'player' : 'bot'; // Правильно устанавливаем тип цели
                $user->save();

                // Добавляем сообщение в лог
                $logA = $this->getBattleLogKey($user);

                // Атакуем новую цель
                if ($user->current_target_type === 'player') {
                    $newTarget = User::find($user->current_target_id);

                    // Наносим урон новой цели
                    $damageToDef = max($user->profile->strength - $newTarget->profile->armor, 0);
                    $newTarget->profile->hp = max($newTarget->profile->hp - $damageToDef, 0);
                    $newTarget->profile->save();
                    $newTarget->last_attacker_id = $user->id;
                    $newTarget->save();

                    $logD = $this->getBattleLogKey($newTarget);

                    // Используем LogFormattingService для единого формата логов
                    $attackLog = $this->logFormatter->formatPlayerAttack($user, $newTarget, $damageToDef, false);
                    $this->battleLogService->addLog($logA, $attackLog, 'success');

                    // Сообщение для защищающегося игрока
                    $defenseLog = $this->logFormatter->formatGenericMessage(
                        "", // Без иконки
                        "<span class='text-gray-400'>{$user->name}</span> <span class='text-red-600'>→</span>", // Атакующий ->
                        $damageToDef,
                        'text-red-400 font-medium', // Цвет урона
                        "<span class='text-red-600'>Вам</span>" // Вам
                    );
                    $this->battleLogService->addLog($logD, $defenseLog, 'danger');

                    // Проверка на смерть
                    if ($newTarget->profile->hp <= 0) {
                        return [
                            'success' => true,
                            'message' => '',
                            'redirect' => true,
                            'need_handle_death' => true,
                            'dead_user' => $newTarget,
                            'killer' => $user
                        ];
                    }

                    return [
                        'success' => true,
                        'message' => '',
                        'redirect' => true
                    ];
                } elseif ($user->current_target_type === 'bot') {
                    $newBot = Bot::find($user->current_target_id);

                    // Проверяем, не своей ли фракции бот
                    if ($newBot && $newBot->race === $user->profile->race) {
                        $user->current_target_id = null;
                        $user->current_target_type = null;
                        $user->save();

                        return [
                            'success' => false,
                            'message' => 'Вы не можете атаковать ботов своей фракции!',
                            'redirect' => true
                        ];
                    }

                    // Наносим урон боту
                    $damageToDef = max($user->profile->strength - $newBot->armor, 0);
                    $newBot->hp = max($newBot->hp - $damageToDef, 0);
                    $newBot->last_attacker_id = $user->id;
                    $newBot->last_attacker_type = 'player';
                    $newBot->save();

                    // Используем LogFormattingService для единого формата логов
                    $attackLog = $this->logFormatter->formatPlayerAttack($user, $newBot, $damageToDef, false);
                    $this->battleLogService->addLog($logA, $attackLog, 'success');

                    return [
                        'success' => true,
                        'message' => '',
                        'redirect' => true,
                        'attack_bot' => true,
                        'bot' => $newBot
                    ];
                }
            }
        }

        // Создаем запрос для поиска случайного врага, исключая текущую цель
        $potentialEnemyPlayers = User::where('id', '!=', $user->id)
            ->where(function ($query) use ($user) {
                if ($user->current_target_type === 'player' && $user->current_target_id) {
                    $query->where('id', '!=', $user->current_target_id); // Важно: исключаем текущую цель
                }
            })
            ->whereHas('profile', function ($q) use ($enemyFaction) {
                $q->where('race', $enemyFaction)
                    ->where('hp', '>', 0);
            })
            ->whereHas('statistics', function ($q) use ($currentLocation) {
                $q->where('current_location', $currentLocation);
            })
            ->get();

        // Аналогично для ботов
        $potentialEnemyBots = Bot::where(function ($query) use ($user) {
            if ($user->current_target_type === 'bot' && $user->current_target_id) {
                $query->where('id', '!=', $user->current_target_id); // Важно: исключаем текущую цель
            }
        })
            ->where('race', $enemyFaction)
            ->where('location', $currentLocation)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->get();

        // Объединяем возможные цели
        $allPotentialEnemies = $potentialEnemyPlayers->merge($potentialEnemyBots);

        // Если нет других врагов (только текущая цель) - сбрасываем цель и выводим сообщение
        if ($allPotentialEnemies->isEmpty()) {
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return [
                'success' => false,
                'message' => 'Никого нет.',
                'redirect' => true
            ];
        }

        // Выбираем случайного врага из оставшихся
        $enemy = $allPotentialEnemies->random();

        // Определяем тип врага и устанавливаем цель
        if ($enemy instanceof User) {
            $user->current_target_type = 'player';
            $user->current_target_id = $enemy->id;
            $user->save();

            // Наносим урон новой цели
            $damageToDef = max($user->profile->strength - $enemy->profile->armor, 0);
            $enemy->profile->hp = max($enemy->profile->hp - $damageToDef, 0);
            $enemy->profile->save();
            $enemy->last_attacker_id = $user->id;
            $enemy->save();

            // Логирование
            $logA = $this->getBattleLogKey($user);
            $logD = $this->getBattleLogKey($enemy);

            // Используем LogFormattingService для единого формата логов
            $attackLog = $this->logFormatter->formatPlayerAttack($user, $enemy, $damageToDef, false);
            $this->battleLogService->addLog($logA, $attackLog, 'success');

            // Сообщение для защищающегося игрока
            $defenseLog = $this->logFormatter->formatGenericMessage(
                "", // Без иконки
                "<span class='text-gray-400'>{$user->name}</span> <span class='text-red-600'>→</span>", // Атакующий ->
                $damageToDef,
                'text-red-400 font-medium', // Цвет урона
                "<span class='text-red-600'>Вам</span>" // Вам
            );
            $this->battleLogService->addLog($logD, $defenseLog, 'danger');

            // Регистрируем действие нанесения урона для обелиска
            // Получаем текущее HP цели до нанесения урона для корректного расчета БА
            $targetCurrentHp = $enemy->profile->hp;
            $this->obeliskService->registerAction(
                'Эльфийская Гавань', // локация
                'damage',              // тип действия (урон)
                $damageToDef,         // количество урона
                'player',              // тип источника (игрок)
                $user->id,             // ID источника
                'player',              // тип цели (игрок)
                $enemy->id,            // ID цели
                $targetCurrentHp       // Текущее HP цели до нанесения урона
            );

            // Проверка на смерть
            if ($enemy->profile->hp <= 0) {
                return [
                    'success' => true,
                    'message' => '',
                    'redirect' => true,
                    'need_handle_death' => true,
                    'dead_user' => $enemy,
                    'killer' => $user
                ];
            }
        } elseif ($enemy instanceof Bot) {
            $user->current_target_type = 'bot';
            $user->current_target_id = $enemy->id;
            $user->save();

            return [
                'success' => true,
                'message' => '',
                'redirect' => true,
                'attack_bot' => true,
                'bot' => $enemy
            ];
        }

        return [
            'success' => true,
            'message' => '',
            'redirect' => true
        ];
    }

    /**
     * Получает ключ для логов с учетом текущей локации
     *
     * @param mixed $user Пользователь
     * @return string Ключ для логов
     */
    private function getBattleLogKey($user)
    {
        // Получаем текущую локацию пользователя
        $currentLocation = $user->statistics->current_location ?? 'Неизвестно';

        // Проверяем, есть ли логи из Тарнмора, сохраненные с специфическим ключом
        $tarnmoreKey = "location:tarnmore_quarry:{$user->id}";
        if (\Illuminate\Support\Facades\Cache::has("{$tarnmoreKey}:logs")) {
            \Log::info("Найдены логи Тарнмора, будем использовать ключ {$tarnmoreKey}");
            return $tarnmoreKey;
        }

        // Используем общий ключ для всех боевых локаций, если специфический не найден
        return $this->battleLogService->getBattleLogKey($user->id);
    }
}
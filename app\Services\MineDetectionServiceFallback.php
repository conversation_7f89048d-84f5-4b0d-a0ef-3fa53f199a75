<?php

namespace App\Services;

use App\Models\User;
use App\Models\MineLocation;
use App\Models\ActiveEffect;
use App\Events\MineDetectionEvent;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * Временный сервис для управления системой обнаружения в рудниках
 * Использует таблицу active_effects вместо mine_marks пока не выполнена миграция
 */
class MineDetectionServiceFallback
{
    /**
     * Длительность метки обнаружения в секундах (5 минут)
     */
    const DETECTION_DURATION = 300;

    /**
     * Применить дебаф обнаружения к игроку при добыче ресурса
     */
    public function applyDetectionDebuff(User $player, MineLocation $mineLocation): ?ActiveEffect
    {
        try {
            // Удаляем существующие дебафы обнаружения для этого игрока в этом руднике
            $this->removeExistingDetectionEffects($player->id, $mineLocation->location_id, $mineLocation->id);

            // Создаем новый дебаф
            $effect = ActiveEffect::create([
                'target_type' => 'App\\Models\\User',
                'target_id' => $player->id,
                'effect_type' => 'mine_detection_debuff',
                'effect_data' => [
                    'location_id' => $mineLocation->location_id,
                    'mine_location_id' => $mineLocation->id,
                    'location_name' => $mineLocation->name,
                    'detected_at' => now()->toISOString()
                ],
                'starts_at' => now(),
                'ends_at' => now()->addSeconds(self::DETECTION_DURATION),
                'is_permanent' => false
            ]);

            Log::info('Применен дебаф обнаружения в руднике (fallback)', [
                'player_id' => $player->id,
                'player_name' => $player->name,
                'mine_location_id' => $mineLocation->id,
                'mine_location_name' => $mineLocation->name,
                'location_id' => $mineLocation->location_id,
                'effect_id' => $effect->id,
                'expires_at' => $effect->ends_at
            ]);

            return $effect;

        } catch (\Exception $e) {
            Log::error('Ошибка при применении дебафа обнаружения (fallback)', [
                'player_id' => $player->id,
                'mine_location_id' => $mineLocation->id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Проверить, обнаружен ли игрок
     */
    public function isPlayerDetected(int $playerId, int $locationId, ?int $mineLocationId = null): bool
    {
        $query = ActiveEffect::where('effect_type', 'mine_detection_debuff')
            ->where('target_type', 'App\\Models\\User')
            ->where('target_id', $playerId)
            ->where('ends_at', '>', now());

        if ($mineLocationId) {
            $query->whereJsonContains('effect_data->mine_location_id', $mineLocationId);
        } else {
            $query->whereJsonContains('effect_data->location_id', $locationId);
        }

        return $query->exists();
    }

    /**
     * Получить всех обнаруженных игроков в руднике
     */
    public function getDetectedPlayers(int $locationId, ?int $mineLocationId = null): array
    {
        $query = ActiveEffect::with('target')
            ->where('effect_type', 'mine_detection_debuff')
            ->where('target_type', 'App\\Models\\User')
            ->where('ends_at', '>', now());

        if ($mineLocationId) {
            $query->whereJsonContains('effect_data->mine_location_id', $mineLocationId);
        } else {
            $query->whereJsonContains('effect_data->location_id', $locationId);
        }

        $effects = $query->get();

        $players = [];
        foreach ($effects as $effect) {
            if ($effect->target) {
                $players[] = [
                    'effect_id' => $effect->id,
                    'player_id' => $effect->target_id,
                    'player_name' => $effect->target->name ?? 'Unknown',
                    'mine_location_id' => $effect->effect_data['mine_location_id'] ?? null,
                    'location_id' => $effect->effect_data['location_id'] ?? $locationId,
                    'location_name' => $effect->effect_data['location_name'] ?? 'Unknown',
                    'expires_at' => $effect->ends_at->timestamp,
                    'remaining_seconds' => $effect->ends_at->diffInSeconds(now()),
                    'detected_at' => $effect->effect_data['detected_at'] ?? null
                ];
            }
        }

        return $players;
    }

    /**
     * Удалить дебаф обнаружения с игрока
     */
    public function removeDetectionDebuff(int $playerId, ?int $locationId = null, ?int $mineLocationId = null): int
    {
        $query = ActiveEffect::where('effect_type', 'mine_detection_debuff')
            ->where('target_type', 'App\\Models\\User')
            ->where('target_id', $playerId);

        if ($mineLocationId) {
            $query->whereJsonContains('effect_data->mine_location_id', $mineLocationId);
        } elseif ($locationId) {
            $query->whereJsonContains('effect_data->location_id', $locationId);
        }

        $deletedCount = $query->delete();

        if ($deletedCount > 0) {
            Log::info('Удален дебаф обнаружения (fallback)', [
                'player_id' => $playerId,
                'location_id' => $locationId,
                'mine_location_id' => $mineLocationId,
                'deleted_count' => $deletedCount
            ]);
        }

        return $deletedCount;
    }

    /**
     * Очистить истекшие дебафы обнаружения
     */
    public function cleanupExpiredMarks(): int
    {
        $count = ActiveEffect::where('effect_type', 'mine_detection_debuff')
            ->where('ends_at', '<', now())
            ->delete();

        if ($count > 0) {
            Log::info('Очищены истекшие дебафы обнаружения (fallback)', [
                'cleaned_count' => $count
            ]);
        }

        return $count;
    }

    /**
     * Удалить существующие дебафы обнаружения для предотвращения дубликатов
     */
    private function removeExistingDetectionEffects(int $playerId, int $locationId, int $mineLocationId): int
    {
        return ActiveEffect::where('effect_type', 'mine_detection_debuff')
            ->where('target_type', 'App\\Models\\User')
            ->where('target_id', $playerId)
            ->where(function ($query) use ($locationId, $mineLocationId) {
                $query->whereJsonContains('effect_data->location_id', $locationId)
                    ->orWhereJsonContains('effect_data->mine_location_id', $mineLocationId);
            })
            ->delete();
    }

    /**
     * Проверить, может ли игрок быть атакован мобами
     */
    public function canPlayerBeAttackedByMobs(User $player, int $locationId, ?int $mineLocationId = null): bool
    {
        return $this->isPlayerDetected($player->id, $locationId, $mineLocationId);
    }

    /**
     * Обновить время последней атаки (для совместимости)
     */
    public function updateLastAttack(int $playerId, int $mineLocationId): bool
    {
        // В fallback версии просто возвращаем true
        return true;
    }

    /**
     * Получить активную метку игрока (для совместимости)
     */
    public function getActiveMark(int $playerId, int $mineLocationId): ?ActiveEffect
    {
        return ActiveEffect::where('effect_type', 'mine_detection_debuff')
            ->where('target_type', 'App\\Models\\User')
            ->where('target_id', $playerId)
            ->whereJsonContains('effect_data->mine_location_id', $mineLocationId)
            ->where('ends_at', '>', now())
            ->first();
    }
}
<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use App\Listeners\CreateInitialFarmingResources;
use App\Events\ObeliskMarkCreated;
use App\Events\ObeliskMarkExpired;
use App\Listeners\HandleObeliskMarkCreated;
use App\Listeners\HandleObeliskMarkExpired;
use App\Events\AccessKeyUsed;
use App\Events\AccessKeyExpired;
use App\Events\AccessKeyDeactivated;
use App\Events\AllAccessKeysDeactivated;
use App\Listeners\LogAccessKeyUsage;
use App\Listeners\HandleExpiredAccessKey;
use App\Listeners\ForceUserLogout;

// События и слушатели новой системы ботов аванпостов
use App\Events\Outpost\BotAttackEvent;
use App\Events\Outpost\BotHealingEvent;
use App\Listeners\Outpost\LogBotAttackListener;
use App\Listeners\Outpost\LogBotHealingListener;

// События и слушатели новой системы ботов рудников
use App\Events\Mine\BotAttackEvent as MineBotAttackEvent;
use App\Events\Mine\BotHealingEvent as MineBotHealingEvent;
use App\Listeners\Mine\LogBotAttackListener as MineLogBotAttackListener;
use App\Listeners\Mine\LogBotHealingListener as MineLogBotHealingListener;

// События и слушатели умного распределения целей ботов
use App\Events\BotTargetSwitchEvent;
use App\Listeners\LogBotTargetSwitchListener;

// События системы уведомлений о тикетах (без кэширования для максимальной надежности)
// use App\Events\AdminCommentAdded; // Не используется, так как нет слушателей

// События системы расплавки предметов
use App\Events\ItemMelted;
use App\Listeners\LogItemMelting;

// Новые события системы расплавки с PostgreSQL
use App\Events\MeltingProcessStarted;
use App\Events\MeltingProcessCompleted;
use App\Events\MeltingProcessFailed;
use App\Listeners\LogMeltingProcessStarted;
use App\Listeners\LogMeltingProcessCompleted;
use App\Listeners\LogMeltingProcessFailed;
use App\Listeners\AddFlashMessageForMeltingStarted;
use App\Listeners\AddFlashMessageForMeltingCompleted;
use App\Events\UserOnlineStatusChanged;
use App\Listeners\HandleUserOnlineStatusChange;
use App\Events\UserLocationChanged;
use App\Listeners\HandleUserLocationChange;

// События системы чата для оптимизации производительности
use App\Events\ChatMessageSent;
use App\Events\ChatChannelViewed;
use App\Listeners\ChatMessageSentListener;

// События системы групп для оптимизации производительности
use App\Events\PartyCreated;
use App\Events\PartyDisbanded;
use App\Events\MemberJoined;
use App\Events\MemberLeft;
use App\Listeners\PartyCreatedListener;
use App\Listeners\PartyDisbandedListener;
use App\Listeners\MemberJoinedListener;
use App\Listeners\MemberLeftListener;

// События системы подземелий
use App\Events\DungeonStarted;
use App\Events\DungeonCompleted;
use App\Events\DungeonAutoCompleted;
use App\Events\DungeonFinalized;
use App\Events\RewardDistributed;
use App\Listeners\ForceDungeonRedirect;
use App\Listeners\DungeonCompletedListener;
use App\Listeners\DungeonAutoCompletedListener;
use App\Listeners\DungeonFinalizedListener;
use App\Listeners\RewardDistributedListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string|string>>
     */
    protected $listen = [
            // Существующие слушатели
        Registered::class => [
            SendEmailVerificationNotification::class,
            CreateInitialFarmingResources::class, // Новый слушатель
        ],

            // События системы обелисков
        ObeliskMarkCreated::class => [
            HandleObeliskMarkCreated::class,
        ],

        ObeliskMarkExpired::class => [
            HandleObeliskMarkExpired::class,
        ],

            // События системы временного доступа
        AccessKeyUsed::class => [
            LogAccessKeyUsage::class,
        ],

        AccessKeyExpired::class => [
            HandleExpiredAccessKey::class,
        ],

            // События деактивации ключей доступа
        AccessKeyDeactivated::class => [
            ForceUserLogout::class . '@handleSingleKeyDeactivation',
        ],

        AllAccessKeysDeactivated::class => [
            ForceUserLogout::class . '@handleMassDeactivation',
        ],

            // События новой системы ботов аванпостов
        BotAttackEvent::class => [
            LogBotAttackListener::class,
        ],

        BotHealingEvent::class => [
            LogBotHealingListener::class,
        ],

            // События новой системы ботов рудников
        MineBotAttackEvent::class => [
            MineLogBotAttackListener::class,
        ],

        MineBotHealingEvent::class => [
            MineLogBotHealingListener::class,
        ],

            // События умного распределения целей ботов
        BotTargetSwitchEvent::class => [
            LogBotTargetSwitchListener::class,
        ],

            // События системы уведомлений о тикетах (без слушателей - используем прямые запросы к БД)
            // AdminCommentAdded::class => [],

            // События системы расплавки предметов (старая система)
        ItemMelted::class => [
            LogItemMelting::class,
        ],

            // Новые события системы расплавки с PostgreSQL
        MeltingProcessStarted::class => [
            LogMeltingProcessStarted::class,
            AddFlashMessageForMeltingStarted::class,
        ],
        MeltingProcessCompleted::class => [
            LogMeltingProcessCompleted::class,
            AddFlashMessageForMeltingCompleted::class,
        ],
        MeltingProcessFailed::class => [
            LogMeltingProcessFailed::class,
        ],

            // События системы онлайн-статуса пользователей (новая архитектура)
        UserOnlineStatusChanged::class => [
            HandleUserOnlineStatusChange::class, // Старый слушатель (для совместимости)
            \App\Listeners\UpdateOnlineCountCacheListener::class,
            \App\Listeners\LogUserActivityListener::class,
            \App\Listeners\BroadcastOnlineStatusListener::class,
        ],

            // События изменения местоположения пользователей
        UserLocationChanged::class => [
            HandleUserLocationChange::class,
        ],

            // События системы чата для оптимизации производительности
        ChatMessageSent::class => [
            ChatMessageSentListener::class,
        ],

            // События системы групп для оптимизации производительности
        PartyCreated::class => [
            PartyCreatedListener::class,
        ],

        PartyDisbanded::class => [
            PartyDisbandedListener::class,
        ],

        MemberJoined::class => [
            MemberJoinedListener::class,
        ],

        MemberLeft::class => [
            MemberLeftListener::class,
        ],

            // События системы подземелий
        DungeonStarted::class => [
            ForceDungeonRedirect::class,
        ],

        DungeonCompleted::class => [
            DungeonCompletedListener::class,
        ],

        DungeonAutoCompleted::class => [
            DungeonAutoCompletedListener::class,
        ],

        DungeonFinalized::class => [
            DungeonFinalizedListener::class,
        ],

        RewardDistributed::class => [
            RewardDistributedListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
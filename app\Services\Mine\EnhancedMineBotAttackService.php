<?php

namespace App\Services\Mine;

use App\Models\Bot;
use App\Models\User;
use App\Events\Mine\BotAttackEvent;
use App\Events\BotTargetSwitchEvent;
use App\Services\DamageService;
use App\Services\PlayerHealthService;
use App\Services\BotTargetDistributionService;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Улучшенный сервис атак ботов в рудниках с системой обнаружения
 * 
 * Расширяет базовый MineBotAttackService дополнительной логикой:
 * - Приоритет обнаруженных игроков (с дебафом 'detection_debuff')
 * - Умное распределение целей для избежания фокуса на одном игроке
 * - Адаптивная система переключения целей
 */
class EnhancedMineBotAttackService extends MineBotAttackService
{
    protected MineDetectionService $detectionService;

    public function __construct(
        MineBotCacheService $cacheService,
        DamageService $damageService,
        PlayerHealthService $playerHealthService,
        BotTargetDistributionService $targetDistributionService,
        MineDetectionService $detectionService
    ) {
        parent::__construct($cacheService, $damageService, $playerHealthService, $targetDistributionService);
        $this->detectionService = $detectionService;
    }

    /**
     * Находит цель для атаки с приоритетом для обнаруженных игроков
     */
    protected function findTarget(Bot $bot, string $location): ?User
    {
        // Проверяем, нужно ли сменить текущую цель
        if ($this->shouldSwitchCurrentTarget($bot, $location)) {
            $this->releaseCurrentTarget($bot, $location);
        }

        // Сначала ищем обнаруженных игроков с высоким приоритетом
        $detectedTargets = $this->getDetectedTargets($bot, $location);
        
        if (!$detectedTargets->isEmpty()) {
            Log::info('MineBotAttack: Найдены обнаруженные цели', [
                'bot_id' => $bot->id,
                'location' => $location,
                'detected_count' => $detectedTargets->count()
            ]);

            // Используем умное распределение среди обнаруженных игроков
            $target = $this->targetDistributionService->getOptimalTarget(
                $bot,
                $detectedTargets,
                'mine_detected',
                $location
            );

            if ($target) {
                Log::info('MineBotAttack: Выбрана обнаруженная цель', [
                    'bot_id' => $bot->id,
                    'target_id' => $target->id,
                    'target_name' => $target->name,
                    'location' => $location
                ]);
                return $target;
            }
        }

        // Если обнаруженных игроков нет, используем стандартную логику
        $potentialTargets = $this->getPotentialTargets($bot, $location);

        if ($potentialTargets->isEmpty()) {
            return null;
        }

        // Используем сервис умного распределения целей
        return $this->targetDistributionService->getOptimalTarget(
            $bot,
            $potentialTargets,
            'mine',
            $location
        );
    }

    /**
     * Получает обнаруженных игроков в локации как приоритетные цели
     */
    protected function getDetectedTargets(Bot $bot, string $location)
    {
        // Получаем ID основной локации из slug
        $mineLocation = \App\Models\MineLocation::where('slug', $location)->first();
        if (!$mineLocation) {
            return collect();
        }

        // Получаем всех обнаруженных игроков в этой локации
        $detectedPlayersData = $this->detectionService->getDetectedPlayers(
            $mineLocation->location_id,
            $mineLocation->id
        );

        if (empty($detectedPlayersData)) {
            return collect();
        }

        $detectedPlayerIds = array_column($detectedPlayersData, 'player_id');
        $enemyRace = $bot->getEnemyRace();
        
        // Онлайн порог - 20 минут как в стандартной системе
        $onlineThreshold = now()->timestamp - (20 * 60);

        return User::whereIn('id', $detectedPlayerIds)
            ->whereHas('profile', function ($query) use ($enemyRace) {
                $query->where('race', $enemyRace)
                    ->where('current_hp', '>', 0);
            })
            ->whereHas('statistics', function ($query) use ($location) {
                $query->where('current_location', $location);
            })
            ->where(function ($query) use ($onlineThreshold) {
                $query->where('is_online', true)
                    ->orWhere('last_activity_timestamp', '>', $onlineThreshold);
            })
            ->with(['profile', 'statistics'])
            ->get();
    }

    /**
     * Проверяет, является ли цель валидной с учетом системы обнаружения
     */
    protected function isValidTarget(User $target, Bot $bot, string $location): bool
    {
        // Базовые проверки
        if (!parent::isValidTarget($target, $bot, $location)) {
            return false;
        }

        // Дополнительная проверка: если игрок обнаружен, он всегда валидная цель
        $mineLocation = \App\Models\MineLocation::where('slug', $location)->first();
        if ($mineLocation && $this->detectionService->isPlayerDetected(
            $target->id, 
            $mineLocation->location_id, 
            $mineLocation->id
        )) {
            Log::debug('MineBotAttack: Цель валидна (обнаружена)', [
                'target_id' => $target->id,
                'bot_id' => $bot->id,
                'location' => $location
            ]);
            return true;
        }

        return true;
    }

    /**
     * Выполняет атаку с улучшенным логированием
     */
    protected function executeAttack(Bot $bot, User $target): bool
    {
        try {
            $mineLocation = \App\Models\MineLocation::where('slug', $target->statistics->current_location)->first();
            $isDetected = $mineLocation ? $this->detectionService->isPlayerDetected(
                $target->id, 
                $mineLocation->location_id, 
                $mineLocation->id
            ) : false;

            // Рассчитываем урон
            $damage = $this->damageService->calculateBotDamage($bot, $target);

            // Множитель урона для обнаруженных игроков (бонус 10-20%)
            if ($isDetected) {
                $detectionBonus = rand(110, 120) / 100; // 10-20% бонус урона
                $damage = round($damage * $detectionBonus);
                
                Log::info('MineBotAttack: Бонус урона за обнаружение', [
                    'bot_id' => $bot->id,
                    'target_id' => $target->id,
                    'bonus_multiplier' => $detectionBonus,
                    'final_damage' => $damage
                ]);
            }

            // Применяем урон
            $this->playerHealthService->reduceHealth($target, $damage);

            // Запускаем событие атаки
            event(new BotAttackEvent($bot, $target, $damage, [
                'attack_type' => 'mine_bot_attack',
                'location' => $target->statistics->current_location,
                'is_detected_target' => $isDetected,
                'detection_bonus_applied' => $isDetected
            ]));

            Log::info('MineBotAttack: Успешная атака', [
                'bot_id' => $bot->id,
                'bot_name' => $bot->name,
                'target_id' => $target->id,
                'target_name' => $target->name,
                'damage' => $damage,
                'target_hp_after' => $target->profile->current_hp,
                'location' => $target->statistics->current_location,
                'was_detected' => $isDetected
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('MineBotAttack: Ошибка выполнения атаки', [
                'bot_id' => $bot->id,
                'target_id' => $target->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Применяет интеллектуальное переключение целей с учетом обнаружения
     */
    protected function applyIntelligentTargeting(Bot $bot, $targets): ?User
    {
        // Разделяем цели на обнаруженные и обычные
        $detectedTargets = collect();
        $normalTargets = collect();

        foreach ($targets as $target) {
            $mineLocation = \App\Models\MineLocation::where('slug', $target->statistics->current_location)->first();
            $isDetected = $mineLocation ? $this->detectionService->isPlayerDetected(
                $target->id, 
                $mineLocation->location_id, 
                $mineLocation->id
            ) : false;

            if ($isDetected) {
                $detectedTargets->push($target);
            } else {
                $normalTargets->push($target);
            }
        }

        // Приоритет обнаруженным целям (80% шанс)
        if (!$detectedTargets->isEmpty() && (rand(1, 100) <= 80)) {
            return $this->selectRandomWeightedTarget($detectedTargets);
        }

        // Если нет обнаруженных или не прошли вероятность, выбираем из всех
        return $this->selectRandomWeightedTarget($targets);
    }

    /**
     * Выбирает случайную цель с весами
     */
    protected function selectRandomWeightedTarget($targets): ?User
    {
        if ($targets->isEmpty()) {
            return null;
        }

        // Применяем веса основываясь на HP (игроки с меньшим HP имеют больший вес)
        $weightedTargets = [];
        foreach ($targets as $target) {
            $hpPercent = $target->profile->current_hp / $target->profile->max_hp;
            // Инвертируем процент HP для веса (меньше HP = больший вес)
            $weight = round((1 - $hpPercent) * 100) + 1; // +1 чтобы избежать веса 0
            
            for ($i = 0; $i < $weight; $i++) {
                $weightedTargets[] = $target;
            }
        }

        return $weightedTargets[array_rand($weightedTargets)];
    }

    /**
     * Проверяет, нужно ли переключиться с текущей цели с учетом обнаружения
     */
    protected function shouldSwitchCurrentTarget(Bot $bot, string $location): bool
    {
        $currentTarget = $this->getCurrentTarget($bot);
        
        if (!$currentTarget) {
            return false;
        }

        // Если текущая цель больше не валидна, переключаемся
        if (!$this->isValidTarget($currentTarget, $bot, $location)) {
            Log::info('MineBotAttack: Переключение - текущая цель не валидна', [
                'bot_id' => $bot->id,
                'current_target_id' => $currentTarget->id
            ]);
            return true;
        }

        // Проверяем, есть ли обнаруженные цели с более высоким приоритетом
        $detectedTargets = $this->getDetectedTargets($bot, $location);
        $mineLocation = \App\Models\MineLocation::where('slug', $location)->first();
        
        if ($mineLocation) {
            $isCurrentTargetDetected = $this->detectionService->isPlayerDetected(
                $currentTarget->id,
                $mineLocation->location_id,
                $mineLocation->id
            );

            // Если текущая цель не обнаружена, а есть обнаруженные цели, переключаемся (50% шанс)
            if (!$isCurrentTargetDetected && !$detectedTargets->isEmpty() && rand(1, 100) <= 50) {
                Log::info('MineBotAttack: Переключение на обнаруженную цель', [
                    'bot_id' => $bot->id,
                    'current_target_id' => $currentTarget->id,
                    'detected_targets_count' => $detectedTargets->count()
                ]);
                return true;
            }
        }

        // Стандартная логика переключения (случайное переключение для разнообразия)
        return rand(1, 100) <= 15; // 15% шанс случайного переключения
    }

    /**
     * Освобождает текущую цель и логирует переключение
     */
    protected function releaseCurrentTarget(Bot $bot, string $location): void
    {
        $currentTarget = $this->getCurrentTarget($bot);
        
        if ($currentTarget) {
            Log::info('MineBotAttack: Освобождение текущей цели', [
                'bot_id' => $bot->id,
                'released_target_id' => $currentTarget->id,
                'location' => $location
            ]);

            // Событие переключения цели
            event(new BotTargetSwitchEvent($bot, $currentTarget, null, 'target_switch', [
                'reason' => 'intelligent_targeting',
                'location' => $location
            ]));
        }

        $bot->current_target_id = null;
        $bot->current_target_type = null;
        $bot->save();
    }
}
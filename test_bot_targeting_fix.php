<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use App\Services\battle\FactionCountService;
use App\Services\battle\LocationPlayerCacheService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Тестирование исправления таргетирования ботов ===\n\n";

$testLocation = 'аааааааааааа';
$testRace = 'lunarius'; // Предполагаем, что проблемный бот - лунариус

echo "Тестируем локацию: {$testLocation}\n";
echo "Ищем ботов расы: {$testRace}\n\n";

// Очищаем кеш сначала
$locationCacheService = app(LocationPlayerCacheService::class);
$locationCacheService->clearLocationCache($testLocation);
echo "✓ Кеш локации очищен\n\n";

echo "=== ДО И ПОСЛЕ ИСПРАВЛЕНИЯ ===\n";

// 1. Проверяем через FactionCountService (счетчики)
echo "1. Счетчики (FactionCountService):\n";
$factionCountService = app(FactionCountService::class);
$factionCounts = $factionCountService->getLocationFactionCounts($testLocation);

$lunariusBotCount = $factionCounts['bot_counts']['lunarius']['total'] ?? 0;
echo "   Ботов Lunarius в счетчиках: {$lunariusBotCount}\n";

// 2. Проверяем через LocationPlayerCacheService (атака)
echo "\n2. Поиск целей для атаки (LocationPlayerCacheService):\n";
$botsForAttack = $locationCacheService->getCachedBotsInLocation($testLocation, $testRace);
echo "   Ботов {$testRace} доступных для атаки: " . $botsForAttack->count() . "\n";

// 3. Подробности найденных ботов
if ($botsForAttack->count() > 0) {
    echo "\n   Найденные боты для атаки:\n";
    foreach ($botsForAttack as $bot) {
        echo "     - {$bot->name} (ID: {$bot->id})\n";
        echo "       HP: {$bot->hp}/{$bot->max_hp}\n";
        echo "       Локация: {$bot->location}\n";
        echo "       Mine Location ID: " . ($bot->mine_location_id ?: 'NULL') . "\n";
    }
} else {
    echo "   ❌ Боты не найдены для атаки\n";
}

// 4. Симуляция атаки
echo "\n=== СИМУЛЯЦИЯ КОМАНДЫ 'БИТЬ ЛЮБОГО' ===\n";

if ($lunariusBotCount > 0 && $botsForAttack->count() > 0) {
    echo "✅ ИСПРАВЛЕНО: Счетчики показывают ботов И они доступны для атаки\n";
    echo "   Результат: Кнопка 'Бить любого' будет работать\n";
} elseif ($lunariusBotCount > 0 && $botsForAttack->count() == 0) {
    echo "❌ ПРОБЛЕМА ОСТАЕТСЯ: Счетчики показывают ботов, но они недоступны для атаки\n";
    echo "   Результат: Сообщение 'В локации нет игроков или ботов противоположной фракции'\n";
} elseif ($lunariusBotCount == 0) {
    echo "ℹ️ В локации действительно нет ботов в счетчиках\n";
} else {
    echo "✅ Ситуация нормальная: есть боты для атаки\n";
}

// 5. Проверим конкретную локацию рудника
echo "\n=== ДЕТАЛИ ЛОКАЦИИ РУДНИКА ===\n";
$mineLocation = MineLocation::where('name', $testLocation)->first();

if ($mineLocation) {
    echo "Локация рудника:\n";
    echo "  ID: {$mineLocation->id}\n";
    echo "  Название: {$mineLocation->name}\n";
    echo "  Активна: " . ($mineLocation->is_active ? 'ДА' : 'НЕТ') . "\n";
    echo "  Это подлокация: " . ($mineLocation->isSubLocation() ? 'ДА' : 'НЕТ') . "\n";
    
    // Проверим всех ботов в этой локации
    $allBotsInMineLocation = Bot::where('mine_location_id', $mineLocation->id)
        ->where('is_active', true)
        ->get();
        
    echo "  Всего активных ботов в локации: " . $allBotsInMineLocation->count() . "\n";
    
    foreach ($allBotsInMineLocation as $bot) {
        $problems = [];
        if ($bot->hp <= 0) $problems[] = "HP <= 0";
        if ($bot->death_time) $problems[] = "death_time установлен";
        if (!$bot->location || $bot->location === '') $problems[] = "нет локации";
        
        $status = empty($problems) ? "✅ OK" : "❌ " . implode(', ', $problems);
        
        echo "    - {$bot->name} ({$bot->race} {$bot->class}) - {$status}\n";
        echo "      HP: {$bot->hp}/{$bot->max_hp}\n";
    }
} else {
    echo "❌ Локация рудника не найдена\n";
}

echo "\n=== РЕКОМЕНДАЦИИ ===\n";
if ($lunariusBotCount > 0 && $botsForAttack->count() == 0) {
    echo "1. Проверьте, активна ли локация рудника (is_active)\n";
    echo "2. Убедитесь, что у ботов нет death_time\n";
    echo "3. Проверьте поле location у ботов\n";
    echo "4. Очистите кеш: php clear_location_counters_cache.php\n";
} else {
    echo "✅ Проблема решена или отсутствует\n";
}

echo "\n=== Тест завершен ===\n";
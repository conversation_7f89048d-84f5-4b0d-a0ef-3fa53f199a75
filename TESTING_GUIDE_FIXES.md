# Руководство по тестированию исправлений

## Проблемы которые были исправлены

### 1. Ошибка с отсутствующим столбцом `parent_location_id`

**Проблема:** В файле `app/Services/MineTargetResetService.php:227` использовался несуществующий столбец `parent_location_id`.

**Исправление:** Заменено на правильный столбец `parent_id` и исправлена логика запроса.

```php
// Было:
$sublocations = MineLocation::where('parent_location_id', $mineLocation->baseLocation->id ?? 0)

// Стало:
$sublocations = MineLocation::where('parent_id', $mineLocation->id)
```

### 2. Неправильный подсчет ботов и игроков в счетчиках фракций

**Проблема:** 
- Боты с классами `archer` и `rogue` не учитывались в счетчиках
- Отсутствовало логирование для неподдерживаемых классов и рас
- Фабрика ботов создавала ботов с неподдерживаемыми классами

**Исправления:**
1. Обновлена фабрика ботов (`database/factories/BotFactory.php`) для использования только поддерживаемых классов: `warrior`, `mage`, `priest`
2. Добавлено логирование в `FactionCountService` для отслеживания ботов и игроков с неподдерживаемыми классами/расами
3. Улучшена обработка ошибок в методах подсчета

## Команды для тестирования

### Проверка данных в базе
```bash
php check_bot_data.php
```
Показывает:
- Уникальные расы и классы ботов
- Уникальные расы и классы игроков
- Статистику активных ботов

### Отладка подсчета фракций
```bash
php debug_faction_counting.php
```
Показывает:
- Детальный разбор ботов и игроков в тестовой локации
- Результаты работы FactionCountService
- Список неподдерживаемых классов/рас

### Проверка работы системы
```bash
# Проверка логов Laravel
tail -f storage/logs/laravel.log | grep -i "faction\|bot\|player"

# Проверка состояния кеша Redis (если используется)
redis-cli KEYS "*faction*"
redis-cli KEYS "*location*"
```

## Поддерживаемые классы и расы

### Классы (classes)
- `warrior` - Воин
- `mage` - Маг  
- `priest` - Жрец (отображается как Knight в интерфейсе)

### Расы (races)
- `solarius` - Альянс Солариус
- `lunarius` - Орда Лунариус

## Мониторинг проблем

После исправлений рекомендуется отслеживать логи на предмет warning сообщений:

```bash
# Мониторинг неподдерживаемых классов/рас
tail -f storage/logs/laravel.log | grep -i "неподдерживаем"

# Мониторинг ошибок SQL
tail -f storage/logs/laravel.log | grep -i "parent_location_id"
```

## Создание новых ботов

При создании новых ботов убедитесь, что используются только поддерживаемые значения:

```php
Bot::create([
    'race' => 'solarius', // или 'lunarius'
    'class' => 'warrior', // 'mage' или 'priest'
    // остальные поля...
]);
```

## Очистка созданных файлов после тестирования

```bash
# Удаление отладочных скриптов (если они больше не нужны)
rm check_bot_data.php
rm debug_faction_counting.php
```

Важно: НЕ удаляйте эти файлы, если планируете продолжать отладку или мониторинг системы.
<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
// УДАЛЕНО: Старые Job классы заменены на новую систему ботов аванпостов
// Пример команды, показывающей цитаты (встроенная демонстрация Laravel)
Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Расписание вашей команды в Laravel 11
// Убедитесь, что расписание команд задаётся корректно


Schedule::command('users:mark-inactive')
    ->everyMinute()
    ->withoutOverlapping(5)
    ->appendOutputTo(storage_path('logs/user_activity.log'))
    ->onOneServer();


// ОТКЛЮЧЕНО: слишком частое выполнение
// Schedule::command('effects:cleanup-active')->everyFiveSeconds();

// УДАЛЕНО: Старая команда автоатаки мобов заменена на новую систему ботов аванпостов
// Используйте: php artisan outpost:process-bots --all

// ЭКСТРЕННАЯ ДИАГНОСТИКА ПАМЯТИ
Artisan::command('system:emergency-diagnosis', function () {
    $this->info('🚨 ЭКСТРЕННАЯ ДИАГНОСТИКА СИСТЕМЫ');
    $this->newLine();

    // Проверка памяти
    $memoryUsage = memory_get_usage(true);
    $memoryPeak = memory_get_peak_usage(true);
    $memoryLimit = ini_get('memory_limit');

    $this->info("💾 Память:");
    $this->info("  Текущая: " . round($memoryUsage / 1024 / 1024, 2) . " MB");
    $this->info("  Пиковая: " . round($memoryPeak / 1024 / 1024, 2) . " MB");
    $this->info("  Лимит: " . $memoryLimit);

    // Проверка процессов
    $this->info("🔍 Активные процессы планировщика:");
    if (PHP_OS_FAMILY === 'Windows') {
        $processes = shell_exec('tasklist /FI "IMAGENAME eq php.exe" /FO CSV');
    } else {
        $processes = shell_exec('ps aux | grep "artisan schedule" | grep -v grep');
    }
    $this->info($processes ?: "Нет активных процессов");

    // Проверка Redis
    try {
        \Illuminate\Support\Facades\Redis::ping();
        $this->info("✅ Redis: Подключен");
    } catch (\Exception $e) {
        $this->error("❌ Redis: " . $e->getMessage());
    }

    // Проверка ботов
    $activeBots = \App\Models\Bot::where('is_active', true)->count();
    $this->info("🤖 Активных ботов: " . $activeBots);

    // Рекомендации
    $this->newLine();
    $this->warn("🔧 РЕКОМЕНДАЦИИ:");
    if ($memoryUsage > 200 * 1024 * 1024) {
        $this->error("⚠️  Высокое использование памяти! Перезапустите планировщик");
    }
    $this->info("1. Остановите планировщик: sudo pkill -f 'artisan schedule'");
    $this->info("2. Очистите память: sudo sync && echo 3 | sudo tee /proc/sys/vm/drop_caches");
    $this->info("3. Запустите безопасно: ./start_scheduler_memory_safe.sh start");

})->purpose('Emergency system diagnosis for memory leaks');

// Команда для ручного запуска управления ботами в локации
Artisan::command('bots:process {location?}', function ($location = null) {
    \Log::info('Ручной запуск управления ботами' . ($location ? " в локации $location" : ""));
    if ($location) {
        Artisan::call('bots:manage-location', ['location' => $location]);
        $this->info("Управление ботами в локации $location запущено");
    } else {
        Artisan::call('bots:manage-location');
        $this->info("Управление ботами во всех локациях запущено");
    }
})->purpose('Process bots in locations');

// УДАЛЕНО: Старая команда автоатаки ботов заменена на новую систему ботов аванпостов
// Используйте: php artisan outpost:process-bots --location="Название аванпоста"

// УДАЛЕНО: Старая команда принудительных атак заменена на новую систему

// УДАЛЕНО: Старая команда лечения ботов заменена на новую систему
// Лечение интегрировано в: php artisan outpost:process-bots --location="Название аванпоста"

// УДАЛЕНО: Диагностика старой системы лечения больше не нужна
// Используйте: php artisan outpost:test-bot-system --full

// Команда для ручного запуска воскрешения ботов
Artisan::command('bots:respawn-manual {location?} {--force} {--stats}', function ($location = null) {
    $options = [];
    if ($location)
        $options['--location'] = $location;
    if ($this->option('force'))
        $options['--force'] = true;
    if ($this->option('stats'))
        $options['--stats'] = true;

    \Log::info('Ручной запуск воскрешения ботов' . ($location ? " в локации $location" : ""));
    $exitCode = $this->call('bots:respawn', $options);

    if ($exitCode === 0) {
        $this->info('✅ Воскрешение ботов завершено успешно');
    } else {
        $this->error('❌ Воскрешение ботов завершено с ошибками');
    }
})->purpose('Manual bot respawn with options');

// Команда для тестирования системы воскрешения ботов
Artisan::command('bots:test-respawn-full {--location=Тестовая Локация} {--cleanup}', function () {
    $location = $this->option('location');
    $cleanup = $this->option('cleanup');

    $options = ['--location' => $location];
    if ($cleanup)
        $options['--cleanup'] = true;

    $this->info('🧪 Запуск полного тестирования системы воскрешения ботов...');
    $exitCode = $this->call('bots:test-respawn-system', $options);

    if ($exitCode === 0) {
        $this->info('✅ Тестирование завершено успешно');
    } else {
        $this->error('❌ Тестирование завершено с ошибками');
    }
})->purpose('Full respawn system testing');

// Команда для исправления локаций ботов в рудниках
Artisan::command('bots:fix-mine-locations', function () {
    $this->info('Исправляем локации ботов в рудниках...');

    $mineLocations = \App\Models\MineLocation::where('is_active', true)->get();
    $totalFixed = 0;

    foreach ($mineLocations as $mineLocation) {
        // Ищем ботов с location = ID локации
        $bots = \App\Models\Bot::where('location', (string) $mineLocation->id)->get();

        if ($bots->count() > 0) {
            $this->info("Локация: {$mineLocation->name} (ID: {$mineLocation->id})");
            $this->info("  Найдено ботов с неправильной локацией: {$bots->count()}");

            foreach ($bots as $bot) {
                $oldLocation = $bot->location;
                $bot->location = $mineLocation->name;
                $bot->save();
                $this->line("    ✅ {$bot->name}: '{$oldLocation}' → '{$bot->location}'");
                $totalFixed++;
            }
        }
    }

    $this->info("✅ Исправлено локаций у ботов: {$totalFixed}");
})->purpose('Fix bot locations in mines');



// УДАЛЕНО: команды с рекурсивными вызовами, которые создавали бесконечные циклы
// Эти команды должны быть реализованы как отдельные классы в app/Console/Commands/
// или их логика должна быть встроена непосредственно в эти функции

// TODO: Создать отдельные команды для:
// - bots:check-and-reset
// - bots:test-attack
// - bots:fix-damage
// - bots:test-targeting

// === НОВЫЕ КОМАНДЫ ДИАГНОСТИКИ И ТЕСТИРОВАНИЯ ===

// ИСПРАВЛЕНО: команда диагностики системы ботов (убрана рекурсия)
Artisan::command('bots:diagnose {--location=} {--fix} {--detailed}', function () {
    $location = $this->option('location');
    $fix = $this->option('fix');
    $detailed = $this->option('detailed');

    $this->info('🔍 Запуск диагностики системы ботов...');

    // Простая диагностика без рекурсивного вызова
    $query = \App\Models\Bot::query();
    if ($location) {
        $query->where('location', $location);
        $this->info("📍 Локация: {$location}");
    }

    $totalBots = $query->count();
    $activeBots = $query->where('is_active', true)->count();
    $deadBots = $query->where('hp', '<=', 0)->count();

    $this->info("🤖 Всего ботов: {$totalBots}");
    $this->info("✅ Активных: {$activeBots}");
    $this->info("💀 Мертвых: {$deadBots}");

    if ($fix && $deadBots > 0) {
        $this->info('🔧 Исправление мертвых ботов...');
        Artisan::call('bots:fix-activity', ['--location' => $location]);
        $this->info('✅ Исправление завершено');
    }

    $this->info('✅ Диагностика завершена');
})->purpose('Полная диагностика системы ботов');

// Команда для быстрого тестирования системы ботов
Artisan::command('bots:test {--location=} {--create-test-data} {--run-attacks}', function () {
    $location = $this->option('location');
    $createTestData = $this->option('create-test-data');
    $runAttacks = $this->option('run-attacks');

    $this->info('Запуск быстрого тестирования системы ботов');
    $this->call('bots:test-quick', [
        '--location' => $location,
        '--create-test-data' => $createTestData,
        '--run-attacks' => $runAttacks
    ]);
})->purpose('Быстрое тестирование системы ботов');

// Используем стандартные интервалы Laravel 11
// Запускаем задачу автоатаки мобов каждые 10 секунд
// Планирование запуска каждые 10 секунд (как и для обычной задачи автоатаки)
// Schedule::command('mobs:tarnmore-attack')
//     ->everyTenSeconds()
//     ->withoutOverlapping(5);
//     // ->runInBackground();

// УДАЛЕНО: TarnmoreMobsAttackJob больше не используется
// Функционал перенесен в MobAutoAttackJob для единой системы обработки мобов

// === УДАЛЕНО: СТАРАЯ СИСТЕМА БОТОВ РУДНИКОВ ===
// Старая система с Lua-скриптами полностью заменена на новую систему ботов аванпостов
// Используйте команду: php artisan outpost:process-bots --all

// === УДАЛЕНО: СТАРАЯ СИСТЕМА ЛЕЧЕНИЯ БОТОВ С LUA ===
// Лечение ботов теперь интегрировано в новую систему ботов аванпостов
// Жрецы автоматически лечат союзников в рамках команды: php artisan outpost:process-bots --all

// === НОВАЯ СИСТЕМА БОТОВ АВАНПОСТОВ (БЕЗ LUA) ===

// ИСПРАВЛЕНО: ОПТИМИЗИРОВАННАЯ СИСТЕМА ботов аванпостов с умным распределением целей
// Используем стандартные методы Laravel вместо 6-позиционных cron-выражений
// Интервал 15-30 секунд с рандомизацией для непредсказуемости

// БАЗОВЫЙ ИНТЕРВАЛ: Обработка ботов каждые 15 секунд
Schedule::command('outpost:process-bots --all')
    ->everyFifteenSeconds()  // Стандартный метод Laravel (каждые 15 секунд)
    ->name('outpost_bots_processing_15s')
    ->withoutOverlapping(45)  // Таймаут для предотвращения конфликтов
    ->appendOutputTo(storage_path('logs/outpost-bots.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для обработки ботов аванпостов: ' . $e->getMessage());
            return false;
        }
    });

// ДОПОЛНИТЕЛЬНАЯ РАНДОМИЗАЦИЯ: Дополнительные атаки каждые 30 секунд
Schedule::command('outpost:process-bots --all')
    ->everyThirtySeconds()  // Стандартный метод Laravel (каждые 30 секунд)
    ->name('outpost_bots_processing_30s')
    ->withoutOverlapping(30)
    ->appendOutputTo(storage_path('logs/outpost-bots-random.log'))
    ->onOneServer()
    ->when(function () {
        // Рандомизация: выполняем только в 60% случаев для непредсказуемости
        if (rand(1, 100) > 60) {
            return false;
        }

        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для рандомных атак ботов: ' . $e->getMessage());
            return false;
        }
    });

// ДОПОЛНИТЕЛЬНАЯ ЧАСТОТА: Атаки каждую минуту с рандомизацией
Schedule::command('outpost:process-bots --all')
    ->everyMinute()  // Стандартный метод Laravel (каждую минуту)
    ->name('outpost_bots_processing_1m')
    ->withoutOverlapping(25)
    ->appendOutputTo(storage_path('logs/outpost-bots-max.log'))
    ->onOneServer()
    ->when(function () {
        // Рандомизация: выполняем только в 40% случаев
        if (rand(1, 100) > 40) {
            return false;
        }

        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для дополнительных атак ботов: ' . $e->getMessage());
            return false;
        }
    });

// === СТАРАЯ СИСТЕМА ПОЛНОСТЬЮ ОТКЛЮЧЕНА ===
// Старая система MobAutoAttackJob больше не используется
// Все автоатаки ботов теперь обрабатываются через новую команду outpost:process-bots

// Альтернативный вариант - использование cron выражения для произвольного интервала
// (раскомментируйте, если нужно именно 8 секунд)
// Schedule::command('mobs:auto-attack')
//     ->cron('*/8 * * * * *')  // каждые 8 секунд
//     ->withoutOverlapping(5)
//     ->runInBackground();


// ОТКЛЮЧЕНО: дублирует outpost:process-bots и создает утечку памяти
// Schedule::job(new MobAutoAttackJob())
//     ->everyTenSeconds()
//     ->withoutOverlapping(5);

// ОТКЛЮЧЕНО: слишком частое выполнение
// Schedule::command('skills:update-cooldowns')->everyFiveSeconds()->withoutOverlapping();
// Schedule::command('blacksmith:clear-logs')->everyFiveSeconds();
Schedule::command('resources:respawn')
    ->everyMinute()
    ->appendOutputTo(storage_path('logs/resource-respawn.log'))
    ->withoutOverlapping()
    ->onOneServer();
;

// ИСПРАВЛЕНО: заменена несуществующая команда на правильную
Schedule::command('bots:fix-targeting')
    ->everyFiveMinutes()  // Было 30 секунд, стало 5 минут
    ->withoutOverlapping(10)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/bots-targeting.log'));

// ИСПРАВЛЕНО: автоматический сброс таймеров ботов каждые 5 минут
Schedule::command('bots:reset-timers')
    ->everyFiveMinutes()
    ->withoutOverlapping(5)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/bots-timers.log'));

// Запускаем диагностику ботов каждый час
Schedule::command('bots:diagnose')
    ->hourly()
    ->appendOutputTo(storage_path('logs/bots-diagnosis.log'));

// ИСПРАВЛЕНО: восстановление активности ботов каждые 10 минут
Schedule::command('bots:fix-activity')
    ->everyTenMinutes()
    ->withoutOverlapping(5)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/bots-activity.log'));

// ИСПРАВЛЕНО: ОПТИМИЗИРОВАННАЯ СИСТЕМА ботов рудников с умным распределением целей
// Используем стандартные методы Laravel вместо 6-позиционных cron-выражений
// Интервал 15-60 секунд с рандомизацией для непредсказуемости

// БАЗОВЫЙ ИНТЕРВАЛ: Обработка ботов рудников каждые 15 секунд
Schedule::command('mine:process-bots --all')
    ->everyFifteenSeconds()  // Стандартный метод Laravel (каждые 15 секунд)
    ->name('mine_bots_processing_15s')
    ->withoutOverlapping(50)  // Таймаут для предотвращения конфликтов
    ->appendOutputTo(storage_path('logs/mine-bots.log'))
    ->onOneServer()
    ->when(function () {
        // Рандомизация: выполняем только в 80% случаев (чаще чем аванпосты)
        if (rand(1, 100) > 80) {
            return false;
        }

        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для обработки ботов рудников: ' . $e->getMessage());
            return false;
        }
    });

// СРЕДНИЙ ИНТЕРВАЛ: Дополнительные атаки ботов рудников каждые 30 секунд
Schedule::command('mine:process-bots --all')
    ->everyThirtySeconds()  // Стандартный метод Laravel (каждые 30 секунд)
    ->name('mine_bots_processing_30s')
    ->withoutOverlapping(35)
    ->appendOutputTo(storage_path('logs/mine-bots-medium.log'))
    ->onOneServer()
    ->when(function () {
        // Рандомизация: выполняем только в 70% случаев
        if (rand(1, 100) > 70) {
            return false;
        }

        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для средних атак ботов рудников: ' . $e->getMessage());
            return false;
        }
    });

// МАКСИМАЛЬНЫЙ ИНТЕРВАЛ: Атаки каждую минуту (верхний предел для рудников)
Schedule::command('mine:process-bots --all')
    ->everyMinute()  // Стандартный метод Laravel (каждую минуту)
    ->name('mine_bots_processing_1m')
    ->withoutOverlapping(30)
    ->appendOutputTo(storage_path('logs/mine-bots-max.log'))
    ->onOneServer()
    ->when(function () {
        // Рандомизация: выполняем только в 50% случаев
        if (rand(1, 100) > 50) {
            return false;
        }

        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для максимальных атак ботов рудников: ' . $e->getMessage());
            return false;
        }
    });

// УДАЛЕНО: старая команда автоатаки ботов заменена на новую систему ботов аванпостов и рудников
// Используйте: php artisan outpost:process-bots --all или php artisan mine:process-bots --all

// НОВОЕ: обработка атак мобов в аванпостах для игроков с метками обелиска
Schedule::command('outposts:process-obelisk-mob-attacks')
    ->everyTenSeconds()  // Каждые 10 секунд
    ->withoutOverlapping(5)
    ->appendOutputTo(storage_path('logs/obelisk-mob-attacks.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем, что Redis доступен
        try {
            \Illuminate\Support\Facades\Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для обработки атак мобов в аванпостах: ' . $e->getMessage());
            return false;
        }
    });

// НОВОЕ: автоматические атаки мобов на замеченных игроков в рудниках
Schedule::job(new \App\Jobs\MineAutoAttackJob())
    ->everyFifteenSeconds()  // Каждые 15 секунд для регулярных атак
    ->name('mine_auto_attack_primary')
    ->withoutOverlapping(45)  // Таймаут 45 секунд
    ->onOneServer()
    ->when(function () {
        // Проверяем, что Redis доступен
        try {
            \Illuminate\Support\Facades\Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для автоатак мобов в рудниках: ' . $e->getMessage());
            return false;
        }
    });

// ДОПОЛНИТЕЛЬНО: вторичные атаки мобов в рудниках каждые 30 секунд с рандомизацией
Schedule::job(new \App\Jobs\MineAutoAttackJob())
    ->everyThirtySeconds()  // Каждые 30 секунд
    ->name('mine_auto_attack_secondary')
    ->withoutOverlapping(75)  // Таймаут 75 секунд
    ->onOneServer()
    ->when(function () {
        // Рандомизация: выполняем только в 70% случаев
        if (rand(1, 100) > 70) {
            return false;
        }

        // Проверяем, что Redis доступен
        try {
            \Illuminate\Support\Facades\Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для вторичных автоатак мобов в рудниках: ' . $e->getMessage());
            return false;
        }
    });

// УЛУЧШЕНО: автоматическая очистка истекших меток обелисков
Schedule::command('obelisk:cleanup-expired-marks')
    ->everyTwoMinutes()  // Каждые 2 минуты для более быстрой очистки
    ->withoutOverlapping(10)
    ->appendOutputTo(storage_path('logs/obelisk-cleanup.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем, что Redis доступен
        try {
            \Illuminate\Support\Facades\Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для очистки меток обелисков: ' . $e->getMessage());
            return false;
        }
    });

// НОВОЕ: автоматическая очистка истекших меток рудников
Schedule::command('mine:cleanup-marks')
    ->everyTwoMinutes()  // Каждые 2 минуты для синхронизации с обелисками
    ->name('mine_marks_cleanup')
    ->withoutOverlapping(120)  // Таймаут 2 минуты
    ->onOneServer()
    ->when(function () {
        // Проверяем, что Redis доступен
        try {
            \Illuminate\Support\Facades\Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для очистки меток рудников: ' . $e->getMessage());
            return false;
        }
    });

// НОВОЕ: автоматическое воскрешение ботов согласно их индивидуальным интервалам
Schedule::command('bots:respawn')
    ->everyMinute()  // Интервал из конфигурации (по умолчанию каждую минуту)
    ->withoutOverlapping(config('bot_respawn.scheduler_overlap_timeout', 10))
    ->appendOutputTo(storage_path('logs/bots-respawn.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем, что система не перегружена согласно конфигурации
        try {
            $memoryUsage = memory_get_usage(true);
            $memoryLimitMB = config('bot_respawn.memory_limit_mb', 512);
            $currentMemoryMB = round($memoryUsage / 1024 / 1024, 2);

            return $currentMemoryMB < $memoryLimitMB;
        } catch (\Exception $e) {
            return true; // В случае ошибки разрешаем выполнение
        }
    });

// УДАЛЕНО: Старые системы ботов аванпостов заменены на новую упрощенную архитектуру

// ОПТИМИЗИРОВАННЫЙ: автоматический респаун мобов в аванпостах
Schedule::command('mobs:respawn')
    ->everyMinute()  // Проверяем каждую минуту
    ->withoutOverlapping(30)  // Увеличен таймаут для предотвращения наложений
    ->appendOutputTo(storage_path('logs/mobs-respawn.log'))
    ->onOneServer()  // Выполняем только на одном сервере в кластере
    ->when(function () {
        // Проверяем, что есть мертвые мобы для респауна
        try {
            // Оптимизированный запрос с лимитом для быстрой проверки
            return \App\Models\Mob::where('hp', '<=', 0)
                ->where(function ($query) {
                $query->whereRaw('death_time <= NOW() - (respawn_time * INTERVAL \'1 minute\')')
                    ->orWhereNull('death_time');
            })
                ->limit(1)
                ->exists();
        } catch (\Exception $e) {
            \Log::error('Ошибка при проверке мобов для респауна: ' . $e->getMessage());
            return false;
        }
    });

// РЕЗЕРВНЫЙ: Респаун мобов через очереди (более надежный способ)
Schedule::job(new \App\Jobs\MobRespawnJob())
    ->everyTwoMinutes()  // Запускаем каждые 2 минуты как резерв
    ->withoutOverlapping(60)
    ->onOneServer()
    ->when(function () {
        // Запускаем только если основная команда не работает
        try {
            // Проверяем, есть ли мобы, которые должны были респавниться более 5 минут назад
            return \App\Models\Mob::where('hp', '<=', 0)
                ->where(function ($query) {
                $query->whereRaw('death_time <= NOW() - (respawn_time + 5) * INTERVAL \'1 minute\'')
                    ->orWhere('death_time', '<=', now()->subMinutes(10));
            })
                ->limit(1)
                ->exists();
        } catch (\Exception $e) {
            \Log::error('Ошибка при проверке резервного респавна мобов: ' . $e->getMessage());
            return false;
        }
    });

// ОПТИМИЗИРОВАНО: Очистка истекших эффектов предметов каждые 5 минут
Schedule::command('game:cleanup-item-effects')
    ->everyFiveMinutes()  // Было каждую минуту
    ->withoutOverlapping(10)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/item-effects.log'));

// ОПТИМИЗИРОВАНО: Очистка истекших активных эффектов умений каждые 5 минут
Schedule::command('effects:cleanup-active')
    ->everyFiveMinutes()  // Было каждую минуту
    ->withoutOverlapping(10)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/active-effects.log'));

// ОТКЛЮЧЕНО: Команда brewing:check-status не существует - вызывает утечки памяти
// Schedule::command('brewing:check-status')
//     ->everyMinute()
//     ->withoutOverlapping(5)
//     ->appendOutputTo(storage_path('logs/brewing-status.log'))
//     ->onOneServer();

// ВРЕМЕННО: Используем встроенную команду для проверки статуса варки
Artisan::command('brewing:check-status', function () {
    $this->info('Проверка статусов варок зелий...');

    try {
        // Получаем все активные варки
        $activeBrewings = \App\Models\ActiveBrewing::where('status', 'brewing')
            ->where('finished_at', '<=', now())
            ->get();

        $completedCount = 0;
        foreach ($activeBrewings as $brewing) {
            if ($brewing->isCompleted()) {
                $brewing->status = 'completed';
                $brewing->save();
                $completedCount++;
            }
        }

        $this->info("Обработано завершенных варок: {$completedCount}");

        // Принудительная очистка памяти
        unset($activeBrewings);
        gc_collect_cycles();

    } catch (\Exception $e) {
        $this->error('Ошибка при проверке статусов варок: ' . $e->getMessage());
        \Log::error('brewing:check-status error: ' . $e->getMessage());
    }
})->purpose('Проверка и обновление статусов варок зелий');

// Запускаем проверку статусов варок каждые 2 минуты (снижена частота)
Schedule::command('brewing:check-status')
    ->everyTwoMinutes()  // Было каждую минуту
    ->withoutOverlapping(10)
    ->appendOutputTo(storage_path('logs/brewing-status.log'))
    ->onOneServer();

// Команда для ручного запуска проверки статусов варок зелий
Artisan::command('brewing:check-status-manual {user_id?}', function ($userId = null) {
    $this->info('Ручной запуск проверки статусов варок зелий');

    if ($userId) {
        $this->call('brewing:check-status', ['user_id' => $userId]);
    } else {
        $this->call('brewing:check-status');
    }
})->purpose('Manually check and update brewing statuses');

// Команда для проверки записей с нулевым количеством в таблице user_resources
Artisan::command('resources:check-zero-quantity', function () {
    $zeroQuantityCount = DB::table('user_resources')->where('quantity', 0)->count();
    $this->info("Количество записей с нулевым количеством: {$zeroQuantityCount}");

    if ($zeroQuantityCount > 0) {
        $zeroQuantityResources = DB::table('user_resources')
            ->where('quantity', 0)
            ->get();

        $this->info("Список записей с нулевым количеством:");
        foreach ($zeroQuantityResources as $resource) {
            $this->info("ID: {$resource->id}, User ID: {$resource->user_id}, Resource ID: {$resource->resource_id}, Location: {$resource->location}");
        }

        if ($this->confirm('Хотите обновить эти записи, установив количество 5?')) {
            DB::table('user_resources')
                ->where('quantity', 0)
                ->update(['quantity' => 5]);

            $this->info("Записи обновлены!");
        }
    } else {
        $this->info("Записей с нулевым количеством не найдено!");
    }
})->purpose('Check and fix zero quantity resources');

// Команда для респауна ресурсов в локациях
Artisan::command('resources:respawn', function () {
    $service = app(\App\Services\LocationResourceService::class);
    $service->respawnResources();
    $this->info('Ресурсы успешно обновлены');
})->purpose('Респаун ресурсов в локациях');

// Команда для исправления проблемы с инвентарем
Artisan::command('inventory:fix-issue {user_id?}', function ($userId = null) {
    if ($userId) {
        $this->call('inventory:fix', ['user_id' => $userId]);
    } else {
        $this->call('inventory:fix');
    }
})->purpose('Fix inventory issues when resources are not added despite having free slots');

// Команда для тестирования переноса ресурсов из банка
Artisan::command('test:bank-transfer-simple {user_id} {resource_id} {quantity}', function ($userId, $resourceId, $quantity) {
    $this->call('test:bank-transfer', [
        'user_id' => $userId,
        'resource_id' => $resourceId,
        'quantity' => $quantity
    ]);
})->purpose('Тестирование переноса ресурсов из банка в инвентарь');

// Запускаем проверку и исправление проблем с инвентарем каждый час
Schedule::command('inventory:prevent-overflow')
    ->hourly()
    ->withoutOverlapping(10)
    ->appendOutputTo(storage_path('logs/inventory-overflow.log'))
    ->onOneServer();

// Запускаем проверку и исправление проблем с переполнением инвентаря каждый день в полночь
Schedule::command('inventory:fix-overflow')
    ->dailyAt('00:00')
    ->withoutOverlapping(30)
    ->appendOutputTo(storage_path('logs/inventory-fix.log'))
    ->onOneServer();

Artisan::command('queue:clear-redis-all', function () {
    // Получаем все ключи очередей
    $queueKeys = Redis::keys('queues:*');
    $laravelKeys = Redis::keys('laravel_database_*');
    $allKeys = array_merge($queueKeys, $laravelKeys);

    if (empty($allKeys)) {
        $this->warn('Очереди Redis уже пусты.');
        return;
    }

    $this->info('Найдено ключей для удаления: ' . count($allKeys));
    foreach ($allKeys as $key) {
        $type = Redis::type($key);
        if ($type === 'list') {
            $length = Redis::llen($key);
            $this->line("- {$key}: {$length} элементов");
        }
    }

    Redis::del(...$allKeys);
    $this->info('Все очереди Redis успешно очищены.');
})->purpose('Очистка всех очередей Redis');

Artisan::command('queue:clear-redis-force', function () {
    // Принудительная очистка без подтверждения
    $queueKeys = Redis::keys('queues:*');
    $laravelKeys = Redis::keys('laravel_database_*');
    $allKeys = array_merge($queueKeys, $laravelKeys);

    $totalDeleted = 0;
    if (!empty($allKeys)) {
        $totalDeleted = Redis::del(...$allKeys);
    }

    $this->info("Принудительно удалено {$totalDeleted} ключей очередей из Redis.");
})->purpose('Принудительная очистка всех очередей Redis');

Artisan::command('redis:flush-queues', function () {
    // Полная очистка всех ключей связанных с очередями
    $this->info('Очистка всех ключей очередей в Redis...');

    // Получаем все возможные ключи очередей
    $patterns = ['queues:*', 'laravel_database_*', '*queue*', 'horizon:*'];
    $allKeys = [];

    foreach ($patterns as $pattern) {
        $keys = Redis::keys($pattern);
        $allKeys = array_merge($allKeys, $keys);
    }

    $allKeys = array_unique($allKeys);

    if (!empty($allKeys)) {
        $this->info('Найдено ключей: ' . count($allKeys));
        foreach ($allKeys as $key) {
            $this->line("- {$key}");
        }

        Redis::del(...$allKeys);
        $this->info('Все ключи очередей удалены.');
    } else {
        $this->info('Ключи очередей не найдены.');
    }

    // Дополнительно очищаем специфичные ключи
    Redis::flushdb();
    $this->warn('ВНИМАНИЕ: Выполнена полная очистка текущей базы данных Redis!');
})->purpose('Полная очистка Redis от всех ключей очередей');

// Команда для отладки данных MP пользователя
Artisan::command('debug:user-mp {userId}', function ($userId) {
    $user = \App\Models\User::with('profile')->find($userId);
    if (!$user) {
        $this->error("Пользователь с ID {$userId} не найден");
        return;
    }

    $this->info("Данные пользователя {$user->name} (ID: {$userId}):");
    $this->info("current_mp: " . ($user->profile->current_mp ?? 'NULL'));
    $this->info("max_mp: " . $user->profile->max_mp);
    $this->info("last_regeneration_at: " . ($user->profile->last_regeneration_at ?? 'NULL'));
})->purpose('Отладка данных MP пользователя');

// Команда для исправления проблемы с current_mp пользователя
Artisan::command('fix:user-mp {userId}', function ($userId) {
    $user = \App\Models\User::with('profile')->find($userId);
    if (!$user) {
        $this->error("Пользователь с ID {$userId} не найден");
        return;
    }

    $this->info("Исправление данных пользователя {$user->name} (ID: {$userId}):");

    // Устанавливаем last_regeneration_at если он NULL
    if ($user->profile->last_regeneration_at === null) {
        $user->profile->last_regeneration_at = now();
        $this->info("Установлен last_regeneration_at: " . now());
    }

    // Проверяем и исправляем current_mp если он NULL
    if ($user->profile->current_mp === null) {
        $user->profile->current_mp = $user->profile->max_mp;
        $this->info("Установлен current_mp: " . $user->profile->max_mp);
    }

    $user->profile->save();
    $this->info("Данные пользователя исправлены!");
})->purpose('Исправление проблем с MP пользователя');

// Команда для проверки и исправления всех пользователей с NULL значениями
Artisan::command('fix:all-users-mp', function () {
    $this->info("Поиск пользователей с проблемами в данных...");

    $usersWithIssues = \App\Models\User::with('profile')
        ->whereHas('profile', function ($query) {
            $query->whereNull('current_mp')
                ->orWhereNull('current_hp')
                ->orWhereNull('last_regeneration_at');
        })
        ->get();

    if ($usersWithIssues->isEmpty()) {
        $this->info("Пользователи с проблемами не найдены!");
        return;
    }

    $this->info("Найдено пользователей с проблемами: " . $usersWithIssues->count());

    foreach ($usersWithIssues as $user) {
        $this->info("Исправление пользователя: {$user->name} (ID: {$user->id})");

        $needsSave = false;

        if ($user->profile->current_mp === null) {
            $user->profile->current_mp = $user->profile->max_mp;
            $needsSave = true;
            $this->line("  - Установлен current_mp: " . $user->profile->max_mp);
        }

        if ($user->profile->current_hp === null) {
            $user->profile->current_hp = $user->profile->max_hp;
            $needsSave = true;
            $this->line("  - Установлен current_hp: " . $user->profile->max_hp);
        }

        if ($user->profile->last_regeneration_at === null) {
            $user->profile->last_regeneration_at = now();
            $needsSave = true;
            $this->line("  - Установлен last_regeneration_at: " . now());
        }

        if ($needsSave) {
            $user->profile->save();
            $this->line("  - Данные сохранены");
        }
    }

    $this->info("Исправление завершено!");
})->purpose('Исправление проблем с MP/HP всех пользователей');

// === УДАЛЕНО: СТАРЫЕ КОМАНДЫ ТЕСТИРОВАНИЯ REDIS-СЕРВИСОВ ===
// Команды для тестирования старой Lua-системы больше не нужны
// Используйте новые команды: php artisan outpost:test-bot-system

// === УДАЛЕНО: КОМАНДЫ СТАРОЙ СИСТЕМЫ ЛЕЧЕНИЯ БОТОВ ===
// Все команды миграции, тестирования и диагностики старой Lua-системы удалены
// Новая система не требует миграции Redis-ключей и использует упрощенную архитектуру

// === СИСТЕМА БОТОВ РУДНИКОВ (НОВАЯ СИСТЕМА БЕЗ LUA) ===

// === СИСТЕМА БОТОВ РУДНИКОВ (ИСПРАВЛЕНО) ===
// ИСПРАВЛЕНО: Laravel не поддерживает секундные интервалы в стандартном планировщике
// Используем минутные интервалы с разными смещениями для имитации секундных интервалов

// Основная обработка ботов рудников каждую минуту (имитация 25-40 секундных интервалов)
Schedule::command('mine:process-bots --all')
    ->everyMinute()
    ->name('mine_bots_processing_primary')
    ->withoutOverlapping(45)  // Таймаут 45 секунд
    ->appendOutputTo(storage_path('logs/mine-bots-primary.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для обработки ботов рудников (primary): ' . $e->getMessage());
            return false;
        }
    });

// Дополнительная обработка ботов рудников каждые 2 минуты со смещением
Schedule::command('mine:process-bots --all')
    ->everyTwoMinutes()
    ->name('mine_bots_processing_secondary')
    ->withoutOverlapping(90)  // Таймаут 90 секунд
    ->appendOutputTo(storage_path('logs/mine-bots-secondary.log'))
    ->onOneServer()
    ->when(function () {
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для обработки ботов рудников (secondary): ' . $e->getMessage());
            return false;
        }
    });

// Обработка ботов рудников через очередь каждые 3 минуты
Schedule::command('mine:process-bots --all --queue')
    ->cron('*/3 * * * *')  // Каждые 3 минуты
    ->name('mine_bots_queue_processing')
    ->withoutOverlapping(120)  // Таймаут 2 минуты
    ->appendOutputTo(storage_path('logs/mine-bots-queue.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем доступность Redis и очередей
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для очередной обработки ботов рудников (queue): ' . $e->getMessage());
            return false;
        }
    });

// === КОМАНДЫ МОНИТОРИНГА ПЛАНИРОВЩИКА БОТОВ РУДНИКОВ ===

// Команда для мониторинга планировщика ботов рудников
Artisan::command('mine:monitor-scheduler {--duration=60} {--detailed}', function () {
    $duration = (int) $this->option('duration');
    $detailed = $this->option('detailed');

    $this->info('⛏️ МОНИТОРИНГ ПЛАНИРОВЩИКА БОТОВ РУДНИКОВ');
    $this->info('==========================================');
    $this->newLine();

    // Проверяем статус планировщика
    $this->info('🔍 Проверка статуса планировщика...');

    // Проверяем процессы
    if (PHP_OS_FAMILY === 'Windows') {
        $processes = shell_exec('wmic process where "name=\'php.exe\'" get commandline 2>nul | findstr schedule');
    } else {
        $processes = shell_exec('ps aux | grep "artisan schedule" | grep -v grep');
    }

    if (empty($processes)) {
        $this->error('❌ Планировщик НЕ ЗАПУЩЕН!');
        $this->warn('💡 Запустите планировщик: php artisan schedule:work');
        return 1;
    } else {
        $this->info('✅ Планировщик запущен');
        if ($detailed) {
            $this->line('   Процессы: ' . trim($processes));
        }
    }

    // Проверяем Redis
    try {
        Redis::ping();
        $this->info('✅ Redis доступен');
    } catch (\Exception $e) {
        $this->error('❌ Redis недоступен: ' . $e->getMessage());
        return 1;
    }

    // Проверяем блокировки
    $this->info('🔒 Проверка блокировок...');
    $mutexKeys = Redis::keys('framework:schedule-*');
    if (empty($mutexKeys)) {
        $this->info('✅ Блокировки отсутствуют');
    } else {
        $this->warn('⚠️ Найдено блокировок: ' . count($mutexKeys));
        if ($detailed) {
            foreach ($mutexKeys as $key) {
                $this->line('   - ' . $key);
            }
        }
    }

    // Мониторинг выполнения
    $this->info("📊 Мониторинг выполнения команд ({$duration} секунд)...");
    $this->newLine();

    $startTime = time();
    $executions = [];

    while ((time() - $startTime) < $duration) {
        // Проверяем логи
        $logFiles = [
            'mine-bots-primary.log',
            'mine-bots-secondary.log',
            'mine-bots-queue.log'
        ];

        foreach ($logFiles as $logFile) {
            $logPath = storage_path('logs/' . $logFile);
            if (file_exists($logPath)) {
                $lastLine = trim(shell_exec("tail -n 1 \"$logPath\" 2>/dev/null"));
                if (!empty($lastLine) && strpos($lastLine, date('Y-m-d H:i')) !== false) {
                    $executions[] = [
                        'time' => date('H:i:s'),
                        'command' => str_replace('.log', '', $logFile),
                        'status' => 'executed'
                    ];
                }
            }
        }

        sleep(5);
    }

    // Отчет
    $this->newLine();
    $this->info('📈 ОТЧЕТ О ВЫПОЛНЕНИИ:');
    if (empty($executions)) {
        $this->warn('⚠️ За период мониторинга команды не выполнялись');
    } else {
        $this->info('✅ Выполнено команд: ' . count($executions));
        if ($detailed) {
            foreach ($executions as $exec) {
                $this->line("   {$exec['time']} - {$exec['command']}");
            }
        }
    }

    return 0;
})->purpose('Мониторинг планировщика ботов рудников');

// Команда для диагностики планировщика
Artisan::command('mine:diagnose-scheduler {--fix}', function () {
    $fix = $this->option('fix');

    $this->info('🔧 ДИАГНОСТИКА ПЛАНИРОВЩИКА БОТОВ РУДНИКОВ');
    $this->info('============================================');
    $this->newLine();

    $issues = [];

    // 1. Проверка команд в планировщике
    $this->info('1️⃣ Проверка конфигурации планировщика...');
    $scheduleList = shell_exec('php artisan schedule:list 2>&1');

    if (strpos($scheduleList, 'mine:process-bots') === false) {
        $issues[] = 'Команды mine:process-bots не найдены в планировщике';
        $this->error('   ❌ Команды mine:process-bots не настроены');
    } else {
        $this->info('   ✅ Команды mine:process-bots настроены');

        // Проверяем блокировки
        if (strpos($scheduleList, 'Has Mutex') !== false) {
            $issues[] = 'Команды заблокированы мьютексом';
            $this->warn('   ⚠️ Обнаружены заблокированные команды');
        }
    }

    // 2. Проверка процессов
    $this->info('2️⃣ Проверка процессов...');
    if (PHP_OS_FAMILY === 'Windows') {
        $processes = shell_exec('wmic process where "name=\'php.exe\'" get commandline 2>nul | findstr schedule');
    } else {
        $processes = shell_exec('ps aux | grep "artisan schedule" | grep -v grep');
    }

    if (empty($processes)) {
        $issues[] = 'Планировщик не запущен';
        $this->error('   ❌ Планировщик не запущен');
    } else {
        $this->info('   ✅ Планировщик запущен');
    }

    // 3. Проверка Redis
    $this->info('3️⃣ Проверка Redis...');
    try {
        Redis::ping();
        $this->info('   ✅ Redis доступен');
    } catch (\Exception $e) {
        $issues[] = 'Redis недоступен: ' . $e->getMessage();
        $this->error('   ❌ Redis недоступен: ' . $e->getMessage());
    }

    // 4. Проверка логов
    $this->info('4️⃣ Проверка логов...');
    $logFiles = [
        'mine-bots-primary.log',
        'mine-bots-secondary.log',
        'mine-bots-queue.log'
    ];

    $recentLogs = 0;
    foreach ($logFiles as $logFile) {
        $logPath = storage_path('logs/' . $logFile);
        if (file_exists($logPath)) {
            $lastModified = filemtime($logPath);
            if ((time() - $lastModified) < 300) { // 5 минут
                $recentLogs++;
            }
        }
    }

    if ($recentLogs === 0) {
        $issues[] = 'Нет недавних записей в логах';
        $this->warn('   ⚠️ Нет недавних записей в логах (последние 5 минут)');
    } else {
        $this->info("   ✅ Найдено {$recentLogs} активных лог-файлов");
    }

    // Отчет и исправления
    $this->newLine();
    if (empty($issues)) {
        $this->info('🎉 Планировщик работает корректно!');
        return 0;
    } else {
        $this->error('❌ Обнаружены проблемы:');
        foreach ($issues as $issue) {
            $this->line('   • ' . $issue);
        }

        if ($fix) {
            $this->newLine();
            $this->info('🔧 Применение исправлений...');

            // Очистка блокировок
            if (in_array('Команды заблокированы мьютексом', $issues)) {
                Artisan::call('schedule:clear-cache');
                $this->info('   ✅ Блокировки очищены');
            }

            // Рекомендации по запуску планировщика
            if (in_array('Планировщик не запущен', $issues)) {
                $this->warn('   💡 Запустите планировщик: php artisan schedule:work');
            }
        }

        return 1;
    }
})->purpose('Диагностика планировщика ботов рудников');

// === СИСТЕМА ВРЕМЕННОГО ДОСТУПА ===

// Автоматическая очистка истекших ключей доступа (ежедневно в 2:00)
Schedule::command('access:cleanup-expired --days=7')
    ->dailyAt('02:00')
    ->withoutOverlapping(30)
    ->appendOutputTo(storage_path('logs/access-cleanup.log'))
    ->onOneServer();

// Команда для ручной очистки истекших ключей
Artisan::command('access:cleanup-manual {--days=30} {--force}', function () {
    $days = $this->option('days');
    $force = $this->option('force');

    $options = ['--days' => $days];
    if ($force) {
        $options['--force'] = true;
    }

    $this->call('access:cleanup-expired', $options);
})->purpose('Ручная очистка истекших ключей доступа');

// === МОНИТОРИНГ ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ ===

// МОНИТОРИНГ ПРОИЗВОДИТЕЛЬНОСТИ: Автоматическая диагностика каждые 5 минут
Schedule::command('outpost:monitor-performance --duration=60 --interval=10 --log')
    ->everyFiveMinutes()
    ->name('outpost_bots_performance_monitoring')
    ->withoutOverlapping(300)
    ->appendOutputTo(storage_path('logs/outpost-bots-performance.log'))
    ->onOneServer()
    ->when(function () {
        // Запускаем мониторинг только если система ботов активна
        $config = config('outpost_bots');
        return !empty($config) && ($config['monitoring']['enabled'] ?? true);
    });

// ДИАГНОСТИКА ПРОБЛЕМ: Автоматическая проверка каждые 15 минут
Schedule::command('outpost:diagnose-performance --check-config --check-indexes')
    ->everyFifteenMinutes()
    ->name('outpost_bots_diagnostics')
    ->withoutOverlapping(600)
    ->appendOutputTo(storage_path('logs/outpost-bots-diagnostics.log'))
    ->onOneServer()
    ->when(function () {
        $config = config('outpost_bots');
        return !empty($config) && ($config['diagnostics']['enabled'] ?? true);
    });

// ТЕСТИРОВАНИЕ ПРОИЗВОДИТЕЛЬНОСТИ: Еженедельно в воскресенье в 3:00
Schedule::command('outpost:test-performance --iterations=10 --compare --detailed')
    ->weeklyOn(0, '03:00')  // Воскресенье в 3:00
    ->name('outpost_bots_performance_testing')
    ->withoutOverlapping(1800)  // 30 минут таймаут
    ->appendOutputTo(storage_path('logs/outpost-bots-performance-test.log'))
    ->onOneServer()
    ->when(function () {
        $config = config('outpost_bots');
        return !empty($config) && ($config['testing']['enabled'] ?? false);
    });

// === ИСПРАВЛЕНИЕ КРИТИЧЕСКОЙ ПРОБЛЕМЫ: СИНХРОНИЗАЦИЯ ОНЛАЙН-СТАТУСА ===

// КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Автоматическая синхронизация онлайн-статуса игроков
// Исправляет проблему, когда боты не лечат игроков из-за неактуального поля is_online
Schedule::command('fix:online-status')
    ->everyFiveMinutes()  // Каждые 5 минут для своевременной синхронизации
    ->name('fix_online_status_sync')
    ->withoutOverlapping(300)  // Таймаут 5 минут
    ->appendOutputTo(storage_path('logs/online-status-fix.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем доступность Redis для корректной работы
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для синхронизации онлайн-статуса: ' . $e->getMessage());
            return false;
        }
    });

// НОВОЕ: Поддержание синхронизации онлайн статуса между Redis и БД
Schedule::command('maintain:online-status')
    ->everyFiveMinutes()  // Каждые 5 минут для поддержания актуальности
    ->name('maintain_online_status_sync')
    ->withoutOverlapping(300)  // Таймаут 5 минут
    ->appendOutputTo(storage_path('logs/maintain-online-status.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для поддержания онлайн-статуса: ' . $e->getMessage());
            return false;
        }
    });

// НОВОЕ: Автоматическое обновление статусов активности (is_idle, is_active)
Schedule::command('activity:update-statuses')
    ->everyMinute()  // Каждую минуту для актуальности индикаторов
    ->name('update_activity_statuses')
    ->withoutOverlapping(60)
    ->onOneServer()
    ->runInBackground();

// НОВАЯ СИСТЕМА: Автоматическая очистка неактивных пользователей из Redis
// Обеспечивает надежность онлайн системы и устраняет проблему "невидимок"
Schedule::job(new \App\Jobs\CleanupInactiveOnlineUsersJob())
    ->everyFiveMinutes()  // Каждые 5 минут для поддержания актуальности
    ->name('cleanup_inactive_online_users')
    ->withoutOverlapping(300)  // Таймаут 5 минут
    ->onOneServer()
    ->when(function () {
        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для очистки неактивных пользователей: ' . $e->getMessage());
            return false;
        }
    });

// УНИФИЦИРОВАННАЯ СИСТЕМА: Оптимизированная очистка неактивных онлайн пользователей
// Использует новый унифицированный сервис для точного соблюдения 15-минутного таймаута
Schedule::command('online:cleanup-optimized --force')
    ->everyFiveMinutes()  // Каждые 5 минут для стабильной работы
    ->name('unified_online_cleanup')
    ->withoutOverlapping(300);  // Таймаут 5 минут

// СИСТЕМА ПОДЗЕМЕЛИЙ: Автоматическая очистка завершенных инстансов подземелий
// Завершает активные инстансы с побежденными мобами и очищает старые данные
Schedule::command('dungeons:cleanup-instances --hours=6')
    ->everyTenMinutes()  // Каждые 10 минут для предотвращения накопления проблемных инстансов
    ->name('cleanup_dungeon_instances')
    ->withoutOverlapping(600)  // Таймаут 10 минут
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/dungeon-cleanup.log'))
    ->appendOutputTo(storage_path('logs/unified-online-cleanup.log'))
    ->when(function () {
        // Проверяем доступность Redis
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для унифицированной очистки онлайн пользователей: ' . $e->getMessage());
            return false;
        }
    });

// === НОВАЯ АРХИТЕКТУРА ОНЛАЙН СТАТУСА ===

// МОНИТОРИНГ ПРОИЗВОДИТЕЛЬНОСТИ: Автоматический мониторинг каждые 15 минут
Schedule::command('online:monitor-performance --duration=60 --interval=5 --log')
    ->everyFifteenMinutes()
    ->name('online_performance_monitoring')
    ->withoutOverlapping(300)
    ->appendOutputTo(storage_path('logs/online-performance-monitoring.log'))
    ->onOneServer()
    ->when(function () {
        // Запускаем мониторинг только если система активна
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для мониторинга производительности онлайн статуса: ' . $e->getMessage());
            return false;
        }
    });

// ПРОВЕРКА ЦЕЛОСТНОСТИ: Автоматическая проверка синхронизации каждые 30 минут
Schedule::command('test:online-status-sync --detailed')
    ->everyThirtyMinutes()
    ->name('online_status_integrity_check')
    ->withoutOverlapping(600)
    ->appendOutputTo(storage_path('logs/online-status-integrity.log'))
    ->onOneServer()
    ->when(function () {
        try {
            Redis::ping();
            return true;
        } catch (\Exception $e) {
            \Log::warning('Redis недоступен для проверки целостности онлайн статуса: ' . $e->getMessage());
            return false;
        }
    });

// НАГРУЗОЧНОЕ ТЕСТИРОВАНИЕ: Еженедельное тестирование производительности
Schedule::command('online:load-test --users=50 --duration=120 --operations-per-second=5 --cleanup')
    ->weeklyOn(0, '02:00')  // Воскресенье в 2:00
    ->name('online_status_load_testing')
    ->withoutOverlapping(1800)  // 30 минут таймаут
    ->appendOutputTo(storage_path('logs/online-status-load-test.log'))
    ->onOneServer()
    ->when(function () {
        // Запускаем нагрузочное тестирование только в продакшн среде
        $isProduction = config('app.env') === 'production';
        $isTestingEnabled = config('online_status.load_testing.enabled', false);

        return $isProduction && $isTestingEnabled;
    });

// АНАЛИЗ МИГРАЦИИ: Ежедневная проверка готовности к миграции
Schedule::command('online:migration-plan --analyze')
    ->dailyAt('01:00')
    ->name('online_migration_analysis')
    ->withoutOverlapping(600)
    ->appendOutputTo(storage_path('logs/online-migration-analysis.log'))
    ->onOneServer()
    ->when(function () {
        // Проверяем, нужна ли миграция
        try {
            return \Illuminate\Support\Facades\Schema::hasTable('online_status');
        } catch (\Exception $e) {
            return false;
        }
    });

// === ТЕСТИРОВАНИЕ СИСТЕМЫ СЛОТОВ ЭКИПИРОВКИ ===

// Команда для тестирования новой системы слотов экипировки
Artisan::command('test:equipment-slots {user_id=1}', function ($userId) {
    $this->call('test:equipment-slots', ['user_id' => $userId]);
})->purpose('Тестирование системы слотов экипировки с 10 стандартизированными слотами');

// Команда для тестирования системы блокировки локаций
Artisan::command('test:location-access {--user-id=} {--location=}', function () {
    $this->call('test:location-access', [
        '--user-id' => $this->option('user-id'),
        '--location' => $this->option('location')
    ]);
})->purpose('Тестирование системы блокировки доступа к локациям');

// Команда для быстрой проверки пользователей
Artisan::command('debug:users', function () {
    $this->info('=== СПИСОК ПОЛЬЗОВАТЕЛЕЙ ===');
    $users = \App\Models\User::select('id', 'name', 'email', 'role')->take(10)->get();

    foreach ($users as $user) {
        $this->info("ID: {$user->id}, Имя: {$user->name}, Роль: " . ($user->role ?? 'НЕ УСТАНОВЛЕНА'));
    }

    $this->newLine();
    $this->info('=== АДМИНЫ ===');
    $admins = \App\Models\User::where('role', 'admin')->select('id', 'name', 'email')->get();

    if ($admins->isEmpty()) {
        $this->warn('Админы не найдены!');
    } else {
        foreach ($admins as $admin) {
            $this->info("ID: {$admin->id}, Имя: {$admin->name}, Email: {$admin->email}");
        }
    }
})->purpose('Отладка пользователей и ролей');

// Команда для проверки локаций
Artisan::command('debug:locations', function () {
    $this->info('=== ЛОКАЦИИ В БАЗЕ ДАННЫХ ===');
    $locations = \App\Models\Location::select('id', 'name', 'min_level', 'max_level', 'gs_requirement')->get();

    if ($locations->isEmpty()) {
        $this->warn('Локации не найдены в базе данных!');
    } else {
        foreach ($locations as $location) {
            $this->info("ID: {$location->id}, Название: {$location->name}, Мин.уровень: {$location->min_level}, Макс.уровень: {$location->max_level}, GS: {$location->gs_requirement}");
        }
    }

    $this->newLine();
    $this->info('=== КЕШ ЛОКАЦИЙ ===');
    $locationService = app(\App\Services\LocationAccessService::class);

    // Попробуем получить доступ к приватному свойству через рефлексию
    $reflection = new \ReflectionClass($locationService);
    $cacheProperty = $reflection->getProperty('locationsCache');
    $cacheProperty->setAccessible(true);
    $cache = $cacheProperty->getValue($locationService);

    if ($cache === null) {
        $this->warn('Кеш локаций пуст (null)');
    } elseif (empty($cache)) {
        $this->warn('Кеш локаций пуст (empty array)');
    } else {
        $this->info('Локации в кеше:');
        foreach ($cache as $name => $data) {
            $this->info("  - {$name}");
        }
    }
})->purpose('Отладка локаций и кеша');

// Команда для просмотра последних логов
Artisan::command('debug:logs {--lines=20}', function () {
    $lines = $this->option('lines');
    $logFile = storage_path('logs/laravel.log');

    if (!file_exists($logFile)) {
        $this->warn('Файл логов не найден');
        return;
    }

    $this->info("=== ПОСЛЕДНИЕ {$lines} СТРОК ЛОГОВ ===");
    $command = PHP_OS_FAMILY === 'Windows' ? "powershell Get-Content {$logFile} -Tail {$lines}" : "tail -{$lines} {$logFile}";
    $output = shell_exec($command);
    $this->line($output ?: 'Логи пусты');
})->purpose('Просмотр последних логов Laravel');

// ========================================
// КОМАНДЫ СИСТЕМЫ ТЕХНИЧЕСКОГО ОБСЛУЖИВАНИЯ
// ========================================

// Быстрая команда для проверки статуса технических работ
Artisan::command('maintenance:status', function () {
    $this->info('=== СТАТУС СИСТЕМЫ ТЕХНИЧЕСКОГО ОБСЛУЖИВАНИЯ ===');

    // Используем ту же логику проверки, что и в middleware/командах
    $actualStatus = false;
    $message = 'Игра временно недоступна в связи с техническими работами. Приносим извинения за неудобства.';
    $completion = null;

    // Проверяем Redis, если настроен (приоритет)
    $redisStatus = null;
    if (config('maintenance.redis.connection')) {
        try {
            $connection = \Illuminate\Support\Facades\Redis::connection(config('maintenance.redis.connection'));
            $prefix = config('maintenance.redis.prefix');

            $redisEnabled = $connection->get($prefix . 'enabled');
            $redisMessage = $connection->get($prefix . 'message');
            $redisCompletion = $connection->get($prefix . 'estimated_completion');

            if ($redisEnabled !== null) {
                $actualStatus = (bool) $redisEnabled;
                $redisStatus = $actualStatus ? 'включен' : 'выключен';

                if ($redisMessage) {
                    $message = $redisMessage;
                }
                if ($redisCompletion) {
                    $completion = $redisCompletion;
                }
            } else {
                $redisStatus = 'не установлен';
            }
        } catch (\Exception $e) {
            $redisStatus = 'Ошибка: ' . $e->getMessage();
        }
    }

    // Если Redis не дал результат, проверяем кеш
    if ($redisStatus === null || $redisStatus === 'не установлен') {
        $cacheKey = config('maintenance.cache.keys.mode_status');
        $cachedStatus = \Illuminate\Support\Facades\Cache::get($cacheKey);

        if ($cachedStatus !== null) {
            $actualStatus = (bool) $cachedStatus;

            // Получаем сообщение и время завершения из кеша
            $cachedMessage = \Illuminate\Support\Facades\Cache::get('maintenance:message');
            $cachedCompletion = \Illuminate\Support\Facades\Cache::get('maintenance:estimated_completion');

            if ($cachedMessage) {
                $message = $cachedMessage;
            }
            if ($cachedCompletion) {
                $completion = $cachedCompletion;
            }
        } else {
            // Fallback на конфигурацию
            $actualStatus = config('maintenance.enabled', false);
            $message = config('maintenance.message', $message);
            $completion = config('maintenance.estimated_completion');
        }
    }

    $this->line("Режим технических работ: " . ($actualStatus ? '🔴 ВКЛЮЧЕН' : '🟢 ВЫКЛЮЧЕН'));
    $this->line("Сообщение: {$message}");

    if ($completion) {
        $this->line("Время завершения: {$completion}");
    } else {
        $this->line("Время завершения: не указано");
    }

    $this->newLine();
    $this->line("=== ТЕХНИЧЕСКАЯ ИНФОРМАЦИЯ ===");
    $this->line("Конфигурация: " . (config('maintenance.enabled', false) ? 'включен' : 'выключен'));

    // Проверяем кеш
    $cacheKey = config('maintenance.cache.keys.mode_status');
    $cachedStatus = \Illuminate\Support\Facades\Cache::get($cacheKey);
    $this->line("Кеш: " . ($cachedStatus !== null ? ($cachedStatus ? 'включен' : 'выключен') : 'не установлен'));

    if (config('maintenance.redis.connection')) {
        $this->line("Redis: " . ($redisStatus !== null ? ($redisStatus ? 'включен' : 'выключен') : 'не установлен'));
    } else {
        $this->line("Redis: не настроен");
    }

    // Показываем разрешенных пользователей
    $allowedUsers = config('maintenance.allowed_users', []);
    if (!empty($allowedUsers)) {
        $this->newLine();
        $this->line("=== РАЗРЕШЕННЫЕ ПОЛЬЗОВАТЕЛИ ===");

        // Разделяем ID и имена пользователей
        $userIds = [];
        $userNames = [];

        foreach ($allowedUsers as $userIdentifier) {
            if (is_numeric($userIdentifier)) {
                $userIds[] = (int) $userIdentifier;
            } else {
                $userNames[] = (string) $userIdentifier;
            }
        }

        // Получаем пользователей по ID и по именам отдельно
        $usersById = collect();
        $usersByName = collect();

        if (!empty($userIds)) {
            $usersById = \App\Models\User::whereIn('id', $userIds)->get(['id', 'name']);
        }

        if (!empty($userNames)) {
            $usersByName = \App\Models\User::whereIn('name', $userNames)->get(['id', 'name']);
        }

        // Отображаем всех пользователей
        foreach ($allowedUsers as $userIdentifier) {
            if (is_numeric($userIdentifier)) {
                $user = $usersById->firstWhere('id', (int) $userIdentifier);
                if ($user) {
                    $this->line("  ✅ ID: {$userIdentifier} - {$user->name}");
                } else {
                    $this->line("  ⚠️  ID: {$userIdentifier} - пользователь не найден");
                }
            } else {
                $user = $usersByName->firstWhere('name', (string) $userIdentifier);
                if ($user) {
                    $this->line("  ✅ Имя: {$userIdentifier} (ID: {$user->id})");
                } else {
                    $this->line("  ⚠️  Имя: {$userIdentifier} - пользователь не найден");
                }
            }
        }
    }

    // Показываем разрешенные IP
    $allowedIps = config('maintenance.allowed_ips', []);
    if (!empty($allowedIps)) {
        $this->newLine();
        $this->line("=== РАЗРЕШЕННЫЕ IP-АДРЕСА ===");
        foreach ($allowedIps as $ip) {
            $this->line("  ✅ {$ip}");
        }
    }

})->purpose('Проверка статуса системы технического обслуживания');

// Быстрая команда для очистки кеша технических работ
Artisan::command('maintenance:clear-cache', function () {
    $this->info('🔄 Очистка кеша системы технического обслуживания...');

    try {
        // Очищаем кеш Laravel
        $cacheKeys = config('maintenance.cache.keys', []);
        foreach ($cacheKeys as $key) {
            \Illuminate\Support\Facades\Cache::forget($key);
        }

        // Дополнительные ключи
        \Illuminate\Support\Facades\Cache::forget('maintenance:message');
        \Illuminate\Support\Facades\Cache::forget('maintenance:estimated_completion');

        $this->line('   ✅ Кеш Laravel очищен');

        // Очищаем Redis, если настроен
        if (config('maintenance.redis.connection')) {
            $connection = \Illuminate\Support\Facades\Redis::connection(config('maintenance.redis.connection'));
            $prefix = config('maintenance.redis.prefix');

            $keys = $connection->keys($prefix . '*');
            if (!empty($keys)) {
                $connection->del($keys);
                $this->line('   ✅ Кеш Redis очищен');
            } else {
                $this->line('   ℹ️  Кеш Redis уже пуст');
            }
        }

        $this->info('✅ Кеш системы технического обслуживания успешно очищен!');

    } catch (\Exception $e) {
        $this->error('❌ Ошибка при очистке кеша: ' . $e->getMessage());
    }
})->purpose('Очистка кеша системы технического обслуживания');

// НОВОЕ: Автоматическая очистка застрявших сущностей
Schedule::command('game:cleanup-stuck-entities')
    ->everyTenMinutes()  // Каждые 10 минут для предотвращения накопления проблем
    ->withoutOverlapping(300)  // Таймаут 5 минут
    ->appendOutputTo(storage_path('logs/cleanup-stuck-entities.log'))
    ->onOneServer()
    ->runInBackground();

// Команда для ручной очистки застрявших сущностей
Artisan::command('cleanup:stuck-entities-manual {--dry-run}', function () {
    $dryRun = $this->option('dry-run');
    $options = $dryRun ? ['--dry-run' => true] : [];
    
    $this->call('game:cleanup-stuck-entities', $options);
})->purpose('Ручная очистка застрявших сущностей с опцией тестового режима');

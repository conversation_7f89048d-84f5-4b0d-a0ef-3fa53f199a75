# 🚨 ДИАГНОСТИКА: Боты атакуют одних и тех же игроков

## 📋 ПОШАГОВАЯ ДИАГНОСТИКА

### 1. ПРОВЕРКА ДАННЫХ В БАЗЕ

#### Шаг 1: Проверить расположение ботов
```sql
-- Проверяем ботов в рудниках
SELECT 
    b.id,
    b.name,
    b.race,
    b.location,
    b.mine_location_id,
    ml.name as mine_sublocation_name,
    ml.parent_id,
    l.name as base_location_name
FROM bots b
LEFT JOIN mine_locations ml ON b.mine_location_id = ml.id
LEFT JOIN locations l ON ml.location_id = l.id
WHERE b.location LIKE '%рудник%' OR b.mine_location_id IS NOT NULL
ORDER BY b.location, b.mine_location_id;
```

#### Шаг 2: Проверить игроков в подлокациях
```sql
-- Проверяем где находятся игроки
SELECT 
    u.id,
    u.name,
    up.race,
    us.current_location,
    us.last_location_change
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN user_statistics us ON u.id = us.user_id
WHERE us.current_location LIKE '%рудник%'
ORDER BY us.current_location;
```

### 2. ОСНОВНЫЕ ПРОБЛЕМЫ И РЕШЕНИЯ

#### 🔴 ПРОБЛЕМА 1: Боты не привязаны к подлокациям
**Симптом**: В админке у ботов `location = "базовая_рудника"` вместо подлокации

**Диагностика**:
1. Проверить `app/Http/Controllers/Admin/BotController.php` строка 286
2. Убедиться, что `$botData['location'] = $mineLocation->name;` работает

**Исправление**:
```php
// В методе handleMineLocationAssignment должно быть:
$botData['location'] = $mineLocation->name; // ИМЯ ПОДЛОКАЦИИ
$botData['mine_location_id'] = $mineLocation->id;
```

#### 🔴 ПРОБЛЕМА 2: Существующие боты в базовых локациях
**Симптом**: Старые боты остались в базовых локациях

**Исправление**:
```sql
-- Обновить существующих ботов для привязки к подлокациям
UPDATE bots b
JOIN mine_locations ml ON b.mine_location_id = ml.id
SET b.location = ml.name
WHERE b.mine_location_id IS NOT NULL 
  AND b.location != ml.name;
```

#### 🔴 ПРОБЛЕМА 3: Логика выбора цели не работает
**Симптом**: Боты выбирают одних и тех же игроков

**Диагностика**:
Проверить `app/Services/BotBehaviorService.php` строки 447-453:
```php
// Должно быть:
$targetLocation = $bot->location; // Точная подлокация
$players = User::whereHas('statistics', function ($q) use ($targetLocation) {
    $q->where('current_location', $targetLocation);
})->where('is_online', true)->with('profile')->get();
```

### 3. КОМАНДЫ ДЛЯ ПРОВЕРКИ

#### Команда 1: Проверка распределения ботов
```bash
php artisan tinker
```
```php
// В tinker:
$bots = \App\Models\Bot::where('location', 'LIKE', '%рудник%')->get();
foreach($bots as $bot) {
    echo "Бот: {$bot->name}, Локация: {$bot->location}, Mine ID: {$bot->mine_location_id}\n";
}
```

#### Команда 2: Проверка игроков в подлокациях
```bash
php artisan tinker
```
```php
// В tinker:
$users = \App\Models\User::whereHas('statistics', function($q) {
    $q->where('current_location', 'LIKE', '%рудник%');
})->with('statistics', 'profile')->get();

foreach($users as $user) {
    echo "Игрок: {$user->name}, Локация: {$user->statistics->current_location}, Раса: {$user->profile->race}\n";
}
```

### 4. ИСПРАВЛЕНИЯ ПО ФАЙЛАМ

#### Файл: `app/Http/Controllers/Admin/BotController.php`
**Проверить строку 286**:
```php
// Должно быть:
$botData['location'] = $mineLocation->name; // ИМЯ ПОДЛОКАЦИИ, НЕ БАЗОВОЙ!
```

#### Файл: `app/Services/BotBehaviorService.php`
**Проверить строку 448**:
```php
// Должно быть:
$targetLocation = $bot->location; // Используем точную локацию бота
```

**Проверить строку 86**:
```php
// Должно быть:
if (!$this->areInSameMineSubLocation($bot, $player)) {
    return false; // Не атакуем из других подлокаций
}
```

### 5. БЫСТРАЯ ПРОВЕРКА ИСПРАВЛЕНИЯ

#### Создать тестовую команду:
```bash
php artisan make:command DiagnoseMineBotsLocation
```

#### Содержимое команды:
```php
<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Bot;
use App\Models\User;

class DiagnoseMineBotsLocation extends Command
{
    protected $signature = 'diagnose:mine-bots-location';
    protected $description = 'Диагностика локаций ботов в рудниках';

    public function handle()
    {
        $this->info('🔍 Диагностика локаций ботов в рудниках');
        
        // Проверка ботов
        $bots = Bot::where('location', 'LIKE', '%рудник%')->get();
        $this->info("Найдено ботов в рудниках: " . $bots->count());
        
        $locationGroups = [];
        foreach($bots as $bot) {
            $locationGroups[$bot->location][] = $bot;
        }
        
        foreach($locationGroups as $location => $locationBots) {
            $this->warn("📍 Локация: {$location}");
            foreach($locationBots as $bot) {
                $this->line("  🤖 {$bot->name} ({$bot->race}) - Mine ID: {$bot->mine_location_id}");
            }
        }
        
        // Проверка игроков
        $users = User::whereHas('statistics', function($q) {
            $q->where('current_location', 'LIKE', '%рудник%');
        })->with('statistics', 'profile')->get();
        
        $this->info("Найдено игроков в рудниках: " . $users->count());
        
        $playerGroups = [];
        foreach($users as $user) {
            $playerGroups[$user->statistics->current_location][] = $user;
        }
        
        foreach($playerGroups as $location => $locationPlayers) {
            $this->warn("📍 Локация: {$location}");
            foreach($locationPlayers as $player) {
                $this->line("  👤 {$player->name} ({$player->profile->race})");
            }
        }
        
        return 0;
    }
}
```

### 6. СРОЧНЫЕ ДЕЙСТВИЯ

1. **Сначала запустите диагностику**:
```bash
php artisan diagnose:mine-bots-location
```

2. **Если боты в базовых локациях - исправьте**:
```sql
UPDATE bots b
JOIN mine_locations ml ON b.mine_location_id = ml.id
SET b.location = ml.name
WHERE b.mine_location_id IS NOT NULL 
  AND b.location != ml.name;
```

3. **Проверьте метод в BotController**:
Строка 286 в `app/Http/Controllers/Admin/BotController.php` должна быть:
```php
$botData['location'] = $mineLocation->name; // НЕ baseLocation->name!
```

4. **Протестируйте**:
```bash
php artisan test:mine-bots-attack-fix --verbose
```

### 7. ОЖИДАЕМЫЙ РЕЗУЛЬТАТ

После исправления:
- Боты должны быть привязаны к конкретным подлокациям
- В админке должны отображаться названия подлокаций
- Боты должны атаковать только игроков в своей подлокации
- Разные подлокации должны иметь разных ботов

### 8. ЛОГИ ДЛЯ ОТСЛЕЖИВАНИЯ

```bash
tail -f storage/logs/laravel.log | grep -E "(Принудительная атака в Руднике|Проверка подлокации рудника)"
```

## 🎯 ГЛАВНОЕ ПРАВИЛО

**Бот должен атаковать ТОЛЬКО игроков в ТОЧНО ТАКОЙ ЖЕ подлокации!**

Если `bot.location = "Рудник_Подлокация_1"`, то он атакует только игроков с `user.statistics.current_location = "Рудник_Подлокация_1"`.
@php use Illuminate\Support\Facades\Auth; @endphp {{-- Используем фасад Auth для проверки роли --}}
<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- Устанавливаем заголовок страницы --}}
    <title>Управление ботами - Админка</title>

    {{-- Подключаем Vite для CSS и JS --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

{{-- Устанавливаем темный фон и основной шрифт для тела страницы (стиль из sample.blade.php) --}}

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">

    {{-- Основной контейнер страницы админки, стилизованный под sample.blade.php --}}
    {{-- Используем max-w-full вместо max-w-4xl для отображения на весь экран --}}
    <div
        class="container max-w-full mx-auto px-2 py-8 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden my-5">

        {{-- Основной контент --}}
        <div class="flex-1 flex flex-col overflow-hidden">

            {{-- Главная область контента --}}
            <main class="flex-1 overflow-x-hidden overflow-y-auto"> {{-- Убрали bg-gray-200 --}}
                <div class="container mx-auto px-6 py-8">
                    {{-- Заголовок страницы, стилизованный под sample.blade.php --}}
                    <h3
                        class="block text-center text-white py-1.5 px-8 rounded-md shadow-lg bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] text-xl font-semibold mb-4">
                        Управление ботами
                    </h3>

                    {{-- Хлебные крошки, стилизованные под темную тему --}}
                    <div class="mt-4 mb-6 text-sm text-[#d3c6a6]">
                        {{-- Ссылка на главную страницу админки --}}
                        <a href="{{ route('admin.dashboard') }}" class="text-[#e5b769] hover:text-[#f0d89e]">Админка</a>
                        <span class="mx-2 text-[#a6925e]">/</span> {{-- Разделитель --}}
                        <span class="text-[#f5f5f5]">Управление ботами</span> {{-- Текущая страница --}}
                    </div>

                    {{-- Отображение сообщений --}}
                    @if (session('success'))
                        <div class="mt-4 bg-[#284228] border border-[#7cfc00] text-[#7cfc00] px-4 py-3 rounded relative mb-4"
                            role="alert">
                            <strong class="font-bold">Успех!</strong>
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mt-4 bg-[#5a2626] border border-[#ff6666] text-[#ff6666] px-4 py-3 rounded relative mb-4"
                            role="alert">
                            <strong class="font-bold">Ошибка!</strong>
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    @if (session('info'))
                        <div class="mt-4 bg-[#2a3a5a] border border-[#6699ff] text-[#6699ff] px-4 py-3 rounded relative mb-4"
                            role="alert">
                            <strong class="font-bold">Информация:</strong>
                            <span class="block sm:inline">{{ session('info') }}</span>
                        </div>
                    @endif

                    {{-- Кнопки управления ботами --}}
                    <div class="mt-6 mb-4 flex flex-wrap gap-4 items-center">
                        {{-- Кнопка создания нового бота --}}
                        <a href="{{ route('admin.bots.create') }}"
                            class="inline-block bg-[#c4a76d] hover:bg-[#d4b781] text-[#2f2d2b] font-bold py-2 px-4 rounded shadow-lg transition duration-300">
                            ➕ Создать нового бота
                        </a>

                        {{-- Кнопка удаления всех ботов --}}
                        <button type="button" onclick="openDeleteAllModal()"
                            class="inline-block bg-[#a65e5e] hover:bg-[#b16b6b] text-white font-bold py-2 px-4 rounded shadow-lg transition duration-300">
                            🗑️ Удалить всех ботов
                        </button>

                        {{-- Информация о количестве ботов --}}
                        <div class="text-[#a6925e] text-sm font-medium">
                            Всего ботов: {{ $bots->total() }}
                        </div>
                    </div>

                    {{-- Форма фильтрации по локации --}}
                    <div class="p-4 bg-[#3b3a33] border border-[#514b3c] rounded mb-6">
                        <form action="{{ route('admin.bots.index') }}" method="GET" class="flex items-center space-x-4">
                            <div>
                                <label for="location" class="block text-[#d9d3b8] font-semibold mb-1 text-sm">Фильтр по
                                    локации:</label>
                                <select name="location" id="location"
                                    class="bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769] text-sm">
                                    <option value="">Все локации</option>
                                    @isset($locations)
                                        <optgroup label="🏛️ Основные локации">
                                            @foreach($locations as $location)
                                                {{-- Используем имя локации, так как в модели Bot поле location - строка --}}
                                                <option value="{{ $location->name }}" {{ $selectedLocation == $location->name ? 'selected' : '' }}>
                                                    {{ $location->name }}
                                                </option>
                                            @endforeach
                                        </optgroup>
                                    @endisset
                                    @isset($mineLocations)
                                        @if($mineLocations->count() > 0)
                                            <optgroup label="🏭 Подлокации рудников">
                                                @foreach($mineLocations as $mineLocation)
                                                    <option value="{{ $mineLocation->name }}" {{ $selectedLocation == $mineLocation->name ? 'selected' : '' }}>
                                                        {{ $mineLocation->name }}
                                                        @if($mineLocation->baseLocation)
                                                            ({{ $mineLocation->baseLocation->name }})
                                                        @endif
                                                    </option>
                                                @endforeach
                                            </optgroup>
                                        @endif
                                    @endisset
                                </select>
                            </div>
                            <div class="flex items-end space-x-2 pt-5"> {{-- pt-5 для выравнивания кнопок --}}
                                <button type="submit"
                                    class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-2 px-4 rounded text-sm">
                                    Применить
                                </button>
                                <a href="{{ route('admin.bots.index') }}"
                                    class="bg-[#a65e5e] hover:bg-[#b16b6b] text-white py-2 px-4 rounded text-sm">
                                    Сбросить
                                </a>
                            </div>
                        </form>
                    </div>

                    {{-- Таблица со списком ботов (стилизована под темную тему) --}}
                    {{-- Контейнер таблицы: темный фон, тень, скругление, отступы, горизонтальная прокрутка --}}
                    <div class="bg-[#211f1a] border border-[#514b3c] shadow-inner rounded my-6 overflow-x-auto">
                        {{-- Сама таблица, ширина на весь экран, темные разделители строк --}}
                        <table class="w-full divide-y divide-[#514b3c]">
                            {{-- Шапка таблицы с темным фоном --}}
                            <thead class="bg-[#38352c]">
                                <tr>
                                    {{-- Заголовок колонки ID --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        ID
                                    </th>
                                    {{-- Заголовок колонки Имя --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Имя
                                    </th>
                                    {{-- Заголовок колонки Раса --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Раса
                                    </th>
                                    {{-- Заголовок колонки Класс --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Класс
                                    </th>
                                    {{-- Заголовок колонки Уровень --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Уровень
                                    </th>
                                    {{-- Заголовок колонки HP --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        HP / Макс.
                                    </th>
                                    {{-- Заголовок колонки MP --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        MP / Макс.
                                    </th>
                                    {{-- Заголовок колонки Локация --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Локация
                                    </th>
                                    {{-- Заголовок колонки Активен --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-center text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Активен
                                    </th>
                                    {{-- Заголовок колонки Возрождение --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Возрождение
                                    </th>
                                    {{-- Заголовок колонки Источник --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-center text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Источник
                                    </th>
                                    {{-- Заголовок колонки Действия --}}
                                    <th scope="col"
                                        class="px-6 py-3 text-right text-xs font-medium text-[#a6925e] uppercase tracking-wider">
                                        Действия
                                    </th>
                                </tr>
                            </thead>
                            {{-- Тело таблицы с темным фоном и темными разделителями --}}
                            <tbody class="bg-[#2f2d2b] divide-y divide-[#514b3c]">
                                {{-- Директива Blade для итерации по коллекции $bots. Если коллекция пуста, выполняется
                                блок @empty --}}
                                @forelse ($bots as $bot)
                                    {{-- Строка для одного бота --}}
                                    <tr>
                                        {{-- Ячейка с ID бота --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d3c6a6]">{{ $bot->id }}</td>
                                        {{-- Ячейка с именем бота --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#f5f5f5]">{{ $bot->name }}</td>
                                        {{-- Ячейка с расой бота (первая буква заглавная) --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d3c6a6]">
                                            {{ ucfirst($bot->race) }}
                                        </td>
                                        {{-- Ячейка с классом бота (первая буква заглавная) --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d3c6a6]">
                                            {{ ucfirst($bot->class) }}
                                        </td>
                                        {{-- Ячейка с уровнем бота --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d3c6a6]">{{ $bot->level }}
                                        </td>
                                        {{-- Ячейка с текущим и максимальным HP --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d3c6a6]">{{ $bot->hp }} /
                                            {{ $bot->max_hp }}
                                        </td>
                                        {{-- Ячейка с текущим и максимальным MP --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d3c6a6]">{{ $bot->mp }} /
                                            {{ $bot->max_mp }}
                                        </td>
                                        {{-- Ячейка с локацией бота --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#d3c6a6]">{{ $bot->location }}
                                        </td>
                                        {{-- Ячейка со статусом активности --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                            {{-- Отображение плашки 'Да' или 'Нет' с разным цветом фона и текста --}}
                                            <span
                                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $bot->is_active ? 'bg-[#284228] text-[#7cfc00]' : 'bg-[#5a2626] text-[#ff6666]' }}">
                                                {{-- Тернарный оператор для вывода текста --}}
                                                {{ $bot->is_active ? 'Да' : 'Нет' }}
                                            </span>
                                        </td>
                                        {{-- Ячейка с временем возрождения --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-[#a6925e]">
                                            {{-- Форматирование даты и времени возрождения, если оно установлено, иначе
                                            вывод '-' --}}
                                            {{ $bot->respawn_time ? $bot->respawn_time->format('d.m.Y H:i') : '-' }}
                                        </td>
                                        {{-- Ячейка с источником создания --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                            <span
                                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $bot->created_by_admin ? 'bg-[#4a3728] text-[#e5b769]' : 'bg-[#2a3a2a] text-[#7cfc00]' }}">
                                                {{ $bot->created_by_admin ? 'Админ' : 'Авто' }}
                                            </span>
                                        </td>
                                        {{-- Ячейка с действиями (редактирование, удаление) --}}
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            {{-- Кнопка воскрешения только для неактивных ботов --}}
                                            @if (!$bot->is_active)
                                                <form action="{{ route('admin.bots.respawn', $bot->id) }}" method="POST"
                                                    class="inline-block">
                                                    @csrf
                                                    <button type="submit"
                                                        class="text-green-500 hover:text-green-400 mr-3">Воскресить</button>
                                                </form>
                                            @endif
                                            {{-- Существующие кнопки --}}
                                            <a href="{{ route('admin.bots.edit', $bot->id) }}"
                                                class="text-[#e5b769] hover:text-[#f0d89e] mr-3">Ред.</a>
                                            <form action="{{ route('admin.bots.destroy', $bot->id) }}" method="POST"
                                                class="inline-block"
                                                onsubmit="return confirm('Вы уверены, что хотите удалить бота \'{{ $bot->name }}\'? Это действие необратимо!');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    class="text-red-500 hover:text-red-400">Удалить</button>
                                            </form>
                                        </td>
                                    </tr>
                                    {{-- Этот блок выполняется, если переменная $bots пуста --}}
                                @empty
                                    <tr>
                                        {{-- Ячейка, занимающая все 12 колонок, для сообщения об отсутствии данных --}}
                                        <td colspan="12"
                                            class="px-6 py-4 whitespace-nowrap text-sm text-[#a6925e] text-center">
                                            В базе данных пока нет ботов.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    {{-- Блок пагинации (стилизован под темную тему) --}}
                    <div class="mt-4">
                        {{-- Отображение ссылок пагинации, сгенерированных Laravel (метод ->links()) --}}
                        {{-- Предполагается, что стандартные вьюшки пагинации будут адаптированы или кастомизированы для
                        темной темы --}}
                        {{ $bots->links() }}
                        {{-- Необходимо убедиться, что в контроллере используется $bots = Bot::paginate(); --}}
                        {{-- Для полной стилизации пагинации может потребоваться публикация и изменение файлов пагинации
                        Laravel: `php artisan vendor:publish --tag=laravel-pagination` --}}
                    </div>
                </div> {{-- container mx-auto px-6 py-8 --}}
            </main>
        </div> {{-- flex-1 flex flex-col overflow-hidden --}}
    </div> {{-- container max-w-full --}}

    {{-- Модальное окно подтверждения удаления всех ботов --}}
    <div id="deleteAllModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg p-6 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="text-6xl mb-4">⚠️</div>
                <h3 class="text-xl font-bold text-[#e5b769] mb-4">КРИТИЧЕСКОЕ ДЕЙСТВИЕ</h3>
                <p class="text-[#d9d3b8] mb-4">
                    Вы собираетесь удалить <strong class="text-[#ff6666]">ВСЕХ {{ $bots->total() }} ботов</strong> из
                    базы данных!
                </p>
                <p class="text-[#ff6666] mb-6 font-semibold">
                    Это действие НЕОБРАТИМО и удалит всех ботов без возможности восстановления!
                </p>

                <div class="mb-6">
                    <label for="confirmText" class="block text-[#d9d3b8] mb-2">
                        Для подтверждения введите: <strong class="text-[#e5b769]">УДАЛИТЬ ВСЕХ БОТОВ</strong>
                    </label>
                    <input type="text" id="confirmText"
                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                        placeholder="Введите текст подтверждения">
                </div>

                <div class="flex gap-4 justify-center">
                    <button type="button" onclick="closeDeleteAllModal()"
                        class="bg-[#5e5e5e] hover:bg-[#707070] text-white py-2 px-4 rounded transition duration-300">
                        Отмена
                    </button>
                    <form action="{{ route('admin.bots.destroy-all') }}" method="POST" class="inline-block">
                        @csrf
                        @method('DELETE')
                        <button type="submit" id="confirmDeleteBtn" disabled
                            class="bg-[#a65e5e] hover:bg-[#b16b6b] text-white py-2 px-4 rounded transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
                            🗑️ Удалить всех ботов
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {{-- JavaScript для модального окна --}}
    <script>
        function openDeleteAllModal() {
            document.getElementById('deleteAllModal').classList.remove('hidden');
            document.getElementById('confirmText').focus();
        }

        function closeDeleteAllModal() {
            document.getElementById('deleteAllModal').classList.add('hidden');
            document.getElementById('confirmText').value = '';
            document.getElementById('confirmDeleteBtn').disabled = true;
        }

        // Проверка введенного текста
        document.getElementById('confirmText').addEventListener('input', function () {
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            const requiredText = 'УДАЛИТЬ ВСЕХ БОТОВ';

            if (this.value.trim().toUpperCase() === requiredText) {
                confirmBtn.disabled = false;
                confirmBtn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
            } else {
                confirmBtn.disabled = true;
                confirmBtn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
            }
        });

        // Закрытие модального окна по клику вне его
        document.getElementById('deleteAllModal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeDeleteAllModal();
            }
        });

        // Закрытие модального окна по нажатию Escape
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
                closeDeleteAllModal();
            }
        });
    </script>

</body>

</html>
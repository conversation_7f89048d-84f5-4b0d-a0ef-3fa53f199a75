<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_profiles', function (Blueprint $table) {
            // Добавляем поля для отслеживания поражений
            $table->boolean('is_defeated')->default(false)->comment('Побежден ли игрок');
            $table->string('defeated_by_type')->nullable()->comment('Тип убийцы (player, bot, mob)');
            $table->bigInteger('defeated_by_id')->nullable()->comment('ID убийцы');
            $table->timestamp('defeated_at')->nullable()->comment('Время поражения');
            
            // Добавляем индексы для оптимизации
            $table->index('is_defeated', 'idx_user_profiles_is_defeated');
            $table->index(['defeated_by_type', 'defeated_by_id'], 'idx_user_profiles_defeated_by');
            $table->index('defeated_at', 'idx_user_profiles_defeated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_profiles', function (Blueprint $table) {
            // Удаляем индексы
            $table->dropIndex('idx_user_profiles_is_defeated');
            $table->dropIndex('idx_user_profiles_defeated_by');
            $table->dropIndex('idx_user_profiles_defeated_at');
            
            // Удаляем поля
            $table->dropColumn(['is_defeated', 'defeated_by_type', 'defeated_by_id', 'defeated_at']);
        });
    }
};
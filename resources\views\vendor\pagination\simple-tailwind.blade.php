@if ($paginator->hasPages() || $paginator->total() > $paginator->perPage())
    <div class="mt-6 mb-4 flex justify-center items-center">
        {{-- Более симметричный контейнер с выпуклым эффектом --}}
        <div class="relative">
            {{-- Основной контейнер пагинации с эффектом выпуклости --}}
            <div
                class="relative bg-gradient-to-b from-[#312e25] to-[#1a1814] rounded-lg px-2 py-2 sm:px-4 sm:py-3 md:px-5 md:py-3 
                    border border-[#a6925e] shadow-md">

                <div class="flex items-center justify-center space-x-1 sm:space-x-2">
                    {{-- Предыдущая страница --}}
                    @if ($paginator->onFirstPage())
                        <button disabled
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                    @else
                        <a href="{{ $paginator->previousPageUrl() }}"
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#a6925e] bg-gradient-to-b from-[#38352c] to-[#28241c] text-[#e5b769] shadow-lg hover:from-[#413c33] hover:to-[#2f2b23] transition-all duration-300 relative z-10">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </a>
                    @endif

                    {{-- Номера страниц --}}
                    @php
                        $current = $paginator->currentPage();
                        $last = $paginator->lastPage();
                        
                        // Показываем текущую страницу + соседние + последнюю
                        $showPages = collect();
                        
                        // Всегда показываем первую страницу
                        $showPages->push(1);
                        
                        // Добавляем предыдущую страницу (если есть и она не 1)
                        if ($current > 1 && $current - 1 != 1) {
                            $showPages->push($current - 1);
                        }
                        
                        // Добавляем текущую страницу (если она не 1)
                        if ($current != 1) {
                            $showPages->push($current);
                        }
                        
                        // Добавляем следующую страницу (если есть и она не последняя)
                        if ($current < $last && $current + 1 != $last) {
                            $showPages->push($current + 1);
                        }
                        
                        // Добавляем последнюю страницу (если она не равна текущей или первой)
                        if ($last > 1 && $last != $current) {
                            $showPages->push($last);
                        }
                        
                        // Сортируем и убираем дубли
                        $showPages = $showPages->unique()->sort()->values();
                    @endphp
                    
                    {{-- Отображаем страницы --}}
                    @foreach ($showPages as $index => $page)
                        {{-- Добавляем многоточие если есть пропуск --}}
                        @if ($index > 0 && $page - $showPages->get($index - 1) > 1)
                            <span class="w-6 h-8 sm:w-8 sm:h-10 flex items-center justify-center text-[#9a9483] text-xs sm:text-sm">...</span>
                        @endif
                        
                        {{-- Кнопка страницы --}}
                        @if ($page == $current)
                            <span class="w-6 h-8 sm:w-8 sm:h-10 md:w-10 md:h-10 flex items-center justify-center rounded border border-[#c1a96e] bg-gradient-to-b from-[#e5b769] to-[#c4a76d] text-[#1a1814] shadow-lg font-bold text-xs sm:text-sm">
                                {{ $page }}
                            </span>
                        @else
                            <a href="{{ $paginator->url($page) }}"
                                class="w-6 h-8 sm:w-8 sm:h-10 md:w-10 md:h-10 flex items-center justify-center rounded border border-[#a6925e] bg-gradient-to-b from-[#38352c] to-[#28241c] text-[#e5b769] shadow-lg hover:from-[#413c33] hover:to-[#2f2b23] transition-all duration-300 text-xs sm:text-sm font-medium relative z-10">
                                {{ $page }}
                            </a>
                        @endif
                    @endforeach

                    {{-- Следующая страница --}}
                    @if ($paginator->hasMorePages())
                        <a href="{{ $paginator->nextPageUrl() }}"
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#a6925e] bg-gradient-to-b from-[#38352c] to-[#28241c] text-[#e5b769] shadow-lg hover:from-[#413c33] hover:to-[#2f2b23] transition-all duration-300 relative z-10">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    @else
                        <button disabled
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- Информация о текущей странице - более четкая и лаконичная --}}
    <div class="text-center mb-4">
        <div class="inline-block px-2 py-1 sm:px-3 sm:py-1 bg-[#1a1814] rounded border-t border-b border-[#514b3c]">
            <span class="text-[#9a9483] text-xs sm:text-sm">Страница {{ $paginator->currentPage() }} из
                {{ $paginator->lastPage() }}</span>
        </div>
        
        {{-- Отладочная информация --}}
        @if(config('app.debug'))
            <div class="mt-2 text-xs text-[#9a9483] border border-[#514b3c] p-1 sm:p-2 rounded">
                <div>Всего: {{ $paginator->total() }} | На странице: {{ $paginator->perPage() }} | Показано: {{ $paginator->count() }}</div>
                <div>Текущая: {{ $paginator->currentPage() }} | Последняя: {{ $paginator->lastPage() }}</div>
                <div>hasPages: {{ $paginator->hasPages() ? 'Да' : 'Нет' }} | hasMorePages: {{ $paginator->hasMorePages() ? 'Да' : 'Нет' }}</div>
                <div>nextUrl: {{ $paginator->nextPageUrl() ?? 'null' }}</div>
                <div>Ожидаемых страниц: {{ ceil($paginator->total() / $paginator->perPage()) }}</div>
                @php
                    $expectedNext = $paginator->currentPage() < $paginator->lastPage();
                @endphp
                <div>Должна быть следующая: {{ $expectedNext ? 'Да' : 'Нет' }}</div>
                <div class="mt-2">
                    <div>Тест ссылки: <a href="{{ $paginator->url(1) }}" class="text-[#e5b769] underline">Страница 1</a></div>
                    @if($paginator->hasMorePages())
                        <div>Тест следующей: <a href="{{ $paginator->nextPageUrl() }}" class="text-[#e5b769] underline">Следующая</a></div>
                    @endif
                </div>
            </div>
        @endif
    </div>
@endif
<?php

echo "=== ТЕСТ ИСПРАВЛЕНИЯ ПЕРЕМЕННОЙ ===\n\n";

// Проверяем, что файл UserLocationService.php не содержит ошибок синтаксиса
$filePath = __DIR__ . '/app/Services/battle/UserLocationService.php';

if (!file_exists($filePath)) {
    echo "❌ Файл не найден: $filePath\n";
    exit(1);
}

echo "1. Проверка синтаксиса UserLocationService.php:\n";

// Проверка синтаксиса PHP
$output = [];
$returnCode = 0;
exec("php -l \"$filePath\" 2>&1", $output, $returnCode);

if ($returnCode === 0) {
    echo "✅ Синтаксис корректен\n";
} else {
    echo "❌ Ошибка синтаксиса:\n";
    foreach ($output as $line) {
        echo "   $line\n";
    }
    exit(1);
}

echo "\n2. Проверка переменной \$normalizedLocation:\n";

$content = file_get_contents($filePath);

// Ищем проблемную переменную
if (strpos($content, '$normalizedLocation') !== false) {
    // Ищем использование переменной в методе resetUserTargetsOnLocationChange
    $lines = explode("\n", $content);
    $inMethod = false;
    $lineNumber = 0;
    
    foreach ($lines as $line) {
        $lineNumber++;
        
        if (strpos($line, 'function resetUserTargetsOnLocationChange') !== false) {
            $inMethod = true;
            continue;
        }
        
        if ($inMethod && strpos($line, '$normalizedLocation') !== false) {
            echo "❌ Найдена проблемная переменная на строке $lineNumber:\n";
            echo "   " . trim($line) . "\n";
            exit(1);
        }
        
        if ($inMethod && strpos($line, 'function ') !== false && strpos($line, 'resetUserTargetsOnLocationChange') === false) {
            $inMethod = false;
        }
    }
    
    echo "✅ Переменная \$normalizedLocation используется корректно\n";
} else {
    echo "✅ Переменная \$normalizedLocation не найдена (это может быть нормально)\n";
}

echo "\n3. Проверка использования \$newLocation:\n";

if (strpos($content, 'resetLastAttackerForOtherPlayers($user, $newLocation)') !== false) {
    echo "✅ Найден корректный вызов: resetLastAttackerForOtherPlayers(\$user, \$newLocation)\n";
} else {
    echo "❌ Корректный вызов не найден\n";
    exit(1);
}

echo "\n=== РЕЗУЛЬТАТ ===\n";
echo "✅ Ошибка 'Undefined variable \$normalizedLocation' исправлена\n";
echo "✅ Теперь используется корректная переменная \$newLocation\n";
echo "✅ Файл готов к работе\n";

echo "\nТеперь можно безопасно заходить в подлокации рудников!\n";
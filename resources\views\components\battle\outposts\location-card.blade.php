@props([
    'locationName' => '',
    'locationInfo' => [],
    'canAccess' => false,
    'canEnterOutposts' => false,
    'userLevel' => 1,
    'deniedReason' => null
])

{{--
Компонент для отображения карточки локации аванпоста
Принимает:
- locationName: название локации
- locationInfo: массив с информацией о локации (image_path, min_level, max_level, gs_requirement, route_name, outpost_id, id)
- canAccess: может ли игрок получить доступ к локации
- canEnterOutposts: может ли игрок войти в боевые локации (здоровье > 75%)
- userLevel: уровень игрока
- deniedReason: причина отказа в доступе (если есть)
--}}

@php
    // Формируем текст с уровнями
    $levelText = '';
    if ($locationInfo['min_level'] > 0 && $locationInfo['max_level'] > 0) {
        $levelText = "Уровень: {$locationInfo['min_level']}-{$locationInfo['max_level']}";
    } elseif ($locationInfo['min_level'] > 0 && $locationInfo['max_level'] === 0) {
        $levelText = "Уровень: от {$locationInfo['min_level']}";
    }

    // Проверяем, может ли игрок войти в локацию
    $finalCanAccess = $canAccess && $canEnterOutposts;
@endphp

@if($finalCanAccess)
    {{-- Доступная локация --}}
    <div class="location-item mb-4 rounded-lg overflow-hidden border border-[#a6925e] shadow-lg">
        {{-- Изображение локации с наложенным градиентом --}}
        <div class="relative h-28 w-full overflow-hidden">
            @php
                $svgText = $locationName;
                $svgBase64 = base64_encode('<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#3b3a33"/><text x="50%" y="50%" font-family="Arial" font-size="14" fill="#d9d3b8" text-anchor="middle" alignment-baseline="middle">' . $svgText . '</text></svg>');
            @endphp
            <img src="{{ asset($locationInfo['image_path']) }}" alt="{{ $locationName }}"
                class="w-full h-full object-cover location-image"
                onerror="this.onerror=null; this.src='data:image/svg+xml;base64,{{ $svgBase64 }}';">
            <div class="absolute inset-0 bg-gradient-to-t from-[#000000] via-[#00000080] to-transparent">
            </div>

            {{-- Информация о локации, наложенная на изображение --}}
            <div class="absolute bottom-0 left-0 right-0 p-3">
                <div class="flex justify-between items-end">
                    <div>
                        <h3 class="text-[#e5b769] font-bold text-lg drop-shadow-[0_2px_2px_rgba(0,0,0,0.8)]">
                            {{ $locationName }}
                        </h3>
                        <span class="inline-block bg-[#00000080] text-white text-xs px-2 py-0.5 rounded">
                            {{ $levelText }} | GS: от {{ $locationInfo['gs_requirement'] }}
                        </span>
                    </div>

                    {{-- Кнопка перехода --}}
                    @php
                        // Проверяем наличие outpost_id для маршрута battle.outposts.show
                        $hasValidOutpostId = isset($locationInfo['outpost_id']) && !empty($locationInfo['outpost_id']);
                        $isCustomOutpost = strpos($locationInfo['route_name'], 'battle.outposts.show') === 0;

                        // Формируем URL в зависимости от типа маршрута и наличия outpost_id
                        if ($isCustomOutpost && $hasValidOutpostId) {
                            $locationUrl = route($locationInfo['route_name'], ['id' => $locationInfo['outpost_id']]);
                            // Логируем успешное формирование URL для отладки
                            \Illuminate\Support\Facades\Log::info('Сформирован URL для локации аванпоста', [
                                'location_name' => $locationName,
                                'outpost_id' => $locationInfo['outpost_id'],
                                'url' => $locationUrl
                            ]);
                        } elseif ($isCustomOutpost && !$hasValidOutpostId) {
                            // Если outpost_id отсутствует, но маршрут требует его, пробуем использовать location_id
                            if (isset($locationInfo['id']) && !empty($locationInfo['id'])) {
                                // Ищем запись OutpostLocation по location_id
                                $outpostLocation = \App\Models\OutpostLocation::where('location_id', $locationInfo['id'])->first();
                                if ($outpostLocation) {
                                    $locationUrl = route($locationInfo['route_name'], ['id' => $outpostLocation->id]);
                                    \Illuminate\Support\Facades\Log::info('Сформирован URL для локации аванпоста через location_id', [
                                        'location_name' => $locationName,
                                        'location_id' => $locationInfo['id'],
                                        'outpost_id' => $outpostLocation->id,
                                        'url' => $locationUrl
                                    ]);
                                } else {
                                    // Если не удалось найти запись OutpostLocation, используем запасной вариант
                                    \Illuminate\Support\Facades\Log::error('Не удалось найти запись OutpostLocation по location_id', [
                                        'location_name' => $locationName,
                                        'location_id' => $locationInfo['id'],
                                        'route_name' => $locationInfo['route_name']
                                    ]);
                                    $locationUrl = route('battle.outposts.index'); // Перенаправляем на список аванпостов
                                }
                            } else {
                                // Если id отсутствует, но маршрут требует его, логируем ошибку и используем запасной вариант
                                \Illuminate\Support\Facades\Log::error('Отсутствует outpost_id для локации аванпоста', [
                                    'location_name' => $locationName,
                                    'route_name' => $locationInfo['route_name']
                                ]);
                                $locationUrl = route('battle.outposts.index'); // Перенаправляем на список аванпостов
                            }
                        } else {
                            $locationUrl = route($locationInfo['route_name']);
                        }
                    @endphp
                    <a href="{{ $locationUrl }}"
                        class="bg-gradient-to-b from-[#e5b769] to-[#c4a76d] text-[#2f2d2b] px-4 py-1.5
                                                                                                                                    rounded font-bold text-sm uppercase tracking-wide shadow-md
                                                                                                                                    hover:shadow-[0_0_10px_rgba(229,183,105,0.6)] transition-all duration-200">
                        Войти
                    </a>
                </div>
            </div>
        </div>
    </div>
@else
    {{-- Недоступная локация --}}
    <div
        class="location-item mb-4 rounded-lg overflow-hidden border border-[#514b3c] shadow-lg opacity-80 hover:opacity-95 transition-all duration-300">
        {{-- Изображение локации с наложенным градиентом --}}
        <div class="relative h-28 w-full overflow-hidden">
            @php
                $svgText = $locationName;
                $svgBase64 = base64_encode('<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#3b3a33"/><text x="50%" y="50%" font-family="Arial" font-size="14" fill="#d9d3b8" text-anchor="middle" alignment-baseline="middle">' . $svgText . '</text></svg>');
            @endphp
            <img src="{{ asset($locationInfo['image_path']) }}" alt="{{ $locationName }}"
                class="w-full h-full object-cover grayscale-[50%] blur-[3px]"
                onerror="this.onerror=null; this.src='data:image/svg+xml;base64,{{ $svgBase64 }}';">
            <div class="absolute inset-0 bg-gradient-to-t from-[#000000] via-[#00000080] to-transparent">
            </div>

            {{-- Индикатор требований по уровню --}}
            <div
                class="absolute top-0 right-0 bg-[#2f2d2b] text-[#e5b769] text-xs px-3 py-1 border-l border-b border-[#514b3c] rounded-bl-md opacity-90 flex items-center">
                @if($userLevel < $locationInfo['min_level'])
                    <span class="text-[#e5b769] mr-1">🔒</span>
                    <span>Мин. уровень {{ $locationInfo['min_level'] }}</span>
                @elseif($locationInfo['max_level'] > 0 && $userLevel > $locationInfo['max_level'])
                    <span class="text-[#e5b769] mr-1">⚠️</span>
                    <span>Макс. уровень {{ $locationInfo['max_level'] }}</span>
                @elseif(!$canEnterOutposts)
                    <span class="text-[#e5b769] mr-1">❤️</span>
                    <span>Недостаточно здоровья (< 10%)</span>
                @elseif(isset($deniedReason) && $deniedReason)
                    <span class="text-[#e5b769] mr-1">⚠️</span>
                    <span>{{ $deniedReason }}</span>
                @endif
            </div>

            {{-- Информация о локации, наложенная на изображение --}}
            <div class="absolute bottom-0 left-0 right-0 p-3">
                <div class="flex justify-between items-end">
                    <div>
                        <h3 class="text-[#e5b769] font-bold text-lg drop-shadow-[0_2px_2px_rgba(0,0,0,0.8)]">
                            {{ $locationName }}
                        </h3>
                        <span class="inline-block bg-[#00000080] text-white text-xs px-2 py-0.5 rounded">
                            {{ $levelText }} | GS: от {{ $locationInfo['gs_requirement'] }}
                        </span>
                    </div>

                    {{-- Кнопка "Закрыто" --}}
                    <button disabled
                        class="bg-gradient-to-b from-[#8a8a8a] to-[#6a6a6a] text-[#5a5a5a] px-4 py-1.5
                                                                                                    rounded font-bold text-sm uppercase tracking-wide opacity-70 cursor-not-allowed">
                        Закрыто
                    </button>
                </div>
            </div>
        </div>
    </div>
@endif

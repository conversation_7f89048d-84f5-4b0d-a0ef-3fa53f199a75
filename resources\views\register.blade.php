<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Создание героя - Echoes of Eternity</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        /* Стили для кастомных селектов */
        .custom-select {
            position: relative;
        }
        
        .custom-select-button {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .custom-select-button:hover {
            border-color: rgba(193, 169, 110, 0.7) !important;
        }
        
        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            background-color: #1a1814;
            border: 2px solid #3b3629;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            margin-top: 0.25rem;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        
        .custom-select-dropdown.show {
            display: block;
        }
        
        .custom-select-option {
            padding: 0.75rem;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid rgba(59, 54, 41, 0.3);
        }
        
        .custom-select-option:last-child {
            border-bottom: none;
        }
        
        .custom-select-option:hover {
            background-color: #2a2722;
        }
        
        .custom-select-option img {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }
        
        .custom-select-arrow {
            transition: transform 0.2s ease;
        }
        
        .custom-select-arrow.rotated {
            transform: rotate(180deg);
        }
        
        .custom-select.disabled .custom-select-button {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .custom-select.disabled .custom-select-button:hover {
            border-color: #3b3629 !important;
        }
    </style>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    {{-- Основной контейнер в стиле группы --}}
    <div class="container max-w-md mx-auto px-1 py-3 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
         style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">
        
        {{-- Заголовок в стиле локации --}}
        <div class="text-center mb-6 bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-4">
            <h1 class="text-[#fceac4] text-xl font-bold mb-2" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                Регистрация
            </h1>
            <p class="text-[#a6925e] text-sm">Добро пожаловать к созданию персонажа</p>
        </div>

        {{-- Сообщения об ошибках --}}
        @if ($errors->any())
            <div class="bg-gradient-to-r from-[#4a1f1a]/20 to-[#5e2b26]/20 border border-[#4a1f1a]/40 rounded-lg p-3 mb-4">
                @foreach ($errors->all() as $error)
                    <p class="text-[#d4675a] text-sm font-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                        {{ $error }}
                    </p>
                @endforeach
            </div>
        @endif

        {{-- Форма регистрации в стиле группы --}}
        <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-4">
            <form action="{{ route('auth.register.submit') }}" method="POST" class="space-y-4">
                @csrf

                {{-- Имя пользователя --}}
                <div>
                    <label for="username" class="block text-[#fceac4] font-semibold text-sm mb-1">Имя героя</label>
                    <input type="text" id="username" name="username"
                        class="w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                               focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                               transition-all duration-200"
                        style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                        value="{{ old('username') }}" 
                        placeholder="Введите уникальное имя"
                        required>
                </div>

                {{-- Электронная почта --}}
                <div>
                    <label for="email" class="block text-[#fceac4] font-semibold text-sm mb-1">Электронная почта</label>
                    <input type="email" id="email" name="email"
                        class="w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                               focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                               transition-all duration-200"
                        style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                        value="{{ old('email') }}" 
                        placeholder="почта для восстановления"
                        required>
                </div>

                {{-- Пароль --}}
                <div>
                    <label for="password" class="block text-[#fceac4] font-semibold text-sm mb-1">Пароль</label>
                    <input type="password" id="password" name="password"
                        class="w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                               focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                               transition-all duration-200"
                        style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                        placeholder="Минимум 6 символов"
                        required>
                </div>

                {{-- Подтверждение пароля --}}
                <div>
                    <label for="password_confirmation" class="block text-[#fceac4] font-semibold text-sm mb-1">Подтверждение</label>
                    <input type="password" id="password_confirmation" name="password_confirmation"
                        class="w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                               focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                               transition-all duration-200"
                        style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                        placeholder="Повторите пароль"
                        required>
                </div>

                {{-- Раса --}}
                <div>
                    <label for="race" class="block text-[#fceac4] font-semibold text-sm mb-1">Раса</label>
                    <div class="custom-select">
                        <button type="button" class="custom-select-button w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                                   focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                                   transition-all duration-200 text-left"
                                style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                                id="race-button">
                            <span class="flex items-center">
                                <img id="race-icon" src="" alt="" class="w-5 h-5 mr-2 hidden">
                                <span id="race-text">Выберите расу</span>
                            </span>
                            <svg class="custom-select-arrow w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                        <div class="custom-select-dropdown" id="race-dropdown">
                            <div class="custom-select-option" data-value="solarius">
                                <img src="/assets/race/Racesolarius.png" alt="Солариус">
                                <span class="text-[#d4cbb0]" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">Солариус</span>
                                <span class="text-[#998d66] text-sm ml-2">- Дети света</span>
                            </div>
                            <div class="custom-select-option" data-value="lunarius">
                                <img src="/assets/race/Racelunarius.png" alt="Лунариус">
                                <span class="text-[#d4cbb0]" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">Лунариус</span>
                                <span class="text-[#998d66] text-sm ml-2">- Дети тьмы</span>
                            </div>
                        </div>
                    </div>
                    <select id="race" name="race" style="display: none;" required>
                        <option value="">Выберите расу</option>
                        <option value="solarius">Солариус</option>
                        <option value="lunarius">Лунариус</option>
                    </select>
                </div>

                {{-- Класс --}}
                <div>
                    <label for="class" class="block text-[#fceac4] font-semibold text-sm mb-1">Класс</label>
                    <div class="custom-select disabled" id="class-select">
                        <button type="button" class="custom-select-button w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                                   focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                                   transition-all duration-200 text-left"
                                style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                                id="class-button">
                            <span class="flex items-center">
                                <img id="class-icon" src="" alt="" class="w-5 h-5 mr-2 hidden">
                                <span id="class-text">Сначала выберите расу</span>
                            </span>
                            <svg class="custom-select-arrow w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                        <div class="custom-select-dropdown" id="class-dropdown">
                            <!-- Опции будут добавлены динамически -->
                        </div>
                    </div>
                    <select id="class" name="class" style="display: none;" required>
                        <option value="">Выберите класс</option>
                        <option value="warrior">Воин</option>
                        <option value="priest">Жрец</option>
                        <option value="mage">Маг</option>
                    </select>
                </div>

                {{-- Кнопки --}}
                <div class="flex flex-col space-y-2 pt-2">
                    <button type="submit"
                        class="w-full py-3 px-4 font-bold text-[#f8eac2] transition-all duration-200
                               bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3e5c48] hover:to-[#243c2f]
                               border-2 border-[#3b3629] hover:border-[#c1a96e]/50 rounded-lg
                               shadow-lg hover:shadow-xl"
                        style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                        Зарегестрироваться
                    </button>
                    
                    <a href="{{ route('auth.login') }}"
                       class="w-full py-3 px-4 font-bold text-[#f8eac2] text-center transition-all duration-200
                              bg-gradient-to-b from-[#4a3c66] to-[#3a2d52] hover:from-[#5d4a7a] hover:to-[#4a3c66]
                              border-2 border-[#3b3629] hover:border-[#c1a96e]/50 rounded-lg
                              shadow-lg hover:shadow-xl"
                       style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                        Уже есть персонаж?
                    </a>
                </div>
            </form>

            {{-- Разделитель --}}
            <div class="border-t border-[#3b3629] my-4"></div>

            {{-- Информация --}}
            <div class="text-center">
                <p class="text-[#998d66] text-xs leading-relaxed" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                    Выбирайте класс и расу персонажа мудро - они определят ваш путь в мире Echoes of Eternity <br>
                    Вы сможете в дальнейшем изменить Расу и Класс если нужно будет
                </p>
            </div>
        </div>
    </div>

    {{-- Футер для неавторизованных --}}
    <x-auth.footer />

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Элементы интерфейса
            const raceButton = document.getElementById('race-button');
            const raceDropdown = document.getElementById('race-dropdown');
            const raceSelect = document.getElementById('race');
            const raceIcon = document.getElementById('race-icon');
            const raceText = document.getElementById('race-text');
            const raceArrow = raceButton.querySelector('.custom-select-arrow');
            
            const classButton = document.getElementById('class-button');
            const classDropdown = document.getElementById('class-dropdown');
            const classSelect = document.getElementById('class');
            const classIcon = document.getElementById('class-icon');
            const classText = document.getElementById('class-text');
            const classArrow = classButton.querySelector('.custom-select-arrow');
            const classContainer = document.getElementById('class-select');
            
            // Данные о классах для разных рас
            const classData = {
                'solarius': {
                    'warrior': { name: 'Воин', icon: '/assets/race/raceSolarWarrior.png' },
                    'priest': { name: 'Жрец', icon: '/assets/race/raceSolarPriest.png' },
                    'mage': { name: 'Маг', icon: '/assets/race/raceSolarMage.png' }
                },
                'lunarius': {
                    'warrior': { name: 'Воин', icon: '/assets/race/raceLunarWarrior.png' },
                    'priest': { name: 'Жрец', icon: '/assets/race/raceLunarPriest.png' },
                    'mage': { name: 'Маг', icon: '/assets/race/raceLunarMage.png' }
                }
            };
            
            // Обработчик клика по кнопке расы
            raceButton.addEventListener('click', function() {
                const isOpen = raceDropdown.classList.contains('show');
                
                if (isOpen) {
                    raceDropdown.classList.remove('show');
                    raceArrow.classList.remove('rotated');
                } else {
                    raceDropdown.classList.add('show');
                    raceArrow.classList.add('rotated');
                    // Закрываем другие селекты
                    classDropdown.classList.remove('show');
                    classArrow.classList.remove('rotated');
                }
            });
            
            // Обработчик клика по кнопке класса
            classButton.addEventListener('click', function() {
                if (classContainer.classList.contains('disabled')) return;
                
                const isOpen = classDropdown.classList.contains('show');
                
                if (isOpen) {
                    classDropdown.classList.remove('show');
                    classArrow.classList.remove('rotated');
                } else {
                    classDropdown.classList.add('show');
                    classArrow.classList.add('rotated');
                    // Закрываем другие селекты
                    raceDropdown.classList.remove('show');
                    raceArrow.classList.remove('rotated');
                }
            });
            
            // Обработчики выбора расы
            raceDropdown.addEventListener('click', function(e) {
                const option = e.target.closest('.custom-select-option');
                if (!option) return;
                
                const value = option.dataset.value;
                const img = option.querySelector('img');
                const text = option.querySelector('span').textContent;
                
                // Обновляем отображение
                raceSelect.value = value;
                raceText.textContent = text;
                
                if (img) {
                    raceIcon.src = img.src;
                    raceIcon.classList.remove('hidden');
                }
                
                // Закрываем dropdown
                raceDropdown.classList.remove('show');
                raceArrow.classList.remove('rotated');
                
                // Активируем выбор класса
                classContainer.classList.remove('disabled');
                updateClassOptions(value);
            });
            
            // Обработчики выбора класса
            classDropdown.addEventListener('click', function(e) {
                const option = e.target.closest('.custom-select-option');
                if (!option) return;
                
                const value = option.dataset.value;
                const img = option.querySelector('img');
                const text = option.querySelector('span').textContent;
                
                // Обновляем отображение
                classSelect.value = value;
                classText.textContent = text;
                
                if (img) {
                    classIcon.src = img.src;
                    classIcon.classList.remove('hidden');
                }
                
                // Закрываем dropdown
                classDropdown.classList.remove('show');
                classArrow.classList.remove('rotated');
            });
            
            // Функция обновления опций класса
            function updateClassOptions(race) {
                const classes = classData[race];
                if (!classes) return;
                
                classDropdown.innerHTML = '';
                
                Object.keys(classes).forEach(classKey => {
                    const classInfo = classes[classKey];
                    const option = document.createElement('div');
                    option.className = 'custom-select-option';
                    option.dataset.value = classKey;
                    
                    option.innerHTML = `
                        <img src="${classInfo.icon}" alt="${classInfo.name}" onerror="this.style.display='none'">
                        <span class="text-[#d4cbb0]" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">${classInfo.name}</span>
                    `;
                    
                    classDropdown.appendChild(option);
                });
                
                // Сбрасываем выбор класса
                classSelect.value = '';
                classText.textContent = 'Выберите класс';
                classIcon.classList.add('hidden');
            }
            
            // Закрытие при клике вне селектов
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.custom-select')) {
                    raceDropdown.classList.remove('show');
                    raceArrow.classList.remove('rotated');
                    classDropdown.classList.remove('show');
                    classArrow.classList.remove('rotated');
                }
            });
        });
    </script>
</body>

</html>
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\Log;

class CleanupExpiredMineMarks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mine:cleanup-marks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup expired mine marks';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $mineDetectionService = app(MineDetectionService::class);
            $cleaned = $mineDetectionService->cleanupExpiredMarks();
            
            if ($cleaned > 0) {
                Log::info("[Scheduler] Очищено истекших меток рудников: {$cleaned}");
                $this->info("Очищено истекших меток рудников: {$cleaned}");
            } else {
                $this->info("Нет истекших меток для очистки");
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error("[Scheduler] Ошибка очистки меток рудников: " . $e->getMessage());
            $this->error("Ошибка очистки меток рудников: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;
use App\Models\Location;

class TestMinesPagination extends Command
{
    protected $signature = 'test:mines-pagination';
    protected $description = 'Тестирует пагинацию рудников на странице /battle/mines';

    public function handle()
    {
        $this->info('=== Тестирование пагинации рудников ===');

        // Проверяем общее количество активных рудников
        $totalMines = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->count();
        
        $this->info("Общее количество активных рудников: $totalMines");

        // Проверяем первую страницу (4 рудника)
        $firstPage = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->with('baseLocation')
            ->orderBy('order')
            ->paginate(4);

        $this->info("Рудников на первой странице: " . $firstPage->count());
        $this->info("Всего страниц: " . $firstPage->lastPage());
        $this->info("Текущая страница: " . $firstPage->currentPage());

        // Показываем рудники на первой странице
        if ($firstPage->count() > 0) {
            $this->info("Рудники на первой странице:");
            foreach ($firstPage as $index => $mine) {
                $this->info("  " . ($index + 1) . ". {$mine->name} (slug: {$mine->slug})");
                if ($mine->baseLocation) {
                    $this->info("     Базовая локация: {$mine->baseLocation->name}");
                }
            }
        }

        // Если есть вторая страница, показываем её
        if ($firstPage->hasMorePages()) {
            $secondPage = MineLocation::whereNull('parent_id')
                ->where('is_active', true)
                ->with('baseLocation')
                ->orderBy('order')
                ->paginate(4, ['*'], 'page', 2);

            $this->info("\nРудники на второй странице:");
            foreach ($secondPage as $index => $mine) {
                $this->info("  " . ($index + 1) . ". {$mine->name} (slug: {$mine->slug})");
                if ($mine->baseLocation) {
                    $this->info("     Базовая локация: {$mine->baseLocation->name}");
                }
            }
        }

        // Проверяем наличие базовых локаций для рудников
        $minesWithoutBaseLocation = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->whereDoesntHave('baseLocation')
            ->count();

        if ($minesWithoutBaseLocation > 0) {
            $this->warn("Найдено $minesWithoutBaseLocation рудников без базовой локации!");
        } else {
            $this->info("Все рудники имеют базовую локацию ✓");
        }

        // Проверяем порядок сортировки
        $this->info("\nПроверка порядка сортировки:");
        $allMines = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('order')
            ->get(['name', 'order']);

        foreach ($allMines as $mine) {
            $this->info("  {$mine->name} (порядок: {$mine->order})");
        }

        // Симуляция URL пагинации
        $this->info("\nСимуляция URL пагинации:");
        $this->info("Страница 1: /battle/mines");
        $this->info("Страница 2: /battle/mines?page=2");
        if ($firstPage->lastPage() > 2) {
            $this->info("Страница 3: /battle/mines?page=3");
        }

        $this->info("\n=== Тестирование завершено ===");
        
        // Итоговая сводка
        $this->info('ИТОГОВАЯ СВОДКА:');
        $this->info("- Общее количество рудников: $totalMines");
        $this->info("- Рудников на странице: 4");
        $this->info("- Общее количество страниц: " . $firstPage->lastPage());
        $this->info("- Пагинация настроена: ✓");
        $this->info("- Базовые локации: " . ($minesWithoutBaseLocation == 0 ? '✓' : '⚠'));
        
        return 0;
    }
}
<?php

/**
 * Реальная диагностика проблемы с дебафом "Замечен" при добыче
 */

require_once __DIR__ . '/vendor/autoload.php';

// Инициализируем Laravel приложение
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Services\MineDetectionService;
use App\Services\MineDetectionServiceFallback;

echo "🔍 ДИАГНОСТИКА ПРОБЛЕМЫ С ДЕБАФОМ 'ЗАМЕЧЕН'\n";
echo "==========================================\n\n";

try {
    // 1. Проверяем основные компоненты системы
    echo "1️⃣ Проверка системных компонентов...\n";
    
    $hasMineMarks = Schema::hasTable('mine_marks');
    echo "   Таблица mine_marks: " . ($hasMineMarks ? '✅ Есть' : '❌ Нет') . "\n";
    
    $hasActiveEffects = Schema::hasTable('active_effects');
    echo "   Таблица active_effects: " . ($hasActiveEffects ? '✅ Есть' : '❌ Нет') . "\n";
    
    // 2. Находим тестовые данные
    echo "\n2️⃣ Поиск тестовых данных...\n";
    
    $user = User::with('profile')->where('name', 'admin')->first() ?? User::with('profile')->first();
    if (!$user) {
        echo "   ❌ Пользователь не найден!\n";
        exit(1);
    }
    echo "   Пользователь: {$user->name} (ID: {$user->id})\n";
    
    $mineLocation = MineLocation::where('is_active', true)->first();
    if (!$mineLocation) {
        echo "   ❌ Активная локация рудника не найдена!\n";
        exit(1);
    }
    echo "   Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    echo "   Основная локация: ID {$mineLocation->location_id}\n";
    
    // 3. Проверяем текущие дебафы/метки
    echo "\n3️⃣ Проверка текущих дебафов...\n";
    
    if ($hasMineMarks) {
        $activeMarks = MineMark::where('player_id', $user->id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->count();
        echo "   Активных меток (mine_marks): {$activeMarks}\n";
    }
    
    $activeEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection_debuff')
        ->where('ends_at', '>', now())
        ->count();
    echo "   Активных дебафов (active_effects): {$activeEffects}\n";
    
    // 4. ГЛАВНЫЙ ТЕСТ - пытаемся применить дебаф вручную
    echo "\n4️⃣ ТЕСТИРОВАНИЕ ПРИМЕНЕНИЯ ДЕБАФА...\n";
    
    try {
        if ($hasMineMarks) {
            echo "   🎯 Попытка через MineDetectionService...\n";
            $service = app(MineDetectionService::class);
            
            // Выводим детальную информацию о процессе
            echo "   📋 Параметры:\n";
            echo "      - Игрок: {$user->id} ({$user->name})\n";
            echo "      - Рудник: {$mineLocation->id} ({$mineLocation->name})\n";
            echo "      - Основная локация: {$mineLocation->location_id}\n";
            
            $mark = $service->createMark($user, $mineLocation, 300);
            
            if ($mark) {
                echo "   ✅ Метка создана успешно!\n";
                echo "      - ID метки: {$mark->id}\n";
                echo "      - Истекает: {$mark->expires_at}\n";
                echo "      - Активна: " . ($mark->is_active ? 'Да' : 'Нет') . "\n";
                
                // Проверяем в БД
                $dbMark = MineMark::find($mark->id);
                echo "   📊 Проверка в БД: " . ($dbMark ? 'Найдена' : 'НЕ НАЙДЕНА') . "\n";
                
            } else {
                echo "   ❌ Не удалось создать метку!\n";
            }
        } else {
            echo "   🎯 Попытка через MineDetectionServiceFallback...\n";
            $service = app(MineDetectionServiceFallback::class);
            
            echo "   📋 Параметры:\n";
            echo "      - Игрок: {$user->id} ({$user->name})\n";
            echo "      - Рудник: {$mineLocation->id} ({$mineLocation->name})\n";
            echo "      - Основная локация: {$mineLocation->location_id}\n";
            
            $effect = $service->applyDetectionDebuff($user, $mineLocation);
            
            if ($effect) {
                echo "   ✅ Эффект создан успешно!\n";
                echo "      - ID эффекта: {$effect->id}\n";
                echo "      - Истекает: {$effect->ends_at}\n";
                echo "      - Тип: {$effect->effect_type}\n";
                
                // Проверяем в БД
                $dbEffect = ActiveEffect::find($effect->id);
                echo "   📊 Проверка в БД: " . ($dbEffect ? 'Найден' : 'НЕ НАЙДЕН') . "\n";
                
            } else {
                echo "   ❌ Не удалось создать эффект!\n";
            }
        }
        
    } catch (\Exception $e) {
        echo "   ❌ ОШИБКА при создании дебафа:\n";
        echo "      Сообщение: {$e->getMessage()}\n";
        echo "      Файл: {$e->getFile()}:{$e->getLine()}\n";
        echo "      Трассировка:\n";
        $trace = $e->getTraceAsString();
        $lines = explode("\n", $trace);
        foreach (array_slice($lines, 0, 5) as $line) {
            echo "         {$line}\n";
        }
    }
    
    // 5. Проверяем обнаружение после создания
    echo "\n5️⃣ Проверка обнаружения игрока...\n";
    
    $service = $hasMineMarks ? app(MineDetectionService::class) : app(MineDetectionServiceFallback::class);
    
    if ($hasMineMarks) {
        $isDetected = $service->hasActiveMark($user->id, $mineLocation->location_id, $mineLocation->id);
    } else {
        $isDetected = $service->isPlayerDetected($user->id, $mineLocation->location_id, $mineLocation->id);
    }
    
    echo "   Игрок обнаружен: " . ($isDetected ? '✅ Да' : '❌ Нет') . "\n";
    
    // 6. Симуляция кода из MinesController
    echo "\n6️⃣ СИМУЛЯЦИЯ КОДА ИЗ MINESCONTROLLER...\n";
    
    try {
        echo "   🧪 Выполнение того же кода, что и в hitResource...\n";
        
        // Точно такой же код как в MinesController
        if (Schema::hasTable('mine_marks')) {
            echo "   📝 Используется MineDetectionService\n";
            $mineDetectionService = app(MineDetectionService::class);
            $detectionEffect = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
        } else {
            echo "   📝 Используется MineDetectionServiceFallback\n";
            $mineDetectionService = app(\App\Services\MineDetectionServiceFallback::class);
            $detectionEffect = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
        }
        
        if ($detectionEffect) {
            echo "   ✅ Дебаф применен через симуляцию MinesController!\n";
            if ($hasMineMarks && $detectionEffect instanceof MineMark) {
                echo "      - Тип: MineMark, ID: {$detectionEffect->id}\n";
            } elseif ($detectionEffect instanceof ActiveEffect) {
                echo "      - Тип: ActiveEffect, ID: {$detectionEffect->id}\n";
            }
        } else {
            echo "   ❌ Дебаф НЕ применен через симуляцию MinesController!\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ ОШИБКА в симуляции MinesController:\n";
        echo "      {$e->getMessage()}\n";
    }
    
    // 7. Финальная проверка активных дебафов
    echo "\n7️⃣ Финальная проверка активных дебафов...\n";
    
    if ($hasMineMarks) {
        $finalMarks = MineMark::where('player_id', $user->id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->get();
        echo "   Активных меток (mine_marks): " . $finalMarks->count() . "\n";
        
        foreach ($finalMarks as $mark) {
            echo "      - ID: {$mark->id}, Рудник: {$mark->mine_location_id}, Истекает: {$mark->expires_at}\n";
        }
    }
    
    $finalEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection_debuff')
        ->where('ends_at', '>', now())
        ->get();
    echo "   Активных дебафов (active_effects): " . $finalEffects->count() . "\n";
    
    foreach ($finalEffects as $effect) {
        echo "      - ID: {$effect->id}, Истекает: {$effect->ends_at}\n";
    }
    
    // 8. Проверяем логи
    echo "\n8️⃣ Проверка логов...\n";
    
    $logFile = storage_path('logs/laravel.log');
    if (file_exists($logFile)) {
        $logContent = shell_exec("tail -20 {$logFile} | grep -i 'mine.*detection\\|замечен' || echo 'Записи не найдены'");
        echo "   Последние записи в логах:\n";
        if (!empty($logContent) && $logContent !== "Записи не найдены\n") {
            $lines = explode("\n", trim($logContent));
            foreach ($lines as $line) {
                if (!empty($line)) {
                    echo "      {$line}\n";
                }
            }
        } else {
            echo "      Записей о системе обнаружения в рудниках не найдено\n";
        }
    } else {
        echo "   Файл логов не найден\n";
    }
    
    echo "\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА!\n";
    
    if ($hasMineMarks) {
        $totalMarks = MineMark::where('player_id', $user->id)->count();
        if ($totalMarks > 0) {
            echo "\n💡 РЕШЕНИЕ: Система работает правильно. Проверьте:\n";
            echo "   1. Запущен ли планировщик: php artisan schedule:work\n";
            echo "   2. Есть ли живые мобы в рудниках\n";
        } else {
            echo "\n❌ ПРОБЛЕМА: Дебафы не создаются. Нужно исследовать код глубже.\n";
        }
    } else {
        $totalEffects = ActiveEffect::where('target_id', $user->id)
            ->where('effect_type', 'mine_detection_debuff')
            ->count();
        if ($totalEffects > 0) {
            echo "\n💡 РЕШЕНИЕ: Fallback система работает. Создайте таблицу mine_marks для оптимальной производительности.\n";
        } else {
            echo "\n❌ ПРОБЛЕМА: Ни основная, ни fallback система не работает.\n";
        }
    }
    
} catch (Exception $e) {
    echo "\n❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Файл: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
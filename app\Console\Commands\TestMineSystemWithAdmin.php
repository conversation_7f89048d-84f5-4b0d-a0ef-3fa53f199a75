<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\MineMark;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use App\Jobs\MineAutoAttackJob;

class TestMineSystemWithAdmin extends Command
{
    protected $signature = 'test:mine-admin {--attack : Выполнить реальную атаку} {--cleanup : Очистить тестовые данные}';
    protected $description = 'Тестирование системы рудников с админом и Огром';

    public function handle()
    {
        $this->info('🎯 ТЕСТИРОВАНИЕ СИСТЕМЫ РУДНИКОВ С АДМИНОМ');
        $this->info('=' . str_repeat('=', 50));

        if ($this->option('cleanup')) {
            $this->cleanup();
            return 0;
        }

        // Получаем данные
        $admin = User::where('name', 'admin')->first();
        $location = Location::find(137); // аааааааааааа
        $mineLocation = MineLocation::find(172); // рудник аааааааааааа
        $ogre = Mob::find(14); // Огр

        if (!$admin || !$location || !$mineLocation || !$ogre) {
            $this->error('❌ Не найдены необходимые данные');
            return 1;
        }

        $this->info("✅ Данные найдены:");
        $this->info("   👤 Админ: {$admin->name} (ID: {$admin->id})");
        $this->info("   📍 Локация: {$location->name} (ID: {$location->id})");
        $this->info("   ⛏️  Рудник: {$mineLocation->name} (ID: {$mineLocation->id})");
        $this->info("   👹 Огр: {$ogre->name} (ID: {$ogre->id}, HP: {$ogre->hp})");

        // Создаем метку "Замечен"
        $this->createDetectionMark($admin, $mineLocation);

        // Тестируем умное распределение
        $this->testSmartDistribution($admin, $mineLocation, $ogre);

        // Выполняем атаку если запрошено
        if ($this->option('attack')) {
            $this->executeAttack();
        }

        return 0;
    }

    private function createDetectionMark($admin, $mineLocation)
    {
        $this->info("\n🎯 СОЗДАНИЕ МЕТКИ 'ЗАМЕЧЕН':");

        $mineDetectionService = app(MineDetectionService::class);

        // Проверяем существующие метки
        $existingMark = MineMark::where('player_id', $admin->id)
            ->where('mine_location_id', $mineLocation->id)
            ->where('is_active', true)
            ->first();

        if ($existingMark) {
            $this->info("   ⚠️  Метка уже существует (ID: {$existingMark->id})");
            return;
        }

        // Создаем новую метку
        $mark = $mineDetectionService->createMark($admin, $mineLocation, 600); // 10 минут
        $this->info("   ✅ Создана метка (ID: {$mark->id})");
        $this->info("   ⏰ Истекает: {$mark->expires_at}");
    }

    private function testSmartDistribution($admin, $mineLocation, $ogre)
    {
        $this->info("\n🧠 ТЕСТИРОВАНИЕ УМНОГО РАСПРЕДЕЛЕНИЯ:");

        $distributionService = app(MineTargetDistributionService::class);

        // Подготавливаем данные игроков
        $markedPlayers = collect([
            [
                'player' => $admin,
                'mine_location' => $mineLocation,
                'last_attack_at' => null
            ]
        ]);

        // Тестируем получение оптимальной пары
        $pair = $distributionService->getOptimalMobPlayerPair($markedPlayers, $mineLocation);

        if ($pair) {
            $this->info("   ✅ Создана пара моб-игрок:");
            $this->info("      👹 Моб: {$pair['mob']->name} (ID: {$pair['mob']->id}, HP: {$pair['mob']->hp})");
            $this->info("      👤 Игрок: {$pair['player']->name} (ID: {$pair['player']->id})");
            
            $playerHp = round(($pair['player']->profile->current_hp / $pair['player']->profile->max_hp) * 100, 1);
            $this->info("      ❤️  HP игрока: {$playerHp}%");
        } else {
            $this->warn("   ⚠️  Не удалось создать пару моб-игрок");
            
            // Диагностика
            $this->info("   🔍 Диагностика:");
            $availableMobs = Mob::where('location_id', $mineLocation->location_id)
                ->where('mob_type', 'mine')
                ->where('hp', '>', 0)
                ->get();
            
            $this->info("      - Доступных мобов: {$availableMobs->count()}");
            foreach ($availableMobs as $mob) {
                $this->info("        * {$mob->name} (ID: {$mob->id}, HP: {$mob->hp})");
            }
        }

        // Показываем статистику
        $stats = $distributionService->getDistributionStats($mineLocation);
        $this->info("   📊 Статистика распределения:");
        $this->info("      - Игроков с мобами: {$stats['total_players_with_mobs']}");
        $this->info("      - Всего назначений: {$stats['total_mob_assignments']}");
    }

    private function executeAttack()
    {
        $this->info("\n⚔️  ВЫПОЛНЕНИЕ АТАКИ:");

        try {
            $job = new MineAutoAttackJob();
            
            $this->info("   🚀 Запуск MineAutoAttackJob...");
            
            $job->handle(
                app(\App\Services\MineDetectionService::class),
                app(\App\Services\MineTargetDistributionService::class),
                app(\App\Services\BattleLogService::class),
                app(\App\Services\PlayerHealthService::class),
                app(\App\Services\CombatFormulaService::class),
                app(\App\Services\LogFormattingService::class)
            );
            
            $this->info("   ✅ Атака выполнена успешно");
            
            // Проверяем изменения
            $admin = User::find(7);
            $ogre = Mob::find(14);
            
            $this->info("   📊 Результат:");
            $this->info("      👤 HP админа: {$admin->profile->current_hp}/{$admin->profile->max_hp}");
            $this->info("      👹 HP Огра: {$ogre->hp}");
            
        } catch (\Exception $e) {
            $this->error("   ❌ Ошибка при выполнении атаки:");
            $this->error("      {$e->getMessage()}");
            
            if ($this->option('verbose')) {
                $this->error("      Трассировка: {$e->getTraceAsString()}");
            }
        }
    }

    private function cleanup()
    {
        $this->info("🧹 ОЧИСТКА ТЕСТОВЫХ ДАННЫХ:");

        // Удаляем метки админа
        $deletedMarks = MineMark::where('player_id', 7)->delete();
        $this->info("   🗑️  Удалено меток: {$deletedMarks}");

        // Восстанавливаем HP админа
        $admin = User::find(7);
        if ($admin && $admin->profile) {
            $admin->profile->current_hp = $admin->profile->max_hp;
            $admin->profile->save();
            $this->info("   ❤️  HP админа восстановлено");
        }

        // Восстанавливаем HP Огра
        $ogre = Mob::find(14);
        if ($ogre) {
            $ogre->hp = 455; // Исходное значение
            $ogre->save();
            $this->info("   👹 HP Огра восстановлено");
        }

        $this->info("   ✅ Очистка завершена");
    }
}

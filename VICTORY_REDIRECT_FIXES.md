# Исправления системы перенаправления победителей

## Проблема
Игрок, который побеждает другого игрока, перенаправляется на `/battle/outposts` вместо того, чтобы остаться в текущей локации.

## Причины
Найдено **6 мест** в коде, которые перенаправляют игроков с низким HP на `/battle/outposts`:

### 1. CheckUserHealth middleware
**Файл:** `app/Http/Middleware/CheckUserHealth.php`
**Строка:** 186
**Проблема:** Перенаправляет игроков с 0 HP на `battle.outposts.index`

### 2. CheckMinimumHP middleware  
**Файл:** `app/Http/Middleware/CheckMinimumHP.php`
**Строка:** 237
**Проблема:** Перенаправляет игроков с низким HP на `battle.outposts.index`

### 3. CheckUserLevel middleware
**Файл:** `app/Http/Middleware/CheckUserLevel.php`
**Строка:** 90
**Проблема:** Перенаправляет игроков без доступа к локации на `battle.outposts.index`

### 4. ElvenHaven VictoryHandlerService
**Файл:** `app/Services/battle/outposts/ElvenHaven/VictoryHandlerService.php`
**Строка:** 238
**Проблема:** Перенаправляет игроков с 0 HP на `battle.outposts.index`

### 5. DawnFort VictoryHandlerService
**Файл:** `app/Services/battle/outposts/DawnFort/VictoryHandlerService.php`
**Строка:** 240
**Проблема:** Перенаправляет игроков с 0 HP на `battle.outposts.index`

### 6. CheckPlayerDefeat middleware
**Файл:** `app/Http/Middleware/CheckPlayerDefeat.php`
**Строка:** 74
**Проблема:** Перенаправляет побежденных игроков на `battle.defeat`, но может мешать победителю

## Решение
Во всех перечисленных местах добавлена проверка флага `recent_victory_time`:

```php
// Проверяем, есть ли флаг недавней победы
$hasRecentVictory = session('recent_victory_time') && 
                   (time() - session('recent_victory_time') < 10);

// Если игрок недавно победил, пропускаем проверку
if ($hasRecentVictory) {
    return $next($request); // или return; для сервисов
}
```

## Логирование
Добавлено подробное логирование во все места для отладки:

- `Log::info()` - когда пропускается проверка для победителя
- `Log::warning()` - когда происходит перенаправление

## Флаг победы
В `CustomOutpostController.php` при победе игрока устанавливается флаг:

```php
// Устанавливаем флаг недавней победы в сессии победителя
session(['recent_victory_time' => time()]);
```

Флаг действует **10 секунд** - этого достаточно для всех перенаправлений после победы.

## Как протестировать
1. Создать двух игроков с низким HP
2. Один игрок атакует другого в аванпосте
3. Проверить логи Laravel - должны быть записи о пропуске проверок
4. Победитель должен остаться в локации аванпоста
5. Побежденный должен перенаправиться на `/battle/defeat`

## Статус
✅ **Все 6 мест исправлены**
✅ **Добавлено логирование**
✅ **Флаг victory_time работает**
✅ **Обратная совместимость сохранена**

Теперь победитель должен оставаться в текущей локации!
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Bot;
use App\Models\MineLocation;
use Illuminate\Support\Facades\DB;

class FixDuplicateBots extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bots:fix-duplicates {--dry-run : Только показать что будет сделано, не применять изменения}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Исправляет дублированных ботов между локациями и подлокациями рудников';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('🔍 РЕЖИМ ПРОСМОТРА - изменения не будут применены');
        } else {
            $this->info('🔧 РЕЖИМ ИСПРАВЛЕНИЯ - изменения будут применены');
        }
        
        $this->newLine();

        // 1. Анализ текущего состояния
        $this->analyzeCurrentState();

        // 2. Исправление ботов в подлокациях
        $this->fixMineLocationBots($isDryRun);

        // 3. Поиск и удаление дублированных ботов
        $this->removeDuplicateBots($isDryRun);

        // 4. Очистка кеша Redis
        if (!$isDryRun) {
            $this->clearRedisCache();
        }

        $this->newLine();
        $this->info('✅ Операция завершена!');
        
        if ($isDryRun) {
            $this->comment('💡 Для применения изменений запустите команду без --dry-run');
        }

        return 0;
    }

    /**
     * Анализирует текущее состояние ботов
     */
    private function analyzeCurrentState(): void
    {
        $this->info('📊 Анализ текущего состояния:');

        // Общее количество ботов
        $totalBots = Bot::count();
        $this->line("   Всего ботов: {$totalBots}");

        // Боты в подлокациях рудников
        $mineLocationBots = Bot::whereNotNull('mine_location_id')->count();
        $this->line("   Ботов в подлокациях рудников: {$mineLocationBots}");

        // Дублированные боты по имени
        $duplicates = Bot::select('name', DB::raw('COUNT(*) as count'))
            ->groupBy('name')
            ->having('count', '>', 1)
            ->get();

        if ($duplicates->count() > 0) {
            $this->warn("   ⚠️  Найдено дублированных имен: {$duplicates->count()}");
            foreach ($duplicates as $duplicate) {
                $this->line("      - {$duplicate->name}: {$duplicate->count} копий");
            }
        } else {
            $this->line("   ✅ Дублированных имен не найдено");
        }

        // Проблемные боты (в подлокациях, но с именем базовой локации)
        $problematicBots = $this->findProblematicBots();
        if ($problematicBots->count() > 0) {
            $this->warn("   ⚠️  Проблемных ботов (неправильная локация): {$problematicBots->count()}");
        } else {
            $this->line("   ✅ Проблемных ботов не найдено");
        }

        $this->newLine();
    }

    /**
     * Находит ботов с неправильной локацией
     */
    private function findProblematicBots()
    {
        return Bot::whereNotNull('mine_location_id')
            ->get()
            ->filter(function($bot) {
                $mineLocation = MineLocation::with('baseLocation')->find($bot->mine_location_id);
                return $mineLocation && 
                       $mineLocation->baseLocation && 
                       $bot->location === $mineLocation->baseLocation->name;
            });
    }

    /**
     * Исправляет ботов в подлокациях рудников
     */
    private function fixMineLocationBots(bool $isDryRun): void
    {
        $this->info('🔧 Исправление ботов в подлокациях рудников:');

        $problematicBots = $this->findProblematicBots();

        if ($problematicBots->count() === 0) {
            $this->line('   ✅ Все боты в подлокациях настроены правильно');
            return;
        }

        $this->line("   Найдено ботов для исправления: {$problematicBots->count()}");

        foreach ($problematicBots as $bot) {
            $mineLocation = MineLocation::with('baseLocation')->find($bot->mine_location_id);
            
            if ($mineLocation && $mineLocation->baseLocation) {
                $oldLocation = $bot->location;
                $newLocation = $mineLocation->name;
                
                $this->line("   - Бот '{$bot->name}' (ID: {$bot->id}):");
                $this->line("     Старая локация: '{$oldLocation}'");
                $this->line("     Новая локация: '{$newLocation}'");

                if (!$isDryRun) {
                    $bot->update(['location' => $newLocation]);
                    
                    // Синхронизация с Redis
                    $bot->syncToRedis();
                    
                    $this->line("     ✅ Исправлено");
                } else {
                    $this->line("     📝 Будет исправлено");
                }
            }
        }

        $this->newLine();
    }

    /**
     * Удаляет дублированных ботов
     */
    private function removeDuplicateBots(bool $isDryRun): void
    {
        $this->info('🗑️  Удаление дублированных ботов:');

        // Находим дублированные имена
        $duplicateNames = Bot::select('name', DB::raw('COUNT(*) as count'))
            ->groupBy('name')
            ->having('count', '>', 1)
            ->pluck('name');

        if ($duplicateNames->count() === 0) {
            $this->line('   ✅ Дублированных ботов не найдено');
            return;
        }

        $totalRemoved = 0;

        foreach ($duplicateNames as $name) {
            $bots = Bot::where('name', $name)->orderBy('id')->get();
            $keepBot = $bots->first(); // Оставляем бота с наименьшим ID
            $duplicatesToRemove = $bots->skip(1);

            $this->line("   Дублированные боты '{$name}':");
            $this->line("     Оставляем: ID {$keepBot->id} (локация: '{$keepBot->location}')");

            foreach ($duplicatesToRemove as $duplicate) {
                $this->line("     Удаляем: ID {$duplicate->id} (локация: '{$duplicate->location}')");
                
                if (!$isDryRun) {
                    $duplicate->delete();
                    $totalRemoved++;
                } else {
                    $totalRemoved++;
                }
            }
        }

        if ($isDryRun) {
            $this->line("   📝 Будет удалено ботов: {$totalRemoved}");
        } else {
            $this->line("   ✅ Удалено ботов: {$totalRemoved}");
        }

        $this->newLine();
    }

    /**
     * Очищает кеш Redis
     */
    private function clearRedisCache(): void
    {
        $this->info('🧹 Очистка кеша Redis:');

        try {
            // Попытка очистить только ключи связанные с ботами
            $redis = app('redis');
            $keys = $redis->keys('bot:*');
            
            if (!empty($keys)) {
                $redis->del($keys);
                $this->line("   ✅ Удалено ключей Redis: " . count($keys));
            } else {
                $this->line("   ℹ️  Ключи ботов в Redis не найдены");
            }

            // Также очищаем ключи локаций
            $locationKeys = $redis->keys('location:*');
            if (!empty($locationKeys)) {
                $redis->del($locationKeys);
                $this->line("   ✅ Удалено ключей локаций: " . count($locationKeys));
            }

        } catch (\Exception $e) {
            $this->warn("   ⚠️  Ошибка очистки Redis: " . $e->getMessage());
            $this->line("   💡 Вы можете очистить Redis вручную: redis-cli FLUSHALL");
        }

        $this->newLine();
    }
}
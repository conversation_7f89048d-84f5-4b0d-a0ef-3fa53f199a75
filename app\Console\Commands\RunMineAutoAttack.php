<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\MineAutoAttackJob;

/**
 * Команда для запуска автоатак мобов в рудниках
 */
class RunMineAutoAttack extends Command
{
    protected $signature = 'mine:auto-attack';
    protected $description = 'Запустить автоатаки мобов на замеченных игроков в рудниках';

    public function handle()
    {
        $this->info('Запуск автоатак мобов в рудниках...');
        
        try {
            // Проверяем, существует ли таблица mine_marks
            if (!\Illuminate\Support\Facades\Schema::hasTable('mine_marks')) {
                $this->warn('⚠️  Таблица mine_marks не существует, используем fallback режим');
                $this->info('💡 Для исправления запустите: php artisan migrate --path=database/migrations/2025_07_21_160000_create_mine_marks_table_fixed.php');
                
                // Используем fallback джоб
                \App\Jobs\MineAutoAttackJobFallback::dispatch();
                $this->info('✅ Задача автоатак мобов в рудниках поставлена в очередь (fallback режим)');
            } else {
                // Используем обычный джоб
                MineAutoAttackJob::dispatch();
                $this->info('✅ Задача автоатак мобов в рудниках поставлена в очередь');
            }
            
            return 0;
        } catch (\Exception $e) {
            $this->error('❌ Ошибка при запуске автоатак: ' . $e->getMessage());
            
            // Показываем дополнительную информацию для отладки
            if (strpos($e->getMessage(), 'mine_marks') !== false) {
                $this->warn('💡 Похоже на проблему с таблицей mine_marks');
                $this->info('🔧 Решения:');
                $this->info('   1. Запустите миграцию: run_mine_migrations.bat');
                $this->info('   2. Или создайте таблицу вручную: create_mine_marks_table.sql');
                $this->info('   3. Или запустите настройку: php setup_mine_detection_system.php');
            }
            
            return 1;
        }
    }
}
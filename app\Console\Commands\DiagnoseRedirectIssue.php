<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;

class DiagnoseRedirectIssue extends Command
{
    protected $signature = 'diagnose:redirect-issue';
    protected $description = 'Диагностирует проблему с редиректом победителя';

    public function handle()
    {
        $this->info('🔍 Диагностика проблемы с редиректом...');
        
        // Проверяем маршруты
        $this->info('📍 Проверка маршрутов:');
        
        $outpostRoutes = collect(Route::getRoutes())->filter(function ($route) {
            return str_contains($route->getName() ?? '', 'battle.outposts');
        });
        
        foreach ($outpostRoutes as $route) {
            $this->info("   - {$route->getName()}: {$route->uri()}");
        }
        
        // Проверяем конкретный маршрут battle.outposts.show
        $showRoute = Route::getRoutes()->getByName('battle.outposts.show');
        if ($showRoute) {
            $this->info("✅ Маршрут battle.outposts.show найден: {$showRoute->uri()}");
            $this->info("   Controller: " . $showRoute->getControllerClass());
            $this->info("   Action: " . $showRoute->getActionMethod());
            
            // Проверяем middleware
            $middleware = $showRoute->middleware();
            $this->info("   Middleware: " . implode(', ', $middleware));
        } else {
            $this->error("❌ Маршрут battle.outposts.show не найден!");
        }
        
        // Проверяем маршрут battle.outposts.index
        $indexRoute = Route::getRoutes()->getByName('battle.outposts.index');
        if ($indexRoute) {
            $this->info("✅ Маршрут battle.outposts.index найден: {$indexRoute->uri()}");
            $this->info("   Controller: " . $indexRoute->getControllerClass());
            $this->info("   Action: " . $indexRoute->getActionMethod());
        } else {
            $this->error("❌ Маршрут battle.outposts.index не найден!");
        }
        
        // Проверяем route helper
        $this->info('');
        $this->info('🔗 Проверка route helper:');
        
        try {
            $showUrl = route('battle.outposts.show', 1);
            $this->info("✅ route('battle.outposts.show', 1) = {$showUrl}");
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при генерации route('battle.outposts.show', 1): {$e->getMessage()}");
        }
        
        try {
            $indexUrl = route('battle.outposts.index');
            $this->info("✅ route('battle.outposts.index') = {$indexUrl}");
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при генерации route('battle.outposts.index'): {$e->getMessage()}");
        }
        
        // Проверяем паттерны URL
        $this->info('');
        $this->info('🌐 Анализ URL паттернов:');
        
        if (str_contains($showUrl ?? '', '/battle/outposts')) {
            $this->info("✅ URL battle.outposts.show содержит '/battle/outposts'");
        } else {
            $this->warn("⚠️ URL battle.outposts.show НЕ содержит '/battle/outposts'");
        }
        
        if (str_contains($indexUrl ?? '', '/battle/outposts')) {
            $this->info("✅ URL battle.outposts.index содержит '/battle/outposts'");
        } else {
            $this->warn("⚠️ URL battle.outposts.index НЕ содержит '/battle/outposts'");
        }
        
        // Проверяем проблемные middleware
        $this->info('');
        $this->info('🛡️ Проверка middleware:');
        
        $problematicMiddleware = [
            'CheckPlayerDefeat',
            'resetTarget.OnLocationChange',
            'check.user.health',
            'check.user.minimumHP'
        ];
        
        foreach ($problematicMiddleware as $middleware) {
            $this->info("   - {$middleware}: " . (class_exists("App\\Http\\Middleware\\{$middleware}") ? "✅ Найден" : "❌ Не найден"));
        }
        
        // Рекомендации
        $this->info('');
        $this->info('💡 Рекомендации для диагностики:');
        $this->info('1. Проверьте, что battle.outposts.show ведет на правильный контроллер');
        $this->info('2. Убедитесь, что middleware не перенаправляет на другую страницу');
        $this->info('3. Проверьте логи на наличие редиректов в момент победы');
        $this->info('4. Возможно, проблема в том, что battle.outposts.show и battle.outposts.index ведут на один URL');
        
        // Проверяем на дублирование URL
        if (($showUrl ?? '') === ($indexUrl ?? '')) {
            $this->error("❌ ПРОБЛЕМА: battle.outposts.show и battle.outposts.index ведут на один URL!");
            $this->error("   Это может быть причиной проблемы с редиректом");
        }
        
        $this->info('');
        $this->info('✅ Диагностика завершена');
    }
}
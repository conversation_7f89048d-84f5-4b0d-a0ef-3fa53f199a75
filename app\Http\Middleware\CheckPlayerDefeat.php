<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Log;

/**
 * Middleware для проверки статуса поражения игрока
 * Если у игрока статус поражения, перенаправляет на страницу поражения
 */
class CheckPlayerDefeat
{
    /**
     * Обрабатывает входящий запрос.
     * Если у игрока статус поражения, перенаправляет на страницу поражения.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Проверяем авторизацию
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Проверяем, есть ли у пользователя профиль
        if (!$user->profile) {
            return $next($request);
        }

        // Проверяем, является ли пользователь временным пользователем пролога
        // Для пользователей пролога не применяем проверку поражения
        if ($this->isPrologueUser($user)) {
            return $next($request);
        }

        // Получаем актуальные значения HP/MP пользователя с учетом регенерации
        $actualResources = $user->profile->getActualResources();
        $actualHp = $actualResources['current_hp'];

        // Проверяем, не находимся ли мы уже на странице поражения
        $isDefeatPage = $request->route()->getName() === 'battle.defeat';

        // Проверяем, не является ли текущий запрос запросом на возрождение
        $isRespawnRequest = $request->route()->getName() === 'battle.respawn';

        // Если мы уже на странице поражения или выполняем возрождение, пропускаем запрос
        // Контроллер battle.defeat сам проверит, имеет ли игрок право видеть эту страницу
        if ($isDefeatPage || $isRespawnRequest) {
            return $next($request);
        }

        // Проверяем флаг поражения в сессии или базе данных
        $isDefeatedInDb = false;
        try {
            $isDefeatedInDb = $user->profile->is_defeated ?? false;
        } catch (\Exception $e) {
            // Столбец еще не создан, используем только сессию
            $isDefeatedInDb = false;
        }
        
        if (session('is_defeated') === true || $isDefeatedInDb) {
            // Логируем перенаправление
            Log::info("Игрок #{$user->id} перенаправлен на страницу поражения из-за флага is_defeated", [
                'user_id' => $user->id,
                'current_route' => $request->route()->getName(),
                'current_hp' => $actualHp,
                'session_defeated' => session('is_defeated'),
                'db_defeated' => $user->profile->is_defeated
            ]);

            // Перенаправляем на страницу поражения
            return Redirect::route('battle.defeat');
        }

        // Если HP равно 0, но флаг поражения не установлен, устанавливаем его
        if ($actualHp <= 0 && !$isDefeatPage && !$isRespawnRequest) {
            // Проверяем, есть ли флаг недавней победы в сессии ПЕРВЫМ делом
            $hasRecentVictory = session('recent_victory_time') && 
                               (time() - session('recent_victory_time') < 10);
            
            // Если игрок недавно победил, НЕ устанавливаем флаг поражения
            if ($hasRecentVictory) {
                Log::info("Игрок #{$user->id} недавно победил, не устанавливаем флаг поражения", [
                    'user_id' => $user->id,
                    'current_hp' => $actualHp,
                    'recent_victory_time' => session('recent_victory_time'),
                    'current_time' => time(),
                    'victory_elapsed' => time() - session('recent_victory_time')
                ]);
                return $next($request);
            }
            
            // Проверяем, не является ли это результатом успешной атаки игрока
            $isAttackRequest = $request->isMethod('post') && 
                              (str_contains($request->url(), '/attack-player') || 
                               str_contains($request->url(), '/attack-bot') || 
                               str_contains($request->url(), '/attack-any-player') ||
                               str_contains($request->url(), '/retaliate'));
            
            // Проверяем, не является ли это GET запросом после атаки (перенаправление после победы)
            $isPostAttackRedirect = $request->isMethod('get') && 
                                   (str_contains($request->url(), '/battle/outposts/') || 
                                    str_contains($request->url(), '/battle/outposts'));
            
            // Проверяем, есть ли недавняя атака (в течение последних 5 секунд)
            $hasRecentAttack = session('recent_attack_time') && 
                              (time() - session('recent_attack_time') < 5);
            
            if ($isAttackRequest && $user->current_target_id) {
                // Игрок атаковал и возможно победил, не перенаправляем на поражение
                // Сохраняем время атаки для последующих проверок
                session(['recent_attack_time' => time()]);
                Log::info("Игрок #{$user->id} атаковал с низким HP, не перенаправляем на поражение", [
                    'user_id' => $user->id,
                    'current_hp' => $actualHp,
                    'target_id' => $user->current_target_id,
                    'target_type' => $user->current_target_type
                ]);
                return $next($request);
            }
            
            if ($isPostAttackRedirect && $hasRecentAttack) {
                // Игрок был перенаправлен после недавней атаки, не перенаправляем на поражение
                Log::info("Игрок #{$user->id} перенаправлен после атаки с низким HP, не перенаправляем на поражение", [
                    'user_id' => $user->id,
                    'current_hp' => $actualHp,
                    'has_recent_attack' => $hasRecentAttack,
                    'request_url' => $request->url()
                ]);
                return $next($request);
            }

            // Устанавливаем флаг поражения в сессии и базе данных
            session(['is_defeated' => true]);

            // Определяем информацию об убийце
            $killerType = session('killer_type');
            $killerId = session('killer_id');
            
            // Если информация об убийце не установлена в сессии, берем из базы данных
            if (!$killerType || !$killerId) {
                if ($user->last_attacker_id && $user->last_attacker_type) {
                    $killerType = $user->last_attacker_type;
                    $killerId = $user->last_attacker_id;
                    session([
                        'killer_type' => $killerType,
                        'killer_id' => $killerId
                    ]);
                } else {
                    $killerType = 'unknown';
                    $killerId = null;
                    session([
                        'killer_type' => $killerType,
                        'killer_id' => $killerId
                    ]);
                }
            }

            // Обновляем флаг поражения в базе данных (если столбцы существуют)
            try {
                $user->profile->update([
                    'is_defeated' => true,
                    'defeated_by_type' => $killerType,
                    'defeated_by_id' => $killerId,
                    'defeated_at' => now()
                ]);
            } catch (\Exception $e) {
                // Столбцы еще не созданы, пропускаем
                Log::warning("Не удалось обновить поля поражения в middleware: " . $e->getMessage());
            }

            // Логируем установку флага поражения
            Log::info("Установлен флаг поражения для игрока #{$user->id}", [
                'user_id' => $user->id,
                'current_hp' => $actualHp,
                'killer_type' => session('killer_type'),
                'killer_id' => session('killer_id')
            ]);

            // Перенаправляем на страницу поражения
            return Redirect::route('battle.defeat');
        }

        return $next($request);
    }

    /**
     * Проверяет, является ли пользователь временным пользователем пролога
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    private function isPrologueUser($user): bool
    {
        // Проверяем по паттерну email временных пользователей
        return str_starts_with($user->email, 'temp_') && str_ends_with($user->email, '@prologue.local');
    }
}

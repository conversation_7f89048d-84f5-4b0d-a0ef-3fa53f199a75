<?php

/**
 * Скрипт для тестирования исправления дебафа обнаружения в кастомных рудниках
 * 
 * Этот скрипт проверяет:
 * 1. Наличие нужных таблиц (mine_marks, mine_locations, spawned_resources)
 * 2. Состояние тестового пользователя и его ресурсов
 * 3. Активные метки обнаружения в рудниках
 * 4. Логи применения дебафов
 */

require __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\SpawnedResource;
use App\Models\MineMark;
use App\Services\MineDetectionService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЯ ДЕБАФА В КАСТОМНЫХ РУДНИКАХ ===\n\n";

try {
    // 1. Проверяем наличие необходимых таблиц
    echo "1. Проверка таблиц:\n";
    
    $tables = ['mine_marks', 'mine_locations', 'spawned_resources', 'active_effects'];
    foreach ($tables as $table) {
        $exists = Schema::hasTable($table);
        echo "   - $table: " . ($exists ? "✅ Существует" : "❌ Не найдена") . "\n";
    }
    echo "\n";

    // 2. Проверяем существование кастомных рудников
    echo "2. Проверка кастомных рудников:\n";
    $mineLocations = MineLocation::where('is_active', true)->take(3)->get();
    
    if ($mineLocations->isEmpty()) {
        echo "   ❌ Активных рудников не найдено!\n\n";
    } else {
        foreach ($mineLocations as $mine) {
            echo "   - ID: {$mine->id}, Slug: {$mine->slug}, Название: {$mine->name}\n";
        }
        echo "\n";
    }

    // 3. Проверяем ресурсы в рудниках
    echo "3. Проверка ресурсов в рудниках:\n";
    $resources = SpawnedResource::with('resource', 'mineLocation')
        ->where('is_active', true)
        ->whereNotNull('mine_location_id')
        ->take(5)->get();
    
    if ($resources->isEmpty()) {
        echo "   ❌ Активных ресурсов в рудниках не найдено!\n\n";
    } else {
        foreach ($resources as $resource) {
            $mineName = $resource->mineLocation->name ?? 'Unknown';
            $resourceName = $resource->resource->name ?? 'Unknown';
            echo "   - Рудник: {$mineName}, Ресурс: {$resourceName}, Прочность: {$resource->durability}/{$resource->max_durability}\n";
        }
        echo "\n";
    }

    // 4. Проверяем admin пользователя
    echo "4. Проверка тестового пользователя 'admin':\n";
    $admin = User::where('name', 'admin')->first();
    
    if (!$admin) {
        echo "   ❌ Пользователь 'admin' не найден!\n\n";
    } else {
        echo "   - ID: {$admin->id}, Имя: {$admin->name}\n";
        echo "   - Текущая цель: {$admin->current_target_type} (ID: {$admin->current_target_id})\n";
        echo "   - Локация: {$admin->location_id}\n\n";
    }

    // 5. Проверяем активные метки обнаружения
    echo "5. Проверка активных меток обнаружения:\n";
    
    if (Schema::hasTable('mine_marks')) {
        $activeMarks = MineMark::where('is_active', true)
            ->where('expires_at', '>', now())
            ->with(['player', 'mineLocation'])
            ->get();
        
        if ($activeMarks->isEmpty()) {
            echo "   ℹ️ Активных меток обнаружения нет\n\n";
        } else {
            foreach ($activeMarks as $mark) {
                $playerName = $mark->player->name ?? 'Unknown';
                $mineName = $mark->mineLocation->name ?? 'Unknown';
                $timeRemaining = $mark->expires_at->diffInSeconds(now());
                echo "   - Игрок: {$playerName}, Рудник: {$mineName}, Истекает через: {$timeRemaining} сек\n";
            }
            echo "\n";
        }
    } else {
        echo "   ⚠️ Таблица mine_marks не найдена, система работает через active_effects\n\n";
    }

    // 6. Тест создания метки через сервис
    echo "6. Тест создания метки через MineDetectionService:\n";
    
    if ($admin && !$mineLocations->isEmpty()) {
        $testMine = $mineLocations->first();
        
        try {
            $service = app(MineDetectionService::class);
            $mark = $service->applyDetectionDebuff($admin, $testMine);
            
            if ($mark) {
                echo "   ✅ Метка успешно создана!\n";
                echo "   - ID метки: {$mark->id}\n";
                echo "   - Игрок: {$admin->name}\n";
                echo "   - Рудник: {$testMine->name}\n";
                echo "   - Истекает: {$mark->expires_at}\n";
                
                // Сразу удаляем тестовую метку
                $mark->delete();
                echo "   - Тестовая метка удалена\n";
            } else {
                echo "   ❌ Не удалось создать метку\n";
            }
        } catch (\Exception $e) {
            echo "   ❌ Ошибка при создании метки: " . $e->getMessage() . "\n";
        }
        echo "\n";
    } else {
        echo "   ⚠️ Пропущен - нет пользователя admin или рудников\n\n";
    }

    // 7. Проверка логов
    echo "7. Анализ последних логов (поиск записей о дебафах):\n";
    
    // Ищем в логах записи о дебафах за последние 10 минут
    $logPath = storage_path('logs/laravel.log');
    
    if (file_exists($logPath)) {
        $logContent = file_get_contents($logPath);
        $lines = explode("\n", $logContent);
        
        // Фильтруем строки с MINE DEBUG за последние записи
        $mineDebugLines = array_filter($lines, function($line) {
            return strpos($line, 'MINE DEBUG') !== false && 
                   strpos($line, date('Y-m-d')) !== false;
        });
        
        if (empty($mineDebugLines)) {
            echo "   ℹ️ Записей о дебафах рудников сегодня не найдено\n";
        } else {
            echo "   📄 Найдено записей о дебафах: " . count($mineDebugLines) . "\n";
            
            // Показываем последние 3 записи
            $recentLogs = array_slice($mineDebugLines, -3);
            foreach ($recentLogs as $log) {
                // Извлекаем время из лога
                if (preg_match('/\[([\d\-\s:]+)\]/', $log, $matches)) {
                    $time = $matches[1];
                    echo "   - {$time}: " . (strlen($log) > 100 ? substr($log, 0, 100) . '...' : $log) . "\n";
                }
            }
        }
        echo "\n";
    } else {
        echo "   ⚠️ Файл логов не найден: $logPath\n\n";
    }

    // 8. Рекомендации по тестированию
    echo "8. Рекомендации по тестированию:\n";
    echo "   1. Войдите в игру под пользователем 'admin' (пароль: qwe123)\n";
    echo "   2. Перейдите в любой кастомный рудник: http://127.0.0.1:8000/battle/mines/{slug}\n";
    echo "   3. Выберите ресурс и нажмите 'Добыть'\n";
    echo "   4. Проверьте сообщение - должно появиться: 'Вы были замечены при добыче ресурса!'\n";
    echo "   5. Проверьте логи: tail -f storage/logs/laravel.log | grep 'MINE DEBUG'\n";
    
    if (!$mineLocations->isEmpty()) {
        $firstMine = $mineLocations->first();
        echo "   6. Прямая ссылка для тестирования: http://127.0.0.1:8000/battle/mines/{$firstMine->slug}\n";
    }
    
    echo "\n";

    echo "=== ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка при выполнении теста: " . $e->getMessage() . "\n";
    echo "Трассировка: " . $e->getTraceAsString() . "\n";
}
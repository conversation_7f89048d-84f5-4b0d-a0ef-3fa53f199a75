<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;
use App\Models\User;

class DebugMinesPagination extends Command
{
    protected $signature = 'debug:mines-pagination';
    protected $description = 'Отладка пагинации рудников для понимания проблемы';

    public function handle()
    {
        $this->info('=== ОТЛАДКА ПАГИНАЦИИ РУДНИКОВ ===');

        // Проверяем общее количество рудников
        $totalMines = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->count();
        
        $this->info("Общее количество активных рудников: $totalMines");

        if ($totalMines == 0) {
            $this->error('В базе данных нет активных рудников!');
            $this->info('Создайте рудники через админку: /admin/mine-locations/create');
            return;
        }

        // Симулируем запрос как в контроллере
        $customMineLocations = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->with('baseLocation')
            ->orderBy('order')
            ->paginate(4);

        $this->info("Результаты пагинации:");
        $this->info("- Текущая страница: " . $customMineLocations->currentPage());
        $this->info("- Всего страниц: " . $customMineLocations->lastPage());
        $this->info("- Всего элементов: " . $customMineLocations->total());
        $this->info("- Элементов на странице: " . $customMineLocations->count());
        $this->info("- Есть страницы: " . ($customMineLocations->hasPages() ? 'ДА' : 'НЕТ'));
        $this->info("- Есть еще страницы: " . ($customMineLocations->hasMorePages() ? 'ДА' : 'НЕТ'));

        // Показываем рудники на текущей странице
        $this->info("\nРудники на текущей странице:");
        foreach ($customMineLocations as $index => $mine) {
            $this->info("  " . ($index + 1) . ". {$mine->name} (ID: {$mine->id}, slug: {$mine->slug})");
            $this->info("      Активен: " . ($mine->is_active ? 'ДА' : 'НЕТ'));
            $this->info("      Родитель: " . ($mine->parent_id ? $mine->parent_id : 'НЕТ'));
            $this->info("      Порядок: " . $mine->order);
            if ($mine->baseLocation) {
                $this->info("      Базовая локация: {$mine->baseLocation->name}");
            } else {
                $this->warn("      Базовая локация: НЕ НАЙДЕНА");
            }
        }

        // Проверяем условие отображения пагинации
        $shouldShowPagination = isset($customMineLocations) && $customMineLocations->hasPages();
        $this->info("\nПоказывать пагинацию: " . ($shouldShowPagination ? 'ДА' : 'НЕТ'));

        if (!$shouldShowPagination) {
            $this->warn('ПРОБЛЕМА: Пагинация не будет отображена!');
            
            if ($totalMines <= 4) {
                $this->warn("Причина: Недостаточно рудников ($totalMines <= 4)");
                $this->info("Решение: Создайте больше рудников через админку");
            } else {
                $this->warn("Причина: Неизвестная проблема с пагинацией");
            }
        }

        // Проверяем статус всех рудников
        $this->info("\nВсе рудники в базе:");
        $allMines = MineLocation::whereNull('parent_id')->get();
        foreach ($allMines as $mine) {
            $status = $mine->is_active ? 'АКТИВЕН' : 'НЕАКТИВЕН';
            $this->info("  {$mine->name} - $status (ID: {$mine->id})");
        }

        // Создаем тестовые рудники если их мало
        if ($totalMines < 5) {
            $this->warn("\nСоздаем тестовые рудники для демонстрации пагинации...");
            
            // Получаем первую базовую локацию
            $baseLocation = \App\Models\Location::first();
            if (!$baseLocation) {
                $this->error('Не найдена базовая локация для создания тестовых рудников');
                return;
            }

            for ($i = 1; $i <= 6; $i++) {
                $mineExists = MineLocation::where('slug', "test-mine-$i")->exists();
                if (!$mineExists) {
                    MineLocation::create([
                        'name' => "Тестовый рудник $i",
                        'slug' => "test-mine-$i",
                        'description' => "Тестовый рудник для демонстрации пагинации",
                        'base_location_id' => $baseLocation->id,
                        'is_active' => true,
                        'order' => $i,
                        'parent_id' => null,
                    ]);
                    $this->info("Создан тестовый рудник $i");
                }
            }

            $this->info("\nТестовые рудники созданы. Проверьте пагинацию еще раз.");
        }

        // Финальная проверка после создания
        $finalCount = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->count();
        
        $this->info("\nФинальное количество рудников: $finalCount");
        $this->info("Пагинация должна работать: " . ($finalCount > 4 ? 'ДА' : 'НЕТ'));

        return 0;
    }
}
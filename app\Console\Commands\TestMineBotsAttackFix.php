<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Bot;
use App\Models\User;
use App\Models\Location;
use App\Services\BotBehaviorService;
use Illuminate\Support\Facades\Log;

/**
 * Команда для тестирования исправления атак ботов в рудниках
 * Проверяет новую логику принудительных атак враждебных рас
 */
class TestMineBotsAttackFix extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:mine-bots-attack-fix {--verbose : Подробный вывод}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Тестирует исправление атак ботов в рудниках - принудительные атаки враждебных рас только в своей подлокации';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔧 Тестирование исправления атак ботов в рудниках');
        $this->info('================================================');

        $botBehaviorService = app(BotBehaviorService::class);
        $verbose = $this->option('verbose');

        // Получаем все рудники
        $mineLocations = Location::where('location_type', 'mine')
            ->where('is_active', true)
            ->get();

        if ($mineLocations->isEmpty()) {
            $this->error('❌ Не найдено активных рудников в базе данных');
            return 1;
        }

        $this->info("📍 Найдено рудников: {$mineLocations->count()}");

        $totalTests = 0;
        $passedTests = 0;
        $failedTests = 0;

        foreach ($mineLocations as $location) {
            $this->info("\n🏔️ Тестирование рудника: {$location->name}");
            $this->info('----------------------------------------');

            // Получаем ботов в этом руднике
            $bots = Bot::where('location', $location->name)->get();

            if ($bots->isEmpty()) {
                $this->warn("⚠️ В руднике {$location->name} нет ботов");
                continue;
            }

            // Получаем игроков в этом руднике
            $players = User::whereHas('statistics', function ($q) use ($location) {
                $q->where('current_location', $location->name);
            })->where('is_online', true)->with('profile')->get();

            if ($players->isEmpty()) {
                $this->warn("⚠️ В руднике {$location->name} нет игроков");
                continue;
            }

            $this->info("🤖 Ботов в руднике: {$bots->count()}");
            $this->info("👥 Игроков в руднике: {$players->count()}");

            // Тестируем каждого бота против каждого игрока
            foreach ($bots as $bot) {
                foreach ($players as $player) {
                    $totalTests++;

                    $result = $this->testBotAttack($bot, $player, $botBehaviorService, $verbose);
                    
                    if ($result['passed']) {
                        $passedTests++;
                    } else {
                        $failedTests++;
                    }

                    if ($verbose) {
                        $status = $result['passed'] ? '✅' : '❌';
                        $this->line("{$status} {$result['message']}");
                    }
                }
            }
        }

        // Итоги тестирования
        $this->info("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ");
        $this->info('==========================');
        $this->info("Всего тестов: {$totalTests}");
        $this->info("Прошли: {$passedTests}");
        $this->info("Провалены: {$failedTests}");

        $successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
        $this->info("Успешность: {$successRate}%");

        if ($failedTests === 0) {
            $this->info('🎉 Все тесты прошли успешно! Исправление работает корректно.');
            return 0;
        } else {
            $this->error("❌ {$failedTests} тестов провалены. Требуется дополнительная проверка.");
            return 1;
        }
    }

    /**
     * Тестирует атаку конкретного бота на конкретного игрока
     *
     * @param Bot $bot
     * @param User $player
     * @param BotBehaviorService $service
     * @param bool $verbose
     * @return array
     */
    private function testBotAttack(Bot $bot, User $player, BotBehaviorService $service, bool $verbose): array
    {
        try {
            $botRace = $bot->race ?? 'unknown';
            $playerRace = $player->profile->race ?? 'unknown';

            // Получаем локации для анализа
            $botLocation = $bot->location;
            $playerLocation = $player->statistics->current_location ?? 'unknown';
            
            // Проверяем, находятся ли в одной подлокации
            $sameSubLocation = $botLocation === $playerLocation;
            
            // Определяем, должны ли расы быть враждебными
            $shouldBeEnemies = $this->areEnemyRaces($botRace, $playerRace);

            // Проверяем, что бот атакует
            $shouldAttack = $service->shouldBotAttack($bot, $player);

            // Логика проверки
            if (!$sameSubLocation) {
                // Разные подлокации - бот НЕ должен атаковать независимо от расы
                if (!$shouldAttack) {
                    return [
                        'passed' => true,
                        'message' => "Бот {$bot->name} корректно НЕ атакует игрока {$player->name} из другой подлокации ({$botLocation} ≠ {$playerLocation})"
                    ];
                } else {
                    return [
                        'passed' => false,
                        'message' => "❌ ОШИБКА: Бот {$bot->name} атакует игрока {$player->name} из другой подлокации ({$botLocation} ≠ {$playerLocation})"
                    ];
                }
            }
            
            // Одинаковая подлокация - проверяем расы
            if ($shouldBeEnemies) {
                // Враждебные расы в одной подлокации - бот ДОЛЖЕН атаковать
                if ($shouldAttack) {
                    return [
                        'passed' => true,
                        'message' => "Бот {$bot->name} ({$botRace}) корректно атакует враждебного игрока {$player->name} ({$playerRace}) в подлокации {$botLocation}"
                    ];
                } else {
                    return [
                        'passed' => false,
                        'message' => "❌ ОШИБКА: Бот {$bot->name} ({$botRace}) НЕ атакует враждебного игрока {$player->name} ({$playerRace}) в подлокации {$botLocation}"
                    ];
                }
            } else {
                // Союзные расы в одной подлокации - бот НЕ должен атаковать
                if (!$shouldAttack) {
                    return [
                        'passed' => true,
                        'message' => "Бот {$bot->name} ({$botRace}) корректно НЕ атакует союзного игрока {$player->name} ({$playerRace}) в подлокации {$botLocation}"
                    ];
                } else {
                    return [
                        'passed' => false,
                        'message' => "❌ ОШИБКА: Бот {$bot->name} ({$botRace}) атакует союзного игрока {$player->name} ({$playerRace}) в подлокации {$botLocation}"
                    ];
                }
            }

        } catch (\Exception $e) {
            return [
                'passed' => false,
                'message' => "❌ ИСКЛЮЧЕНИЕ: Ошибка при тестировании бота {$bot->name} против игрока {$player->name}: {$e->getMessage()}"
            ];
        }
    }

    /**
     * Проверяет, являются ли расы враждебными
     *
     * @param string $race1
     * @param string $race2
     * @return bool
     */
    private function areEnemyRaces(string $race1, string $race2): bool
    {
        $raceRelations = [
            'solarius' => ['lunarius'],
            'lunarius' => ['solarius'],
        ];

        $enemyRaces = $raceRelations[$race1] ?? [];
        return in_array($race2, $enemyRaces, true);
    }
}
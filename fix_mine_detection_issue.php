<?php

/**
 * Исправление проблемы с системой обнаружения в рудниках
 * 
 * Скрипт диагностирует и исправляет проблемы с дебафом "Замечен" и атаками мобов
 */

require_once __DIR__ . '/vendor/autoload.php';

// Инициализируем Laravel приложение
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Models\Mob;
use App\Services\MineDetectionService;
use App\Services\MineDetectionServiceFallback;

echo "🔧 ДИАГНОСТИКА И ИСПРАВЛЕНИЕ СИСТЕМЫ ОБНАРУЖЕНИЯ В РУДНИКАХ\n";
echo "========================================================\n\n";

try {
    // 1. Проверяем таблицу mine_marks
    echo "1️⃣ Проверка таблицы mine_marks...\n";
    
    $mineMarksTableExists = Schema::hasTable('mine_marks');
    echo "   Таблица mine_marks: " . ($mineMarksTableExists ? '✅ Существует' : '❌ Отсутствует') . "\n";
    
    if (!$mineMarksTableExists) {
        echo "   🔧 Создание таблицы mine_marks...\n";
        
        try {
            // Запускаем миграцию
            $migrationPath = 'database/migrations/2025_07_21_160000_create_mine_marks_table_fixed.php';
            if (file_exists($migrationPath)) {
                $exitCode = shell_exec("php artisan migrate --path={$migrationPath} 2>&1");
                echo "   📋 Результат миграции: {$exitCode}\n";
                
                // Проверяем повторно
                $mineMarksTableExists = Schema::hasTable('mine_marks');
                echo "   ✅ Таблица создана: " . ($mineMarksTableExists ? 'Да' : 'Нет') . "\n";
            } else {
                echo "   ❌ Файл миграции не найден: {$migrationPath}\n";
            }
        } catch (\Exception $e) {
            echo "   ❌ Ошибка создания таблицы: " . $e->getMessage() . "\n";
        }
    }
    
    // 2. Проверяем текущие дебафы/метки
    echo "\n2️⃣ Проверка активных дебафов обнаружения...\n";
    
    if ($mineMarksTableExists) {
        $activeMarks = MineMark::where('is_active', true)
            ->where('expires_at', '>', now())
            ->count();
        echo "   Активных меток (mine_marks): {$activeMarks}\n";
    } else {
        $activeMarks = 0;
    }
    
    // Проверяем fallback (ActiveEffect)
    $activeEffects = ActiveEffect::where('effect_type', 'mine_detection_debuff')
        ->where('ends_at', '>', now())
        ->count();
    echo "   Активных дебафов (active_effects): {$activeEffects}\n";
    
    // 3. Проверяем локации рудников
    echo "\n3️⃣ Проверка локаций рудников...\n";
    
    $mineLocations = MineLocation::where('is_active', true)->count();
    echo "   Активных локаций рудников: {$mineLocations}\n";
    
    if ($mineLocations == 0) {
        echo "   ⚠️ Нет активных локаций рудников!\n";
    }
    
    // 4. Проверяем мобов в рудниках
    echo "\n4️⃣ Проверка мобов в рудниках...\n";
    
    $mineMobs = Mob::whereNotNull('mine_location_id')
        ->where('current_health', '>', 0)
        ->count();
    echo "   Живых мобов в рудниках: {$mineMobs}\n";
    
    if ($mineMobs == 0) {
        echo "   ⚠️ Нет живых мобов в рудниках!\n";
    }
    
    // 5. Тестируем сервисы
    echo "\n5️⃣ Тестирование сервисов обнаружения...\n";
    
    $user = User::with('profile')->first();
    $mineLocation = MineLocation::where('is_active', true)->first();
    
    if ($user && $mineLocation) {
        echo "   Тестируем с пользователем: {$user->name} (ID: {$user->id})\n";
        echo "   Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";
        
        // Тестируем основной сервис или fallback
        if ($mineMarksTableExists) {
            echo "   🎯 Использование MineDetectionService...\n";
            $service = app(MineDetectionService::class);
            
            try {
                $mark = $service->applyDetectionDebuff($user, $mineLocation);
                if ($mark) {
                    echo "   ✅ Дебаф успешно применен (MineMark ID: {$mark->id})\n";
                    echo "   ⏰ Истекает в: {$mark->expires_at}\n";
                } else {
                    echo "   ❌ Не удалось применить дебаф\n";
                }
            } catch (\Exception $e) {
                echo "   ❌ Ошибка при применении дебафа: " . $e->getMessage() . "\n";
            }
        } else {
            echo "   🎯 Использование MineDetectionServiceFallback...\n";
            $service = app(MineDetectionServiceFallback::class);
            
            try {
                $effect = $service->applyDetectionDebuff($user, $mineLocation);
                if ($effect) {
                    echo "   ✅ Дебаф успешно применен (ActiveEffect ID: {$effect->id})\n";
                    echo "   ⏰ Истекает в: {$effect->ends_at}\n";
                } else {
                    echo "   ❌ Не удалось применить дебаф\n";
                }
            } catch (\Exception $e) {
                echo "   ❌ Ошибка при применении дебафа: " . $e->getMessage() . "\n";
            }
        }
        
        // Проверяем обнаружение игрока
        echo "   🔍 Проверка обнаружения игрока...\n";
        $isDetected = $mineMarksTableExists 
            ? app(MineDetectionService::class)->isPlayerDetected($user->id, $mineLocation->location_id, $mineLocation->id)
            : app(MineDetectionServiceFallback::class)->isPlayerDetected($user->id, $mineLocation->location_id, $mineLocation->id);
        
        echo "   📊 Игрок обнаружен: " . ($isDetected ? '✅ Да' : '❌ Нет') . "\n";
        
    } else {
        echo "   ⚠️ Нет данных для тестирования (пользователей или локаций рудников)\n";
    }
    
    // 6. Проверяем планировщик
    echo "\n6️⃣ Проверка планировщика...\n";
    
    if (PHP_OS_FAMILY === 'Windows') {
        $schedulerProcess = shell_exec('wmic process where "name=\'php.exe\'" get commandline 2>nul | findstr "schedule:work"');
    } else {
        $schedulerProcess = shell_exec('ps aux | grep "artisan schedule:work" | grep -v grep');
    }
    
    if (empty($schedulerProcess)) {
        echo "   ❌ Планировщик НЕ ЗАПУЩЕН!\n";
        echo "   💡 Для запуска используйте: php artisan schedule:work\n";
    } else {
        echo "   ✅ Планировщик запущен\n";
    }
    
    // 7. Проверяем команду автоатак
    echo "\n7️⃣ Тестирование команды автоатак...\n";
    
    try {
        $command = 'php artisan mine:auto-attack';
        echo "   🚀 Выполнение: {$command}\n";
        
        $output = shell_exec("{$command} 2>&1");
        echo "   📋 Результат:\n";
        
        $lines = explode("\n", trim($output));
        foreach ($lines as $line) {
            if (!empty($line)) {
                echo "      {$line}\n";
            }
        }
    } catch (\Exception $e) {
        echo "   ❌ Ошибка выполнения команды: " . $e->getMessage() . "\n";
    }
    
    // 8. Рекомендации по исправлению
    echo "\n8️⃣ РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ:\n";
    echo "================================\n";
    
    $issues = [];
    $fixes = [];
    
    if (!$mineMarksTableExists) {
        $issues[] = "Отсутствует таблица mine_marks";
        $fixes[] = "Выполните: php artisan migrate --path=database/migrations/2025_07_21_160000_create_mine_marks_table_fixed.php";
    }
    
    if ($mineLocations == 0) {
        $issues[] = "Нет активных локаций рудников";
        $fixes[] = "Проверьте таблицу mine_locations и активируйте нужные локации";
    }
    
    if ($mineMobs == 0) {
        $issues[] = "Нет живых мобов в рудниках";
        $fixes[] = "Добавьте мобов в рудники или воскресите их командой: php artisan mobs:respawn";
    }
    
    if (empty($schedulerProcess)) {
        $issues[] = "Планировщик не запущен";
        $fixes[] = "Запустите планировщик: php artisan schedule:work";
    }
    
    if (!empty($issues)) {
        echo "❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ:\n";
        foreach ($issues as $i => $issue) {
            echo "   " . ($i + 1) . ". {$issue}\n";
        }
        
        echo "\n🔧 ИСПРАВЛЕНИЯ:\n";
        foreach ($fixes as $i => $fix) {
            echo "   " . ($i + 1) . ". {$fix}\n";
        }
    } else {
        echo "✅ Проблем не обнаружено! Система должна работать корректно.\n";
    }
    
    echo "\n9️⃣ КОМАНДЫ ДЛЯ МОНИТОРИНГА:\n";
    echo "=========================\n";
    echo "• Тестирование системы: php test_mine_detection_system.php\n";
    echo "• Ручной запуск атак: php artisan mine:auto-attack\n";
    echo "• Проверка меток: SELECT * FROM mine_marks WHERE is_active = 1;\n";
    echo "• Проверка дебафов: SELECT * FROM active_effects WHERE effect_type = 'mine_detection_debuff';\n";
    echo "• Логи планировщика: tail -f storage/logs/mine-auto-attacks.log\n";
    
    echo "\n🎉 ДИАГНОСТИКА ЗАВЕРШЕНА!\n";
    
} catch (Exception $e) {
    echo "\n❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    echo "📍 Файл: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "📜 Трассировка:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
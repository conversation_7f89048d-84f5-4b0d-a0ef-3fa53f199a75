<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Вход - Echoes of Eternity</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    {{-- Основной контейнер в стиле группы --}}
    <div class="container max-w-md mx-auto px-1 py-3 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
         style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">
        
        {{-- Заголовок в стиле локации --}}
        <div class="text-center mb-6 bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-4">
            <h1 class="text-[#fceac4] text-xl font-bold mb-2" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                Вход
            </h1>
            <p class="text-[#a6925e] text-sm">Войдите в мир Echoes of Eternity</p>
        </div>

        {{-- Сообщения об ошибках --}}
        @if ($errors->any())
            <div class="bg-gradient-to-r from-[#4a1f1a]/20 to-[#5e2b26]/20 border border-[#4a1f1a]/40 rounded-lg p-3 mb-4">
                @foreach ($errors->all() as $error)
                    <p class="text-[#d4675a] text-sm font-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                        {{ $error }}
                    </p>
                @endforeach
            </div>
        @endif

        {{-- Форма авторизации в стиле группы --}}
        <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-4">
            <form action="{{ route('auth.login.submit') }}" method="POST" class="space-y-4">
                @csrf

                {{-- Поле логина --}}
                <div>
                    <label for="name" class="block text-[#fceac4] font-semibold text-sm mb-1">Логин</label>
                    <input type="text" id="name" name="name"
                        class="w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                               focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                               transition-all duration-200"
                        style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                        required>
                </div>

                {{-- Поле пароля --}}
                <div>
                    <label for="password" class="block text-[#fceac4] font-semibold text-sm mb-1">Пароль</label>
                    <input type="password" id="password" name="password"
                        class="w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                               focus:outline-none focus:border-[#c1a96e] focus:ring-2 focus:ring-[#c1a96e]/20 
                               transition-all duration-200"
                        style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);"
                        required>
                </div>

                {{-- Кнопки --}}
                <div class="flex flex-col space-y-2 pt-2">
                    <button type="submit"
                        class="w-full py-3 px-4 font-bold text-[#f8eac2] transition-all duration-200
                               bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3e5c48] hover:to-[#243c2f]
                               border-2 border-[#3b3629] hover:border-[#c1a96e]/50 rounded-lg
                               shadow-lg hover:shadow-xl"
                        style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                        Войти в мир
                    </button>
                    
                
                </div>
            </form>

            {{-- Разделитель --}}
            <div class="border-t border-[#3b3629] my-4"></div>

            {{-- Навигационные ссылки --}}
            <div class="text-center space-y-2">
                <a href="{{ route('auth.register') }}" 
                   class="block text-[#c1a96e] hover:text-[#e4d7b0] text-sm transition-colors duration-200"
                   style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                    Нет аккаунта? Зарегестрироваться
                </a>
                <a href="#" 
                   class="block text-[#998d66] hover:text-[#c1a96e] text-sm transition-colors duration-200"
                   style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                    Восстановить
                </a>
            </div>
        </div>
    </div>

    {{-- Футер для неавторизованных --}}
    <x-auth.footer />
</body>

</html>
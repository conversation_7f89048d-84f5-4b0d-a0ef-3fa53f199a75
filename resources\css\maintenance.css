/* 
 * Стили для страницы технических работ
 * Стилизовано в соответствии с существующими модальными окнами проекта
 */

/* Основные стили страницы */
.maintenance-body {
    @apply m-0 p-0 min-h-screen;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1814 0%, #2f2d2b 100%);
    overflow: hidden;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Контейнер страницы */
.maintenance-container {
    @apply fixed inset-0 flex items-center justify-center p-4;
    z-index: 9999;
}

/* Затемненный фон с эффектом размытия */
.maintenance-backdrop {
    @apply fixed inset-0;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* Модальное окно */
.maintenance-modal {
    @apply relative bg-[#1a1814] rounded-lg shadow-2xl border border-[#514b3c] max-w-lg w-full mx-auto;
    transform: scale(0.9) translateY(-20px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8),
        0 0 0 1px rgba(161, 146, 94, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.maintenance-modal.show {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Заголовок */
.maintenance-header {
    @apply text-center p-8 pb-4;
}

.maintenance-icon {
    @apply mb-4 flex justify-center;
}

.maintenance-title {
    @apply text-2xl font-bold text-[#d9d3b8] mb-0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Анимация шестеренки */
.gear-animation {
    @apply text-[#a6925e];
    animation: rotate 3s linear infinite;
    filter: drop-shadow(0 0 8px rgba(166, 146, 94, 0.4));
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Основное содержимое */
.maintenance-content {
    @apply px-8 pb-6;
}

/* Персонализированное приветствие */
.maintenance-greeting {
    @apply mb-4 text-center;
}

.greeting-text {
    @apply text-[#ffd700] text-lg font-semibold;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Сообщение о технических работах */
.maintenance-message {
    @apply mb-6 text-center;
}

.maintenance-message p {
    @apply text-[#d9d3b8] text-base leading-relaxed;
    margin: 0;
}

/* Информация для пользователя */
.user-help-info {
    @apply mt-4 p-4 bg-[#2a2721] border border-[#a6925e] rounded-lg;
    box-shadow: 0 0 10px rgba(166, 146, 94, 0.2);
}

.help-text {
    @apply text-[#d9d3b8] text-sm leading-relaxed;
    margin: 0;
}

.help-text strong {
    @apply text-[#ffd700];
}

/* Время завершения */
.maintenance-completion {
    @apply mb-6 p-4 bg-[#252117] border border-[#514b3c] rounded-lg text-center;
}

.completion-label {
    @apply text-[#a6925e] text-sm font-medium mb-2;
}

.completion-time {
    @apply text-[#d9d3b8] text-lg font-bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Дополнительная информация */
.maintenance-info {
    @apply mb-6 space-y-3;
}

.info-item {
    @apply flex items-start space-x-3 text-[#b8b3a0] text-sm;
    word-wrap: break-word;
}

.info-icon {
    @apply text-[#a6925e] flex-shrink-0 mt-0.5;
    width: 16px;
    height: 16px;
}

/* Кнопка обновления */
.maintenance-actions {
    @apply text-center mb-6;
}

.refresh-button {
    @apply inline-flex items-center justify-center space-x-2 bg-[#613f36] text-[#ffeac1] py-3 px-6 rounded-lg shadow-md;
    @apply hover:bg-[#714a41] focus:outline-none focus:ring-2 focus:ring-[#a6925e] focus:ring-opacity-50;
    @apply transition-all duration-200 transform hover:scale-105;
    @apply min-w-0 min-h-[44px];
    border: 1px solid rgba(166, 146, 94, 0.2);
    touch-action: manipulation;
}

.refresh-button:active {
    transform: scale(0.98);
}

.refresh-icon {
    @apply flex-shrink-0;
    animation: spin-slow 2s linear infinite;
    width: 20px;
    height: 20px;
}

/* Адаптивные размеры иконок */
@media (max-width: 768px) {
    .refresh-icon {
        width: 18px;
        height: 18px;
    }
}

@media (max-width: 480px) {
    .refresh-icon {
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 360px) {
    .refresh-icon {
        width: 14px;
        height: 14px;
    }
}

@keyframes spin-slow {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.refresh-button:hover .refresh-icon {
    animation: spin-fast 0.5s linear infinite;
}

@keyframes spin-fast {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Футер */
.maintenance-footer {
    @apply px-8 pb-8 pt-4 border-t border-[#514b3c] text-center;
}

.footer-text {
    @apply text-[#b8b3a0] text-sm leading-relaxed mb-4;
    margin: 0;
}

.footer-time {
    @apply text-[#a6925e] text-xs font-mono;
}

/* Адаптивные стили */

/* Большие планшеты и маленькие настольные компьютеры */
@media (max-width: 1024px) and (min-width: 769px) {
    .maintenance-modal {
        @apply max-w-2xl;
    }

    .maintenance-header {
        @apply p-10 pb-6;
    }

    .maintenance-title {
        @apply text-3xl;
    }

    .maintenance-content {
        @apply px-10 pb-8;
    }

    .maintenance-footer {
        @apply px-10 pb-10;
    }

    .gear-animation {
        width: 72px;
        height: 72px;
    }

    .greeting-text {
        @apply text-xl;
    }

    .maintenance-message p {
        @apply text-lg;
    }

    .completion-label {
        @apply text-base;
    }

    .completion-time {
        @apply text-2xl;
    }

    .info-item {
        @apply text-base;
    }

    .footer-text {
        @apply text-base;
    }

    .footer-time {
        @apply text-sm;
    }
}

/* Планшеты */
@media (max-width: 768px) and (min-width: 641px) {
    .maintenance-container {
        @apply p-6;
    }

    .maintenance-modal {
        @apply max-w-xl;
    }

    .maintenance-header {
        @apply p-8 pb-4;
    }

    .maintenance-title {
        @apply text-2xl;
    }

    .maintenance-content {
        @apply px-8 pb-6;
    }

    .maintenance-footer {
        @apply px-8 pb-8;
    }

    .gear-animation {
        width: 56px;
        height: 56px;
    }

    .refresh-button {
        @apply py-3 px-8 text-base;
    }

    .greeting-text {
        @apply text-lg;
    }

    .maintenance-message p {
        @apply text-base;
    }

    .completion-label {
        @apply text-sm;
    }

    .completion-time {
        @apply text-xl;
    }

    .info-item {
        @apply text-sm;
    }

    .footer-text {
        @apply text-sm;
    }

    .footer-time {
        @apply text-xs;
    }
}

/* Мобильные устройства */
@media (max-width: 640px) {
    .maintenance-container {
        @apply p-2;
    }

    .maintenance-modal {
        @apply max-w-none mx-2;
    }

    .maintenance-header {
        @apply p-6 pb-3;
    }

    .maintenance-title {
        @apply text-xl;
    }

    .maintenance-content {
        @apply px-6 pb-4;
    }

    .maintenance-footer {
        @apply px-6 pb-6;
    }

    .gear-animation {
        width: 48px;
        height: 48px;
    }
}

/* Маленькие мобильные устройства */
@media (max-width: 480px) {
    .maintenance-container {
        @apply p-1;
    }

    .maintenance-modal {
        @apply mx-1 rounded-md;
    }

    .maintenance-header {
        @apply p-4 pb-2;
    }

    .maintenance-title {
        @apply text-lg leading-tight;
    }

    .maintenance-content {
        @apply px-4 pb-3;
    }

    .maintenance-footer {
        @apply px-4 pb-4;
    }

    .refresh-button {
        @apply py-2 px-4 text-sm;
    }

    .gear-animation {
        width: 40px;
        height: 40px;
    }

    .greeting-text {
        @apply text-base;
    }

    .maintenance-message p {
        @apply text-sm;
    }

    .completion-label {
        @apply text-xs;
    }

    .completion-time {
        @apply text-base;
    }

    .info-item {
        @apply text-xs;
    }

    .footer-text {
        @apply text-xs;
    }
}

/* Очень маленькие экраны */
@media (max-width: 360px) {
    .maintenance-container {
        @apply p-0;
    }

    .maintenance-modal {
        @apply mx-0 rounded-none min-h-screen justify-center flex flex-col;
    }

    .maintenance-header {
        @apply p-3 pb-2;
    }

    .maintenance-title {
        @apply text-base leading-tight;
        word-wrap: break-word;
        hyphens: auto;
    }

    .maintenance-content {
        @apply px-3 pb-2;
    }

    .maintenance-footer {
        @apply px-3 pb-3;
    }

    .refresh-button {
        @apply py-2 px-3 text-xs;
        min-height: 36px;
    }

    .gear-animation {
        width: 32px;
        height: 32px;
    }

    .greeting-text {
        @apply text-sm;
        word-wrap: break-word;
    }

    .maintenance-message p {
        @apply text-xs;
        word-wrap: break-word;
        line-height: 1.4;
    }

    .completion-label {
        @apply text-xs;
    }

    .completion-time {
        @apply text-sm;
        word-wrap: break-word;
    }

    .info-item {
        @apply text-xs;
        word-wrap: break-word;
    }

    .footer-text {
        @apply text-xs;
        word-wrap: break-word;
        line-height: 1.3;
    }

    .user-help-info {
        @apply p-3;
        margin-bottom: 0.75rem;
    }

    .maintenance-completion {
        @apply p-3;
        margin-bottom: 0.75rem;
    }

    .maintenance-info {
        @apply space-y-2;
    }

    .maintenance-actions {
        @apply mb-4;
    }
}

/* Эффекты при загрузке */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.maintenance-content > * {
    animation: fadeInUp 0.6s ease-out forwards;
}

.maintenance-content > *:nth-child(1) {
    animation-delay: 0.1s;
}
.maintenance-content > *:nth-child(2) {
    animation-delay: 0.2s;
}
.maintenance-content > *:nth-child(3) {
    animation-delay: 0.3s;
}
.maintenance-content > *:nth-child(4) {
    animation-delay: 0.4s;
}

/* Пульсирующий эффект для важной информации */
.maintenance-completion {
    animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    from {
        box-shadow: 0 0 5px rgba(166, 146, 94, 0.2);
    }
    to {
        box-shadow: 0 0 15px rgba(166, 146, 94, 0.4);
    }
}

/* Альбомная ориентация */
@media (max-height: 500px) and (orientation: landscape) {
    .maintenance-container {
        @apply p-2;
    }

    .maintenance-modal {
        @apply max-w-2xl max-h-full overflow-y-auto;
    }

    .maintenance-header {
        @apply p-4 pb-2;
    }

    .maintenance-title {
        @apply text-xl;
    }

    .maintenance-content {
        @apply px-4 pb-2;
    }

    .maintenance-footer {
        @apply px-4 pb-4;
    }

    .gear-animation {
        width: 40px;
        height: 40px;
    }

    .maintenance-message p {
        @apply text-sm;
    }

    .info-item {
        @apply text-xs;
    }

    .footer-text {
        @apply text-xs;
    }

    .user-help-info {
        @apply p-2;
    }

    .maintenance-completion {
        @apply p-2;
    }
}

/* Альбомная ориентация на мобильных устройствах */
@media (max-height: 400px) and (orientation: landscape) and (max-width: 768px) {
    .maintenance-modal {
        @apply mx-1 rounded-md;
    }

    .maintenance-header {
        @apply p-3 pb-1;
    }

    .maintenance-title {
        @apply text-lg;
    }

    .maintenance-content {
        @apply px-3 pb-1;
    }

    .maintenance-footer {
        @apply px-3 pb-3;
    }

    .gear-animation {
        width: 32px;
        height: 32px;
    }

    .maintenance-message p {
        @apply text-xs;
    }

    .completion-label {
        @apply text-xs;
    }

    .completion-time {
        @apply text-sm;
    }

    .info-item {
        @apply text-xs;
    }

    .footer-text {
        @apply text-xs;
    }

    .refresh-button {
        @apply py-1 px-3 text-xs;
    }
}

/* Стили для темной темы (если нужно) */
@media (prefers-color-scheme: dark) {
    .maintenance-body {
        background: linear-gradient(135deg, #0f0e0c 0%, #1a1814 100%);
    }
}

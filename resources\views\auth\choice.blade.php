<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Выбор действия - Echoes of Eternity</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif min-h-screen flex flex-col">
    {{-- Основной контейнер в стиле группы --}}
    <div class="container max-w-md mx-auto px-1 py-3 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
         style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">
        
        {{-- Заголовок в стиле локации --}}
        <div class="text-center mb-6 bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-4">
            <div class="text-[#c1a96e] mb-3">
                <svg class="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                        clip-rule="evenodd" />
                </svg>
            </div>
            <h1 class="text-[#fceac4] text-xl font-bold mb-2" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                Добро пожаловать!
            </h1>
            <p class="text-[#a6925e] text-sm">Ваш ключ доступа принят. Выберите действие.</p>
            
            @if($isAdmin)
                <div class="mt-3 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium 
                           bg-gradient-to-r from-[#4a1f1a]/20 to-[#5e2b26]/20 text-[#d4675a] 
                           border border-[#4a1f1a]/40">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                            clip-rule="evenodd" />
                        <path fill-rule="evenodd" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" clip-rule="evenodd" />
                    </svg>
                    Административный доступ
                </div>
            @endif
        </div>

        {{-- Уведомления --}}
        @if(session('success'))
            <div class="bg-gradient-to-r from-[#2f473c]/20 to-[#1e2e27]/20 border border-[#2f473c]/40 rounded-lg p-3 mb-4">
                <p class="text-[#c1a96e] text-sm font-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                    {{ session('success') }}
                </p>
            </div>
        @endif

        @if(session('info'))
            <div class="bg-gradient-to-r from-[#3b3629]/20 to-[#2a2722]/20 border border-[#3b3629]/40 rounded-lg p-3 mb-4">
                <p class="text-[#c1a96e] text-sm font-semibold" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                    {{ session('info') }}
                </p>
            </div>
        @endif

        {{-- Основные действия --}}
        <div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-4">
            <div class="space-y-3">
                {{-- Кнопка регистрации --}}
                <form method="POST" action="{{ route('auth.choice.register') }}">
                    @csrf
                    <button type="submit"
                        class="w-full flex items-center justify-center py-3 px-4 font-bold text-[#f8eac2] 
                               transition-all duration-200 bg-gradient-to-b from-[#2f473c] to-[#1e2e27] 
                               hover:from-[#3e5c48] hover:to-[#243c2f] border-2 border-[#3b3629] 
                               hover:border-[#c1a96e]/50 rounded-lg shadow-lg hover:shadow-xl"
                        style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                        </svg>
                        Создать нового героя
                    </button>
                </form>

                {{-- Разделитель --}}
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-[#3b3629]"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#998d66]">или</span>
                    </div>
                </div>

                {{-- Кнопка входа --}}
                <form method="POST" action="{{ route('auth.choice.login') }}">
                    @csrf
                    <button type="submit"
                        class="w-full flex items-center justify-center py-3 px-4 font-bold text-[#f8eac2] 
                               transition-all duration-200 bg-gradient-to-b from-[#3b3629] to-[#2a2722] 
                               hover:from-[#514b3c] hover:to-[#3d3a2e] border-2 border-[#3b3629] 
                               hover:border-[#c1a96e]/50 rounded-lg shadow-lg hover:shadow-xl"
                        style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                        </svg>
                        Войти существующим героем
                    </button>
                </form>

                {{-- Административная панель --}}
                @if($isAdmin)
                    <div class="pt-3 border-t border-[#3b3629]">
                        <a href="{{ route('admin.dashboard') }}"
                            class="w-full flex items-center justify-center py-3 px-4 font-bold text-[#f8eac2] 
                                   transition-all duration-200 bg-gradient-to-b from-[#4a1f1a]/30 to-[#5e2b26]/30 
                                   hover:from-[#5e2b26]/40 hover:to-[#6b3129]/40 border-2 border-[#4a1f1a]/40 
                                   hover:border-[#d4675a]/50 rounded-lg shadow-lg hover:shadow-xl"
                            style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            Административная панель
                        </a>
                    </div>
                @endif
            </div>

            {{-- Разделитель --}}
            <div class="border-t border-[#3b3629] my-4"></div>

            {{-- Кнопка выхода --}}
            <div class="text-center">
                <button onclick="logout()" 
                        class="text-[#998d66] hover:text-[#c1a96e] text-sm transition-colors duration-200"
                        style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">
                    Выйти из системы доступа
                </button>
            </div>
        </div>
    </div>

    {{-- Футер для неавторизованных --}}
    <x-auth.footer />

    <script>
        function logout() {
            if (confirm('Вы уверены, что хотите выйти? Потребуется повторный ввод ключа доступа.')) {
                window.location.href = '{{ route("access.logout") }}';
            }
        }

        // Проверка истечения ключа каждые 60 секунд
        setInterval(function () {
            fetch('{{ route("access.status") }}')
                .then(response => response.json())
                .then(data => {
                    if (!data.has_access) {
                        alert('Ваш ключ доступа истек. Вы будете перенаправлены на главную страницу.');
                        window.location.href = '{{ route("maintenance.page") }}';
                    }
                })
                .catch(error => console.log('Status check failed:', error));
        }, 60000);
    </script>
</body>

</html>
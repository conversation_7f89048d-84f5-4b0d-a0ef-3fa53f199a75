<?php

namespace App\Console\Commands\Mine;

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Команда для исправления проблем с изоляцией ботов в рудниках
 */
class FixMineBotsIsolation extends Command
{
    /**
     * Название и подпись консольной команды
     */
    protected $signature = 'mine:fix-isolation
                            {--dry-run : Показать изменения без их применения}
                            {--detailed : Подробный вывод}';

    /**
     * Описание консольной команды
     */
    protected $description = 'Исправляет проблемы с изоляцией ботов в подлокациях рудников';

    /**
     * Выполнение команды
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $detailed = $this->option('detailed');

        $this->info('🔧 ИСПРАВЛЕНИЕ ИЗОЛЯЦИИ БОТОВ В РУДНИКАХ');
        $this->info('=====================================');
        $this->newLine();

        if ($dryRun) {
            $this->warn('🔍 РЕЖИМ ПРЕДПРОСМОТРА - изменения не будут сохранены');
            $this->newLine();
        }

        // 1. Исправляем локации ботов
        $fixed = $this->fixBotLocations($dryRun, $detailed);

        // 2. Проверяем и исправляем привязки mine_location_id
        $fixedIds = $this->fixMineLocationIds($dryRun, $detailed);

        // 3. Сбрасываем кулдауны
        $this->resetCooldowns($dryRun);

        // 4. Проверяем результат
        $this->validateResult($detailed);

        $this->newLine();
        $this->info("✅ ИТОГ:");
        $this->info("   - Исправлено локаций: {$fixed}");
        $this->info("   - Исправлено ID привязок: {$fixedIds}");
        
        if ($dryRun) {
            $this->warn("   - Изменения НЕ сохранены (dry-run режим)");
        } else {
            $this->info("   - Изменения успешно применены");
        }

        return 0;
    }

    /**
     * Исправляет названия локаций у ботов
     */
    private function fixBotLocations(bool $dryRun, bool $detailed): int
    {
        $this->info('1. ИСПРАВЛЕНИЕ ЛОКАЦИЙ БОТОВ');
        $this->line(str_repeat('-', 30));

        $mineLocations = MineLocation::where('is_active', true)->get();
        $totalFixed = 0;

        foreach ($mineLocations as $mine) {
            // Ищем ботов с location = ID вместо названия
            $botsWithIdLocation = Bot::where('location', (string) $mine->id)
                ->where('created_by_admin', true)
                ->get();

            if ($botsWithIdLocation->count() > 0) {
                $this->line("📍 {$mine->name} (ID: {$mine->id}):");
                $this->line("   Найдено ботов с ID локации: {$botsWithIdLocation->count()}");

                foreach ($botsWithIdLocation as $bot) {
                    $oldLocation = $bot->location;
                    
                    if ($detailed) {
                        $this->line("   🤖 {$bot->name}: '{$oldLocation}' → '{$mine->name}'");
                    }

                    if (!$dryRun) {
                        $bot->location = $mine->name;
                        $bot->mine_location_id = $mine->id;
                        $bot->save();
                    }

                    $totalFixed++;
                }
            }
        }

        if ($totalFixed === 0) {
            $this->info('   ✅ Все локации корректны');
        } else {
            $this->info("   ✅ Обработано ботов: {$totalFixed}");
        }

        $this->newLine();
        return $totalFixed;
    }

    /**
     * Исправляет привязки mine_location_id
     */
    private function fixMineLocationIds(bool $dryRun, bool $detailed): int
    {
        $this->info('2. ИСПРАВЛЕНИЕ ПРИВЯЗОК MINE_LOCATION_ID');
        $this->line(str_repeat('-', 40));

        $mineLocations = MineLocation::where('is_active', true)->get();
        $totalFixed = 0;

        foreach ($mineLocations as $mine) {
            // Ищем ботов в этой подлокации без правильной привязки
            $botsWithoutId = Bot::where('location', $mine->name)
                ->where('created_by_admin', true)
                ->where(function ($query) use ($mine) {
                    $query->whereNull('mine_location_id')
                        ->orWhere('mine_location_id', '!=', $mine->id);
                })
                ->get();

            if ($botsWithoutId->count() > 0) {
                $this->line("📍 {$mine->name}:");
                $this->line("   Ботов без правильной привязки: {$botsWithoutId->count()}");

                foreach ($botsWithoutId as $bot) {
                    $oldId = $bot->mine_location_id;
                    
                    if ($detailed) {
                        $this->line("   🤖 {$bot->name}: mine_location_id {$oldId} → {$mine->id}");
                    }

                    if (!$dryRun) {
                        $bot->mine_location_id = $mine->id;
                        $bot->save();
                    }

                    $totalFixed++;
                }
            }
        }

        if ($totalFixed === 0) {
            $this->info('   ✅ Все привязки корректны');
        } else {
            $this->info("   ✅ Обработано привязок: {$totalFixed}");
        }

        $this->newLine();
        return $totalFixed;
    }

    /**
     * Сбрасывает кулдауны атак
     */
    private function resetCooldowns(bool $dryRun): void
    {
        $this->info('3. СБРОС КУЛДАУНОВ');
        $this->line(str_repeat('-', 16));

        $botsWithCooldown = Bot::where('created_by_admin', true)
            ->whereNotNull('next_action_time')
            ->count();

        if ($botsWithCooldown > 0) {
            $this->line("   Ботов с кулдауном: {$botsWithCooldown}");
            
            if (!$dryRun) {
                Bot::where('created_by_admin', true)->update(['next_action_time' => null]);
            }
            
            $this->info('   ✅ Кулдауны сброшены');
        } else {
            $this->info('   ✅ Кулдауны уже сброшены');
        }

        $this->newLine();
    }

    /**
     * Проверяет результат исправлений
     */
    private function validateResult(bool $detailed): void
    {
        $this->info('4. ПРОВЕРКА РЕЗУЛЬТАТА');
        $this->line(str_repeat('-', 20));

        $mineLocations = MineLocation::where('is_active', true)->get();
        $totalIssues = 0;

        foreach ($mineLocations as $mine) {
            $bots = Bot::where('location', $mine->name)
                ->where('created_by_admin', true)
                ->get();

            $issues = 0;

            // Проверяем корректность локаций
            foreach ($bots as $bot) {
                if ($bot->location !== $mine->name) {
                    $issues++;
                }
                if ($bot->mine_location_id !== $mine->id) {
                    $issues++;
                }
            }

            if ($detailed || $issues > 0) {
                $status = $issues === 0 ? '✅' : '❌';
                $this->line("   {$status} {$mine->name}: {$bots->count()} ботов, {$issues} проблем");
            }

            $totalIssues += $issues;
        }

        if ($totalIssues === 0) {
            $this->info('   ✅ Все проблемы исправлены');
        } else {
            $this->warn("   ⚠️ Осталось проблем: {$totalIssues}");
        }

        $this->newLine();
    }
}
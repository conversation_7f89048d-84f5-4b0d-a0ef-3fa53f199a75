<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\MineMark;

class CheckMineData extends Command
{
    protected $signature = 'check:mine-data';
    protected $description = 'Проверить данные для тестирования системы рудников';

    public function handle()
    {
        $this->info('🔍 ПРОВЕРКА ДАННЫХ ДЛЯ СИСТЕМЫ РУДНИКОВ');
        $this->info('=' . str_repeat('=', 50));

        // Проверяем админа
        $admin = User::where('name', 'admin')->first();
        if ($admin) {
            $this->info("✅ Админ найден: {$admin->name} (ID: {$admin->id})");
            if ($admin->profile) {
                $hp = $admin->profile->current_hp . '/' . $admin->profile->max_hp;
                $this->info("   HP: {$hp}");
            }
        } else {
            $this->error('❌ Админ не найден');
        }

        // Ищем локацию с "а"
        $locations = Location::where('name', 'like', '%а%')->get();
        $this->info("\n📍 Локации с 'а' в названии:");
        foreach ($locations as $location) {
            $this->info("   - {$location->name} (ID: {$location->id})");
        }

        // Ищем мобов Огр
        $ogres = Mob::where('name', 'like', '%Огр%')->get();
        $this->info("\n👹 Мобы 'Огр':");
        foreach ($ogres as $ogre) {
            $this->info("   - {$ogre->name} (ID: {$ogre->id}, location_id: {$ogre->location_id}, hp: {$ogre->hp})");
            if (isset($ogre->mob_type)) {
                $this->info("     Тип: {$ogre->mob_type}");
            }
        }

        // Проверяем рудники
        $mineLocations = MineLocation::all();
        $this->info("\n⛏️  Рудники:");
        foreach ($mineLocations as $mine) {
            $this->info("   - {$mine->name} (ID: {$mine->id}, location_id: {$mine->location_id})");
        }

        // Проверяем активные метки
        $activeMarks = MineMark::where('is_active', true)
            ->where('expires_at', '>', now())
            ->with('player')
            ->get();
        
        $this->info("\n🎯 Активные метки 'Замечен':");
        if ($activeMarks->isEmpty()) {
            $this->info("   Нет активных меток");
        } else {
            foreach ($activeMarks as $mark) {
                $playerName = $mark->player ? $mark->player->name : 'Неизвестен';
                $this->info("   - Игрок: {$playerName}, Рудник ID: {$mark->mine_location_id}, Истекает: {$mark->expires_at}");
            }
        }

        return 0;
    }
}

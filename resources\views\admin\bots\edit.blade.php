@php use Illuminate\Support\Facades\Auth; @endphp {{-- Используем фасад Auth для проверки роли --}}
<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- Устанавливаем заголовок страницы --}}
    <title>Редактировать Бота: {{ $bot->name }} - Админка</title>

    {{-- Подключаем Vite для CSS и JS --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

{{-- Устанавливаем фон и основной шрифт для тела страницы (стиль админки может отличаться) --}}

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">

    {{-- Основной контейнер страницы админки --}}
    <div
        class="container max-w-2xl mx-auto px-4 py-8 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden my-5">


        {{-- Основной контент --}}
        <div class="flex-1 flex flex-col overflow-hidden">
            {{-- Верхняя панель админки (если есть) --}}
            {{-- @include('partials.admin_header') --}}

            {{-- Главная область контента --}}
            <main class="flex-1 overflow-x-hidden overflow-y-auto"> {{-- Убрали bg-gray-200 --}}
                <div class="container mx-auto px-6 py-8">
                    {{-- Заголовок страницы --}}
                    <h3
                        class="block text-center text-white py-1.5 px-8 rounded-md shadow-lg bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] text-xl font-semibold mb-4">
                        Редактировать Бота: {{ $bot->name }}</h3> {{--
                    Отображаем имя редактируемого бота --}}

                    {{-- Хлебные крошки --}}
                    <div class="mt-4 mb-6 text-sm text-[#d3c6a6]">
                        {{-- Ссылка на главную страницу админки --}}
                        <a href="{{ route('admin.dashboard') }}" class="text-[#e5b769] hover:text-[#f0d89e]">Админка</a>
                        <span class="mx-2 text-[#a6925e]">/</span> {{-- Разделитель --}}
                        {{-- Ссылка на страницу управления ботами --}}
                        <a href="{{ route('admin.bots.index') }}" class="text-[#e5b769] hover:text-[#f0d89e]">Управление
                            ботами</a>
                        <span class="mx-2 text-[#a6925e]">/</span> {{-- Разделитель --}}
                        <span class="text-[#f5f5f5]">Редактировать: {{ $bot->name }}</span> {{-- Текущая страница с
                        именем бота --}}
                    </div>

                    {{-- Форма редактирования --}}
                    <div class="bg-[#211f1a] border border-[#514b3c] shadow-inner rounded px-8 pt-6 pb-8 mb-4"> {{--
                        Контейнер формы: отступы,
                        фон, тень, скругление --}}

                        {{-- Вывод ошибок валидации, если они есть --}}
                        @if ($errors->any())
                            <div class="bg-[#5a2626] border border-[#e74c3c] text-[#f5c6cb] px-4 py-3 rounded relative mb-6"
                                role="alert"> {{-- Стили блока ошибок --}}
                                <strong class="font-bold text-white">Ошибка валидации!</strong> {{-- Заголовок блока --}}
                                <ul class="mt-3 list-disc list-inside text-sm text-[#f5c6cb]"> {{-- Список ошибок --}}
                                    {{-- Цикл для вывода каждой ошибки --}}
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li> {{-- Текст ошибки --}}
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        {{-- Форма отправляет данные на метод update контроллера для указанного бота --}}
                        <form action="{{ route('admin.bots.update', $bot->id) }}" method="POST"> {{-- URL и метод
                            отправки --}}
                            @csrf {{-- Защита от CSRF-атак --}}
                            @method('PUT') {{-- Указание HTTP-метода PUT для обновления ресурса --}}

                            {{-- Поля формы, аналогичные create.blade.php, но с предзаполненными данными бота --}}

                            {{-- Поле для имени бота --}}
                            <div class="mb-6">
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="name"> {{-- Метка поля
                                    --}}
                                    Имя
                                </label>
                                <input
                                    class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                    {{-- Стили поля ввода --}} id="name" name="name" type="text" placeholder="Имя бота"
                                    {{-- Атрибуты поля --}} value="{{ old('name', $bot->name) }}" {{-- Значение: старое
                                    введенное или текущее имя бота --}} required {{-- Поле обязательно --}}>
                            </div>

                            {{-- Поля для выбора расы и класса --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для размещения полей в строку --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Поле Раса --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="race"> {{-- Метка
                                        --}}
                                        Раса
                                    </label>
                                    <div class="relative"> {{-- Обертка для стилизации select --}}
                                        <select
                                            class="block appearance-none w-full bg-[#38352c] border border-[#514b3c] text-[#f5f5f5] py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-[#4a452c] focus:border-[#a6925e]"
                                            {{-- Стили --}} id="race" name="race" required> {{-- Атрибуты --}}
                                            {{-- Опция Solarius, выбрана если старое значение или текущая раса -
                                            solarius --}}
                                            <option value="solarius" {{ old('race', $bot->race) == 'solarius' ? 'selected' : '' }}>Solarius</option>
                                            {{-- Опция Lunarius, выбрана если старое значение или текущая раса -
                                            lunarius --}}
                                            <option value="lunarius" {{ old('race', $bot->race) == 'lunarius' ? 'selected' : '' }}>Lunarius</option>
                                        </select>
                                        <div {{-- Иконка стрелки --}}
                                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-[#a6925e]">
                                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20">
                                                <path
                                                    d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full md:w-1/2 px-3"> {{-- Поле Класс --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="class"> {{-- Метка
                                        --}}
                                        Класс
                                    </label>
                                    <div class="relative"> {{-- Обертка select --}}
                                        <select
                                            class="block appearance-none w-full bg-[#38352c] border border-[#514b3c] text-[#f5f5f5] py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-[#4a452c] focus:border-[#a6925e]"
                                            {{-- Стили --}} id="class" name="class" required> {{-- Атрибуты --}}
                                            {{-- Опции классов, выбрана соответствующая текущему классу бота или старому
                                            значению --}}
                                            <option value="warrior" {{ old('class', $bot->class) == 'warrior' ? 'selected' : '' }}>Воин</option>
                                            <option value="mage" {{ old('class', $bot->class) == 'mage' ? 'selected' : '' }}>Маг</option>
                                            {{-- Опция Priest (Жрец) --}}
                                            <option value="priest" {{ old('class', $bot->class) == 'priest' ? 'selected' : '' }}>Жрец</option>
                                            {{-- Место для добавления других возможных классов --}}
                                        </select>
                                        <div {{-- Иконка стрелки --}}
                                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-[#a6925e]">
                                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20">
                                                <path
                                                    d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- Поле для уровня бота --}}
                            <div class="mb-6">
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="level"> {{-- Метка --}}
                                    Уровень
                                </label>
                                <input
                                    class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                    {{-- Стили --}} id="level" name="level" type="number" placeholder="1" {{-- Атрибуты
                                    --}} value="{{ old('level', $bot->level) }}" {{-- Значение: старое или текущий
                                    уровень --}} required min="1" {{-- Обязательно, минимальное значение 1 --}}>
                            </div>

                            {{-- Поля для HP и Макс. HP --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для полей HP --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Текущее HP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="hp"> {{-- Метка --}}
                                        Текущее HP
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        {{-- Стили --}} id="hp" name="hp" type="number" placeholder="100" {{-- Атрибуты
                                        --}} value="{{ old('hp', $bot->hp) }}" {{-- Значение: старое или текущее HP --}}
                                        required {{-- Обязательно --}}>
                                </div>
                                <div class="w-full md:w-1/2 px-3"> {{-- Максимальное HP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="max_hp"> {{-- Метка
                                        --}}
                                        Максимальное HP
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        {{-- Стили --}} id="max_hp" name="max_hp" type="number" placeholder="100" {{--
                                        Атрибуты --}} value="{{ old('max_hp', $bot->max_hp) }}" {{-- Значение: старое
                                        или текущее макс. HP --}} required {{-- Обязательно --}}>
                                </div>
                            </div>

                            {{-- Поля для MP и Макс. MP --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для полей MP --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Текущее MP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="mp"> {{-- Метка --}}
                                        Текущее MP
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        {{-- Стили --}} id="mp" name="mp" type="number" placeholder="50" {{-- Атрибуты
                                        --}} value="{{ old('mp', $bot->mp) }}" {{-- Значение: старое или текущее MP --}}
                                        required {{-- Обязательно --}}>
                                </div>
                                <div class="w-full md:w-1/2 px-3"> {{-- Максимальное MP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="max_mp"> {{-- Метка
                                        --}}
                                        Максимальное MP
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        {{-- Стили --}} id="max_mp" name="max_mp" type="number" placeholder="50" {{--
                                        Атрибуты --}} value="{{ old('max_mp', $bot->max_mp) }}" {{-- Значение: старое
                                        или текущее макс. MP --}} required {{-- Обязательно --}}>
                                </div>
                            </div>

                            {{-- Поля для характеристик (Сила, Интеллект, Ловкость) --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для характеристик --}}
                                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0"> {{-- Сила --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="strength"> {{--
                                        Метка
                                        --}}
                                        Сила
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        {{-- Стили --}} id="strength" name="strength" type="number" placeholder="10"
                                        {{-- Атрибуты --}} value="{{ old('strength', $bot->strength) }}" {{-- Значение:
                                        старое или текущая сила --}} required {{-- Обязательно --}}>
                                </div>
                                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0"> {{-- Интеллект --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="intelligence"> {{--
                                        Метка --}}
                                        Интеллект
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        {{-- Стили --}} id="intelligence" name="intelligence" type="number"
                                        placeholder="10" {{-- Атрибуты --}}
                                        value="{{ old('intelligence', $bot->intelligence) }}" {{-- Значение: старое или
                                        текущий интеллект --}} required {{-- Обязательно --}}>
                                </div>
                                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0"> {{-- Ловкость --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="dexterity"> {{--
                                        Метка --}}
                                        Ловкость
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        {{-- Стили --}} id="dexterity" name="dexterity" type="number" placeholder="10"
                                        {{-- Атрибуты --}} value="{{ old('dexterity', $bot->dexterity) }}" {{--
                                        Значение: старое или текущая ловкость --}} required {{-- Обязательно --}}>
                                </div>
                            </div>

                            {{-- Поля для защитных характеристик (Броня, Сопр. магии) --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для защиты --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Броня --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="armor"> {{-- Метка
                                        --}}
                                        Броня
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        id="armor" name="armor" type="number" placeholder="5"
                                        value="{{ old('armor', $bot->armor) }}" required>
                                </div>
                                <div class="w-full md:w-1/2 px-3">
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="magic_resistance">
                                        Сопр. магии
                                    </label>
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                        id="magic_resistance" name="magic_resistance" type="number" placeholder="5"
                                        value="{{ old('magic_resistance', $bot->magic_resistance) }}" required>
                                </div>
                            </div>

                            {{-- Поле: Базовый урон (автоматически рассчитывается) --}}
                            <div class="mb-6"> {{-- Контейнер для базового урона --}}
                                {{-- Метка для базового урона --}}
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="base_damage_display">
                                    Базовый урон {{-- Текст метки --}}
                                </label>
                                {{-- Информационное поле (только для чтения) --}}
                                <div
                                    class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#3a3a3a] text-[#a6925e] leading-tight">
                                    <span id="base_damage_display">{{ old('strength', $bot->strength) }} (текущий
                                        базовый урон: {{ $bot->base_damage ?? 'не рассчитан' }})</span>
                                </div>
                                {{-- Подсказка для поля базового урона --}}
                                <p class="text-[#a6925e] text-xs italic mt-1">
                                    Базовый урон рассчитывается автоматически по формуле: урон = сила бота.
                                    Это значение будет обновлено в базе данных при сохранении.
                                </p>
                            </div>

                            {{-- Поле: Локация (выпадающий список) --}}
                            <div class="mb-6"> {{-- Контейнер для локации --}}
                                {{-- Метка для локации --}}
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="location">
                                    Локация {{-- Текст метки --}}
                                </label>
                                <div class="relative"> {{-- Относительный контейнер для позиционирования иконки стрелки --}}
                                    {{-- Выпадающий список для выбора локации --}}
                                    <select
                                        class="block appearance-none w-full bg-[#38352c] border border-[#514b3c] text-[#f5f5f5] py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-[#4a452c] focus:border-[#a6925e]" {{-- Стили select для темной темы --}}
                                        id="location" {{-- ID поля --}}
                                        name="location" {{-- Имя поля --}}
                                        required {{-- Обязательно для выбора --}}
                                    >
                                        <option value="" class="text-[#7a7568]">-- Выберите локацию --</option> {{-- Опция-заглушка по умолчанию --}}

                                        {{-- Группа: Рудники (для автоматизации ботов) --}}
                                        @if(isset($mineLocations) && $mineLocations->count() > 0)
                                            <optgroup label="🏭 Рудники (для автоматизации ботов)" class="bg-[#4a452c] text-[#e5b769] font-bold">
                                                @foreach($mineLocations as $mineLocation)
                                                    @if($mineLocation->baseLocation)
                                                        <option value="{{ $mineLocation->name }}"
                                                                data-mine-location-id="{{ $mineLocation->id }}"
                                                                {{ old('location', $bot->location) == $mineLocation->name && old('mine_location_id', $bot->mine_location_id) == $mineLocation->id ? 'selected' : '' }}
                                                                class="bg-[#38352c] text-[#f5f5f5]">
                                                            {{ $mineLocation->name }} ({{ $mineLocation->baseLocation->name }})
                                                        </option>
                                                    @endif
                                                @endforeach
                                            </optgroup>
                                        @endif

                                        {{-- Группа: Обычные локации --}}
                                        <optgroup label="🏛️ Обычные локации" class="bg-[#4a452c] text-[#e5b769] font-bold">
                                            @foreach($locations as $location)
                                                <option value="{{ $location->name }}" {{ old('location', $bot->location) == $location->name && !old('mine_location_id', $bot->mine_location_id) ? 'selected' : '' }} class="bg-[#38352c] text-[#f5f5f5]">
                                                    {{ $location->name }}
                                                </option>
                                            @endforeach
                                        </optgroup>
                                    </select>
                                    {{-- Иконка стрелки для select --}}
                                    <div
                                        class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-[#a6925e]">
                                        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20">
                                            <path
                                                d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                        </svg>
                                    </div>
                                </div>
                                {{-- Подсказка для поля локации --}}
                                <p class="text-[#a6925e] text-xs italic mt-1">Выберите локацию, где бот будет
                                    появляться.</p>
                            </div>

                            {{-- Активность --}}
                            <div class="mb-6">
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2">
                                    Активен
                                </label>
                                <div class="mt-2">
                                    <label
                                        class="inline-flex items-center text-[#d3c6a6] hover:text-[#f5f5f5] cursor-pointer">
                                        <input type="radio"
                                            class="form-radio h-4 w-4 text-[#e5b769] bg-[#38352c] border-[#514b3c] focus:ring-[#e5b769]"
                                            name="is_active" value="1" {{ old('is_active', $bot->is_active) == 1 ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm">Да (Бот будет появляться и действовать в игре)</span>
                                    </label>
                                    <label
                                        class="inline-flex items-center ml-6 text-[#d3c6a6] hover:text-[#f5f5f5] cursor-pointer">
                                        <input type="radio"
                                            class="form-radio h-4 w-4 text-[#e5b769] bg-[#38352c] border-[#514b3c] focus:ring-[#e5b769]"
                                            name="is_active" value="0" {{ old('is_active', $bot->is_active) == 0 ? 'checked' : '' }}>
                                        <span class="ml-2 text-sm">Нет (Бот временно неактивен)</span>
                                    </label>
                                </div>
                            </div>

                            {{-- Время Возрождения (в секундах) --}}
                            @php
                                // Получаем интервал воскрешения из модели
                                $respawnSeconds = $bot->respawn_interval ?? 300; // 5 минут по умолчанию
                            @endphp
                            <div class="mb-8">
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="respawn_time_seconds">
                                    Интервал воскрешения (сек)
                                </label>
                                <input
                                    class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                    id="respawn_time_seconds" name="respawn_time_seconds" type="number"
                                    placeholder="300" value="{{ old('respawn_time_seconds', $respawnSeconds) }}"
                                    min="0">
                                <p class="text-[#a6925e] text-xs italic mt-1">Время в секундах до автоматического
                                    воскрешения бота после смерти. Каждый бот имеет индивидуальный интервал воскрешения.
                                </p>
                            </div>

                            {{-- Скрытое поле для mine_location_id --}}
                            <input type="hidden" id="mine_location_id" name="mine_location_id" value="{{ old('mine_location_id', $bot->mine_location_id) }}">

                            {{-- Кнопки --}}
                            <div class="flex items-center justify-between mt-8">
                                <button
                                    class="bg-[#c4a76d] hover:bg-[#d4b781] text-[#2f2d2b] font-bold py-2 px-4 rounded shadow-lg focus:outline-none focus:shadow-outline transition duration-300"
                                    type="submit">
                                    Сохранить изменения
                                </button>
                                <a href="{{ route('admin.bots.index') }}"
                                    class="inline-block align-baseline font-bold text-sm text-[#a6925e] hover:text-[#e5b769] transition duration-300">
                                    Отмена
                                </a>
                            </div>
                        </form>
                    </div>

                </div> {{-- container --}}
            </main>
        </div>
    </div>

    {{-- Скрипт для динамического обновления базового урона и mine_location_id --}}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const strengthInput = document.getElementById('strength');
            const baseDamageDisplay = document.getElementById('base_damage_display');
            const locationSelect = document.getElementById('location');
            const mineLocationIdInput = document.getElementById('mine_location_id');
            const currentBaseDamage = {{ $bot->base_damage ?? 0 }};

            // Функция для обновления отображения базового урона
            function updateBaseDamage() {
                const strength = parseInt(strengthInput.value) || 0;
                const baseDamage = strength; // Согласно требованиям: урон = сила
                baseDamageDisplay.innerHTML = baseDamage + ' (будет обновлен при сохранении, текущий: ' + currentBaseDamage + ')';
            }

            // Функция для обновления mine_location_id при выборе локации
            function updateMineLocationId() {
                const selectedOption = locationSelect.options[locationSelect.selectedIndex];
                const mineLocationId = selectedOption.getAttribute('data-mine-location-id');
                
                if (mineLocationId) {
                    mineLocationIdInput.value = mineLocationId;
                } else {
                    mineLocationIdInput.value = '';
                }
            }

            // Обновляем при изменении силы
            strengthInput.addEventListener('input', updateBaseDamage);
            
            // Обновляем mine_location_id при изменении локации
            locationSelect.addEventListener('change', updateMineLocationId);

            // Инициализируем при загрузке страницы
            updateBaseDamage();
            updateMineLocationId();
        });
    </script>
</body>

</html>
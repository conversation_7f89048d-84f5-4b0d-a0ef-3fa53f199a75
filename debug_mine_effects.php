<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\ActiveEffect;
use App\Models\MineMark;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 ДИАГНОСТИКА СИСТЕМЫ ДЕБАФА 'ЗАМЕЧЕН' В РУДНИКАХ\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// 1. Проверяем пользователя admin
echo "1️⃣ Поиск пользователя admin...\n";
$user = User::where('name', 'admin')->first();

if (!$user) {
    echo "❌ Пользователь admin не найден!\n";
    exit(1);
}

echo "✅ Пользователь найден: ID {$user->id}\n\n";

// 2. Проверяем активные эффекты
echo "2️⃣ Проверка активных эффектов...\n";
$allEffects = $user->activeEffects()->get();
echo "   Всего эффектов в БД: {$allEffects->count()}\n";

$mineDetectionEffects = $allEffects->where('effect_type', 'mine_detection');
echo "   Эффектов mine_detection: {$mineDetectionEffects->count()}\n";

foreach ($allEffects as $effect) {
    $isActive = $effect->isActive();
    $remainingTime = $effect->remaining_duration;
    
    echo "   - ID: {$effect->id}\n";
    echo "     Тип: {$effect->effect_type}\n";
    echo "     Название: " . ($effect->effect_name ?? 'N/A') . "\n";
    echo "     Истекает: {$effect->ends_at}\n";
    echo "     Активен: " . ($isActive ? 'Да' : 'Нет') . "\n";
    echo "     Оставшееся время: {$remainingTime}с\n";
    
    if ($effect->effect_type == 'mine_detection') {
        echo "     🎯 НАЙДЕН ЭФФЕКТ MINE_DETECTION!\n";
        echo "     Данные эффекта: " . json_encode($effect->effect_data, JSON_UNESCAPED_UNICODE) . "\n";
    }
    echo "\n";
}

// 3. Проверяем метки рудников
echo "3️⃣ Проверка меток рудников...\n";
if (Schema::hasTable('mine_marks')) {
    $mineMarks = MineMark::where('player_id', $user->id)->get();
    echo "   Всего меток: {$mineMarks->count()}\n";
    
    foreach ($mineMarks as $mark) {
        $isActive = $mark->is_active && $mark->expires_at > now();
        echo "   - ID: {$mark->id}\n";
        echo "     Рудник: {$mark->mine_location_id}\n";
        echo "     Локация: {$mark->location_name}\n";
        echo "     Активна: " . ($isActive ? 'Да' : 'Нет') . "\n";
        echo "     Истекает: {$mark->expires_at}\n";
        echo "     Время до истечения: " . $mark->expires_at->diffInSeconds(now()) . "с\n\n";
    }
} else {
    echo "   ⚠️ Таблица mine_marks не существует\n\n";
}

// 4. Проверяем фильтрацию в контроллере
echo "4️⃣ Симуляция фильтрации как в контроллере...\n";
$allUserEffects = $user->activeEffects()->with('skill')->get();
$userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());

echo "   Всего эффектов (с skill): {$allUserEffects->count()}\n";
echo "   Активных эффектов после фильтрации: {$userEffects->count()}\n";

foreach ($userEffects as $effect) {
    echo "   ✅ Активный эффект: {$effect->effect_type} (ID: {$effect->id})\n";
    echo "      Оставшееся время: {$effect->remaining_duration}с\n";
    
    if ($effect->effect_type == 'mine_detection') {
        echo "      🎯 ЭТОТ ЭФФЕКТ ДОЛЖЕН ОТОБРАЖАТЬСЯ В UI!\n";
    }
}

echo "\n5️⃣ Проверка условий отображения в Blade-компоненте...\n";
foreach ($userEffects as $effect) {
    if ($effect->effect_type == 'mine_detection') {
        $remainingDuration = $effect->remaining_duration;
        $isActiveCheck = $effect->isActive();
        
        echo "   Эффект mine_detection (ID: {$effect->id}):\n";
        echo "   - remaining_duration > 0: " . ($remainingDuration > 0 ? 'Да' : 'Нет') . " ({$remainingDuration})\n";
        echo "   - isActive(): " . ($isActiveCheck ? 'Да' : 'Нет') . "\n";
        echo "   - effect_type == 'mine_detection': Да\n";
        
        if ($remainingDuration > 0 && $isActiveCheck) {
            echo "   🎉 ВСЕ УСЛОВИЯ ВЫПОЛНЕНЫ - эффект ДОЛЖЕН отображаться!\n";
        } else {
            echo "   ❌ НЕ все условия выполнены - эффект НЕ будет отображаться\n";
        }
    }
}

echo "\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА!\n";

<?php
namespace App\Http\Controllers\Outposts;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Services\LocationAccessService;
use App\Services\FlashMessageService;
use App\Models\Location;

class OutpostsController extends Controller
{
    /**
     * Сервис для проверки доступа к локациям
     */
    protected $locationAccessService;

    /**
     * Сервис для отправки флеш-сообщений
     */
    protected $flashMessageService;

    /**
     * Конструктор контроллера
     *
     * @param LocationAccessService $locationAccessService
     * @param FlashMessageService $flashMessageService
     */
    public function __construct(LocationAccessService $locationAccessService, FlashMessageService $flashMessageService)
    {
        $this->locationAccessService = $locationAccessService;
        $this->flashMessageService = $flashMessageService;
    }

    /**
     * Отображение списка всех аванпостов.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы просматривать эту страницу.');
        }

        // Проверяем, является ли пользователь временным пользователем пролога
        $isPrologueUser = $this->isPrologueUser($user);

        // Проверяем, является ли это запросом из пролога
        $isPrologue = $request->has('prologue') && $request->get('prologue') === '1';

        if ($isPrologue || $isPrologueUser) {
            return $this->showPrologueOutposts($user);
        }

        // Проверяем доступ к аванпостам для пользователя в группе со смешанными расами
        if (!$this->locationAccessService->canAccessLocation($user, 'Аванпосты')) {
            $reason = $this->locationAccessService->getAccessDeniedReason($user, 'Аванпосты');
            $this->flashMessageService->error($reason, '⚠️');
            return redirect()->back();
        }

        // Получаем актуальные значения HP/MP для пользователя
        $actualResources = $user->profile->getActualResources();

        // Получаем уровень пользователя
        $userLevel = $user->profile->level;

        // Очищаем кеш локаций перед загрузкой, чтобы получить актуальные данные
        $this->locationAccessService->clearCache();

        // Получаем только аванпосты из базы данных
        $allLocations = $this->locationAccessService->getOutposts();

        // Проверяем и корректируем маршруты для пользовательских аванпостов
        foreach ($allLocations as $locationName => &$locationInfo) {
            // Если это пользовательский аванпост, устанавливаем правильный outpost_id
            if (isset($locationInfo['location_type']) && $locationInfo['location_type'] === 'outpost') {
                // Проверяем, есть ли запись в OutpostLocation
                $outpostLocation = \App\Models\OutpostLocation::where('name', $locationName)->first();
                if ($outpostLocation) {
                    $locationInfo['route_name'] = 'battle.outposts.show';
                    // Важно: используем id из записи OutpostLocation, а не location_id
                    $locationInfo['outpost_id'] = $outpostLocation->id;

                    // Логируем информацию о маршруте для отладки
                    \Illuminate\Support\Facades\Log::info('Установлен маршрут для аванпоста', [
                        'location_name' => $locationName,
                        'outpost_id' => $outpostLocation->id,
                        'location_id' => $locationInfo['id'] ?? null,
                        'route_name' => 'battle.outposts.show'
                    ]);
                } else {
                    // Если запись не найдена, ищем по slug
                    $outpostLocationBySlug = \App\Models\OutpostLocation::where('slug', $locationName)->first();
                    if ($outpostLocationBySlug) {
                        $locationInfo['route_name'] = 'battle.outposts.show';
                        $locationInfo['outpost_id'] = $outpostLocationBySlug->id;

                        \Illuminate\Support\Facades\Log::info('Установлен маршрут для аванпоста по slug', [
                            'location_name' => $locationName,
                            'outpost_id' => $outpostLocationBySlug->id,
                            'location_id' => $locationInfo['id'] ?? null,
                            'route_name' => 'battle.outposts.show'
                        ]);
                    } else {
                        // Если запись не найдена ни по имени, ни по slug, ищем по location_id
                        $outpostLocationByLocationId = \App\Models\OutpostLocation::where('location_id', $locationInfo['id'] ?? 0)->first();
                        if ($outpostLocationByLocationId) {
                            $locationInfo['route_name'] = 'battle.outposts.show';
                            $locationInfo['outpost_id'] = $outpostLocationByLocationId->id;

                            \Illuminate\Support\Facades\Log::info('Установлен маршрут для аванпоста по location_id', [
                                'location_name' => $locationName,
                                'outpost_id' => $outpostLocationByLocationId->id,
                                'location_id' => $locationInfo['id'] ?? null,
                                'route_name' => 'battle.outposts.show'
                            ]);
                        } else {
                            // Если не удалось найти запись в OutpostLocation, логируем предупреждение
                            \Illuminate\Support\Facades\Log::warning('Не удалось найти запись OutpostLocation для аванпоста', [
                                'location_name' => $locationName,
                                'location_id' => $locationInfo['id'] ?? 'не указан'
                            ]);
                            // Оставляем route_name как есть, но без outpost_id
                        }
                    }
                }
            }

            // Дополнительная проверка: если установлен маршрут battle.outposts.show, но не указан outpost_id
            if (
                isset($locationInfo['route_name']) &&
                $locationInfo['route_name'] === 'battle.outposts.show' &&
                (!isset($locationInfo['outpost_id']) || empty($locationInfo['outpost_id']))
            ) {
                // Если outpost_id не установлен, но маршрут требует его, проверяем наличие location_id
                if (isset($locationInfo['id']) && !empty($locationInfo['id'])) {
                    // Ищем запись OutpostLocation по location_id
                    $outpostLocationByLocationId = \App\Models\OutpostLocation::where('location_id', $locationInfo['id'])->first();
                    if ($outpostLocationByLocationId) {
                        // Если нашли запись, устанавливаем outpost_id
                        $locationInfo['outpost_id'] = $outpostLocationByLocationId->id;
                        \Illuminate\Support\Facades\Log::info('Установлен outpost_id для маршрута battle.outposts.show по location_id', [
                            'location_name' => $locationName,
                            'location_id' => $locationInfo['id'],
                            'outpost_id' => $outpostLocationByLocationId->id
                        ]);
                    } else {
                        // Если не нашли запись, меняем маршрут на стандартный
                        $locationInfo['route_name'] = 'battle.outposts.index';
                        \Illuminate\Support\Facades\Log::error('Не удалось найти запись OutpostLocation по location_id для маршрута battle.outposts.show', [
                            'location_name' => $locationName,
                            'location_id' => $locationInfo['id']
                        ]);
                    }
                } else {
                    // Если location_id не установлен, меняем маршрут на стандартный
                    $locationInfo['route_name'] = 'battle.outposts.index';
                    \Illuminate\Support\Facades\Log::error('Отсутствует outpost_id и location_id для маршрута battle.outposts.show', [
                        'location_name' => $locationName
                    ]);
                }
            }
        }

        // Получаем список всех доступных локаций для пользователя
        $accessibleLocations = $this->locationAccessService->getAccessibleLocations($user);

        // Проверяем здоровье для входа в боевые локации
        $hpPercent = $actualResources['current_hp'] / max(1, $user->profile->max_hp);
        $canEnterOutposts = $hpPercent >= 0.10; // Изменено с 75% на 10%

        // Формируем хлебные крошки
        $breadcrumbs = [
            ['name' => 'Главная', 'url' => route('home')],
            ['name' => 'Аванпосты', 'url' => null],
        ];

        // Проверка на низкое здоровье
        $hasLowHp = $hpPercent < 0.10; // Изменено с 75% на 10%
        $hpMessage = 'Вам нужно восстановить здоровье выше 10%, чтобы войти в боевую локацию!'; // Изменено сообщение

        // Формируем массив с причинами недоступности локаций
        $locationDeniedReasons = [];
        foreach ($allLocations as $locationName => $locationData) {
            if (!in_array($locationName, $accessibleLocations)) {
                $locationDeniedReasons[$locationName] = $this->locationAccessService->getAccessDeniedReason($user, $locationName);
            }
        }

        // Если у игрока низкое здоровье, не разрешаем вход
        if (!$canEnterOutposts) {
            return view('battle.outposts', [
                'userProfile' => $user->profile,
                'actualResources' => $actualResources,
                'error' => $hpMessage,
                'canEnterOutposts' => $canEnterOutposts,
                'userLevel' => $userLevel,
                'breadcrumbs' => $breadcrumbs,
                'onlineCount' => \App\Models\User::where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)->count(),
                'hasLowHp' => $hasLowHp,
                'hpMessage' => $hpMessage,
                'accessibleLocations' => $accessibleLocations,
                'allLocations' => $allLocations,
                'locationDeniedReasons' => $locationDeniedReasons
            ]);
        }

        // Передаем все необходимые данные в представление
        return view('battle.outposts', [
            'userProfile' => $user->profile,
            'actualResources' => $actualResources,
            'canEnterOutposts' => $canEnterOutposts,
            'userLevel' => $userLevel,
            'breadcrumbs' => $breadcrumbs,
            'onlineCount' => app(\App\Services\OnlineStatusService::class)->getOnlineCount(),
            'hasLowHp' => $hasLowHp,
            'hpMessage' => $hpMessage,
            'accessibleLocations' => $accessibleLocations,
            'allLocations' => $allLocations,
            'locationDeniedReasons' => $locationDeniedReasons
        ]);
    }

    /**
     * Отображение страницы аванпостов для пролога
     *
     * @param \App\Models\User $user
     * @return \Illuminate\View\View
     */
    private function showPrologueOutposts($user)
    {
        // Создаем или получаем ознакомительный аванпост
        $prologueOutpost = $this->getOrCreatePrologueOutpost();

        // Получаем базовые данные для отображения
        $actualResources = $user->profile->getActualResources();
        $userLevel = $user->profile->level;
        $onlineCount = app(\App\Services\OnlineStatusService::class)->getOnlineCount();

        // Хлебные крошки для пролога
        $breadcrumbs = [
            ['name' => 'Главная', 'url' => route('home')],
            ['name' => 'Пролог Аванпост', 'url' => null]
        ];

        // Создаем список локаций только с ознакомительным аванпостом
        // Формируем данные в том же формате, что и в основном методе index
        $prologueLocationData = [
            'id' => $prologueOutpost->id,
            'name' => $prologueOutpost->name,
            'description' => $prologueOutpost->description,
            'image_path' => $prologueOutpost->image_path ?: 'default-location.jpg',
            'min_level' => $prologueOutpost->min_level ?: 1,
            'max_level' => $prologueOutpost->max_level ?: 0,
            'route_name' => $prologueOutpost->route_name ?: 'battle.outposts.show',
            'is_active' => $prologueOutpost->is_active ?? true,
            'order' => $prologueOutpost->order ?: 0,
            'gs_requirement' => 0,
            'location_type' => 'outpost',
            'parent_id' => $prologueOutpost->parent_id,
            'slug' => 'oznakomitelnyy-avanpost'
        ];

        // Получаем запись из OutpostLocation для правильного маршрута
        $outpostLocation = \App\Models\OutpostLocation::where('name', $prologueOutpost->name)->first();
        if ($outpostLocation) {
            $prologueLocationData['outpost_id'] = $outpostLocation->id;
        }

        // Создаем массивы в правильном формате для компонента location-list
        $allLocations = [$prologueOutpost->name => $prologueLocationData];
        $accessibleLocations = [$prologueOutpost->name]; // Массив названий доступных локаций
        $locationDeniedReasons = [];

        return view('battle.outposts', [
            'userProfile' => $user->profile,
            'actualResources' => $actualResources,
            'canEnterOutposts' => true,
            'userLevel' => $userLevel,
            'breadcrumbs' => $breadcrumbs,
            'onlineCount' => $onlineCount,
            'hasLowHp' => false,
            'hpMessage' => null,
            'accessibleLocations' => $accessibleLocations,
            'allLocations' => $allLocations,
            'locationDeniedReasons' => $locationDeniedReasons,
            'isPrologue' => true, // Флаг для отображения модального окна
            'pageTitle' => 'Пролог Аванпост'
        ]);
    }

    /**
     * Получает или создает ознакомительный аванпост для пролога
     *
     * @return \App\Models\Location
     */
    private function getOrCreatePrologueOutpost()
    {
        // Ищем существующий ознакомительный аванпост
        $prologueOutpost = Location::where('name', 'Ознакомительный Аванпост')
            ->where('location_type', 'outpost')
            ->first();

        if (!$prologueOutpost) {
            // Создаем новый ознакомительный аванпост
            $prologueOutpost = Location::create([
                'name' => 'Ознакомительный Аванпост',
                'location_type' => 'outpost',
                'description' => 'Специальный аванпост для ознакомления новичков с механикой игры',
                'is_active' => true,
                'min_level' => 1,
                'max_level' => 0, // Без ограничения
                'order' => 0, // Первый в списке
                'route_name' => 'battle.outposts.show'
            ]);

            // Создаем соответствующую запись в outpost_locations
            \App\Models\OutpostLocation::create([
                'name' => 'Ознакомительный Аванпост',
                'slug' => 'oznakomitelnyy-avanpost',
                'description' => 'Специальный аванпост для ознакомления новичков с механикой игры',
                'is_active' => true,
                'order' => 0,
                'location_id' => $prologueOutpost->id
            ]);

            // Создаем ботов для ознакомительного аванпоста
            $this->createPrologueBots($prologueOutpost->name);

            // Создаем мобов для ознакомительного аванпоста
            $this->createPrologueMobs($prologueOutpost->name, $prologueOutpost->id);

            // Создаем обелиск для ознакомительного аванпоста
            $this->createPrologueObelisk($prologueOutpost->name, $prologueOutpost->id);
        }

        return $prologueOutpost;
    }

    /**
     * Проверяет, является ли пользователь временным пользователем пролога
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    private function isPrologueUser($user): bool
    {
        return str_starts_with($user->email, 'temp_') && str_ends_with($user->email, '@prologue.local');
    }

    /**
     * Создает ботов для ознакомительного аванпоста
     *
     * @param string $locationName
     */
    private function createPrologueBots($locationName)
    {
        $botConfigs = [
            ['race' => 'solarius', 'class' => 'warrior', 'name' => 'Солнечный Воин'],
            ['race' => 'solarius', 'class' => 'mage', 'name' => 'Солнечный Маг'],
            ['race' => 'lunarius', 'class' => 'warrior', 'name' => 'Лунный Воин'],
            ['race' => 'lunarius', 'class' => 'priest', 'name' => 'Лунный Жрец'],
        ];

        foreach ($botConfigs as $config) {
            // Проверяем, не существует ли уже такой бот
            $existingBot = \App\Models\Bot::where('name', $config['name'])
                ->where('location', $locationName)
                ->first();

            if (!$existingBot) {
                \App\Models\Bot::create([
                    'name' => $config['name'],
                    'race' => $config['race'],
                    'class' => $config['class'],
                    'level' => 1,
                    'hp' => 80,
                    'max_hp' => 80,
                    'mp' => 40,
                    'max_mp' => 40,
                    'strength' => 8,
                    'intelligence' => 8,
                    'dexterity' => 8,
                    'armor' => 3,
                    'magic_resistance' => 3,
                    'location' => $locationName,
                    'is_active' => true,
                    'recovery' => 1.0,
                    'respawn_interval' => 30, // 30 секунд на возрождение
                    'created_by_admin' => true // Флаг для новой системы ботов аванпостов
                ]);
            }
        }
    }

    /**
     * Создает мобов для ознакомительного аванпоста
     *
     * @param string $locationName
     * @param int $locationId
     */
    private function createPrologueMobs($locationName, $locationId)
    {
        $mobConfigs = [
            [
                'name' => 'Тренировочная Мишень',
                'hp' => 50,
                'max_hp' => 50,
                'strength' => 5,
                'defense' => 2,
                'agility' => 3,
                'vitality' => 5,
                'intelligence' => 1,
                'experience_reward' => 10,
                'description' => 'Простая мишень для тренировки новичков'
            ],
            [
                'name' => 'Слабый Гоблин',
                'hp' => 60,
                'max_hp' => 60,
                'strength' => 6,
                'defense' => 3,
                'agility' => 4,
                'vitality' => 6,
                'intelligence' => 2,
                'experience_reward' => 15,
                'description' => 'Молодой гоблин, идеальный для первых боев'
            ]
        ];

        foreach ($mobConfigs as $config) {
            // Проверяем, не существует ли уже такой моб
            $existingMob = \App\Models\Mob::where('name', $config['name'])
                ->where('location', $locationName)
                ->first();

            if (!$existingMob) {
                \App\Models\Mob::create(array_merge($config, [
                    'location' => $locationName,
                    'location_id' => $locationId,
                    'slug' => \Illuminate\Support\Str::slug($config['name']),
                    'icon' => 'assets/mobs/default.png',
                    'respawn_time' => 60 // 1 минута на возрождение
                ]));
            }
        }
    }

    /**
     * Создает обелиск для ознакомительного аванпоста
     *
     * @param string $locationName
     * @param int $locationId
     */
    private function createPrologueObelisk($locationName, $locationId)
    {
        // Проверяем, не существует ли уже обелиск для этой локации
        $existingObelisk = \App\Models\Obelisk::where('location_name', $locationName)
            ->where('location_id', $locationId)
            ->first();

        if (!$existingObelisk) {
            \App\Models\Obelisk::create([
                'location_id' => $locationId,
                'location_name' => $locationName,
                'current_state' => 0,
                'max_value' => 1000, // Меньшее значение для пролога
            ]);
        }
    }
}
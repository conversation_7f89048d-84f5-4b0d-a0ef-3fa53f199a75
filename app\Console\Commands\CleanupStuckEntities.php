<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Bot;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class CleanupStuckEntities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'game:cleanup-stuck-entities {--dry-run : Запуск в тестовом режиме без применения изменений}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Очищает застрявшие сущности (ботов и игроков) из счетчиков';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('=== Очистка застрявших сущностей ===');
        $this->info('Режим: ' . ($dryRun ? 'ТЕСТОВЫЙ (без изменений)' : 'РЕАЛЬНЫЙ'));
        $this->newLine();

        $totalCleaned = 0;

        // 1. Очистка мертвых ботов помеченных как активные
        $this->info('1. Поиск мертвых ботов помеченных как активные...');
        $deadActiveBotsCount = Bot::where('is_active', true)
            ->where('hp', '<=', 0)
            ->count();

        if ($deadActiveBotsCount > 0) {
            $this->warn("   Найдено: {$deadActiveBotsCount} мертвых ботов");
            
            if (!$dryRun) {
                $updated = Bot::where('is_active', true)
                    ->where('hp', '<=', 0)
                    ->update(['is_active' => false]);
                
                $this->info("   Деактивировано: {$updated} ботов");
                $totalCleaned += $updated;
                
                Log::info('CleanupStuckEntities: деактивированы мертвые боты', [
                    'count' => $updated
                ]);
            }
        } else {
            $this->info('   Мертвых активных ботов не найдено');
        }

        // 2. Очистка ботов с death_time но is_active = true
        $this->info('2. Поиск ботов с death_time но активных...');
        $deadTimeBotsCount = Bot::where('is_active', true)
            ->whereNotNull('death_time')
            ->count();

        if ($deadTimeBotsCount > 0) {
            $this->warn("   Найдено: {$deadTimeBotsCount} ботов с death_time");
            
            if (!$dryRun) {
                $updated = Bot::where('is_active', true)
                    ->whereNotNull('death_time')
                    ->update(['is_active' => false]);
                
                $this->info("   Деактивировано: {$updated} ботов");
                $totalCleaned += $updated;
                
                Log::info('CleanupStuckEntities: деактивированы боты с death_time', [
                    'count' => $updated
                ]);
            }
        } else {
            $this->info('   Ботов с death_time не найдено');
        }

        // 3. Очистка ботов с некорректными локациями
        $this->info('3. Поиск ботов с некорректными локациями...');
        $invalidLocationBotsCount = Bot::where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('location')
                  ->orWhere('location', '')
                  ->orWhere('location', 'undefined')
                  ->orWhere('location', 'null');
            })
            ->count();

        if ($invalidLocationBotsCount > 0) {
            $this->warn("   Найдено: {$invalidLocationBotsCount} ботов с некорректными локациями");
            
            if (!$dryRun) {
                $updated = Bot::where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('location')
                          ->orWhere('location', '')
                          ->orWhere('location', 'undefined')
                          ->orWhere('location', 'null');
                    })
                    ->update(['is_active' => false]);
                
                $this->info("   Деактивировано: {$updated} ботов");
                $totalCleaned += $updated;
                
                Log::info('CleanupStuckEntities: деактивированы боты с некорректными локациями', [
                    'count' => $updated
                ]);
            }
        } else {
            $this->info('   Ботов с некорректными локациями не найдено');
        }

        // 4. Статистика застрявших игроков (только информация)
        $this->info('4. Проверка застрявших игроков...');
        $stalePlayersCount = User::where('last_activity_timestamp', '<', now()->subMinutes(30)->timestamp)
            ->where('last_activity_timestamp', '>', now()->subDays(1)->timestamp)
            ->whereHas('statistics')
            ->count();

        if ($stalePlayersCount > 0) {
            $this->warn("   Найдено игроков с устаревшей активностью (30 мин - 1 день): {$stalePlayersCount}");
            $this->comment('   Примечание: Игроки не очищаются автоматически, только логируется статистика');
            
            Log::info('CleanupStuckEntities: найдены игроки с устаревшей активностью', [
                'count' => $stalePlayersCount
            ]);
        } else {
            $this->info('   Застрявших игроков не найдено');
        }

        // Результат
        $this->newLine();
        if ($dryRun) {
            $this->info("ТЕСТОВЫЙ РЕЖИМ: Найдено проблем для очистки: " . ($deadActiveBotsCount + $deadTimeBotsCount + $invalidLocationBotsCount));
            $this->comment("Запустите без --dry-run для применения изменений");
        } else {
            $this->info("Всего очищено записей: {$totalCleaned}");
            
            if ($totalCleaned > 0) {
                Log::info('CleanupStuckEntities: общий результат очистки', [
                    'total_cleaned' => $totalCleaned,
                    'dead_active_bots' => $deadActiveBotsCount,
                    'death_time_bots' => $deadTimeBotsCount,
                    'invalid_location_bots' => $invalidLocationBotsCount
                ]);
            }
        }

        return 0;
    }
}
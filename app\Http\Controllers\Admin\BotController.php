<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Bot;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class BotController extends Controller
{
    /**
     * Отображение списка всех ботов с пагинацией и фильтрацией.
     * Показывает страницу управления ботами.
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Получаем значение фильтра локации из запроса
        $selectedLocation = $request->input('location');

        // Запрос для получения ботов
        $botsQuery = Bot::orderBy('id', 'desc');

        // Применяем фильтр по локации (строковое поле), если он задан
        $botsQuery->when($selectedLocation, function ($query, $locationName) {
            return $query->where('location', $locationName);
        });

        // Получаем отфильтрованных ботов с пагинацией
        $bots = $botsQuery->paginate(15)->withQueryString();

        // Получаем все локации для выпадающего списка фильтра
        $locations = Location::orderBy('name')->get();
        
        // Получаем все подлокации рудников для фильтра
        $mineLocations = \App\Models\MineLocation::with('baseLocation')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Передаем данные в шаблон
        return view('admin.bots.index', compact('bots', 'locations', 'mineLocations', 'selectedLocation'));
    }

    /**
     * Показ формы для создания нового бота.
     * Передает список локаций для выпадающего списка.
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Получаем все локации для выпадающего списка
        $locations = Location::orderBy('name')->get();

        // Получаем рудники для отдельного списка (для ботов в рудниках)
        $mineLocations = \App\Models\MineLocation::with('baseLocation')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.bots.create', compact('locations', 'mineLocations'));
    }

    /**
     * Сохранение нового бота в базе данных.
     * Валидирует данные из формы и создает запись.
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:bots,name',
            'race' => 'required|in:solarius,lunarius',
            'class' => 'required|in:warrior,mage,priest',
            'level' => 'required|integer|min:1',
            'hp' => 'required|integer|min:0',
            'max_hp' => 'required|integer|min:1',
            'mp' => 'required|integer|min:0',
            'max_mp' => 'required|integer|min:1',
            'strength' => 'required|integer|min:0',
            'intelligence' => 'required|integer|min:0',
            'dexterity' => 'required|integer|min:0',
            'armor' => 'required|integer|min:0',
            'magic_resistance' => 'required|integer|min:0',
            'location' => 'required|string|max:255',
            'mine_location_id' => 'nullable|exists:mine_locations,id',
            'is_active' => 'required|boolean',
            'respawn_interval' => 'nullable|integer|min:0',
            'created_by_admin' => 'nullable|boolean',
            'base_damage' => 'nullable|integer|min:0',
        ]);

        // Дополнительная валидация: если передан mine_location_id, location должен совпадать с названием подлокации
        $validator->after(function ($validator) use ($request) {
            if ($request->has('mine_location_id') && $request->mine_location_id) {
                $mineLocation = \App\Models\MineLocation::find($request->mine_location_id);
                if ($mineLocation && $request->location !== $mineLocation->name) {
                    $validator->errors()->add('location', 'Локация должна совпадать с выбранной подлокацией рудника.');
                }
            }
        });

        if ($validator->fails()) {
            return redirect()->route('admin.bots.create')
                ->withErrors($validator)
                ->withInput();
        }

        $botData = $validator->validated();

        // Если в форме передан respawn_time_seconds, используем его как respawn_interval
        if (isset($request->respawn_time_seconds)) {
            $botData['respawn_interval'] = $request->respawn_time_seconds;
        }

        // Устанавливаем флаг создания через админ-панель
        $botData['created_by_admin'] = true;

        // Рассчитываем базовый урон на основе силы (согласно требованиям: урон = сила)
        $botData['base_damage'] = $botData['strength'];

        // Обрабатываем локацию рудника
        $this->handleMineLocationAssignment($botData, $request);

        $bot = Bot::create($botData);

        // Принудительная синхронизация с Redis после создания
        $syncResult = $bot->syncToRedis();
        $syncMessage = $syncResult ? ' Данные синхронизированы с Redis.' : ' Ошибка синхронизации с Redis!';

        return redirect()->route('admin.bots.index')
            ->with('success', 'Бот \'' . $bot->name . '\' успешно создан. Базовый урон: ' . $bot->base_damage . $syncMessage);
    }

    /**
     * Отображение информации о конкретном боте.
     * Обычно не используется в CRUD админки, но может быть полезно.
     * @param  \App\Models\Bot  $bot
     * @return \Illuminate\View\View
     */
    public function show(Bot $bot)
    {
        return redirect()->route('admin.bots.edit', $bot->id);
    }

    /**
     * Показ формы для редактирования существующего бота.
     * @param  \App\Models\Bot  $bot
     * @return \Illuminate\View\View
     */
    public function edit(Bot $bot)
    {
        // Получаем все локации для выпадающего списка
        $locations = Location::orderBy('name')->get();

        // Получаем рудники для отдельного списка (для ботов в рудниках)
        $mineLocations = \App\Models\MineLocation::with('baseLocation')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.bots.edit', compact('bot', 'locations', 'mineLocations'));
    }

    /**
     * Обновление информации о существующем боте в базе данных.
     * Валидирует данные и обновляет запись.
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Bot  $bot
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Bot $bot)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:bots,name,' . $bot->id,
            'race' => 'required|in:solarius,lunarius',
            'class' => 'required|in:warrior,mage,priest',
            'level' => 'required|integer|min:1',
            'hp' => 'required|integer|min:0',
            'max_hp' => 'required|integer|min:1',
            'mp' => 'required|integer|min:0',
            'max_mp' => 'required|integer|min:1',
            'strength' => 'required|integer|min:0',
            'intelligence' => 'required|integer|min:0',
            'dexterity' => 'required|integer|min:0',
            'armor' => 'required|integer|min:0',
            'magic_resistance' => 'required|integer|min:0',
            'location' => 'required|string|max:255',
            'mine_location_id' => 'nullable|exists:mine_locations,id',
            'is_active' => 'required|boolean',
            'respawn_interval' => 'nullable|integer|min:0',
            'created_by_admin' => 'nullable|boolean',
            'base_damage' => 'nullable|integer|min:0',
        ]);

        // Дополнительная валидация: если передан mine_location_id, location должен совпадать с названием подлокации
        $validator->after(function ($validator) use ($request) {
            if ($request->has('mine_location_id') && $request->mine_location_id) {
                $mineLocation = \App\Models\MineLocation::find($request->mine_location_id);
                if ($mineLocation && $request->location !== $mineLocation->name) {
                    $validator->errors()->add('location', 'Локация должна совпадать с выбранной подлокацией рудника.');
                }
            }
        });

        if ($validator->fails()) {
            return redirect()->route('admin.bots.edit', $bot->id)
                ->withErrors($validator)
                ->withInput();
        }

        $botData = $validator->validated();

        // Если в форме передан respawn_time_seconds, используем его как respawn_interval
        if (isset($request->respawn_time_seconds)) {
            $botData['respawn_interval'] = $request->respawn_time_seconds;
        }

        // Пересчитываем базовый урон при изменении силы
        $botData['base_damage'] = $botData['strength'];

        // Обрабатываем локацию рудника
        $this->handleMineLocationAssignment($botData, $request);

        $bot->update($botData);

        // Принудительная синхронизация с Redis после обновления
        $syncResult = $bot->syncToRedis();
        $syncMessage = $syncResult ? ' Данные синхронизированы с Redis.' : ' Ошибка синхронизации с Redis!';

        return redirect()->route('admin.bots.index')
            ->with('success', 'Бот \'' . $bot->name . '\' успешно обновлен. Базовый урон: ' . $bot->base_damage . $syncMessage);
    }

    /**
     * Удаление бота из базы данных.
     * @param  \App\Models\Bot  $bot
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Bot $bot)
    {
        $botName = $bot->name;
        $bot->delete();

        return redirect()->route('admin.bots.index')
            ->with('success', 'Бот \'' . $botName . '\' успешно удален.');
    }

    /**
     * Принудительно воскрешает бота независимо от времени
     *
     * @param  \App\Models\Bot  $bot
     * @return \Illuminate\Http\RedirectResponse
     */
    public function respawn(Bot $bot)
    {
        // Воскрешаем бота независимо от времени
        $bot->respawn(true);

        return redirect()->route('admin.bots.index')
            ->with('success', 'Бот \'' . $bot->name . '\' успешно воскрешен.');
    }

    /**
     * Удаление всех ботов из базы данных.
     * Метод для массового удаления всех ботов.
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyAll()
    {
        try {
            // Получаем количество ботов перед удалением для отчета
            $botsCount = Bot::count();

            if ($botsCount === 0) {
                return redirect()->route('admin.bots.index')
                    ->with('info', 'В базе данных нет ботов для удаления.');
            }

            // Удаляем всех ботов
            Bot::truncate(); // Более эффективно чем delete() для полной очистки таблицы

            return redirect()->route('admin.bots.index')
                ->with('success', "Успешно удалено {$botsCount} ботов из базы данных.");

        } catch (\Exception $e) {
            return redirect()->route('admin.bots.index')
                ->with('error', 'Произошла ошибка при удалении ботов: ' . $e->getMessage());
        }
    }

    /**
     * Обрабатывает назначение локации рудника для бота
     * 
     * @param array $botData
     * @param Request $request
     * @return void
     */
    private function handleMineLocationAssignment(array &$botData, Request $request): void
    {
        if ($request->has('mine_location_id') && $request->mine_location_id) {
            $mineLocation = \App\Models\MineLocation::with('baseLocation')->find($request->mine_location_id);
            
            if ($mineLocation) {
                // ИСПРАВЛЕНИЕ: Устанавливаем локацию как ИМЯ ПОДЛОКАЦИИ рудника
                // Это обеспечивает изоляцию между подлокациями
                $botData['location'] = $mineLocation->name;
                $botData['mine_location_id'] = $mineLocation->id;
                
                \Log::info("Бот создан/обновлен в подлокации рудника", [
                    'mine_location_name' => $mineLocation->name,
                    'mine_location_id' => $mineLocation->id,
                    'bot_location' => $botData['location']
                ]);
            }
        } else {
            // Если не выбрана локация рудника, сбрасываем mine_location_id
            // Это происходит когда выбрана обычная локация
            $botData['mine_location_id'] = null;
            
            \Log::info("Бот создан/обновлен в обычной локации", [
                'location' => $botData['location'],
                'mine_location_id' => null
            ]);
        }
    }
}

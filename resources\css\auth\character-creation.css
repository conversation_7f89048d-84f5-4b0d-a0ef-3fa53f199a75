/* Стили для кастомных селектов создания персонажа */

.custom-select-container {
    position: relative;
}

.custom-select-button {
    cursor: pointer;
    user-select: none;
}

.custom-select-button:hover {
    border-color: rgba(193, 169, 110, 0.7);
}

.custom-select-button:focus {
    border-color: #c1a96e;
    box-shadow: 0 0 0 2px rgba(193, 169, 110, 0.2);
}

.custom-select-dropdown {
    border-color: #3b3629;
    background-color: #1a1814;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.custom-select-item {
    border-bottom: 1px solid rgba(59, 54, 41, 0.3);
}

.custom-select-item:last-child {
    border-bottom: none;
}

.custom-select-item:hover {
    background-color: #2a2722;
}

.custom-select-item img {
    flex-shrink: 0;
    object-fit: cover;
    border-radius: 2px;
}

/* Анимации для стрелки */
.custom-select-button svg {
    transition: transform 0.2s ease;
}

/* Скрытие оригинального селекта */
.custom-select-container select {
    display: none !important;
}

/* Адаптивные стили */
@media (max-width: 640px) {
    .custom-select-dropdown {
        max-height: 200px;
    }
    
    .custom-select-item {
        padding: 2.5rem 0.75rem;
    }
    
    .custom-select-item img {
        width: 1.125rem;
        height: 1.125rem;
    }
}

/* Стили для заблокированного состояния */
.custom-select-container.disabled .custom-select-button {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #161411;
}

.custom-select-container.disabled .custom-select-button:hover {
    border-color: #3b3629;
}

/* Стили для выбранного элемента */
.custom-select-item.selected {
    background-color: rgba(193, 169, 110, 0.1);
    color: #c1a96e;
}
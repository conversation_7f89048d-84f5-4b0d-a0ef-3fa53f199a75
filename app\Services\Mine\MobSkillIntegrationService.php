<?php

namespace App\Services\Mine;

use App\Models\Mob;
use App\Models\User;
use App\Models\MineLocation;
use App\Services\MobSkillFramework;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\Log;

/**
 * Сервис интеграции системы скиллов мобов с рудниками
 * 
 * Интегрирует MobSkillFramework с существующей системой боевки в рудниках:
 * - Обрабатывает скиллы мобов при атаках
 * - Реагирует на события (атака, получение урона, обнаружение игроков)
 * - Предоставляет специализированную логику для рудничных мобов
 */
class MobSkillIntegrationService
{
    protected MobSkillFramework $skillFramework;
    protected MineDetectionService $detectionService;

    public function __construct(
        MobSkillFramework $skillFramework,
        MineDetectionService $detectionService
    ) {
        $this->skillFramework = $skillFramework;
        $this->detectionService = $detectionService;
    }

    /**
     * Обрабатывает скиллы моба при атаке на игрока
     */
    public function processMobAttackSkills(Mob $mob, User $target, array $attackContext = []): array
    {
        $mineLocation = $this->getMineLocationFromContext($attackContext);
        
        $context = array_merge($attackContext, [
            'event_type' => 'attack',
            'mine_location_id' => $mineLocation?->id,
            'target_type' => 'player',
            'is_mine_mob' => $mob->mob_type === 'mine'
        ]);

        return $this->skillFramework->processMobSkills($mob, $target, $context);
    }

    /**
     * Обрабатывает скиллы моба при получении урона
     */
    public function processMobDamagedSkills(Mob $mob, User $attacker, int $damage, array $context = []): array
    {
        $mineLocation = $this->getMineLocationFromContext($context);
        
        $context = array_merge($context, [
            'event_type' => 'damaged',
            'damage_received' => $damage,
            'attacker_id' => $attacker->id,
            'mine_location_id' => $mineLocation?->id
        ]);

        return $this->skillFramework->processMobSkills($mob, $attacker, $context);
    }

    /**
     * Обрабатывает скиллы обнаружения при появлении обнаруженных игроков
     */
    public function processDetectionSkills(Mob $mob, array $detectedPlayers, MineLocation $mineLocation): array
    {
        $context = [
            'event_type' => 'detection',
            'detected_players' => $detectedPlayers,
            'detected_count' => count($detectedPlayers),
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id
        ];

        return $this->skillFramework->processMobSkills($mob, null, $context);
    }

    /**
     * Обрабатывает периодические скиллы мобов (например, при патрулировании)
     */
    public function processPeriodicSkills(Mob $mob, MineLocation $mineLocation): array
    {
        // Получаем информацию о обнаруженных игроках в локации
        $detectedPlayers = $this->detectionService->getDetectedPlayers(
            $mineLocation->location_id,
            $mineLocation->id
        );

        $context = [
            'event_type' => 'periodic',
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'detected_players_count' => count($detectedPlayers),
            'has_detected_players' => !empty($detectedPlayers)
        ];

        return $this->skillFramework->processMobSkills($mob, null, $context);
    }

    /**
     * Обрабатывает скиллы моба при низком здоровье
     */
    public function processLowHealthSkills(Mob $mob, array $context = []): array
    {
        $mineLocation = $this->getMineLocationFromContext($context);
        
        $context = array_merge($context, [
            'event_type' => 'low_health',
            'hp_percent' => $mob->hp / $mob->max_hp,
            'mine_location_id' => $mineLocation?->id
        ]);

        return $this->skillFramework->processMobSkills($mob, null, $context);
    }

    /**
     * Создает контекст для специализированных рудничных скиллов
     */
    public function createMineSpecificContext(Mob $mob, MineLocation $mineLocation, array $additionalContext = []): array
    {
        // Получаем информацию о обнаруженных игроках
        $detectedPlayers = $this->detectionService->getDetectedPlayers(
            $mineLocation->location_id,
            $mineLocation->id
        );

        // Определяем опасность локации по количеству игроков
        $playerCount = $this->getPlayersInLocation($mineLocation);
        $threatLevel = $this->calculateThreatLevel($playerCount, count($detectedPlayers));

        return array_merge($additionalContext, [
            'mine_location_id' => $mineLocation->id,
            'mine_name' => $mineLocation->name,
            'location_id' => $mineLocation->location_id,
            'detected_players_count' => count($detectedPlayers),
            'total_players_count' => $playerCount,
            'threat_level' => $threatLevel,
            'is_mine_environment' => true,
            'mob_type' => $mob->mob_type
        ]);
    }

    /**
     * Получает рудничную локацию из контекста
     */
    protected function getMineLocationFromContext(array $context): ?MineLocation
    {
        if (isset($context['mine_location_id'])) {
            return MineLocation::find($context['mine_location_id']);
        }

        if (isset($context['location_slug'])) {
            return MineLocation::where('slug', $context['location_slug'])->first();
        }

        return null;
    }

    /**
     * Подсчитывает количество игроков в локации
     */
    protected function getPlayersInLocation(MineLocation $mineLocation): int
    {
        return \App\Models\User::whereHas('statistics', function ($query) use ($mineLocation) {
            $query->where('current_location', $mineLocation->slug);
        })->count();
    }

    /**
     * Вычисляет уровень угрозы для мобов
     */
    protected function calculateThreatLevel(int $totalPlayers, int $detectedPlayers): string
    {
        if ($detectedPlayers >= 3) {
            return 'critical'; // Критический уровень - много обнаруженных игроков
        } elseif ($detectedPlayers >= 1 || $totalPlayers >= 5) {
            return 'high'; // Высокий уровень - есть обнаруженные или много игроков
        } elseif ($totalPlayers >= 2) {
            return 'medium'; // Средний уровень - несколько игроков
        } else {
            return 'low'; // Низкий уровень - мало или нет игроков
        }
    }

    /**
     * Создает адаптивные скиллы для рудничных мобов
     */
    public function createAdaptiveMineSkills(Mob $mob): array
    {
        $suggestions = [];

        // Базовые скиллы в зависимости от типа моба
        switch ($mob->mob_type) {
            case 'mine':
                $suggestions[] = [
                    'name' => 'Охранник рудника',
                    'skill_type' => MobSkillFramework::SKILL_TYPE_DETECTION,
                    'trigger' => MobSkillFramework::TRIGGER_PLAYER_DETECTED,
                    'chance' => 70,
                    'effect_data' => [
                        'detection_duration' => 180,
                        'detection_bonus' => 1.5,
                        'damage_bonus_vs_detected' => 1.3
                    ]
                ];

                $suggestions[] = [
                    'name' => 'Яростный рывок',
                    'skill_type' => MobSkillFramework::SKILL_TYPE_DAMAGE,
                    'trigger' => MobSkillFramework::TRIGGER_LOW_HP,
                    'chance' => 85,
                    'effect_data' => [
                        'damage_multiplier' => 2.0,
                        'hp_threshold' => 0.3
                    ]
                ];

                $suggestions[] = [
                    'name' => 'Каменная броня',
                    'skill_type' => MobSkillFramework::SKILL_TYPE_SELF_BUFF,
                    'trigger' => MobSkillFramework::TRIGGER_MULTIPLE_ENEMIES,
                    'chance' => 60,
                    'effect_data' => [
                        'buff_type' => 'armor',
                        'duration' => 60,
                        'power' => 50,
                        'min_enemies' => 2
                    ]
                ];
                break;

            case 'dungeon':
                $suggestions[] = [
                    'name' => 'Темная аура',
                    'skill_type' => MobSkillFramework::SKILL_TYPE_DEBUFF,
                    'trigger' => MobSkillFramework::TRIGGER_ON_ATTACK,
                    'chance' => 40,
                    'effect_data' => [
                        'debuff_type' => 'weakness',
                        'duration' => 45,
                        'power' => 25
                    ]
                ];
                break;

            default:
                $suggestions[] = [
                    'name' => 'Базовый удар',
                    'skill_type' => MobSkillFramework::SKILL_TYPE_DAMAGE,
                    'trigger' => MobSkillFramework::TRIGGER_ON_ATTACK,
                    'chance' => 30,
                    'effect_data' => [
                        'damage_multiplier' => 1.2
                    ]
                ];
        }

        return $suggestions;
    }

    /**
     * Получает статистику использования скиллов мобом
     */
    public function getMobSkillStatistics(Mob $mob): array
    {
        $activeEffects = $this->skillFramework->getMobActiveEffects($mob);
        $mobSkills = $mob->skills()->with('skill')->get();

        return [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'mob_type' => $mob->mob_type,
            'total_skills' => $mobSkills->count(),
            'active_effects' => count($activeEffects),
            'skills_on_cooldown' => $mobSkills->filter(function ($skill) {
                return $skill->cooldown_ends_at && now()->lt($skill->cooldown_ends_at);
            })->count(),
            'available_skills' => $mobSkills->filter(function ($skill) {
                return !$skill->cooldown_ends_at || now()->gte($skill->cooldown_ends_at);
            })->count(),
            'effects_by_type' => collect($activeEffects)->groupBy('effect_type')->map->count(),
            'last_skill_used' => $mobSkills->max('updated_at')
        ];
    }
}
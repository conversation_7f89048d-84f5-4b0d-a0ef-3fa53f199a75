<?php

namespace App\Traits;

use App\Models\Location;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

/**
 * Трейт для работы с локациями Аванпостов
 * Обеспечивает централизованное управление списком Аванпостов с кэшированием
 */
trait OutpostLocationTrait
{
    /**
     * Ключ кэша для списка Аванпостов
     */
    private const OUTPOST_CACHE_KEY = 'outpost_locations_list';

    /**
     * Время жизни кэша в секундах (1 час)
     */
    private const CACHE_TTL = 3600;

    /**
     * Получить все активные Аванпосты из базы данных
     * Результат кэшируется в Redis для оптимизации производительности
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getOutpostLocations()
    {
        try {
            // Попытка получить из кэша
            $cachedData = Redis::get(self::OUTPOST_CACHE_KEY);

            if ($cachedData) {
                $locations = unserialize($cachedData);
                Log::info('Аванпосты получены из кэша', ['count' => $locations->count()]);
                return $locations;
            }

            // Получение из базы данных
            $locations = Location::where('location_type', 'outpost')
                ->where('is_active', true)
                ->orderBy('order')
                ->orderBy('name')
                ->get(['id', 'name', 'location_type', 'is_active']);

            // Кэширование результата
            Redis::setex(self::OUTPOST_CACHE_KEY, self::CACHE_TTL, serialize($locations));

            Log::info('Аванпосты получены из БД и закэшированы', ['count' => $locations->count()]);

            return $locations;

        } catch (\Exception $e) {
            Log::error('Ошибка при получении списка Аванпостов', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Возвращаем пустую коллекцию в случае ошибки
            return collect();
        }
    }

    /**
     * Получить только названия активных Аванпостов
     *
     * @return array
     */
    protected function getOutpostLocationNames(): array
    {
        return $this->getOutpostLocations()->pluck('name')->toArray();
    }

    /**
     * Проверить, является ли локация Аванпостом
     *
     * @param string $locationName Название локации
     * @return bool
     */
    public function isOutpostLocation(string $locationName): bool
    {
        $outpostNames = $this->getOutpostLocationNames();
        return in_array($locationName, $outpostNames, true);
    }

    /**
     * Получить список Аванпостов для боевых локаций (включая рудники)
     * Используется в BotBehaviorService для определения локаций с повышенной агрессивностью
     *
     * @return array
     */
    protected function getBattleLocationNames(): array
    {
        try {
            // Получаем Аванпосты
            $outpostNames = $this->getOutpostLocationNames();

            // Получаем рудники (если есть)
            $mineNames = Location::where('location_type', 'mine')
                ->where('is_active', true)
                ->pluck('name')
                ->toArray();

            // Объединяем списки
            $battleLocations = array_merge($outpostNames, $mineNames);

            Log::info('Получен список боевых локаций', [
                'outposts' => count($outpostNames),
                'mines' => count($mineNames),
                'total' => count($battleLocations)
            ]);

            return $battleLocations;

        } catch (\Exception $e) {
            Log::error('Ошибка при получении боевых локаций', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Проверить, является ли локация боевой (Аванпост или рудник)
     *
     * @param string $locationName Название локации
     * @return bool
     */
    protected function isBattleLocation(string $locationName): bool
    {
        $battleLocations = $this->getBattleLocationNames();
        return in_array($locationName, $battleLocations, true);
    }

    /**
     * Проверить, является ли локация рудником
     *
     * @param string $locationName Название локации
     * @return bool
     */
    protected function isMineLocation(string $locationName): bool
    {
        try {
            $mineNames = Location::where('location_type', 'mine')
                ->where('is_active', true)
                ->pluck('name')
                ->toArray();
            
            return in_array($locationName, $mineNames, true);
        } catch (\Exception $e) {
            Log::error('Ошибка при проверке рудника', [
                'location' => $locationName,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Очистить кэш Аванпостов
     * Используется при изменении локаций через админ-панель
     *
     * @return bool
     */
    protected function clearOutpostCache(): bool
    {
        try {
            $result = Redis::del(self::OUTPOST_CACHE_KEY);
            Log::info('Кэш Аванпостов очищен', ['result' => $result]);
            return (bool) $result;
        } catch (\Exception $e) {
            Log::error('Ошибка при очистке кэша Аванпостов', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Получить статистику по Аванпостам
     * Используется в командах для отображения информации
     *
     * @return array
     */
    protected function getOutpostStats(): array
    {
        try {
            $locations = $this->getOutpostLocations();

            $stats = [
                'total_outposts' => $locations->count(),
                'active_outposts' => $locations->where('is_active', true)->count(),
                'outpost_names' => $locations->pluck('name')->toArray(),
                'cache_status' => Redis::exists(self::OUTPOST_CACHE_KEY) ? 'cached' : 'not_cached'
            ];

            return $stats;

        } catch (\Exception $e) {
            Log::error('Ошибка при получении статистики Аванпостов', [
                'error' => $e->getMessage()
            ]);

            return [
                'total_outposts' => 0,
                'active_outposts' => 0,
                'outpost_names' => [],
                'cache_status' => 'error'
            ];
        }
    }
}

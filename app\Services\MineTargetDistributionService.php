<?php

namespace App\Services;

use App\Models\User;
use App\Models\Mob;
use App\Models\MineLocation;
use App\Models\MineMark;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Сервис для умного распределения мобов между игроками с дебафом "Замечен" в рудниках
 */
class MineTargetDistributionService
{
    private const REDIS_KEY_PREFIX = 'mine_mob_targets';
    private const REDIS_TTL = 300; // 5 минут
    private const MAX_MOBS_PER_PLAYER = 3; // Максимум мобов на одного игрока
    private const ATTACK_COOLDOWN_SECONDS = 25; // Кулдаун между атаками на одного игрока
    private const LOW_HP_THRESHOLD = 30; // Порог низкого HP в процентах
    private const SWITCH_TARGET_PROBABILITY = 20; // Вероятность смены цели в процентах

    /**
     * Получить оптимальную пару моб-игрок для атаки
     *
     * @param Collection $markedPlayers Игроки с дебафом "Замечен"
     * @param MineLocation $mineLocation Локация рудника
     * @return array|null ['mob' => Mob, 'player' => User] или null
     */
    public function getOptimalMobPlayerPair(Collection $markedPlayers, MineLocation $mineLocation): ?array
    {
        if ($markedPlayers->isEmpty()) {
            return null;
        }

        // Получаем доступных мобов в руднике
        $availableMobs = $this->getAvailableMobs($mineLocation);

        if ($availableMobs->isEmpty()) {
            Log::info('MineTargetDistribution: Нет доступных мобов в руднике', [
                'mine_location_id' => $mineLocation->id,
                'location_name' => $mineLocation->name
            ]);
            return null;
        }

        // Получаем текущие назначения из Redis
        $currentAssignments = $this->getCurrentAssignments($mineLocation);

        // Фильтруем игроков по кулдауну
        $availablePlayers = $this->filterPlayersByCooldown($markedPlayers);

        if ($availablePlayers->isEmpty()) {
            Log::info('MineTargetDistribution: Все игроки находятся в кулдауне', [
                'mine_location_id' => $mineLocation->id,
                'total_marked_players' => $markedPlayers->count()
            ]);
            return null;
        }

        // Выбираем оптимальную цель с учетом приоритетов
        $targetPlayer = $this->selectOptimalTarget($availablePlayers, $currentAssignments);

        if (!$targetPlayer) {
            return null;
        }

        // Выбираем моба для атаки на выбранного игрока
        $mob = $this->selectMobForTarget($availableMobs, $targetPlayer, $currentAssignments);

        if (!$mob) {
            return null;
        }

        // Записываем назначение в Redis
        $this->assignMobToPlayer($mob, $targetPlayer, $mineLocation);

        Log::info('MineTargetDistribution: Создана пара моб-игрок', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'player_id' => $targetPlayer->id,
            'player_name' => $targetPlayer->name,
            'mine_location' => $mineLocation->name,
            'player_hp_percent' => round(($targetPlayer->profile->current_hp / $targetPlayer->profile->max_hp) * 100, 1)
        ]);

        return [
            'mob' => $mob,
            'player' => $targetPlayer
        ];
    }

    /**
     * Получить доступных мобов в руднике
     */
    private function getAvailableMobs(MineLocation $mineLocation): Collection
    {
        return Mob::where('location_id', $mineLocation->location_id)
            ->where('mob_type', 'mine')
            ->where('hp', '>', 0)
            ->where(function ($query) {
                $query->whereNull('death_time')
                    ->orWhere('death_time', '<', now()->subMinutes(5));
            })
            ->get();
    }

    /**
     * Фильтровать игроков по кулдауну атак
     */
    private function filterPlayersByCooldown(Collection $markedPlayers): Collection
    {
        return $markedPlayers->filter(function ($playerData) {
            $lastAttackAt = $playerData['last_attack_at'] ?? null;

            if (!$lastAttackAt) {
                return true; // Если атак не было, игрок доступен
            }

            $cooldownEnd = Carbon::parse($lastAttackAt)->addSeconds(self::ATTACK_COOLDOWN_SECONDS);
            return $cooldownEnd->isPast();
        });
    }

    /**
     * Выбрать оптимальную цель с учетом приоритетов
     */
    private function selectOptimalTarget(Collection $availablePlayers, array $currentAssignments): ?User
    {
        // Приоритет 1: Игроки с низким HP
        $lowHpPlayers = $this->filterLowHpPlayers($availablePlayers);

        if ($lowHpPlayers->isNotEmpty()) {
            return $this->selectLeastTargetedPlayer($lowHpPlayers, $currentAssignments);
        }

        // Приоритет 2: Игроки с наименьшим количеством атакующих мобов
        return $this->selectLeastTargetedPlayer($availablePlayers, $currentAssignments);
    }

    /**
     * Фильтровать игроков с низким HP
     */
    private function filterLowHpPlayers(Collection $players): Collection
    {
        return $players->filter(function ($playerData) {
            $player = $playerData['player'];
            $hpPercent = ($player->profile->current_hp / $player->profile->max_hp) * 100;
            return $hpPercent <= self::LOW_HP_THRESHOLD;
        });
    }

    /**
     * Выбрать игрока с наименьшим количеством атакующих мобов
     */
    private function selectLeastTargetedPlayer(Collection $players, array $currentAssignments): ?User
    {
        $playersByMobCount = $players->map(function ($playerData) use ($currentAssignments) {
            $player = $playerData['player'];
            $mobCount = count($currentAssignments[$player->id] ?? []);

            return [
                'player' => $player,
                'mob_count' => $mobCount,
                'hp_percent' => ($player->profile->current_hp / $player->profile->max_hp) * 100
            ];
        });

        // Сортируем по количеству мобов, затем по HP
        $sorted = $playersByMobCount->sortBy([
            ['mob_count', 'asc'],
            ['hp_percent', 'asc']
        ]);

        $selected = $sorted->first();
        return $selected ? $selected['player'] : null;
    }

    /**
     * Выбрать моба для атаки на конкретного игрока с учетом интеллектуальных приоритетов
     */
    private function selectMobForTarget(Collection $availableMobs, User $targetPlayer, array $currentAssignments): ?Mob
    {
        // Проверяем, не превышен ли лимит мобов для этого игрока
        $currentMobCount = count($currentAssignments[$targetPlayer->id] ?? []);

        if ($currentMobCount >= self::MAX_MOBS_PER_PLAYER) {
            // Если лимит превышен, есть шанс переключить одного из мобов
            if (rand(1, 100) <= self::SWITCH_TARGET_PROBABILITY) {
                return $this->selectMobByStrength($availableMobs, $targetPlayer);
            }
            return null;
        }

        // Выбираем моба с учетом силы и HP игрока
        return $this->selectMobByStrength($availableMobs, $targetPlayer);
    }

    /**
     * Выбрать моба с учетом силы и состояния игрока
     */
    private function selectMobByStrength(Collection $availableMobs, User $targetPlayer): ?Mob
    {
        $playerHpPercent = ($targetPlayer->profile->current_hp / $targetPlayer->profile->max_hp) * 100;

        // Если у игрока низкое HP, выбираем более слабого моба
        if ($playerHpPercent <= self::LOW_HP_THRESHOLD) {
            $weakMobs = $availableMobs->sortBy('strength')->take(ceil($availableMobs->count() * 0.6));
            return $weakMobs->random();
        }

        // Если у игрока высокое HP, можем выбрать более сильного моба
        if ($playerHpPercent >= 80) {
            $strongMobs = $availableMobs->sortByDesc('strength')->take(ceil($availableMobs->count() * 0.4));
            return $strongMobs->random();
        }

        // Для среднего HP выбираем случайного моба
        return $availableMobs->random();
    }

    /**
     * Получить текущие назначения мобов из Redis
     */
    private function getCurrentAssignments(MineLocation $mineLocation): array
    {
        $redisKey = $this->getRedisKey($mineLocation);
        $assignments = Redis::hgetall($redisKey);

        $result = [];
        foreach ($assignments as $playerId => $mobIds) {
            $result[$playerId] = json_decode($mobIds, true) ?: [];
        }

        return $result;
    }

    /**
     * Назначить моба игроку в Redis
     */
    private function assignMobToPlayer(Mob $mob, User $player, MineLocation $mineLocation): void
    {
        $redisKey = $this->getRedisKey($mineLocation);

        // Получаем текущие назначения для игрока
        $currentMobs = Redis::hget($redisKey, $player->id);
        $mobIds = $currentMobs ? json_decode($currentMobs, true) : [];

        // Добавляем нового моба
        $mobIds[] = [
            'mob_id' => $mob->id,
            'assigned_at' => now()->toISOString()
        ];

        // Ограничиваем количество мобов
        if (count($mobIds) > self::MAX_MOBS_PER_PLAYER) {
            $mobIds = array_slice($mobIds, -self::MAX_MOBS_PER_PLAYER);
        }

        // Сохраняем в Redis
        Redis::hset($redisKey, $player->id, json_encode($mobIds));
        Redis::expire($redisKey, self::REDIS_TTL);
    }

    /**
     * Очистить истекшие назначения
     */
    public function cleanupExpiredAssignments(MineLocation $mineLocation): void
    {
        $redisKey = $this->getRedisKey($mineLocation);
        $assignments = Redis::hgetall($redisKey);

        foreach ($assignments as $playerId => $mobIds) {
            $mobs = json_decode($mobIds, true) ?: [];
            $validMobs = [];

            foreach ($mobs as $mobData) {
                $assignedAt = Carbon::parse($mobData['assigned_at']);
                if ($assignedAt->addSeconds(self::REDIS_TTL)->isFuture()) {
                    $validMobs[] = $mobData;
                }
            }

            if (empty($validMobs)) {
                Redis::hdel($redisKey, $playerId);
            } else {
                Redis::hset($redisKey, $playerId, json_encode($validMobs));
            }
        }
    }

    /**
     * Получить ключ Redis для локации
     */
    private function getRedisKey(MineLocation $mineLocation): string
    {
        return self::REDIS_KEY_PREFIX . ':' . $mineLocation->id;
    }

    /**
     * Получить статистику распределения для отладки
     */
    public function getDistributionStats(MineLocation $mineLocation): array
    {
        $assignments = $this->getCurrentAssignments($mineLocation);

        $stats = [
            'total_players_with_mobs' => count($assignments),
            'total_mob_assignments' => 0,
            'players' => []
        ];

        foreach ($assignments as $playerId => $mobs) {
            $mobCount = count($mobs);
            $stats['total_mob_assignments'] += $mobCount;
            $stats['players'][$playerId] = $mobCount;
        }

        return $stats;
    }
}

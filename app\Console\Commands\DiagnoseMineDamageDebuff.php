<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\ActiveEffect;
use App\Models\MineMark;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\Schema;

class DiagnoseMineDamageDebuff extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'debug:mine-debuff-display {user_id?}';

    /**
     * The description of the console command.
     */
    protected $description = 'Диагностика проблемы с отображением дебаффа "Замечен!" в рудниках';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id') ?? 1; // По умолчанию admin
        
        $this->info("=== ДИАГНОСТИКА ДЕБАФФА РУДНИКОВ ===");
        $this->info("Пользователь ID: {$userId}");
        
        // Находим пользователя
        $user = User::find($userId);
        if (!$user) {
            $this->error("Пользователь с ID {$userId} не найден!");
            return 1;
        }
        
        $this->info("Пользователь: {$user->name}");
        
        // Проверяем таблицы
        $this->info("\n=== ПРОВЕРКА ТАБЛИЦ ===");
        $hasMineMark = Schema::hasTable('mine_marks');
        $hasActiveEffects = Schema::hasTable('active_effects');
        $hasMineLocations = Schema::hasTable('mine_locations');
        
        $this->info("Таблица mine_marks: " . ($hasMineMark ? "✓" : "✗"));
        $this->info("Таблица active_effects: " . ($hasActiveEffects ? "✓" : "✗"));
        $this->info("Таблица mine_locations: " . ($hasMineLocations ? "✓" : "✗"));
        
        if (!$hasActiveEffects) {
            $this->error("Критическая ошибка: таблица active_effects не найдена!");
            return 1;
        }
        
        // Найдем тестовую локацию рудника
        if (!$hasMineLocations) {
            $this->error("Таблица mine_locations не найдена!");
            return 1;
        }
        
        $mineLocation = MineLocation::first();
        if (!$mineLocation) {
            $this->error("Не найдено ни одной локации рудника!");
            return 1;
        }
        
        $this->info("Тестовая локация: {$mineLocation->name} (ID: {$mineLocation->id})");
        
        // Проверяем текущие активные эффекты пользователя
        $this->info("\n=== ТЕКУЩИЕ АКТИВНЫЕ ЭФФЕКТЫ ===");
        $allEffects = $user->activeEffects()->get();
        $this->info("Всего эффектов в БД: " . $allEffects->count());
        
        if ($allEffects->count() > 0) {
            $this->table(['ID', 'Тип', 'Название', 'Активен', 'Истекает', 'Данные'], 
                $allEffects->map(function($effect) {
                    return [
                        $effect->id,
                        $effect->effect_type ?? 'NULL',
                        $effect->effect_name ?? 'NULL',
                        $effect->isActive() ? 'ДА' : 'НЕТ',
                        $effect->ends_at ? $effect->ends_at->format('Y-m-d H:i:s') : 'NULL',
                        json_encode($effect->effect_data ?? [])
                    ];
                })->toArray()
            );
        }
        
        // Фильтруем активные эффекты (как в контроллере)
        $activeEffects = $allEffects->filter(fn($effect) => $effect->isActive());
        $this->info("Активных эффектов: " . $activeEffects->count());
        
        // Проверяем mine_marks (если таблица существует)
        if ($hasMineMark) {
            $this->info("\n=== МЕТКИ РУДНИКОВ (mine_marks) ===");
            $mineMarks = MineMark::where('player_id', $userId)->get();
            $this->info("Всего меток в БД: " . $mineMarks->count());
            
            if ($mineMarks->count() > 0) {
                $this->table(['ID', 'Mine Location', 'Активна', 'Истекает'], 
                    $mineMarks->map(function($mark) {
                        return [
                            $mark->id,
                            $mark->location_name,
                            $mark->is_active && $mark->expires_at > now() ? 'ДА' : 'НЕТ',
                            $mark->expires_at->format('Y-m-d H:i:s')
                        ];
                    })->toArray()
                );
            }
        }
        
        // Тестируем создание дебаффа
        $this->info("\n=== ТЕСТ СОЗДАНИЯ ДЕБАФФА ===");
        
        try {
            $mineDetectionService = app(MineDetectionService::class);
            
            $this->info("Применяем дебафф через MineDetectionService...");
            $result = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
            
            if ($result) {
                $this->info("✓ Дебафф успешно создан!");
                
                // Проверяем, что создалось
                $this->info("\n=== РЕЗУЛЬТАТ СОЗДАНИЯ ===");
                
                // Проверяем MineMark
                if ($hasMineMark) {
                    $newMark = MineMark::where('player_id', $userId)
                        ->where('mine_location_id', $mineLocation->id)
                        ->latest()->first();
                    
                    if ($newMark) {
                        $this->info("✓ MineMark создана: ID {$newMark->id}, истекает {$newMark->expires_at}");
                    } else {
                        $this->error("✗ MineMark не создана!");
                    }
                }
                
                // Проверяем ActiveEffect
                $newEffect = ActiveEffect::where('effect_type', 'mine_detection')
                    ->where('target_type', 'App\\Models\\User')
                    ->where('target_id', $userId)
                    ->latest()->first();
                
                if ($newEffect) {
                    $this->info("✓ ActiveEffect создан: ID {$newEffect->id}, активен: " . 
                        ($newEffect->isActive() ? 'ДА' : 'НЕТ'));
                    $this->info("  - Название: {$newEffect->effect_name}");
                    $this->info("  - Истекает: {$newEffect->ends_at}");
                    $this->info("  - Данные: " . json_encode($newEffect->effect_data));
                } else {
                    $this->error("✗ ActiveEffect не создан!");
                }
                
                // Перепроверяем активные эффекты пользователя
                $this->info("\n=== ПРОВЕРКА ПОСЛЕ СОЗДАНИЯ ===");
                $user->refresh();
                $updatedActiveEffects = $user->activeEffects()->get()->filter(fn($effect) => $effect->isActive());
                $this->info("Активных эффектов после создания: " . $updatedActiveEffects->count());
                
                foreach ($updatedActiveEffects as $effect) {
                    $this->info("  - {$effect->effect_type}: {$effect->effect_name} (активен: " . 
                        ($effect->isActive() ? 'ДА' : 'НЕТ') . ")");
                }
                
            } else {
                $this->error("✗ Не удалось создать дебафф!");
            }
            
        } catch (\Exception $e) {
            $this->error("Ошибка при создании дебаффа: " . $e->getMessage());
            $this->error("Trace: " . $e->getTraceAsString());
        }
        
        $this->info("\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===");
        return 0;
    }
}
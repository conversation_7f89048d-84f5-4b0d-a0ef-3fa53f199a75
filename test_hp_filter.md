# Тест фильтрации игроков с 0 HP

## Внесенные изменения:

### 1. Обновлен компонент target-actions.blade.php:
- Добавлена проверка актуального HP из Redis при определении `$isTargetInLocation`
- Обновлено отображение HP для использования `$targetActualResources` вместо `$target->profile->hp`
- Улучшено сообщение для игроков, которые покинули локацию или были побеждены

### 2. Обновлен компонент target-block.blade.php:
- Добавлена проверка актуального HP из Redis при определении `$isTargetInLocation`
- Используется `$targetActualResources['current_hp']` вместо `$target->profile->hp`

### 3. Метод findRandomEnemyInLocation уже содержал правильную фильтрацию:
- Использует `$actualResources['current_hp'] > 0` для проверки актуального HP
- Логирует процесс фильтрации для отладки

## Результат:
Теперь игроки с 0 HP НЕ будут отображаться в блоке действий как цели, используя актуальные данные HP из Redis.

## Для тестирования:
1. Атакуйте игрока до 0 HP
2. Проверьте, что побежденный игрок НЕ отображается в блоке действий
3. Попробуйте атаковать случайную цель - система не должна выбрать игрока с 0 HP
4. Проверьте, что отображается соответствующее сообщение если цель была побеждена
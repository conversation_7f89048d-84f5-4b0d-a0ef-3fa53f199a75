<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\OnlineStatus;
use Illuminate\Support\Facades\Log;

class UpdateActivityStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'activity:update-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update activity statuses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $updated = OnlineStatus::updateActivityStatuses();
            
            if ($updated > 0) {
                Log::info("[Scheduler] Обновлено статусов активности: {$updated}");
                $this->info("Обновлено статусов активности: {$updated}");
            } else {
                $this->info("Нет статусов для обновления");
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error("[Scheduler] Ошибка обновления статусов активности: " . $e->getMessage());
            $this->error("Ошибка обновления статусов активности: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineLocation;
use App\Models\Location;

class EnsurePaginationWorks extends Command
{
    protected $signature = 'fix:ensure-pagination-works';
    protected $description = 'Гарантирует что пагинация рудников работает корректно';

    public function handle()
    {
        $this->info('=== ОБЕСПЕЧЕНИЕ РАБОТЫ ПАГИНАЦИИ ===');

        // 1. Активируем все неактивные локации
        $this->info('1. Активация неактивных локаций...');
        $inactiveCount = MineLocation::whereNull('parent_id')
            ->where('is_active', false)
            ->whereHas('baseLocation')
            ->update(['is_active' => true]);
        
        $this->info("Активировано локаций: $inactiveCount");

        // 2. Проверяем текущее количество
        $currentCount = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->count();
        
        $this->info("Текущее количество активных локаций: $currentCount");

        // 3. Создаем дополнительные локации если нужно
        $targetCount = 8; // Нужно минимум 5 для демонстрации пагинации
        if ($currentCount < $targetCount) {
            $needToCreate = $targetCount - $currentCount;
            $this->info("Нужно создать дополнительно: $needToCreate локаций");

            $baseLocation = Location::first();
            if (!$baseLocation) {
                $this->error('Не найдена базовая локация');
                return 1;
            }

            for ($i = 1; $i <= $needToCreate; $i++) {
                $slug = "auto-mine-" . ($currentCount + $i);
                
                $existing = MineLocation::where('slug', $slug)->first();
                if ($existing) {
                    continue;
                }

                MineLocation::create([
                    'name' => "Автоматический рудник " . ($currentCount + $i),
                    'slug' => $slug,
                    'description' => "Автоматически созданный рудник для обеспечения пагинации",
                    'base_location_id' => $baseLocation->id,
                    'is_active' => true,
                    'order' => $currentCount + $i,
                    'parent_id' => null,
                ]);

                $this->info("✓ Создан: Автоматический рудник " . ($currentCount + $i));
            }
        }

        // 4. Финальная проверка
        $finalCount = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->count();
        
        $this->info("\nФинальное количество активных локаций: $finalCount");

        // 5. Тест пагинации
        $this->info("\n=== ТЕСТ ПАГИНАЦИИ ===");
        
        $page1 = MineLocation::whereNull('parent_id')
            ->where('is_active', true)
            ->with('baseLocation')
            ->orderBy('order')
            ->paginate(4, ['*'], 'page', 1);

        $this->info("Страница 1:");
        $this->info("- Всего элементов: " . $page1->total());
        $this->info("- Элементов на странице: " . $page1->count());
        $this->info("- Всего страниц: " . $page1->lastPage());
        $this->info("- hasPages(): " . ($page1->hasPages() ? 'true' : 'false'));
        $this->info("- hasMorePages(): " . ($page1->hasMorePages() ? 'true' : 'false'));

        if ($page1->hasMorePages()) {
            $page2 = MineLocation::whereNull('parent_id')
                ->where('is_active', true)
                ->with('baseLocation')
                ->orderBy('order')
                ->paginate(4, ['*'], 'page', 2);

            $this->info("\nСтраница 2:");
            $this->info("- Элементов на странице: " . $page2->count());
            $this->info("- hasMorePages(): " . ($page2->hasMorePages() ? 'true' : 'false'));
        }

        // 6. Проверяем .env для APP_DEBUG
        $isDebug = config('app.debug');
        $this->info("\nAPP_DEBUG: " . ($isDebug ? 'true' : 'false'));
        if (!$isDebug) {
            $this->warn('Включите APP_DEBUG=true в .env файле для просмотра отладочной информации');
        }

        // 7. Результат
        if ($finalCount > 4 && $page1->hasMorePages()) {
            $this->info("\n✅ УСПЕХ! Пагинация должна работать корректно");
            $this->info("Проверьте: http://127.0.0.1:8000/battle/mines");
        } else {
            $this->error("\n❌ ПРОБЛЕМА! Пагинация может не работать");
            $this->info("Запустите: php artisan debug:pagination-details");
        }

        return 0;
    }
}
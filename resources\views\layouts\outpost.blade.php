<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $locationName ?? 'Аванпост' }} | Echoes of Eternity</title>

    {{-- Безопасная загрузка ассетов --}}
    <x-secure-assets />

    {{-- Подключение основных стилей в head --}}
    @vite(['resources/css/app.css', 'resources/css/battle-animations.css'])

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    {{-- Основной контейнер страницы --}}
    <div
        class="container max-w-md mx-auto px-1 py-0 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

   

        {{-- Сообщения --}}
        <div class="text-center flex justify-center space-x-1">
            @if (session('welcome_message'))
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    {{ session('welcome_message') }}
                </div>
            @endif
        </div>

        {{-- Блок изображения локации --}}
        <div class="mb-2">
            {{-- Отображение активных эффектов --}}
            <x-layout.active-effects :userEffects="$userEffects" />

            {{-- Определяем, оглушен ли пользователь --}}
            @php
                $isStunned = $userEffects->contains(function ($effect) {
                    return $effect->skill_id == 14 && $effect->isActive();
                });
            @endphp

            {{-- Блок изображения локации с унифицированным компонентом --}}
            {{-- Используем стандартное фоновое изображение для названий всех аванпостов --}}
            <x-layout.location-image :breadcrumbs="$breadcrumbs" :title="$locationName ?? 'Аванпост'"
                imagePath="assets/outposts/outpost-view.jpg" imageAlt="Вид на аванпост"
                :useCustomLocationName="false" />
        </div>
     {{-- Flash-сообщения --}}
        <x-layout.game-flash-messages />
        {{-- Основное содержимое --}}
        @yield('content')

        {{-- Панель быстрого использования зелий --}}
        <div class="mt-2 mb-2">
            <x-user.quick-potion-bar class="flex justify-center items-center" />
        </div>

        {{-- Панель умений --}}
        <div class="mt-2">
            <x-battle.skills-panel :userSkills="$userSkills ?? []" :user="$user ?? null" :isStunned="$isStunned ?? false" :routePrefix="$routePrefix ?? 'battle.outposts'" />
        </div>

        {{-- Блок участников группы --}}
        @php
            // Определяем параметры для кликабельности в аванпостах
            $locationId = request()->route('id') ?? request()->route('location_id');
        @endphp

        <x-party.members-bar :partyMembers="$partyMembers ?? collect()" :currentUserId="$user->id ?? null"
            :clickable="true" routePrefix="battle.outposts" :routeParams="[$locationId]" />

        {{-- Блок логов --}}
        <div class="mt-2">
            <x-battle.battle-logs :battleLogs="$battleLogs ?? []" />
        </div>
    </div>

    {{-- Нижние кнопки навигации --}}
    <div class="container mx-auto text-center px-2 py-2 flex justify-center space-x-2">
        {{-- Кнопка Рюкзак --}}
        <a href="{{ route('inventory.index') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Рюкзак
        </a>
        {{-- Кнопка Персонаж --}}
        <a href="{{ route('user.profile') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Персонаж
        </a>
        {{-- Кнопка Группа --}}
        <a href="{{ route('party.index') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Группа
        </a>
        {{-- Кнопка Гильдия --}}
        <a href="{{ route('guilds.index') }}"
            class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
            Гильдия
        </a>
    </div>

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />



    {{-- Подключение скриптов --}}
    @vite(['resources/js/app.js', 'resources/js/global/csrf.js', 'resources/js/attackLimiter.js', 'resources/js/layout/footer-counters.js', 'resources/js/layout/server-time.js'])
</body>

</html>
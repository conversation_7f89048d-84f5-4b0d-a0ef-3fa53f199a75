<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 ПРОВЕРКА ДРУГИХ ЭФФЕКТОВ В СИСТЕМЕ\n";
echo "=" . str_repeat("=", 40) . "\n\n";

$effects = ActiveEffect::take(20)->get();
echo "Найдено эффектов: {$effects->count()}\n\n";

$targetTypes = [];
foreach ($effects as $effect) {
    $targetType = $effect->target_type;
    if (!isset($targetTypes[$targetType])) {
        $targetTypes[$targetType] = 0;
    }
    $targetTypes[$targetType]++;
    
    echo "ID: {$effect->id}\n";
    echo "  target_type: '{$targetType}'\n";
    echo "  target_id: {$effect->target_id}\n";
    echo "  effect_type: '{$effect->effect_type}'\n";
    echo "  effect_name: '" . ($effect->effect_name ?? 'NULL') . "'\n\n";
}

echo "Статистика target_type:\n";
foreach ($targetTypes as $type => $count) {
    echo "- '{$type}': {$count} эффектов\n";
}

echo "\n✅ ПРОВЕРКА ЗАВЕРШЕНА!\n";

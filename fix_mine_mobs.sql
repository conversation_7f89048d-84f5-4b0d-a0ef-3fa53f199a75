-- Исправление мобов в рудниках для автоматических атак
-- Этот SQL создаёт или исправляет мобов в кастомных рудниках

-- 1. Сначала проверим, какие рудники есть и нужно ли им мобов
SELECT 
    ml.id as mine_id,
    ml.name as mine_name,
    ml.location_id,
    bl.name as base_location_name,
    COUNT(m.id) as mob_count,
    SUM(CASE WHEN m.mob_type = 'mine' AND m.hp > 0 THEN 1 ELSE 0 END) as mine_mobs_count
FROM mine_locations ml
LEFT JOIN locations bl ON ml.location_id = bl.id  
LEFT JOIN mobs m ON m.location_id = ml.location_id
WHERE ml.is_active = true AND ml.parent_id IS NULL
GROUP BY ml.id, ml.name, ml.location_id, bl.name
ORDER BY ml.name;

-- 2. Обновляем существующих мобов в рудниках, устанавливая им mob_type = 'mine'
UPDATE mobs 
SET mob_type = 'mine'
WHERE location_id IN (
    SELECT DISTINCT ml.location_id 
    FROM mine_locations ml 
    WHERE ml.is_active = true AND ml.parent_id IS NULL
)
AND mob_type != 'mine'
AND hp > 0;

-- 3. Создаём новых мобов для рудников, где их нет (пример)
-- Замените location_id на реальные ID из первого запроса

-- Для локации рудника ID 1 (измените на ваш ID)
INSERT INTO mobs (name, location_id, mob_type, hp, max_hp, level, faction_id, race_id, armor, damage, created_at, updated_at)
SELECT 
    'Рудничный страж' as name,
    ml.location_id,
    'mine' as mob_type,
    120 as hp,
    120 as max_hp,
    6 as level,
    COALESCE((SELECT id FROM factions LIMIT 1), 1) as faction_id,
    COALESCE((SELECT id FROM races LIMIT 1), 1) as race_id,
    15 as armor,
    25 as damage,
    NOW() as created_at,
    NOW() as updated_at
FROM mine_locations ml
WHERE ml.is_active = true 
  AND ml.parent_id IS NULL
  AND ml.location_id NOT IN (
    SELECT DISTINCT location_id 
    FROM mobs 
    WHERE mob_type = 'mine' AND hp > 0
  );

-- 4. Альтернативно - добавить по одному мобу в каждый рудник
INSERT INTO mobs (name, location_id, mob_type, hp, max_hp, level, faction_id, race_id, armor, damage, created_at, updated_at)
SELECT 
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY ml.id) % 3 = 1 THEN 'Рудничный страж'
        WHEN ROW_NUMBER() OVER (ORDER BY ml.id) % 3 = 2 THEN 'Подземный охотник'
        ELSE 'Каменный голем'
    END as name,
    ml.location_id,
    'mine' as mob_type,
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY ml.id) % 3 = 1 THEN 100
        WHEN ROW_NUMBER() OVER (ORDER BY ml.id) % 3 = 2 THEN 80
        ELSE 150
    END as hp,
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY ml.id) % 3 = 1 THEN 100
        WHEN ROW_NUMBER() OVER (ORDER BY ml.id) % 3 = 2 THEN 80
        ELSE 150
    END as max_hp,
    5 + (ROW_NUMBER() OVER (ORDER BY ml.id) % 3) as level,
    1 as faction_id,
    1 as race_id,
    10 + (ROW_NUMBER() OVER (ORDER BY ml.id) % 3) * 5 as armor,
    15 + (ROW_NUMBER() OVER (ORDER BY ml.id) % 3) * 5 as damage,
    NOW() as created_at,
    NOW() as updated_at
FROM mine_locations ml
WHERE ml.is_active = true 
  AND ml.parent_id IS NULL;

-- 5. Проверяем результат
SELECT 
    ml.name as mine_name,
    ml.location_id,
    m.name as mob_name,
    m.mob_type,
    m.hp,
    m.level
FROM mine_locations ml
LEFT JOIN mobs m ON m.location_id = ml.location_id AND m.mob_type = 'mine' AND m.hp > 0
WHERE ml.is_active = true AND ml.parent_id IS NULL
ORDER BY ml.name, m.name;

-- 6. Если нужно добавить больше мобов в конкретный рудник (замените location_id = 1)
/*
INSERT INTO mobs (name, location_id, mob_type, hp, max_hp, level, faction_id, race_id, armor, damage, created_at, updated_at)
VALUES 
    ('Темный страж', 1, 'mine', 90, 90, 5, 1, 1, 12, 18, NOW(), NOW()),
    ('Рудничный тролль', 1, 'mine', 130, 130, 7, 1, 1, 18, 22, NOW(), NOW()),
    ('Подземный дух', 1, 'mine', 70, 70, 4, 1, 1, 8, 15, NOW(), NOW());
*/
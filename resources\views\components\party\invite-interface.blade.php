{{--
Компонент интерфейса поиска и приглашения игроков в группу
Реализует логику поиска игроков по ID или имени и отправки приглашений в стиле WoW/L2
--}}

<div class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-2 border-[#514b3c] rounded-lg shadow-lg p-3 mb-4">
    <h3 class="text-[#fceac4] font-semibold text-sm mb-3 flex items-center">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clip-rule="evenodd" />
        </svg>
        Пригласить игрока
    </h3>

    {{-- Форма поиска и приглашения --}}
    <form id="invite-player-form" class="space-y-3">
        @csrf

        {{-- Поле поиска игрока --}}
        <div class="relative">
            <input type="text" id="player-search-input" name="player_identifier"
                placeholder="Введите ID или имя игрока..."
                class="w-full bg-[#2a2721] border border-[#6c4539] rounded px-3 py-2 text-[#fceac4] text-sm placeholder-[#a6925e] focus:border-[#f28b38] focus:outline-none"
                autocomplete="off">

            {{-- Результаты поиска --}}
            <div id="search-results"
                class="absolute top-full left-0 right-0 bg-[#2a2721] border border-[#6c4539] rounded-b max-h-48 overflow-y-auto z-10 hidden">
                {{-- Результаты будут загружены через JavaScript --}}
            </div>
        </div>

        {{-- Поле для сообщения (опционально) --}}
        <div>
            <textarea id="invitation-message" name="message" placeholder="Сообщение к приглашению (необязательно)..."
                rows="2"
                class="w-full bg-[#2a2721] border border-[#6c4539] rounded px-3 py-2 text-[#fceac4] text-sm placeholder-[#a6925e] focus:border-[#f28b38] focus:outline-none resize-none"></textarea>
        </div>

        {{-- Кнопка отправки приглашения --}}
        <div class="flex justify-end">
            <button type="submit" id="send-invite-btn"
                class="bg-[#3e5c48] hover:bg-[#243c2f] text-[#fceac4] px-4 py-2 rounded text-sm transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled>
                <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                </svg>
                Пригласить
            </button>
        </div>
    </form>

    {{-- Статус отправки приглашения --}}
    <div id="invite-status" class="mt-3 hidden">
        {{-- Статус будет показан через JavaScript --}}
    </div>
</div>

{{-- Скрипт для работы с поиском и приглашениями --}}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const searchInput = document.getElementById('player-search-input');
        const searchResults = document.getElementById('search-results');
        const sendInviteBtn = document.getElementById('send-invite-btn');
        const inviteForm = document.getElementById('invite-player-form');
        const inviteStatus = document.getElementById('invite-status');

        let selectedPlayer = null;
        let searchTimeout = null;

        // Функция для получения цвета класса
        function getClassColor(playerClass) {
            switch(playerClass?.toLowerCase()) {
                case 'воин':
                case 'warrior':
                    return 'bg-red-600';
                case 'маг':
                case 'mage':
                    return 'bg-blue-600';
                case 'лучник':
                case 'archer':
                    return 'bg-green-600';
                case 'жрец':
                case 'priest':
                    return 'bg-yellow-600';
                case 'вор':
                case 'rogue':
                    return 'bg-purple-600';
                case 'паладин':
                case 'paladin':
                    return 'bg-pink-600';
                default:
                    return 'bg-gray-600';
            }
        }

        // Поиск игроков при вводе
        searchInput.addEventListener('input', function () {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 1) {
                hideSearchResults();
                return;
            }

            searchTimeout = setTimeout(() => {
                searchPlayers(query);
            }, 300);
        });

        // Скрытие результатов при клике вне области поиска
        document.addEventListener('click', function (e) {
            if (!e.target.closest('#player-search-input') && !e.target.closest('#search-results')) {
                hideSearchResults();
            }
        });

        // Отправка приглашения
        inviteForm.addEventListener('submit', function (e) {
            e.preventDefault();

            if (!selectedPlayer) {
                showStatus('error', 'Выберите игрока для приглашения');
                return;
            }

            sendInvitation();
        });

        function searchPlayers(query) {
            fetch(`/party/search-players?query=${encodeURIComponent(query)}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSearchResults(data.players);
                    } else {
                        hideSearchResults();
                    }
                })
                .catch(error => {
                    console.error('Ошибка поиска:', error);
                    hideSearchResults();
                });
        }

        function showSearchResults(players) {
            if (players.length === 0) {
                searchResults.innerHTML = '<div class="p-3 text-[#a6925e] text-sm">Игроки не найдены</div>';
            } else {
                searchResults.innerHTML = players.map(player => `
                <div class="p-3 hover:bg-[#3e342c] cursor-pointer border-b border-[#6c4539] last:border-b-0 player-result" 
                     data-player-id="${player.id}" 
                     data-player-name="${player.name}"
                     data-can-invite="${player.can_invite}">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-2 h-2 rounded-full mr-2 ${player.is_online ? 'bg-green-500' : 'bg-gray-500'}"></div>
                            <div class="flex items-center mr-2">
                                <div class="w-4 h-4 rounded-full mr-1 ${player.race === 'solarius' ? 'bg-blue-500' : 'bg-purple-500'}" title="${player.race === 'solarius' ? 'Солариус' : 'Лунариус'}"></div>
                                <div class="w-4 h-4 rounded-sm mr-2 ${getClassColor(player.class)}" title="${player.class}"></div>
                            </div>
                            <div>
                                <div class="text-[#fceac4] text-sm font-medium">${player.name}</div>
                                <div class="text-[#a6925e] text-xs">ID: ${player.id} • Уровень: ${player.level}</div>
                            </div>
                        </div>
                        <div class="text-xs">
                            ${player.is_in_multi_party ?
                        '<span class="text-[#f28b38]">В группе</span>' :
                        (player.is_in_solo_party ?
                            '<span class="text-[#a6925e]">Соло</span>' :
                            (player.can_invite ? '<span class="text-[#3e5c48]">Доступен</span>' : '<span class="text-[#6c4539]">Недоступен</span>')
                        )
                    }
                        </div>
                    </div>
                </div>
            `).join('');
            }

            searchResults.classList.remove('hidden');

            // Добавляем обработчики клика для результатов
            searchResults.querySelectorAll('.player-result').forEach(result => {
                result.addEventListener('click', function () {
                    const canInvite = this.dataset.canInvite === 'true';
                    if (canInvite) {
                        selectPlayer({
                            id: this.dataset.playerId,
                            name: this.dataset.playerName
                        });
                    }
                });
            });
        }

        function hideSearchResults() {
            searchResults.classList.add('hidden');
        }

        function selectPlayer(player) {
            selectedPlayer = player;
            searchInput.value = `${player.name} (ID: ${player.id})`;
            sendInviteBtn.disabled = false;
            hideSearchResults();
        }

        function sendInvitation() {
            const formData = new FormData(inviteForm);
            formData.set('player_identifier', selectedPlayer.id);

            sendInviteBtn.disabled = true;
            sendInviteBtn.innerHTML = '<svg class="w-4 h-4 inline mr-1 animate-spin" fill="currentColor" viewBox="0 0 20 20"><path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4z"/></svg>Отправка...';

            fetch('/party/send-invitation', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('success', data.message);
                        resetForm();
                    } else {
                        showStatus('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Ошибка отправки приглашения:', error);
                    showStatus('error', 'Произошла ошибка при отправке приглашения');
                })
                .finally(() => {
                    sendInviteBtn.disabled = false;
                    sendInviteBtn.innerHTML = '<svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20"><path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>Пригласить';
                });
        }

        function showStatus(type, message) {
            const bgColor = type === 'success' ? 'bg-[#3e5c48]' : 'bg-[#6c4539]';
            inviteStatus.innerHTML = `
            <div class="${bgColor} text-[#fceac4] px-3 py-2 rounded text-sm">
                ${message}
            </div>
        `;
            inviteStatus.classList.remove('hidden');

            setTimeout(() => {
                inviteStatus.classList.add('hidden');
            }, 5000);
        }

        function resetForm() {
            selectedPlayer = null;
            searchInput.value = '';
            document.getElementById('invitation-message').value = '';
            sendInviteBtn.disabled = true;
            hideSearchResults();
        }
    });
</script>
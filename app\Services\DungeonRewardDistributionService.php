<?php

namespace App\Services;

use App\Models\DungeonInstance;
use App\Models\DungeonRewardDistribution;
use App\Models\Party;
use App\Models\User;
use App\Models\GameItem;
use App\Models\CombatActivity;
use App\Services\InventoryService;
use App\Events\RewardDistributed;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * Сервис для управления распределением наград в подземельях
 * 
 * Отвечает за логику распределения наград между участниками группы,
 * обработку запросов на предметы и финальное распределение
 */
class DungeonRewardDistributionService
{
    protected InventoryService $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * Получает все награды для распределения в инстансе подземелья
     *
     * @param DungeonInstance $dungeonInstance
     * @return Collection<DungeonRewardDistribution>
     */
    public function getRewardsForDistribution(DungeonInstance $dungeonInstance): Collection
    {
        return DungeonRewardDistribution::where('dungeon_instance_id', $dungeonInstance->id)
            ->with(['originalAssignee.profile', 'currentAssignee.profile', 'rewardable'])
            ->orderBy('created_at')
            ->get();
    }

    /**
     * Обрабатывает отказ игрока от награды
     *
     * @param DungeonRewardDistribution $reward
     * @param User $user
     * @return array
     */
    public function declineReward(DungeonRewardDistribution $reward, User $user): array
    {
        // Проверяем, может ли пользователь отказаться от награды
        if ($reward->current_assignee_id !== $user->id) {
            return [
                'success' => false,
                'message' => 'Вы не можете отказаться от чужой награды.'
            ];
        }

        if ($reward->status === DungeonRewardDistribution::STATUS_DISTRIBUTED) {
            return [
                'success' => false,
                'message' => 'Награда уже распределена.'
            ];
        }
        
        // Проверяем, можно ли отказаться от награды (до финализации подземелья)
        if (!in_array($reward->status, [
            DungeonRewardDistribution::STATUS_PENDING,
            DungeonRewardDistribution::STATUS_ACCEPTED,
            DungeonRewardDistribution::STATUS_REQUESTED
        ])) {
            return [
                'success' => false,
                'message' => 'От этой награды нельзя отказаться.'
            ];
        }

        // Валюта и опыт не могут быть переданы другим игрокам
        if ($reward->isCurrency() || $reward->isExperience()) {
            return [
                'success' => false,
                'message' => 'Валюта и опыт не могут быть переданы другим игрокам.'
            ];
        }

        try {
            DB::beginTransaction();

            // Получаем информацию о награде для логирования
            $rewardName = $reward->display_name;
            $rewardQuantity = $reward->display_quantity;

            // Получаем участников группы для уведомлений
            $dungeonInstance = $reward->dungeonInstance;
            $partyMembers = $dungeonInstance->party->activeUsers ?? collect();
            
            // Проверяем, есть ли запросы перед отказом
            $hasRequests = !empty($reward->requested_by);
            $requestedBy = $reward->requested_by ?? [];

            $reward->decline();

            Log::info('Игрок отказался от награды', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'reward_id' => $reward->id,
                'reward_name' => $rewardName,
                'reward_quantity' => $rewardQuantity,
                'reward_type' => $reward->rewardable_type,
                'party_members_count' => $partyMembers->count(),
                'had_requests' => $hasRequests,
                'request_count' => count($requestedBy)
            ]);
            
            // Если были запросы, восстанавливаем их и разрешаем конфликт
            if ($hasRequests) {
                $reward->requested_by = $requestedBy;
                $reward->status = DungeonRewardDistribution::STATUS_REQUESTED;
                $reward->save();
                
                $this->resolveRewardConflict($reward, $dungeonInstance);
            } else {
                // Если запросов не было, окончательно очищаем requested_by и оставляем статус declined
                $reward->requested_by = null;
                $reward->status = DungeonRewardDistribution::STATUS_DECLINED; // Оставляем как отказанную
                $reward->save();
            }

            DB::commit();

            // Формируем детальное сообщение
            $message = "Вы отказались от награды: {$rewardName}";
            if ($rewardQuantity) {
                $message .= " ({$rewardQuantity})";
            }

            // Если есть другие участники группы, добавляем информацию о возможности запроса
            $otherMembers = $partyMembers->where('id', '!=', $user->id);
            if ($otherMembers->count() > 0 && $hasRequests) {
                $message .= ". Награда передана другому игроку по результатам боевой активности.";
            } elseif ($otherMembers->count() > 0) {
                $message .= ". Теперь другие участники группы могут запросить эту награду.";
            } else {
                $message .= ". Награда окончательно утеряна.";
            }

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при отказе от награды', [
                'user_id' => $user->id,
                'reward_id' => $reward->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при отказе от награды.'
            ];
        }
    }

    /**
     * Обрабатывает запрос игрока на награду
     *
     * @param DungeonRewardDistribution $reward
     * @param User $user
     * @return array
     */
    public function requestReward(DungeonRewardDistribution $reward, User $user): array
    {
        // Проверяем, может ли пользователь запросить награду
        if (!$reward->canBeRequestedBy($user->id)) {
            return [
                'success' => false,
                'message' => 'Вы не можете запросить эту награду.'
            ];
        }

        try {
            DB::beginTransaction();

            // Получаем информацию о награде для логирования
            $rewardName = $reward->display_name;
            $rewardQuantity = $reward->display_quantity;
            $currentAssignee = $reward->currentAssignee;
            $requestedBy = $reward->requested_by ?? [];

            if ($reward->isRequestedBy($user->id)) {
                // Убираем запрос
                $reward->removeRequest($user->id);
                $message = "Вы отменили запрос на награду: {$rewardName}";
                if ($rewardQuantity) {
                    $message .= " ({$rewardQuantity})";
                }
            } else {
                // Добавляем запрос
                $reward->addRequest($user->id);
                $message = "Вы запросили награду: {$rewardName}";
                if ($rewardQuantity) {
                    $message .= " ({$rewardQuantity})";
                }

                // Добавляем информацию о конкуренции
                $totalRequests = count($requestedBy) + 1; // +1 за текущий запрос
                if ($currentAssignee && $reward->status !== 'declined') {
                    $message .= ". Награда будет распределена на основе боевой активности между {$totalRequests} претендентами.";
                } else {
                    $message .= ". Вы конкурируете с " . ($totalRequests - 1) . " другими игроками.";
                }
            }

            Log::info('Изменен статус запроса награды', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'reward_id' => $reward->id,
                'reward_name' => $rewardName,
                'requested' => $reward->isRequestedBy($user->id),
                'total_requests' => count($reward->requested_by ?? []),
                'current_assignee_id' => $reward->current_assignee_id
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при запросе награды', [
                'user_id' => $user->id,
                'reward_id' => $reward->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при запросе награды.'
            ];
        }
    }

    /**
     * Разрешает конфликты при распределении наград на основе БА
     *
     * @param DungeonInstance $dungeonInstance
     * @return void
     */
    public function resolveRewardConflicts(DungeonInstance $dungeonInstance): void
    {
        $conflictedRewards = DungeonRewardDistribution::where('dungeon_instance_id', $dungeonInstance->id)
            ->where('status', DungeonRewardDistribution::STATUS_REQUESTED)
            ->get();

        foreach ($conflictedRewards as $reward) {
            $this->resolveRewardConflict($reward, $dungeonInstance);
        }
    }

    /**
     * Разрешает конфликт для конкретной награды
     *
     * @param DungeonRewardDistribution $reward
     * @param DungeonInstance $dungeonInstance
     * @return void
     */
    protected function resolveRewardConflict(DungeonRewardDistribution $reward, DungeonInstance $dungeonInstance): void
    {
        $requestedBy = $reward->requested_by ?? [];

        if (empty($requestedBy)) {
            return;
        }

        // Добавляем текущего получателя в список претендентов, если он не отказался
        $candidates = $requestedBy;
        $originalAssigneeDeclined = $reward->status === DungeonRewardDistribution::STATUS_DECLINED;

        if (!$originalAssigneeDeclined && $reward->current_assignee_id) {
            $candidates[] = $reward->current_assignee_id;
        }
        
        // Специальная логика для случая с 2 игроками: 
        // если оригинальный получатель отказался, награда автоматически идет запросившему
        if ($originalAssigneeDeclined && count($requestedBy) === 1) {
            $winnerId = $requestedBy[0];
            
            // Обновляем получателя награды
            $reward->update([
                'current_assignee_id' => $winnerId,
                'status' => DungeonRewardDistribution::STATUS_ACCEPTED,
                'requested_by' => null
            ]);
            
            $winner = User::find($winnerId);
            $rewardName = $reward->display_name;
            $rewardQuantity = $reward->display_quantity;
            
            Log::info('Награда автоматически передана единственному запросившему игроку', [
                'reward_id' => $reward->id,
                'reward_name' => $rewardName,
                'reward_quantity' => $rewardQuantity,
                'winner_id' => $winnerId,
                'winner_name' => $winner?->name,
                'original_assignee_declined' => true
            ]);
            
            return;
        }

        // Получаем БА для всех претендентов в этом подземелье
        $dungeonName = $dungeonInstance->dungeon->name;
        $allCandidates = $candidates;

        // Если оригинальный получатель отказался, добавляем его в проверку БА
        if ($originalAssigneeDeclined) {
            $allCandidates[] = $reward->original_assignee_id;
        }

        $battleActivities = CombatActivity::whereIn('user_id', $allCandidates)
            ->where('location_name', $dungeonName)
            ->get()
            ->keyBy('user_id');

        // Находим игрока с наибольшей БА среди запросивших
        $maxBattleActivity = 0;
        $winnerId = null;

        foreach ($candidates as $candidateId) {
            $activity = $battleActivities->get($candidateId);
            $battleActivity = $activity ? $activity->damage_dealt : 0;

            if ($battleActivity > $maxBattleActivity) {
                $maxBattleActivity = $battleActivity;
                $winnerId = $candidateId;
            }
        }

        // Если оригинальный получатель отказался, но у него топ БА, он может получить предмет обратно
        if ($originalAssigneeDeclined && $winnerId) {
            $originalActivity = $battleActivities->get($reward->original_assignee_id);
            $originalBattleActivity = $originalActivity ? $originalActivity->damage_dealt : 0;

            // Если у оригинального получателя БА больше или равна максимальной среди запросивших
            if ($originalBattleActivity >= $maxBattleActivity) {
                $winnerId = $reward->original_assignee_id;
                $maxBattleActivity = $originalBattleActivity;
            }
        }

        // Если никто не запросил или у всех 0 БА, оставляем у текущего получателя
        if (!$winnerId) {
            $winnerId = $reward->current_assignee_id;
        }

        // Получаем информацию о победителе и награде
        $winner = User::find($winnerId);
        $rewardName = $reward->display_name;
        $rewardQuantity = $reward->display_quantity;

        // Обновляем получателя награды и очищаем запросы
        $reward->update([
            'current_assignee_id' => $winnerId,
            'status' => DungeonRewardDistribution::STATUS_ACCEPTED,
            'requested_by' => null
        ]);

        Log::info('Разрешен конфликт награды на основе БА', [
            'reward_id' => $reward->id,
            'reward_name' => $rewardName,
            'reward_quantity' => $rewardQuantity,
            'winner_id' => $winnerId,
            'winner_name' => $winner?->name,
            'max_battle_activity' => $maxBattleActivity,
            'candidates' => $candidates,
            'candidates_count' => count($candidates)
        ]);
    }

    /**
     * Проверяет, может ли игрок завершить подземелье (проверка инвентаря)
     *
     * @param User $user
     * @param DungeonInstance $dungeonInstance
     * @return array
     */
    public function canCompleteForUser(User $user, DungeonInstance $dungeonInstance): array
    {
        // Все награды со статусом PENDING автоматически принадлежат игроку
        // Игрок может завершить подземелье в любой момент
        // Pending награды будут автоматически приняты при завершении
        
        // НЕ разрешаем конфликты здесь глобально для всех наград
        // Конфликты будут разрешены только при финализации всего подземелья

        // Считаем количество предметов, которые будут добавлены в инвентарь
        // Исключаем уже распределенные награды и отказанные награды
        $itemsToAdd = DungeonRewardDistribution::where('dungeon_instance_id', $dungeonInstance->id)
            ->where('current_assignee_id', $user->id)
            ->whereIn('status', [
                DungeonRewardDistribution::STATUS_ACCEPTED,
                DungeonRewardDistribution::STATUS_PENDING
            ])
            ->whereNotIn('status', [
                DungeonRewardDistribution::STATUS_DECLINED,
                DungeonRewardDistribution::STATUS_DISTRIBUTED
            ])
            ->whereIn('rewardable_type', [
                DungeonRewardDistribution::TYPE_ITEM,
                DungeonRewardDistribution::TYPE_RESOURCE
            ])
            ->sum('quantity');

        if ($itemsToAdd === 0) {
            return [
                'can_complete' => true,
                'message' => ''
            ];
        }

        // Проверяем свободное место в инвентаре
        $canAdd = $this->inventoryService->canAddToInventory($user, $itemsToAdd);

        if (!$canAdd) {
            $inventoryInfo = $this->inventoryService->getInventoryInfo($user);
            $freeSlots = $inventoryInfo['inventory_capacity'] - $inventoryInfo['inventory_used'];

            return [
                'can_complete' => false,
                'message' => "У вас переполнен рюкзак! Нужно {$itemsToAdd} свободных слотов, а доступно только {$freeSlots}. Освободите место или откажитесь от некоторых наград."
            ];
        }

        return [
            'can_complete' => true,
            'message' => ''
        ];
    }

    /**
     * Финально распределяет награды игроку при завершении подземелья
     *
     * @param User $user
     * @param DungeonInstance $dungeonInstance
     * @return array
     */
    public function distributeRewardsToUser(User $user, DungeonInstance $dungeonInstance): array
    {
        // Автоматически принимаем все PENDING награды при завершении
        DungeonRewardDistribution::where('dungeon_instance_id', $dungeonInstance->id)
            ->where('current_assignee_id', $user->id)
            ->where('status', DungeonRewardDistribution::STATUS_PENDING)
            ->update(['status' => DungeonRewardDistribution::STATUS_ACCEPTED]);

        $userRewards = DungeonRewardDistribution::where('dungeon_instance_id', $dungeonInstance->id)
            ->where('current_assignee_id', $user->id)
            ->where('status', DungeonRewardDistribution::STATUS_ACCEPTED)
            ->where('status', '!=', DungeonRewardDistribution::STATUS_DECLINED)
            ->with('rewardable')
            ->get();

        $distributedRewards = [];
        $errors = [];

        try {
            DB::beginTransaction();

            foreach ($userRewards as $reward) {
                $result = $this->distributeReward($reward, $user);

                if ($result['success']) {
                    $distributedRewards[] = $result['reward_info'];
                    $reward->distribute();
                } else {
                    $errors[] = $result['error'];
                }
            }

            DB::commit();

            Log::info('Награды распределены игроку', [
                'user_id' => $user->id,
                'dungeon_instance_id' => $dungeonInstance->id,
                'distributed_count' => count($distributedRewards),
                'errors_count' => count($errors)
            ]);

            return [
                'success' => true,
                'distributed_rewards' => $distributedRewards,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при распределении наград', [
                'user_id' => $user->id,
                'dungeon_instance_id' => $dungeonInstance->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при распределении наград.'
            ];
        }
    }

    /**
     * Распределяет конкретную награду игроку
     *
     * @param DungeonRewardDistribution $reward
     * @param User $user
     * @return array
     */
    protected function distributeReward(DungeonRewardDistribution $reward, User $user): array
    {
        try {
            if ($reward->isItem()) {
                return $this->distributeItemReward($reward, $user);
            } elseif ($reward->isResource()) {
                return $this->distributeResourceReward($reward, $user);
            } elseif ($reward->isCurrency()) {
                return $this->distributeCurrencyReward($reward, $user);
            } elseif ($reward->isExperience()) {
                return $this->distributeExperienceReward($reward, $user);
            }

            return [
                'success' => false,
                'error' => 'Неизвестный тип награды'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Ошибка при распределении награды: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Распределяет предмет игроку
     *
     * @param DungeonRewardDistribution $reward
     * @param User $user
     * @return array
     */
    protected function distributeItemReward(DungeonRewardDistribution $reward, User $user): array
    {
        $item = $reward->rewardable;

        if (!$item) {
            return [
                'success' => false,
                'error' => 'Предмет не найден'
            ];
        }

        // Создаем предмет в инвентаре игрока
        for ($i = 0; $i < $reward->quantity; $i++) {
            GameItem::createFromTemplate(
                $item,
                $user->id,
                GameItem::LOCATION_INVENTORY,
                1,
                $item->quality
            );
        }

        // Обновляем счетчик инвентаря
        $user->profile->increment('inventory_used', $reward->quantity);

        $rewardInfo = [
            'type' => 'item',
            'name' => $item->name,
            'quantity' => $reward->quantity,
            'icon' => $item->icon
        ];

        // Вызываем событие распределения награды
        RewardDistributed::dispatch($reward, $user, $rewardInfo);

        return [
            'success' => true,
            'reward_info' => $rewardInfo
        ];
    }

    /**
     * Распределяет ресурс игроку
     *
     * @param DungeonRewardDistribution $reward
     * @param User $user
     * @return array
     */
    protected function distributeResourceReward(DungeonRewardDistribution $reward, User $user): array
    {
        $resource = $reward->rewardable;

        if (!$resource) {
            return [
                'success' => false,
                'error' => 'Ресурс не найден'
            ];
        }

        // Добавляем ресурс через метод addResourceWithLimit для корректной обработки лимитов
        $lostQuantity = \App\Models\UserResource::addResourceWithLimit(
            $user->id,
            $resource->id,
            $reward->quantity,
            'inventory'
        );

        // Проверяем, что ресурс добавлен полностью
        if ($lostQuantity > 0) {
            Log::error('Часть ресурса потеряна при добавлении в инвентарь', [
                'user_id' => $user->id,
                'resource_id' => $resource->id,
                'resource_name' => $resource->name,
                'requested_quantity' => $reward->quantity,
                'lost_quantity' => $lostQuantity,
                'added_quantity' => $reward->quantity - $lostQuantity
            ]);

            return [
                'success' => false,
                'error' => "Не удалось добавить полное количество ресурса {$resource->name}. Добавлено: " . ($reward->quantity - $lostQuantity) . ", потеряно: {$lostQuantity} (недостаточно места в инвентаре)"
            ];
        }

        Log::info('Ресурс добавлен игроку через dungeon reward', [
            'user_id' => $user->id,
            'resource_id' => $resource->id,
            'resource_name' => $resource->name,
            'quantity_added' => $reward->quantity,
            'lost_quantity' => $lostQuantity
        ]);

        $rewardInfo = [
            'type' => 'resource',
            'name' => $resource->name,
            'quantity' => $reward->quantity,
            'icon' => $resource->icon
        ];

        // Вызываем событие распределения награды
        RewardDistributed::dispatch($reward, $user, $rewardInfo);

        return [
            'success' => true,
            'reward_info' => $rewardInfo
        ];
    }

    /**
     * Распределяет валюту игроку
     *
     * @param DungeonRewardDistribution $reward
     * @param User $user
     * @return array
     */
    protected function distributeCurrencyReward(DungeonRewardDistribution $reward, User $user): array
    {
        $user->profile->addCurrency($reward->currency_type, $reward->quantity);

        $rewardInfo = [
            'type' => 'currency',
            'name' => ucfirst($reward->currency_type),
            'quantity' => $reward->quantity,
            'icon' => $reward->display_icon
        ];

        // Вызываем событие распределения награды
        RewardDistributed::dispatch($reward, $user, $rewardInfo);

        return [
            'success' => true,
            'reward_info' => $rewardInfo
        ];
    }

    /**
     * Распределяет опыт игроку
     *
     * @param DungeonRewardDistribution $reward
     * @param User $user
     * @return array
     */
    protected function distributeExperienceReward(DungeonRewardDistribution $reward, User $user): array
    {
        $user->profile->increment('experience', $reward->experience_amount);

        $rewardInfo = [
            'type' => 'experience',
            'name' => 'Опыт',
            'quantity' => $reward->experience_amount,
            'icon' => '⭐'
        ];

        // Вызываем событие распределения награды
        RewardDistributed::dispatch($reward, $user, $rewardInfo);

        return [
            'success' => true,
            'reward_info' => $rewardInfo
        ];
    }

    /**
     * Финализирует все награды в подземелье когда все игроки завершили
     *
     * @param DungeonInstance $dungeonInstance
     * @return void
     */
    public function finalizeAllRewards(DungeonInstance $dungeonInstance): void
    {
        Log::info('Финализация всех наград в подземелье', [
            'dungeon_instance_id' => $dungeonInstance->id
        ]);

        // Разрешаем все оставшиеся конфликты
        $this->resolveRewardConflicts($dungeonInstance);

        // Помечаем только принятые и pending награды как окончательно распределенные
        // Отказанные награды остаются с статусом declined
        DungeonRewardDistribution::where('dungeon_instance_id', $dungeonInstance->id)
            ->whereIn('status', [
                DungeonRewardDistribution::STATUS_ACCEPTED,
                DungeonRewardDistribution::STATUS_PENDING
            ])
            ->update(['status' => DungeonRewardDistribution::STATUS_DISTRIBUTED]);

        Log::info('Все награды в подземелье финализированы', [
            'dungeon_instance_id' => $dungeonInstance->id
        ]);
    }
}

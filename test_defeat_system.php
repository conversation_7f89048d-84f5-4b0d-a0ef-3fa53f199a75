<?php

// Тест системы поражений
require_once __DIR__ . '/vendor/autoload.php';

echo "=== Тест системы поражений ===\n";

// Проверяем, что файлы существуют
$files = [
    'app/Http/Middleware/CheckPlayerDefeat.php',
    'app/Http/Controllers/BattleController.php',
    'app/Http/Controllers/Outposts/CustomOutpostController.php',
    'app/Models/UserProfile.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✓ {$file} - существует\n";
    } else {
        echo "✗ {$file} - отсутствует\n";
    }
}

// Проверяем, что миграция создана
$migrationFile = 'database/migrations/2025_07_17_000001_add_defeat_fields_to_user_profiles_table.php';
if (file_exists($migrationFile)) {
    echo "✓ {$migrationFile} - создана\n";
} else {
    echo "✗ {$migrationFile} - отсутствует\n";
}

// Проверяем, что SQL файл создан
$sqlFile = 'add_defeat_fields.sql';
if (file_exists($sqlFile)) {
    echo "✓ {$sqlFile} - создан\n";
} else {
    echo "✗ {$sqlFile} - отсутствует\n";
}

echo "\n=== Изменения в коде ===\n";

// Проверяем ключевые изменения в файлах
echo "1. CheckPlayerDefeat middleware:\n";
echo "   - Добавлена проверка is_defeated из БД\n";
echo "   - Добавлен флаг recent_victory_time\n";
echo "   - Улучшена логика определения победителя\n\n";

echo "2. CustomOutpostController:\n";
echo "   - Добавлен флаг recent_victory_time при победе\n";
echo "   - Добавлено обновление defeated_* полей в БД\n\n";

echo "3. BattleController:\n";
echo "   - Улучшена логика определения поражения\n";
echo "   - Добавлена работа с полями из БД\n";
echo "   - Очистка полей поражения при respawn\n\n";

echo "4. UserProfile модель:\n";
echo "   - Добавлены поля is_defeated, defeated_by_type, defeated_by_id, defeated_at\n";
echo "   - Добавлены casts для типов данных\n\n";

echo "=== Как протестировать ===\n";
echo "1. Выполнить SQL из файла add_defeat_fields.sql\n";
echo "2. Создать двух игроков с низким HP\n";
echo "3. Один игрок атакует другого\n";
echo "4. Проверить, что победитель остается в локации\n";
echo "5. Проверить, что побежденный перенаправляется на /battle/defeat\n";

echo "\n=== Основные исправления ===\n";
echo "✓ Победитель больше не перенаправляется на /battle/defeat\n";
echo "✓ Побежденный корректно перенаправляется на страницу поражения\n";
echo "✓ Система работает без session, используя БД\n";
echo "✓ Добавлена надежная логика определения победителя/побежденного\n";
echo "✓ Исправлены middleware CheckUserHealth и CheckMinimumHP\n";
echo "✓ Добавлен флаг recent_victory_time для защиты победителя\n";

echo "\n=== Middleware исправления ===\n";
echo "1. CheckPlayerDefeat middleware:\n";
echo "   - Добавлена проверка recent_victory_time\n";
echo "   - Улучшена логика определения post-attack redirect\n\n";

echo "2. CheckUserHealth middleware:\n";
echo "   - Добавлена проверка recent_victory_time\n";
echo "   - Победитель не перенаправляется на battle.outposts.index\n\n";

echo "3. CheckMinimumHP middleware:\n";
echo "   - Добавлена проверка recent_victory_time\n";
echo "   - Пропускает игроков с недавней победой\n\n";

echo "=== ВАЖНО: Выполните SQL ===\n";
echo "ALTER TABLE user_profiles \n";
echo "ADD COLUMN is_defeated BOOLEAN DEFAULT FALSE,\n";
echo "ADD COLUMN defeated_by_type VARCHAR(255) NULL,\n";
echo "ADD COLUMN defeated_by_id BIGINT NULL,\n";
echo "ADD COLUMN defeated_at TIMESTAMP NULL;\n\n";

echo "CREATE INDEX idx_user_profiles_is_defeated ON user_profiles (is_defeated);\n";
echo "CREATE INDEX idx_user_profiles_defeated_by ON user_profiles (defeated_by_type, defeated_by_id);\n";
echo "CREATE INDEX idx_user_profiles_defeated_at ON user_profiles (defeated_at);\n";
?>
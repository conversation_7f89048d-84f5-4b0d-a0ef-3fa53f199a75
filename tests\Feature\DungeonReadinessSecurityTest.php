<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Dungeon;
use App\Models\Party;
use App\Models\PartyMember;
use App\Models\DungeonInstance;
use App\Services\DungeonReadinessService;
use App\Services\PartyService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class DungeonReadinessSecurityTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $leader;
    protected User $member1;
    protected User $member2;
    protected Dungeon $dungeon;
    protected Party $party;
    protected DungeonReadinessService $readinessService;
    protected PartyService $partyService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->leader = User::factory()->create();
        $this->member1 = User::factory()->create();
        $this->member2 = User::factory()->create();
        
        $this->dungeon = Dungeon::factory()->create([
            'min_players' => 2,
            'max_players' => 3,
            'min_level' => 1,
            'max_level' => 100,
        ]);
        
        $this->party = Party::factory()->create([
            'leader_id' => $this->leader->id,
            'max_members' => 3,
        ]);
        
        $this->readinessService = app(DungeonReadinessService::class);
        $this->partyService = app(PartyService::class);
    }

    /** @test */
    public function leader_cannot_start_dungeon_without_all_members_ready()
    {
        // Создаем группу с участниками
        $this->party->members()->createMany([
            [
                'user_id' => $this->leader->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => true,
                'joined_at' => now(),
            ],
            [
                'user_id' => $this->member1->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => false, // НЕ готов
                'joined_at' => now(),
            ],
        ]);

        // Устанавливаем всех в лобби
        $this->leader->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
        ]);
        $this->member1->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
        ]);

        // Пытаемся начать подземелье
        $response = $this->actingAs($this->leader)
            ->post(route('dungeons.enter', $this->dungeon));

        $response->assertRedirect(route('dungeons.lobby', $this->dungeon));
        $response->assertSessionHas('error');
        
        // Проверяем, что инстанс НЕ создан
        $this->assertDatabaseMissing('dungeon_instances', [
            'dungeon_id' => $this->dungeon->id,
            'party_id' => $this->party->id,
        ]);
    }

    /** @test */
    public function leader_cannot_start_dungeon_with_insufficient_members()
    {
        // Создаем группу только с лидером (меньше минимума)
        $this->party->members()->create([
            'user_id' => $this->leader->id,
            'role' => PartyMember::ROLE_LEADER,
            'status' => PartyMember::STATUS_ACTIVE,
            'is_ready' => true,
            'joined_at' => now(),
        ]);

        $this->leader->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
        ]);

        // Пытаемся начать подземелье
        $response = $this->actingAs($this->leader)
            ->post(route('dungeons.enter', $this->dungeon));

        $response->assertRedirect(route('dungeons.lobby', $this->dungeon));
        $response->assertSessionHas('error');
        
        // Проверяем, что инстанс НЕ создан
        $this->assertDatabaseMissing('dungeon_instances', [
            'dungeon_id' => $this->dungeon->id,
            'party_id' => $this->party->id,
        ]);
    }

    /** @test */
    public function leader_cannot_start_dungeon_with_members_not_in_lobby()
    {
        // Создаем группу с участниками
        $this->party->members()->createMany([
            [
                'user_id' => $this->leader->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => true,
                'joined_at' => now(),
            ],
            [
                'user_id' => $this->member1->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => true,
                'joined_at' => now(),
            ],
        ]);

        // Только лидер в лобби, участник где-то еще
        $this->leader->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
        ]);
        $this->member1->update([
            'in_dungeon_id' => null,
            'in_dungeon_status' => null,
        ]);

        // Пытаемся начать подземелье
        $response = $this->actingAs($this->leader)
            ->post(route('dungeons.enter', $this->dungeon));

        $response->assertRedirect(route('dungeons.lobby', $this->dungeon));
        $response->assertSessionHas('error');
        
        // Проверяем, что инстанс НЕ создан
        $this->assertDatabaseMissing('dungeon_instances', [
            'dungeon_id' => $this->dungeon->id,
            'party_id' => $this->party->id,
        ]);
    }

    /** @test */
    public function non_leader_cannot_start_dungeon()
    {
        // Создаем группу с участниками
        $this->party->members()->createMany([
            [
                'user_id' => $this->leader->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => true,
                'joined_at' => now(),
            ],
            [
                'user_id' => $this->member1->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => true,
                'joined_at' => now(),
            ],
        ]);

        // Оба в лобби
        $this->leader->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
        ]);
        $this->member1->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
        ]);

        // Участник (не лидер) пытается начать подземелье
        $response = $this->actingAs($this->member1)
            ->post(route('dungeons.enter', $this->dungeon));

        $response->assertRedirect(route('dungeons.lobby', $this->dungeon));
        $response->assertSessionHas('error');
        
        // Проверяем, что инстанс НЕ создан
        $this->assertDatabaseMissing('dungeon_instances', [
            'dungeon_id' => $this->dungeon->id,
            'party_id' => $this->party->id,
        ]);
    }

    /** @test */
    public function successful_dungeon_start_creates_instance_for_all_members()
    {
        // Создаем правильную группу
        $this->party->members()->createMany([
            [
                'user_id' => $this->leader->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => true,
                'joined_at' => now(),
            ],
            [
                'user_id' => $this->member1->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => true,
                'joined_at' => now(),
            ],
        ]);

        // Все в лобби
        $this->leader->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
            'last_activity_timestamp' => now()->timestamp,
        ]);
        $this->member1->update([
            'in_dungeon_id' => $this->dungeon->id,
            'in_dungeon_status' => 'lobby',
            'last_activity_timestamp' => now()->timestamp,
        ]);

        // Лидер начинает подземелье
        $response = $this->actingAs($this->leader)
            ->post(route('dungeons.enter', $this->dungeon));

        $response->assertRedirect(route('dungeons.battle', $this->dungeon));
        $response->assertSessionHas('info');
        
        // Проверяем, что инстанс создан
        $this->assertDatabaseHas('dungeon_instances', [
            'dungeon_id' => $this->dungeon->id,
            'party_id' => $this->party->id,
        ]);

        // Проверяем, что все участники переведены в состояние 'battle'
        $this->leader->refresh();
        $this->member1->refresh();
        
        $this->assertEquals('battle', $this->leader->in_dungeon_status);
        $this->assertEquals('battle', $this->member1->in_dungeon_status);
    }

    /** @test */
    public function readiness_service_validates_all_requirements()
    {
        // Создаем группу с неготовыми участниками
        $this->party->members()->createMany([
            [
                'user_id' => $this->leader->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => false,
                'joined_at' => now(),
            ],
            [
                'user_id' => $this->member1->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_ACTIVE,
                'is_ready' => false,
                'joined_at' => now(),
            ],
        ]);

        // Проверяем через сервис
        $result = $this->readinessService->validatePartyReadiness($this->dungeon, $this->party);

        $this->assertFalse($result['ready']);
        $this->assertFalse($result['can_start']);
        $this->assertNotEmpty($result['errors']);
    }
}
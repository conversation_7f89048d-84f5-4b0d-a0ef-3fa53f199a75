<?php

/**
 * Тестирование исправления создания ботов в админке
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use App\Models\Location;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЯ СОЗДАНИЯ БОТОВ\n";
echo "==========================================\n\n";

// 1. Проверим текущие подлокации рудников
echo "1. ДОСТУПНЫЕ ПОДЛОКАЦИИ РУДНИКОВ\n";
echo "-------------------------------\n";

$mineLocations = MineLocation::with('baseLocation')
    ->where('is_active', true)
    ->orderBy('name')
    ->get();

foreach ($mineLocations as $mine) {
    echo "🏭 {$mine->name} (ID: {$mine->id})\n";
    echo "   Базовая локация: " . ($mine->baseLocation->name ?? 'неизвестно') . "\n";
    
    // Проверим, сколько ботов уже в этой подлокации
    $botsCount = Bot::where('location', $mine->name)->count();
    echo "   Ботов в подлокации: {$botsCount}\n";
    
    // Проверим ботов с неправильной локацией
    $wrongBots = Bot::where('location', (string) $mine->id)->count();
    if ($wrongBots > 0) {
        echo "   ❌ Ботов с ID локации: {$wrongBots}\n";
    }
    
    echo "\n";
}

// 2. Проверим обычные локации
echo "2. ОБЫЧНЫЕ ЛОКАЦИИ\n";
echo "-----------------\n";

$locations = Location::orderBy('name')->get();
foreach ($locations as $location) {
    $botsCount = Bot::where('location', $location->name)->count();
    echo "🏛️ {$location->name}: {$botsCount} ботов\n";
}

// 3. Симулируем создание бота через админку
echo "\n3. СИМУЛЯЦИЯ СОЗДАНИЯ БОТА\n";
echo "=========================\n";

$testMineLocation = $mineLocations->first();
if ($testMineLocation) {
    echo "Тестируем создание бота в подлокации: {$testMineLocation->name}\n";
    
    // Симулируем данные формы
    $formData = [
        'name' => 'test_bot_' . now()->timestamp,
        'race' => 'solarius',
        'class' => 'warrior',
        'level' => 1,
        'hp' => 100,
        'max_hp' => 100,
        'mp' => 50,
        'max_mp' => 50,
        'strength' => 10,
        'intelligence' => 5,
        'dexterity' => 7,
        'armor' => 3,
        'magic_resistance' => 2,
        'location' => $testMineLocation->name, // Теперь это название подлокации
        'mine_location_id' => $testMineLocation->id,
        'is_active' => true,
        'created_by_admin' => true,
        'base_damage' => 10
    ];
    
    echo "Данные для создания:\n";
    echo "  location: {$formData['location']}\n";
    echo "  mine_location_id: {$formData['mine_location_id']}\n";
    
    // Создаем бота (без валидации для теста)
    $bot = new Bot($formData);
    $bot->save();
    
    echo "✅ Тестовый бот создан: {$bot->name}\n";
    echo "  Итоговая location: {$bot->location}\n";
    echo "  Итоговый mine_location_id: {$bot->mine_location_id}\n";
    
    // Проверим, что бот появится в админке
    $adminBots = Bot::where('location', $testMineLocation->name)->count();
    echo "  Ботов в админке теперь: {$adminBots}\n";
    
    // Удаляем тестового бота
    $bot->delete();
    echo "  Тестовый бот удален\n";
}

// 4. Проверим исправление существующих ботов
echo "\n4. ИСПРАВЛЕНИЕ СУЩЕСТВУЮЩИХ БОТОВ\n";
echo "================================\n";

$fixedCount = 0;

foreach ($mineLocations as $mine) {
    $botsToFix = Bot::where('location', (string) $mine->id)->get();
    
    foreach ($botsToFix as $bot) {
        $oldLocation = $bot->location;
        $bot->location = $mine->name;
        $bot->mine_location_id = $mine->id;
        $bot->save();
        
        echo "✅ {$bot->name}: '{$oldLocation}' → '{$bot->location}'\n";
        $fixedCount++;
    }
}

if ($fixedCount > 0) {
    echo "Исправлено ботов: {$fixedCount}\n";
} else {
    echo "✅ Все боты уже исправлены\n";
}

// 5. Итоговая проверка
echo "\n5. ИТОГОВАЯ ПРОВЕРКА\n";
echo "===================\n";

// Проверим, остались ли боты с ID локации
$problemBots = Bot::whereRaw('location REGEXP \'^[0-9]+$\'')->count();
echo "Ботов с ID локации: {$problemBots}\n";

// Проверим соответствие данных
$consistencyIssues = 0;

foreach ($mineLocations as $mine) {
    $botsInMine = Bot::where('location', $mine->name)->get();
    
    foreach ($botsInMine as $bot) {
        if ($bot->mine_location_id !== $mine->id) {
            echo "❌ {$bot->name}: location={$bot->location}, но mine_location_id={$bot->mine_location_id}\n";
            $consistencyIssues++;
        }
    }
}

if ($consistencyIssues === 0) {
    echo "✅ Все данные согласованы\n";
} else {
    echo "❌ Проблем с согласованностью: {$consistencyIssues}\n";
}

// 6. Рекомендации
echo "\n6. РЕКОМЕНДАЦИИ\n";
echo "==============\n";

if ($problemBots === 0 && $consistencyIssues === 0) {
    echo "🎉 ВСЕ ИСПРАВЛЕНИЯ ПРИМЕНЕНЫ!\n";
    echo "✅ Новые боты будут создаваться корректно\n";
    echo "✅ Фильтр в админке покажет всех ботов\n";
    echo "✅ Изоляция подлокаций работает правильно\n";
} else {
    echo "⚠️ Требуются дополнительные исправления:\n";
    if ($problemBots > 0) {
        echo "  - Запустите: php instant_fix.php\n";
    }
    if ($consistencyIssues > 0) {
        echo "  - Запустите: php fix_specific_bots.php\n";
    }
}

echo "\n📋 КРАТКИЙ ИТОГ ИСПРАВЛЕНИЙ:\n";
echo "===========================\n";
echo "1. ✅ Исправлена форма создания бота (value=\"{mineLocation->name}\")\n";
echo "2. ✅ Исправлена форма редактирования бота\n";
echo "3. ✅ Добавлены логи в handleMineLocationAssignment\n";
echo "4. ✅ Исправлен фильтр админки (показывает подлокации)\n";
echo "5. ✅ Исправлены существующие боты с неправильными локациями\n";

echo "\n🏁 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО\n";
echo "========================\n";
echo "Время: " . now()->format('H:i:s d.m.Y') . "\n";
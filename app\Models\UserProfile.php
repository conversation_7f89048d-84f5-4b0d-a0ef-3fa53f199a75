<?php
namespace App\Models;

use App\Models\ActiveEffect;
use App\Models\LevelThreshold;
use App\Models\Skill;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\CurrencyService;
use App\Models\UserAlchemyIngredient;
use App\Models\UserRecipe;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Session;
use App\Services\FlashMessageService;
use App\Services\CombatFormulaService;

class UserProfile extends Model
{
    protected $fillable = [
        'user_id',
        'level',
        'experience',
        'alchemy_experience',
        'alchemy_level',
        'potions_created',
        'testing_experience',
        'testing_level',
        'strength',
        'dexterity',
        'intelligence',
        'vitality',
        'max_hp',
        'current_hp',
        'max_mp',
        'current_mp',
        'gold',
        'crit_chance',
        'crit_damage',
        'magic_find',
        'gold_find',
        'gs',
        'GS',
        'stat_points',
        'skill_points',
        'race',
        'class',
        'recovery',
        'armor',
        'resistance_fire',
        'resistance_lightning',
        'resistanceNature',
        'hp',
        'mp',
        'silver',
        'bronze',
        'coinEvent',
        'coinGuild',
        'coinCrystal',
        'profession',
        'inventory_capacity',
        'inventory_used',
        'last_regeneration_at',
        'is_defeated',
        'defeated_by_type',
        'defeated_by_id',
        'defeated_at',
    ];

    /**
     * Серверный accessor для on-the-fly HP (регенерация по recovery и времени)
     */
    // Геттер для динамического HP (регенерация)
    public function getCurrentHpAttribute($value = null)
    {
        // Если $value не передан, берем из attributes (eloquent)
        if ($value === null && array_key_exists('current_hp', $this->attributes)) {
            $value = $this->attributes['current_hp'];
        }
        $now = now();
        $last = $this->last_regeneration_at ? \Carbon\Carbon::parse($this->last_regeneration_at) : $now;
        // Правильное направление: если last_regeneration_at в прошлом, HP увеличивается
        $seconds = max(0, $now->timestamp - $last->timestamp);
        $stats = $this->getEffectiveStats();
        $regen = $stats['recovery'] * 1.5 / 180;

        // Применяем множитель регенерации HP из сессии, если он есть
        $activeMultipliers = session('active_hp_regen_multipliers', []);
        if (!empty($activeMultipliers)) {
            // Если есть несколько множителей, используем максимальный
            $maxMultiplier = max($activeMultipliers);
            $regen *= $maxMultiplier;
        }

        $hp = $value + $seconds * $regen;
        return min($hp, $this->max_hp);
    }

    // Сеттер для eloquent (корректно сохраняет HP в БД)
    public function setCurrentHpAttribute($value)
    {
        $this->attributes['current_hp'] = $value;
    }


    /**
     * Атрибуты, которые должны быть преобразованы.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_regeneration_at' => 'datetime',
        'is_defeated' => 'boolean',
        'defeated_at' => 'datetime',
    ];

    /**
     * Устанавливаем атрибуты по умолчанию при создании модели
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($profile) {
            // Установка значений по умолчанию, если они не указаны
            if (!isset($profile->inventory_capacity)) {
                $profile->inventory_capacity = 20; // Устанавливаем значение по умолчанию 20
            }

            // Обеспечиваем корректные значения для HP и MP
            if (!isset($profile->max_hp) || $profile->max_hp === null) {
                $profile->max_hp = 100;
            }
            if (!isset($profile->current_hp) || $profile->current_hp === null) {
                $profile->current_hp = $profile->max_hp;
            }
            if (!isset($profile->max_mp) || $profile->max_mp === null) {
                $profile->max_mp = 50;
            }
            if (!isset($profile->current_mp) || $profile->current_mp === null) {
                $profile->current_mp = $profile->max_mp;
            }
            if (!isset($profile->last_regeneration_at) || $profile->last_regeneration_at === null) {
                $profile->last_regeneration_at = now();
            }
        });

        static::saving(function ($profile) {
            // Проверяем значения перед сохранением
            if ($profile->current_hp === null) {
                $profile->current_hp = $profile->max_hp ?? 100;
                \Log::warning("Предотвращено сохранение NULL в current_hp", [
                    'user_id' => $profile->user_id,
                    'set_current_hp' => $profile->current_hp
                ]);
            }
            if ($profile->current_mp === null) {
                $profile->current_mp = $profile->max_mp ?? 50;
                \Log::warning("Предотвращено сохранение NULL в current_mp", [
                    'user_id' => $profile->user_id,
                    'set_current_mp' => $profile->current_mp
                ]);
            }
            if ($profile->last_regeneration_at === null) {
                $profile->last_regeneration_at = now();
                \Log::warning("Предотвращено сохранение NULL в last_regeneration_at", [
                    'user_id' => $profile->user_id,
                    'set_last_regeneration_at' => $profile->last_regeneration_at
                ]);
            }
        });
    }

    // Связь с пользователем
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function gameItems()
    {
        return $this->hasMany(GameItem::class, 'owner_id', 'user_id');
    }

    /**
     * Экипировать предмет
     */
    public function equipItem(GameItem $gameItem, string $slot): void
    {
        // Обновляем объект предмета из базы, чтобы убедиться в актуальности данных
        $gameItem->refresh();

        // Проверяем, может ли предмет быть экипирован (например, прочность > 0)
        if ($gameItem->durability <= 0) {
            throw new \Exception('Нельзя экипировать сломанный предмет.');
        }

        // Сбрасываем кеш отношения, чтобы получить актуальные данные
        $this->unsetRelation('gameItems');

        // Если слот не передан, определяем его на основе типа предмета
        if (!$slot) {
            if ($gameItem->item && $gameItem->item->type) {
                $slot = $gameItem->item->type; // Например: "оружие", "кольцо", "шлем" и т.д.
            } else {
                // Добавляем логирование для отладки
                Log::error("Не удалось определить слот для предмета", ['gameItem_id' => $gameItem->id, 'item_id' => $gameItem->item_id]);
                throw new \Exception('Не удалось определить слот для предмета.');
            }
        }

        // Проверяем, свободен ли слот
        $equippedItemInSlot = $this->gameItems()
            ->where('location', 'equipment')
            ->where('slot', $slot)
            ->first();

        if ($equippedItemInSlot) {
            throw new \Exception('Этот слот уже занят другим предметом.');
        }

        // Обновляем экземпляр предмета
        $gameItem->update([
            'is_equipped' => true,
            'slot' => $slot,
            'location' => 'equipment',
        ]);

        // Возобновляем все активные эффекты предмета
        $gameItem->updateEffectsStatus(true);

        // Применяем эффекты предмета к пользователю
        $effectService = app(\App\Services\ItemEffectService::class);
        $appliedEffects = $effectService->applyEffects($gameItem);

        // Логируем примененные эффекты
        if (!empty($appliedEffects)) {
            Log::info("Применены эффекты предмета к пользователю", [
                'user_id' => $this->user_id,
                'game_item_id' => $gameItem->id,
                'applied_effects' => $appliedEffects
            ]);
        }

        // Логируем успешную экипировку
        Log::info("Предмет {$gameItem->item_name} (ID: {$gameItem->id}) экипирован в слот '{$slot}' для пользователя ID: {$this->user_id}");

        // Очищаем кэш эффективных статов, если он используется
        cache()->forget("effective_stats_{$this->user_id}");
    }

    /**
     * Снять предмет
     */
    public function unequipItem(GameItem $gameItem): void
    {
        // Проверяем, действительно ли предмет экипирован этим пользователем
        if (!$gameItem->is_equipped || $gameItem->location !== 'equipment' || $gameItem->owner_id !== $this->user_id) {
            Log::warning("Попытка снять предмет, который не экипирован или не принадлежит пользователю.", [
                'gameItem_id' => $gameItem->id,
                'item_owner' => $gameItem->owner_id,
                'profile_user' => $this->user_id,
                'is_equipped' => $gameItem->is_equipped,
                'location' => $gameItem->location
            ]);
            throw new \Exception('Этот предмет не экипирован или не принадлежит вам.');
        }

        // Загружаем активные эффекты предмета перед снятием
        $gameItem->load('activeEffects');

        // Удаляем эффекты предмета с пользователя
        $effectService = app(\App\Services\ItemEffectService::class);
        foreach ($gameItem->activeEffects as $effect) {
            if ($effect->is_active && !$effect->is_paused) {
                $effectService->removeEffectFromUser($this->user, $effect);

                Log::info("Эффект удален при снятии предмета", [
                    'user_id' => $this->user_id,
                    'game_item_id' => $gameItem->id,
                    'effect_id' => $effect->id,
                    'effect_name' => $effect->effect_name,
                    'effect_type' => $effect->effect_type
                ]);
            }
        }

        // Обновляем предмет
        $gameItem->update([
            'is_equipped' => false,
            'slot' => null,
            'location' => 'inventory',
        ]);

        // Приостанавливаем все активные эффекты предмета
        $gameItem->updateEffectsStatus(false);

        // Логируем снятие предмета
        Log::info("Предмет {$gameItem->item_name} (ID: {$gameItem->id}) снят с пользователя ID: {$this->user_id} и перемещен в инвентарь.");

        // Сбрасываем кэш отношений, чтобы getEffectiveStats получил актуальные данные
        $this->unsetRelation('gameItems');

        // Очищаем кэш эффективных статов
        cache()->forget("effective_stats_{$this->user_id}");
    }

    /**
     * Добавляет статы предмета к характеристикам персонажа
     *
     * @param GameItem $gameItem Предмет, статы которого нужно добавить
     */
    protected function addItemStats(GameItem $gameItem): void
    {
        // Проверяем, не сломан ли предмет
        if ($gameItem->durability <= 0) {
            Log::info("Предмет сломан, статы не применяются (но сам предмет экипирован): {$gameItem->item_name}");
            return; // Статы сломанного предмета не учитываются
        }

        Log::info("(addItemStats) Экипирован предмет: {$gameItem->item_name} для персонажа ID {$this->user_id}. Статы будут учтены через getEffectiveStats.");
    }

    /**
     * Удаляет статы предмета из характеристик персонажа
     *
     * @param GameItem $gameItem Предмет, статы которого нужно удалить
     */
    protected function removeItemStats(GameItem $gameItem): void
    {
        // Проверяем, был ли предмет действительно экипирован (хотя основная проверка в unequipItem)
        if (!$gameItem->wasRecentlyCreated && !$gameItem->getOriginal('is_equipped')) {
            Log::warning("(removeItemStats) Попытка снять статы с предмета, который не был экипирован: {$gameItem->item_name}");
            return;
        }

        Log::info("(removeItemStats) Снят предмет: {$gameItem->item_name} с персонажа ID {$this->user_id}. Статы будут пересчитаны через getEffectiveStats.");
    }

    /**
     * Возвращает сумму бонусов от всех экипированных предметов (только тех, у которых durability > 0)
     */
    public function getEquippedBonusStats(): array
    {
        $bonusStats = [
            'strength' => 0,
            'intelligence' => 0,
            'recovery' => 0,
            'armor' => 0,
            'crit_chance' => 0,
            'crit_damage' => 0,
            'hp' => 0,
            'mp' => 0,
            'resistance_fire' => 0,
            'resistance_lightning' => 0,
        ];

        // Получаем экипированные предметы, у которых прочность больше 0 (то есть предмет не сломан)
        $equippedItems = $this->gameItems()
            ->where('location', 'equipment')
            ->where('durability', '>', 0)
            ->get();

        foreach ($equippedItems as $item) {
            $bonusStats['strength'] += $item->strength ?? 0;
            $bonusStats['intelligence'] += $item->intelligence ?? 0;
            $bonusStats['recovery'] += $item->recovery ?? 0;
            $bonusStats['armor'] += $item->armor ?? 0;
            $bonusStats['crit_chance'] += $item->crit_chance ?? 0;
            $bonusStats['crit_damage'] += $item->crit_damage ?? 0;
            $bonusStats['hp'] += $item->hp ?? 0;
            $bonusStats['mp'] += $item->mp ?? 0;
            $bonusStats['resistance_fire'] += $item->resistance_fire ?? 0;
            $bonusStats['resistance_lightning'] += $item->resistance_lightning ?? 0;
        }
        // Логирование для отладки
        // Log::debug("Рассчитанные бонусы от экипировки для User ID {$this->user_id}:", $bonusStats);

        return $bonusStats;
    }

    /**
     * Возвращает эффективные (итоговые) характеристики, как сумму базовых характеристик профиля,
     * бонусов от экипировки и бонусов от эффектов.
     */
    public function getEffectiveStats(): array
    {
        // Базовые характеристики, которые хранятся в профиле (теперь они НЕ включают статы предметов)
        $baseStats = [
            'strength' => $this->strength,
            'intelligence' => $this->intelligence,
            'recovery' => $this->recovery,
            'armor' => $this->armor,
            'crit_chance' => $this->crit_chance, // Базовый шанс крита из профиля
            'crit_damage' => $this->crit_damage, // Базовая сила крита из профиля
            'hp' => $this->hp,
            'mp' => $this->mp,
            'resistance_fire' => $this->resistance_fire,
            'resistance_lightning' => $this->resistance_lightning,
        ];
        // Log::debug("Базовые статы (до бонусов) для User ID {$this->user_id}:", $baseStats);

        // Получаем бонусы от экипированных предметов (которые не сломаны)
        $this->unsetRelation('gameItems');
        $bonusStats = $this->getEquippedBonusStats();

        // Получаем бонусы от активных эффектов умений
        $skillEffectService = app(\App\Services\SkillEffectService::class);
        $effectBonuses = [
            'strength' => $skillEffectService->getAttributeBonus($this->user, 'strength'),
            'intelligence' => $skillEffectService->getAttributeBonus($this->user, 'intelligence'),
            'recovery' => $skillEffectService->getAttributeBonus($this->user, 'recovery'),
            'armor' => $skillEffectService->getAttributeBonus($this->user, 'armor'),
            'crit_chance' => $skillEffectService->getAttributeBonus($this->user, 'crit_chance'),
            'crit_damage' => $skillEffectService->getAttributeBonus($this->user, 'crit_damage'),
            'hp' => $skillEffectService->getAttributeBonus($this->user, 'hp'),
            'mp' => $skillEffectService->getAttributeBonus($this->user, 'mp'),
            'resistance_fire' => $skillEffectService->getAttributeBonus($this->user, 'resistance_fire'),
            'resistance_lightning' => $skillEffectService->getAttributeBonus($this->user, 'resistance_lightning'),
        ];
        // Log::debug("Бонусы от эффектов для User ID {$this->user_id}:", $effectBonuses);

        // Складываем базовые характеристики, бонусы от предметов и бонусы от эффектов
        $effectiveStats = [];
        foreach ($baseStats as $stat => $value) {
            // Используем null coalescing operator (??) на случай, если стат отсутствует
            $effectiveStats[$stat] = ($value ?? 0) + ($bonusStats[$stat] ?? 0) + ($effectBonuses[$stat] ?? 0);
        }

        // Применяем процентные бонусы к броне от эффектов предметов
        $activeArmorEffects = session('active_armor_percent_effects', []);
        if (!empty($activeArmorEffects)) {
            $totalArmorBoost = 0;
            foreach ($activeArmorEffects as $effectData) {
                // Пересчитываем бонус на основе текущей брони
                $armorBoost = $effectiveStats['armor'] * ($effectData['percent'] / 100);
                $totalArmorBoost += $armorBoost;

                Log::debug("Применен процентный бонус к броне в getEffectiveStats", [
                    'user_id' => $this->user_id,
                    'base_armor' => $effectiveStats['armor'],
                    'percent' => $effectData['percent'],
                    'armor_boost' => $armorBoost
                ]);
            }

            // Добавляем суммарный бонус к броне
            $effectiveStats['armor'] += $totalArmorBoost;

            Log::info("Итоговая броня с учетом процентных бонусов", [
                'user_id' => $this->user_id,
                'final_armor' => $effectiveStats['armor'],
                'total_armor_boost' => $totalArmorBoost,
                'active_effects_count' => count($activeArmorEffects)
            ]);
        }

        // Log::debug("Итоговые эффективные статы для User ID {$this->user_id}:", $effectiveStats);

        return $effectiveStats;
    }

    // Пересчитываем GS на основе эффективных характеристик
    public function calculateGS(): float
    {
        $stats = $this->getEffectiveStats();

        return round(
            $stats['strength'] * 1.1 +
            $stats['intelligence'] * 1.1 +
            $stats['recovery'] * 1.1 +
            $stats['armor'] * 1.1 +
            $stats['crit_chance'] * 1.5 +
            $stats['crit_damage'] * 1.5 +
            //  $stats['hp'] +
            $stats['mp'] +
            ($stats['resistance_fire'] + $stats['resistance_lightning']) * 3
        );
    }

    /**
     * Рассчитывает эффективный процент шанса крита на основе СУММАРНОГО значения стата crit_chance.
     * (Публичный вспомогательный метод для getRawEffectiveCritChance и симуляции в шаблонах)
     *
     * @param float|int $critChanceStat The total raw crit chance stat value (base + items + effects).
     * @return float Effective chance percentage (unrounded).
     */
    public function calculateCritChancePercentage(float|int $critChanceStat): float
    {
        // Базовый шанс крита - 10%
        $baseChance = 10.0;
        // Если у персонажа нет статов крита, возвращаем базовый шанс
        if ($critChanceStat <= 0) {
            return $baseChance;
        }

        // Максимальный шанс при 10000 - 65% (бонус 55%)
        $maxBonusAt10k = 55.0;

        // Расчет бонуса
        if ($critChanceStat <= 10000) {
            $scaleFactor = $critChanceStat / 10000;
            // Добавляем небольшое логарифмическое ускорение
            $scaleFactor = $scaleFactor * (1 + 0.2 * log10(1 + $scaleFactor * 9));
            $critBonus = $maxBonusAt10k * $scaleFactor;
        } else {
            // После 10000 очень медленный рост
            $critBonus = $maxBonusAt10k + 15 * log10(1 + ($critChanceStat - 10000) / 10000);
        }

        // Итоговый шанс крита с ограничением в 100%
        return min(100.0, $baseChance + $critBonus);
    }

    /**
     * Рассчитывает эффективный множитель крит. урона на основе СУММАРНОГО значения стата crit_damage.
     * (Публичный вспомогательный метод для getRawEffectiveCritDamage и симуляции в шаблонах)
     *
     * @param float|int $critDamageStat The total raw crit damage stat value (base + items + effects).
     * @return float Effective damage multiplier (unrounded).
     */
    public function calculateCritDamageMultiplier(float|int $critDamageStat): float
    {
        // Базовый множитель крита - 1.3x
        $baseMultiplier = 1.3;
        // Если у персонажа нет статов силы крита, возвращаем базовый множитель
        if ($critDamageStat <= 0) {
            return $baseMultiplier;
        }

        // Максимальный множитель при 10000 - 3.0 (бонус 1.7)
        $maxBonusAt10k = 1.7;

        // Расчет бонуса
        if ($critDamageStat <= 10000) {
            $scaleFactor = $critDamageStat / 10000;
            // Добавляем небольшое логарифмическое ускорение
            $scaleFactor = $scaleFactor * (1 + 0.2 * log10(1 + $scaleFactor * 9));
            $damageBonus = $maxBonusAt10k * $scaleFactor;
        } else {
            // После 10000 очень медленный рост
            $damageBonus = $maxBonusAt10k + 0.5 * log10(1 + ($critDamageStat - 10000) / 10000);
        }

        // Итоговый множитель крита (без верхнего ограничения)
        return $baseMultiplier + $damageBonus;
    }

    /**
     * Рассчитывает реальный шанс критического удара на основе статов (без округления)
     *
     * @return float Шанс критического удара в процентах
     */
    public function getRawEffectiveCritChance(): float
    {
        // Получаем актуальное СУММАРНОЕ значение crit_chance
        $effectiveStats = $this->getEffectiveStats();
        $critChanceStat = $effectiveStats['crit_chance'];
        // Вызываем вспомогательный метод с суммарным статом
        return $this->calculateCritChancePercentage($critChanceStat);
    }

    /**
     * Рассчитывает реальный множитель критического урона на основе статов (без округления)
     *
     * @return float Множитель критического урона
     */
    public function getRawEffectiveCritDamage(): float
    {
        // Получаем актуальное СУММАРНОЕ значение crit_damage
        $effectiveStats = $this->getEffectiveStats();
        $critDamageStat = $effectiveStats['crit_damage'];
        // Вызываем вспомогательный метод с суммарным статом
        return $this->calculateCritDamageMultiplier($critDamageStat);
    }

    /**
     * Рассчитывает реальный шанс критического удара на основе статов
     * Формула: 10% (базовый) + линейный рост с масштабированием до 65% при 10000 статов
     *
     * @return float Шанс критического удара в процентах (от 10 до 100)
     */
    public function getEffectiveCritChance(): float
    {
        // Используем неокругленный метод и округляем здесь
        $effectiveChance = $this->getRawEffectiveCritChance();
        return round($effectiveChance, 1); // Округляем до одного знака после запятой
    }

    /**
     * Рассчитывает реальный множитель критического урона на основе статов
     * Формула: 1.3x (базовый) + рост до 3.0x при 10000 статов
     *
     * @return float Множитель критического урона (от 1.3 и выше)
     */
    public function getEffectiveCritDamage(): float
    {
        // Используем неокругленный метод и округляем здесь
        $effectiveMultiplier = $this->getRawEffectiveCritDamage();
        return round($effectiveMultiplier, 2); // Округляем до двух знаков после запятой
    }

    /**
     * Форматирует шанс крита для отображения в интерфейсе
     *
     * @return string Отформатированное значение шанса крита
     */
    public function getFormattedCritChance(): string
    {
        $chance = $this->getEffectiveCritChance();
        return "{$chance}%";
    }

    /**
     * Форматирует множитель крита для отображения в интерфейсе
     *
     * @return string Отформатированное значение множителя крита
     */
    public function getFormattedCritDamage(): string
    {
        $multiplier = $this->getEffectiveCritDamage();
        return "x{$multiplier}";
    }

    public function getTotalTimePlayedAttribute()
    {
        $hours = $this->hours_played;
        $minutes = $this->minutes_played;

        // Если минут больше 60, конвертируем их в часы
        if ($minutes >= 60) {
            $additionalHours = intdiv($minutes, 60);
            $hours += $additionalHours;
            $minutes = $minutes % 60;
        }

        return "{$hours}ч {$minutes}м";
    }

    /**
     * Расчет прогресса опыта до следующего уровня с оптимизацией для высоких нагрузок
     *
     * Метод использует Redis кеширование для пороговых значений опыта и оптимизированную
     * логику расчета прогресса. Подходит для игр с большим количеством пользователей онлайн.
     *
     * @return array Массив с данными о прогрессе опыта
     */
    public function getExperienceProgress(): array
    {
        // Глобальный опыт пользователя (не обнуляется при повышении уровня)
        $totalExperience = $this->experience;

        // Используем кешированные пороговые значения опыта для оптимизации производительности
        $levelThresholds = \Illuminate\Support\Facades\Cache::remember(
            'level_thresholds_all',
            86400, // Кешируем на 24 часа
            function () {
                return DB::table('level_thresholds')
                    ->orderBy('level')
                    ->pluck('experience_threshold', 'level');
            }
        );

        // Русский комментарий: Определяем текущий уровень пользователя
        $currentLevel = 1;
        $currentLevelThreshold = 0; // Опыт, необходимый для достижения текущего уровня

        // Русский комментарий: Логика определения уровня согласно структуре БД:
        // В таблице level_thresholds:
        // - level=1, threshold=0 (стартовый уровень)
        // - level=2, threshold=100 (для достижения 2 уровня нужно 100 опыта)
        // - level=3, threshold=150 (для достижения 3 уровня нужно 150 опыта)
        // Интерпретация:
        // - Уровень 1: от 0 до 99 опыта (до порога уровня 2)
        // - Уровень 2: от 100 до 149 опыта (от порога уровня 2 до порога уровня 3)
        // - И так далее...

        // Русский комментарий: Находим максимальный достигнутый уровень
        foreach ($levelThresholds as $level => $threshold) {
            if ($totalExperience >= $threshold) {
                $currentLevel = $level;
                $currentLevelThreshold = $threshold;
            } else {
                break;
            }
        }

        // Русский комментарий: Определяем порог следующего уровня
        $nextLevel = $currentLevel + 1;
        $nextLevelThreshold = $levelThresholds[$nextLevel] ?? null;

        // Русский комментарий: Если следующего уровня нет (достигнут максимальный уровень 85)
        if ($nextLevelThreshold === null || $currentLevel >= 85) {
            // Русский комментарий: Для максимального уровня показываем полный прогресс
            return [
                'current_level' => $currentLevel,
                'current_experience' => formatNumber($totalExperience),
                'next_experience' => formatNumber($totalExperience), // Показываем текущий опыт как максимум
                'percentage' => 100,
                'is_max_level' => true,
            ];
        }

        // Русский комментарий: Расчет прогресса внутри текущего уровня
        // Для уровня 1: currentLevelThreshold = 0, nextLevelThreshold = 100
        // Для уровня 2: currentLevelThreshold = 100, nextLevelThreshold = 150, и т.д.
        $experienceNeeded = $nextLevelThreshold - $currentLevelThreshold; // Опыт, необходимый для перехода на следующий уровень
        $experienceGained = $totalExperience - $currentLevelThreshold; // Опыт, накопленный в пределах текущего уровня

        // Русский комментарий: Рассчитываем процент прогресса (с защитой от деления на ноль)
        $percentage = $experienceNeeded > 0
            ? floor(($experienceGained / $experienceNeeded) * 100)
            : 100;

        return [
            'current_level' => $currentLevel,
            'current_experience' => formatNumber($experienceGained), // Накопленный опыт внутри уровня
            'next_experience' => formatNumber($experienceNeeded), // Требуемый опыт для следующего уровня
            'percentage' => max(0, min($percentage, 100)), // Процент прогресса (0-100)
            'is_max_level' => false,
        ];
    }

    /**
     * Расчет прогресса опыта тестирования до следующего уровня
     *
     * @return array
     */
    public function getTestingExperienceProgress(): array
    {
        $testingExperience = $this->testing_experience ?? 0;

        // Используем ту же формулу, что и в TicketService::calculateTestingLevel
        if ($testingExperience < 10) {
            return [
                'current_level' => 1,
                'current_experience' => $testingExperience,
                'experience_for_current_level' => 0,
                'experience_for_next_level' => 10,
                'experience_to_next_level' => 10 - $testingExperience,
                'progress_percentage' => ($testingExperience / 10) * 100,
                'progress_current' => $testingExperience,
                'progress_max' => 10
            ];
        }

        // Прогрессивная формула: каждый следующий уровень требует больше опыта
        // Уровень 2: 10 опыта, далее увеличение на 20% каждый уровень
        $level = 1;
        $totalRequiredExp = 0;
        $nextLevelExp = 10;

        // Находим текущий уровень
        while ($testingExperience >= $totalRequiredExp + $nextLevelExp && $level < 50) {
            $totalRequiredExp += $nextLevelExp;
            $level++;
            $nextLevelExp = (int) ($nextLevelExp * 1.2); // Увеличение на 20%
        }

        // Опыт для текущего уровня
        $experienceForCurrentLevel = $totalRequiredExp;

        // Опыт для следующего уровня
        $experienceForNextLevel = $totalRequiredExp + $nextLevelExp;

        // Текущий прогресс в рамках уровня
        $currentLevelProgress = $testingExperience - $experienceForCurrentLevel;

        // Процент прогресса
        $progressPercentage = $nextLevelExp > 0 ? ($currentLevelProgress / $nextLevelExp) * 100 : 100;

        return [
            'current_level' => $level,
            'current_experience' => $testingExperience,
            'experience_for_current_level' => $experienceForCurrentLevel,
            'experience_for_next_level' => $experienceForNextLevel,
            'experience_to_next_level' => $nextLevelExp - $currentLevelProgress,
            'progress_percentage' => min(100, $progressPercentage),
            'progress_current' => $currentLevelProgress,
            'progress_max' => $nextLevelExp
        ];
    }

    /**
     * Получить необходимое количество опыта для достижения указанного уровня
     *
     * Этот метод обеспечивает совместимость с контроллерами, которые ожидают
     * метод getExperienceForLevel() в модели UserProfile. Внутренне использует
     * статический метод из модели LevelThreshold для получения данных.
     *
     * @param int $level Целевой уровень
     * @return int|null Количество опыта, необходимое для достижения уровня, или null если уровень не найден
     */
    public function getExperienceForLevel(int $level): ?int
    {
        return LevelThreshold::getExperienceForLevel($level);
    }

    /**
     * Добавляет валюту пользователю с автоматической конвертацией
     *
     * @param string $type Тип валюты (gold, silver, bronze)
     * @param int $amount Количество
     */
    public function addCurrency(string $type, int $amount): void
    {
        app(CurrencyService::class)->awardCurrency($this, $type, $amount);
    }

    /**
     * Списывает валюту у пользователя с автоматической конвертацией
     *
     * @param string $type Тип валюты (gold, silver, bronze)
     * @param int $amount Количество
     * @return bool Успешность операции
     */
    public function subtractCurrency(string $type, int $amount): bool
    {
        return app(CurrencyService::class)->deductCurrency($this, $type, $amount);
    }

    /**
     * Проверяет, достаточно ли у пользователя валюты
     *
     * @param string $type Тип валюты
     * @param int $amount Количество
     * @return bool
     */
    public function hasCurrency(string $type, int $amount): bool
    {
        return app(CurrencyService::class)->hasEnoughCurrency($this, $type, $amount);
    }

    /**
     * Получает общий баланс пользователя в бронзе
     *
     * @return int
     */
    public function getTotalBronzeAttribute(): int
    {
        return app(CurrencyService::class)->toBronze($this->gold, $this->silver, $this->bronze);
    }

    protected static function booted()
    {
        // Автоматическая нормализация валюты при загрузке модели
        static::retrieved(function ($userProfile) {
            // Только если необходимо выполнить конвертацию
            if (
                $userProfile->bronze >= CurrencyService::BRONZE_PER_SILVER ||
                $userProfile->silver >= CurrencyService::SILVER_PER_GOLD
            ) {
                app(CurrencyService::class)->normalizeUserCurrency($userProfile);
            }
        });
    }

    /**
     * Пересчитывает и исправляет значение inventory_used на основе реальных данных
     * @return int Новое значение inventory_used
     */
    public function recalculateInventoryUsed(): int
    {
        try {
            // Получаем количество предметов в инвентаре
            $gameItemsCount = GameItem::where('owner_id', $this->user_id)
                ->where('location', 'inventory')
                ->count();

            // Получаем количество стаков ресурсов в инвентаре
            $resourceStacksCount = UserResource::where('user_id', $this->user_id)
                ->where('location', UserResource::LOCATION_INVENTORY)
                ->where('quantity', '>', 0) // Учитываем только ресурсы с положительным количеством
                ->count();

            // Получаем количество стаков алхимических ингредиентов в инвентаре
            $alchemyIngredientsCount = UserAlchemyIngredient::where('user_id', $this->user_id)
                ->where('location', 'inventory') // Учитываем только ингредиенты в инвентаре
                ->where('quantity', '>', 0) // Учитываем только ингредиенты с положительным количеством
                ->count();

            // Получаем количество стаков рецептов в инвентаре
            $recipesCount = UserRecipe::where('user_id', $this->user_id)
                ->where('location', 'inventory') // Учитываем только рецепты в инвентаре
                ->where('quantity', '>', 0) // Учитываем только рецепты с положительным количеством
                ->count();

            // Общее количество занятых слотов
            $totalUsed = $gameItemsCount + $resourceStacksCount + $alchemyIngredientsCount + $recipesCount;

            // ИСПРАВЛЕНИЕ: Проверяем, не превышает ли количество максимальную вместимость
            if ($totalUsed > $this->inventory_capacity) {
                Log::warning("Обнаружено переполнение инвентаря при пересчете", [
                    'user_id' => $this->user_id,
                    'calculated_used' => $totalUsed,
                    'inventory_capacity' => $this->inventory_capacity,
                    'overflow' => $totalUsed - $this->inventory_capacity,
                    'items_count' => $gameItemsCount,
                    'resources_count' => $resourceStacksCount,
                    'alchemy_ingredients_count' => $alchemyIngredientsCount,
                    'recipes_count' => $recipesCount
                ]);

                // Ограничиваем значение максимальной вместимостью
                $totalUsed = $this->inventory_capacity;
            }

            // Проверяем, изменилось ли значение
            $oldValue = $this->inventory_used;
            if ($oldValue !== $totalUsed) {
                // Обновляем значение в БД
                $this->inventory_used = $totalUsed;
                $this->save();

                Log::info("Пересчитан инвентарь для user_id: {$this->user_id}. Значение изменено с {$oldValue} на {$totalUsed}", [
                    'user_id' => $this->user_id,
                    'items_count' => $gameItemsCount,
                    'resources_count' => $resourceStacksCount,
                    'alchemy_ingredients_count' => $alchemyIngredientsCount,
                    'recipes_count' => $recipesCount,
                    'old_inventory_used' => $oldValue,
                    'new_inventory_used' => $totalUsed,
                    'inventory_capacity' => $this->inventory_capacity
                ]);
            }

            return $totalUsed;
        } catch (\Exception $e) {
            // В случае ошибки логируем её и возвращаем текущее значение
            Log::error("Ошибка при пересчете инвентаря: " . $e->getMessage(), [
                'user_id' => $this->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->inventory_used;
        }
    }

    /**
     * Добавляет предмет в инвентарь пользователя с атомарными операциями
     *
     * @param GameItem $gameItem Игровой предмет для добавления
     * @return bool true, если предмет успешно добавлен, false, если инвентарь заполнен
     */
    public function addItem(GameItem $gameItem): bool
    {
        // Используем транзакцию для атомарности операций
        return DB::transaction(function () use ($gameItem) {
            // Блокируем запись профиля для предотвращения race conditions
            $lockedProfile = self::where('user_id', $this->user_id)->lockForUpdate()->first();

            if (!$lockedProfile) {
                Log::error('Не удалось заблокировать профиль пользователя для добавления предмета', [
                    'user_id' => $this->user_id,
                    'item_id' => $gameItem->id ?? 'new'
                ]);
                return false;
            }

            // Пересчитываем актуальное количество занятых слотов с блокировкой
            $actualInventoryUsed = $lockedProfile->recalculateInventoryUsed();

            // Проверяем, есть ли место в инвентаре
            if ($actualInventoryUsed >= $lockedProfile->inventory_capacity) {
                Log::info('Инвентарь заполнен, предмет не добавлен', [
                    'user_id' => $this->user_id,
                    'actual_inventory_used' => $actualInventoryUsed,
                    'inventory_capacity' => $lockedProfile->inventory_capacity,
                    'item_name' => $gameItem->item_name ?? 'Неизвестный предмет'
                ]);

                // Добавляем сообщение в боевой лог
                $logKey = app(BattleLogService::class)->getBattleLogKey($this->user_id);
                $logMessage = "<span class='flex items-center text-xs'><span class='text-red-500 mr-1'>✖</span><span class='text-red-400 mx-1'>Инвентарь заполнен!</span></span>";
                app(BattleLogService::class)->addLog($logKey, $logMessage, 'error');

                return false;
            }

            // Атомарно устанавливаем владельца и местоположение предмета
            $gameItem->owner_id = $this->user_id;
            $gameItem->location = 'inventory';
            $gameItem->save();

            // Атомарно увеличиваем счетчик занятого места в инвентаре
            $lockedProfile->increment('inventory_used');

            // Логируем успешное добавление предмета
            Log::info("Предмет добавлен в инвентарь (атомарная операция)", [
                'user_id' => $this->user_id,
                'gameItem_id' => $gameItem->id,
                'item_name' => $gameItem->item_name,
                'inventory_used_after' => $lockedProfile->inventory_used
            ]);

            return true;
        });
    }

    /**
     * Псевдоним для метода recalculateInventoryUsed
     * Оставлен для обратной совместимости
     *
     * @return int
     * @deprecated Используйте recalculateInventoryUsed() вместо этого метода
     */
    public function recalculateInventoryUsage(): int
    {
        return $this->recalculateInventoryUsed();
    }

    /**
     * Возвращает отформатированный процент снижения урона от брони для профиля
     * Использует CombatFormulaService и итоговую броню персонажа
     *
     * @return string Процент снижения урона (например, "35.5%")
     */
    public function getFormattedArmorReduction(): string
    {
        // Получаем итоговую броню с учетом предметов и эффектов
        $effectiveStats = $this->getEffectiveStats();
        $armor = round($effectiveStats['armor'] ?? 0);
        // Получаем сервис расчета формул
        $formulaService = app(CombatFormulaService::class);
        // Получаем процент снижения урона (например, 0.355)
        $percent = $formulaService->getArmorReductionPercent($armor);
        // Форматируем в строку с одним знаком после запятой (например, "35.5%")
        return number_format($percent * 100, 1, '.', '') . '%';
    }

    /**
     * Возвращает информацию о скорости восстановления ресурсов персонажа
     * На основе значения recovery и формул: MP = recovery/60, HP = recovery/180
     *
     * @param bool $formatted Если true, возвращает отформатированные значения с округлением и единицами измерения
     * @return array Массив со скоростью восстановления [hp_per_second, mp_per_second, hp_per_minute, mp_per_minute]
     */
    public function getRegenerationRates(bool $formatted = false): array
    {
        // Получаем эффективное значение recovery с учетом предметов и эффектов
        $effectiveStats = $this->getEffectiveStats();
        $recovery = $effectiveStats['recovery'] ?? $this->recovery ?? 0;

        // Формула: HP/сек = recovery * 1.5 / 180, MP/сек = recovery * 1.5 / 60
        // Если нужно изменить скорость регенерации, меняйте множитель 1.5
        $hpRegenPerSecond = $recovery * 1.5 / 180; // HP: восстановление * 1.5 делим на 180
        $mpRegenPerSecond = $recovery * 1.5 / 60;  // MP: восстановление * 1.5 делим на 60

        // Применяем множитель регенерации HP из сессии, если он есть
        $activeMultipliers = session('active_hp_regen_multipliers', []);
        if (!empty($activeMultipliers)) {
            // Если есть несколько множителей, используем максимальный
            $maxMultiplier = max($activeMultipliers);
            $hpRegenPerSecond *= $maxMultiplier;

            Log::info("Применен множитель регенерации HP в getRegenerationRates", [
                'user_id' => $this->user_id,
                'base_hp_regen' => $recovery * 1.5 / 180,
                'multiplier' => $maxMultiplier,
                'new_hp_regen' => $hpRegenPerSecond
            ]);
        }

        // Вычисляем регенерацию в минуту (для удобства отображения)
        $mpRegenPerMinute = $mpRegenPerSecond * 60;
        $hpRegenPerMinute = $hpRegenPerSecond * 60;

        // Если нужны форматированные значения
        if ($formatted) {
            return [
                'hp_per_second' => number_format($hpRegenPerSecond, 2) . ' HP/сек',
                'mp_per_second' => number_format($mpRegenPerSecond, 2) . ' MP/сек',
                'hp_per_minute' => number_format($hpRegenPerMinute, 1) . ' HP/мин',
                'mp_per_minute' => number_format($mpRegenPerMinute, 1) . ' MP/мин',
                'recovery' => $recovery
            ];
        }

        // Возвращаем числовые значения
        return [
            'hp_per_second' => $hpRegenPerSecond,
            'mp_per_second' => $mpRegenPerSecond,
            'hp_per_minute' => $hpRegenPerMinute,
            'mp_per_minute' => $mpRegenPerMinute,
            'recovery' => $recovery
        ];
    }

    /**
     * Возвращает информацию о времени до следующей полной регенерации HP и MP
     *
     * @param bool $formatted Если true, возвращает отформатированное время в виде строки
     * @return array Массив с временем регенерации [time_to_full_hp, time_to_full_mp]
     */
    public function getNextRegenerationTime(bool $formatted = false): array
    {
        // Получаем эффективное значение recovery
        $effectiveStats = $this->getEffectiveStats();
        $recovery = $effectiveStats['recovery'] ?? $this->recovery ?? 0;

        // Если recovery равно 0, регенерации не будет
        if ($recovery <= 0) {
            return [
                'time_to_full_hp' => $formatted ? 'Никогда' : null,
                'time_to_full_mp' => $formatted ? 'Никогда' : null,
                'is_hp_full' => $this->current_hp >= $this->max_hp,
                'is_mp_full' => $this->current_mp >= $this->max_mp
            ];
        }

        // Вычисляем время в секундах до полного восстановления
        // Используем ту же формулу, что и в getRegenerationRates и regenerateResources
        // Формула: HP/сек = recovery * 1.5 / 180, MP/сек = recovery * 1.5 / 60
        // Если нужно изменить скорость регенерации, меняйте множитель 1.5
        $hpRegenPerSecond = $recovery * 1.5 / 180;
        $mpRegenPerSecond = $recovery * 1.5 / 60;

        // Применяем множитель регенерации HP из сессии, если он есть
        $activeMultipliers = session('active_hp_regen_multipliers', []);
        if (!empty($activeMultipliers)) {
            // Если есть несколько множителей, используем максимальный
            $maxMultiplier = max($activeMultipliers);
            $hpRegenPerSecond *= $maxMultiplier;

            Log::info("Применен множитель регенерации HP в getNextRegenerationTime", [
                'user_id' => $this->user_id,
                'base_hp_regen' => $recovery * 1.5 / 180,
                'multiplier' => $maxMultiplier,
                'new_hp_regen' => $hpRegenPerSecond
            ]);
        }

        // Время до полного восстановления в секундах
        $timeToFullHp = $hpRegenPerSecond > 0 ? ceil($this->max_hp - $this->current_hp / $hpRegenPerSecond) : null;
        $timeToFullMp = $mpRegenPerSecond > 0 ? ceil($this->max_mp - $this->current_mp / $mpRegenPerSecond) : null;

        // Если ресурсы уже полные, устанавливаем время в 0
        if ($this->current_hp >= $this->max_hp) {
            $timeToFullHp = 0;
        }
        if ($this->current_mp >= $this->max_mp) {
            $timeToFullMp = 0;
        }

        // Форматируем время, если требуется
        if ($formatted) {
            // Функция для форматирования времени в удобный вид
            $formatTime = function ($seconds) {
                if ($seconds === null)
                    return 'Никогда';
                if ($seconds === 0)
                    return 'Полное';

                $hours = floor($seconds / 3600);
                $minutes = floor(($seconds % 3600) / 60);
                $secs = $seconds % 60;

                if ($hours > 0) {
                    return "{$hours}ч {$minutes}м";
                } elseif ($minutes > 0) {
                    return "{$minutes}м {$secs}с";
                } else {
                    return "{$secs}с";
                }
            };

            return [
                'time_to_full_hp' => $formatTime($timeToFullHp),
                'time_to_full_mp' => $formatTime($timeToFullMp),
                'is_hp_full' => $this->current_hp >= $this->max_hp,
                'is_mp_full' => $this->current_mp >= $this->max_mp
            ];
        }

        // Возвращаем числовые значения
        return [
            'time_to_full_hp' => $timeToFullHp,
            'time_to_full_mp' => $timeToFullMp,
            'is_hp_full' => $this->current_hp >= $this->max_hp,
            'is_mp_full' => $this->current_mp >= $this->max_mp
        ];
    }

    /**
     * Регенерирует здоровье и ману персонажа на основе времени и статистики восстановления
     * Формула: MP восстанавливается со скоростью recovery/60 в секунду
     *          HP восстанавливается со скоростью recovery/180 в секунду
     *
     * @param float|null $secondsElapsed Количество секунд с последнего обновления (если null, считается от last_regeneration_at)
     * @return array Массив с информацией о регенерации [hp_regenerated, mp_regenerated, is_max_hp, is_max_mp]
     */
    public function regenerateResources(?float $secondsElapsed = null): array
    {
        // Получаем текущее время
        $now = now();

        // Если время не передано, вычисляем на основе последней регенерации
        if ($secondsElapsed === null) {
            // Получаем время последней регенерации из базы данных или используем текущее время, если его нет
            $lastRegenTime = $this->last_regeneration_at ? Carbon::parse($this->last_regeneration_at) : $now;
            // Вычисляем прошедшее время в секундах
            $secondsElapsed = $now->diffInSeconds($lastRegenTime);

            // Если прошло менее 1 секунды, нет смысла выполнять регенерацию
            if ($secondsElapsed < 1) {
                return [
                    'hp_regenerated' => 0,
                    'mp_regenerated' => 0,
                    'is_max_hp' => $this->current_hp >= $this->max_hp,
                    'is_max_mp' => $this->current_mp >= $this->max_mp
                ];
            }
        }

        // Получаем эффективные характеристики персонажа
        $effectiveStats = $this->getEffectiveStats();
        $recovery = $effectiveStats['recovery'] ?? $this->recovery ?? 0;

        // Вычисляем регенерацию в секунду
        // Используем ту же формулу, что и в getRegenerationRates
        // Формула: HP/сек = recovery * 1.5 / 180, MP/сек = recovery * 1.5 / 60
        // Если нужно изменить скорость регенерации, меняйте множитель 1.5
        $mpRegenPerSecond = $recovery * 1.5 / 60; // Восстановление маны: recovery * 1.5 / 60 в секунду
        $hpRegenPerSecond = $recovery * 1.5 / 180; // Восстановление здоровья: recovery * 1.5 / 180 в секунду

        // Применяем множитель регенерации HP из сессии, если он есть
        $activeMultipliers = session('active_hp_regen_multipliers', []);
        if (!empty($activeMultipliers)) {
            // Если есть несколько множителей, используем максимальный
            $maxMultiplier = max($activeMultipliers);
            $hpRegenPerSecond *= $maxMultiplier;

            Log::info("Применен множитель регенерации HP из сессии", [
                'user_id' => $this->user_id,
                'base_hp_regen' => $recovery * 1.5 / 180,
                'multiplier' => $maxMultiplier,
                'new_hp_regen' => $hpRegenPerSecond
            ]);
        }

        // Вычисляем сколько ресурсов будет восстановлено за прошедшее время
        $mpToRegen = $mpRegenPerSecond * $secondsElapsed;
        $hpToRegen = $hpRegenPerSecond * $secondsElapsed;

        // Проверяем текущие значения ресурсов
        $isMaxHp = $this->current_hp >= $this->max_hp;
        $isMaxMp = $this->current_mp >= $this->max_mp;

        // Если ресурсы уже максимальны, нет необходимости регенерировать
        if ($isMaxHp && $isMaxMp) {
            // Обновляем время последней регенерации, даже если регенерации не было
            $this->last_regeneration_at = $now;
            $this->save();

            return [
                'hp_regenerated' => 0,
                'mp_regenerated' => 0,
                'is_max_hp' => true,
                'is_max_mp' => true
            ];
        }

        // --- Новая логика: всегда считаем сколько восстановилось ресурсов, даже если меньше 1 в секунду ---
        // Округляем вниз до целого, чтобы не было дробных HP/MP
        $hpToRegen = floor($hpRegenPerSecond * $secondsElapsed); // HP всегда округляем вниз
        $mpToRegen = floor($mpRegenPerSecond * $secondsElapsed); // MP всегда округляем вниз

        // Новые значения HP/MP не могут превышать максимальные значения
        $actualHp = min($this->max_hp, $this->current_hp + $hpToRegen); // HP с округлением вниз
        $actualMp = min($this->max_mp, $this->current_mp + $mpToRegen); // MP с округлением вниз

        // Вычисляем, сколько фактически было восстановлено
        $hpRegenerated = $actualHp - $this->current_hp;
        $mpRegenerated = $actualMp - $this->current_mp;

        // Обновляем значения в базе данных
        $this->current_hp = $actualHp;
        $this->current_mp = $actualMp;
        $this->last_regeneration_at = $now;
        $this->save();

        // Логируем процесс регенерации для отладки
        Log::debug("Регенерация ресурсов для пользователя ID {$this->user_id}:", [
            'seconds_elapsed' => $secondsElapsed,
            'recovery' => $recovery,
            'hp_regen_per_second' => $hpRegenPerSecond,
            'mp_regen_per_second' => $mpRegenPerSecond,
            'hp_regenerated' => $hpRegenerated,
            'mp_regenerated' => $mpRegenerated,
            'new_hp' => $actualHp,
            'new_mp' => $actualMp
        ]);

        return [
            'hp_regenerated' => $hpRegenerated,
            'mp_regenerated' => $mpRegenerated,
            'is_max_hp' => $actualHp >= $this->max_hp,
            'is_max_mp' => $actualMp >= $this->max_mp
        ];
    }

    /**
     * Возвращает актуальные значения HP и MP на текущий момент времени (без сохранения в базу)
     *
     * Этот метод нужен для того, чтобы всегда показывать игроку и другим пользователям
     * актуальное количество здоровья (HP) и маны (MP), даже если игрок давно не был онлайн.
     *
     * Логика:
     * - Сначала проверяем наличие данных в Redis, если они есть - используем их
     * - Если данных в Redis нет, используем данные из базы и вычисляем регенерацию
     * - В базе хранятся только значения current_hp, current_mp и время последней регенерации (last_regeneration_at)
     * - При каждом обращении к этому методу вычисляется, сколько HP/MP должно было восстановиться с момента last_regeneration_at
     * - HP восстанавливается со скоростью recovery / 180 в секунду
     * - MP восстанавливается со скоростью recovery / 60 в секунду
     * - Если прошло много времени, HP/MP не могут превысить максимальные значения (max_hp, max_mp)
     * - Метод не сохраняет новые значения в базу, а только возвращает их для использования в логике
     *
     * @param Carbon|null $now Текущее время (если не указано, берется текущее системное)
     * @return array Массив с ключами: 'current_hp', 'current_mp', 'is_hp_full', 'is_mp_full', 'seconds_elapsed'
     */
    public function getActualResources(?Carbon $now = null): array
    {
        // Если время не передано, используем текущее системное
        $now = $now ?: Carbon::now();

        // Проверяем, находится ли игрок на странице поражения
        // Но теперь мы всегда позволяем регенерацию HP, даже если игрок на странице поражения
        // Логируем для отладки
        // \Illuminate\Support\Facades\Log::debug("Проверка состояния игрока в getActualResources", [
        //     'user_id' => $this->user_id,
        //     'current_hp' => $this->current_hp,
        //     'is_defeated' => session('is_defeated'),
        //     'player_dead' => session('player_dead'),
        //     'last_regeneration_at' => $this->last_regeneration_at ? $this->last_regeneration_at->toDateTimeString() : null
        // ]);

        // Теперь мы всегда позволяем регенерацию HP, даже если оно равно 0
        // Это позволяет игроку восстанавливаться сразу после смерти, даже на странице поражения
        // Логируем состояние для отладки
        // \Illuminate\Support\Facades\Log::debug("Состояние игрока в getActualResources", [
        //     'player_id' => $this->user_id,
        //     'current_hp' => $this->current_hp,
        //     'max_hp' => $this->max_hp,
        //     'player_dead_flag' => session('player_dead'),
        //     'is_defeated_flag' => session('is_defeated'),
        //     'last_regeneration_at' => $this->last_regeneration_at ? $this->last_regeneration_at->toDateTimeString() : null
        // ]);

        // Сначала проверяем наличие данных в Redis для HP
        $healthRedisKey = "player:health:{$this->user_id}";
        $manaRedisKey = "player:mana:{$this->user_id}";

        $healthDataExists = \Illuminate\Support\Facades\Redis::exists($healthRedisKey);
        $manaDataExists = \Illuminate\Support\Facades\Redis::exists($manaRedisKey);

        // Если данные о здоровье и мане есть в Redis, используем их
        if ($healthDataExists && $manaDataExists) {
            // Получаем данные о здоровье из Redis
            $healthData = json_decode(\Illuminate\Support\Facades\Redis::get($healthRedisKey), true);

            // Проверяем, изменился ли max_hp в базе данных
            if ($healthData['max_hp'] != $this->max_hp) {
                // Если max_hp изменился, обновляем его в Redis
                $oldMaxHp = $healthData['max_hp'];
                $healthData['max_hp'] = $this->max_hp;

                // Если текущее HP больше нового максимума, ограничиваем его
                if ($healthData['current_hp'] > $this->max_hp) {
                    $healthData['current_hp'] = $this->max_hp;

                    \Log::info("Ограничено текущее HP до нового максимума в getActualResources", [
                        'user_id' => $this->user_id,
                        'old_max_hp' => $oldMaxHp,
                        'new_max_hp' => $this->max_hp,
                        'current_hp' => $healthData['current_hp']
                    ]);
                }

                \Log::info("Обновлен максимум HP в Redis из базы данных в getActualResources", [
                    'user_id' => $this->user_id,
                    'old_max_hp' => $oldMaxHp,
                    'new_max_hp' => $this->max_hp
                ]);
            }

            // Применяем регенерацию HP на основе прошедшего времени
            $secondsElapsedHP = $now->timestamp - $healthData['last_update'];

            if ($secondsElapsedHP > 0 && $healthData['current_hp'] > 0 && $healthData['current_hp'] < $healthData['max_hp']) {
                // Вычисляем, сколько HP восстановилось за прошедшее время
                $hpToRegen = $healthData['recovery_rate'] * $secondsElapsedHP;

                // Обновляем HP, но не больше максимального
                $healthData['current_hp'] = min($healthData['max_hp'], $healthData['current_hp'] + $hpToRegen);

                // Обновляем время последнего обновления
                $healthData['last_update'] = $now->timestamp;

                // Сохраняем обновленные данные в Redis
                \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
                \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа
            } else {
                // Даже если регенерация не применялась, но max_hp изменился, сохраняем обновленные данные
                if ($healthData['max_hp'] != $this->max_hp) {
                    \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
                    \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа
                }
            }

            // Получаем данные о мане из Redis
            $manaData = json_decode(\Illuminate\Support\Facades\Redis::get($manaRedisKey), true);

            // Применяем регенерацию MP на основе прошедшего времени
            $secondsElapsedMP = $now->timestamp - $manaData['last_update'];

            if ($secondsElapsedMP > 0 && $manaData['current_mp'] < $manaData['max_mp']) {
                // ИСПРАВЛЕНИЕ: Проверяем на отрицательные значения MP
                if ($manaData['current_mp'] < 0) {
                    \Log::warning("Обнаружено отрицательное значение MP в Redis, сброс до 0", [
                        'user_id' => $this->user_id,
                        'current_mp' => $manaData['current_mp'],
                        'max_mp' => $manaData['max_mp']
                    ]);
                    $manaData['current_mp'] = 0;
                }

                // Вычисляем, сколько MP восстановилось за прошедшее время
                $mpToRegen = $manaData['recovery_rate'] * $secondsElapsedMP;

                // Обновляем MP, но не больше максимального и не меньше 0
                $manaData['current_mp'] = min($manaData['max_mp'], max(0, $manaData['current_mp'] + $mpToRegen));

                // Обновляем время последнего обновления
                $manaData['last_update'] = $now->timestamp;

                // Сохраняем обновленные данные в Redis
                \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
                \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа
            }

            // Логируем для отладки
            \Log::debug("getActualResources из Redis для #{$this->user_id}", [
                'redis_hp' => $healthData['current_hp'],
                'redis_max_hp' => $healthData['max_hp'],
                'redis_hp_last_update' => date('Y-m-d H:i:s', $healthData['last_update']),
                'redis_hp_recovery_rate' => $healthData['recovery_rate'],
                'seconds_elapsed_hp' => $secondsElapsedHP,
                'hp_to_regen' => $hpToRegen ?? 0,
                'redis_mp' => $manaData['current_mp'],
                'redis_max_mp' => $manaData['max_mp'],
                'redis_mp_last_update' => date('Y-m-d H:i:s', $manaData['last_update']),
                'redis_mp_recovery_rate' => $manaData['recovery_rate'],
                'seconds_elapsed_mp' => $secondsElapsedMP,
                'mp_to_regen' => $mpToRegen ?? 0
            ]);

            // ИСПРАВЛЕНИЕ: Дополнительная валидация перед возвратом
            $finalHp = max(0, (int) round($healthData['current_hp']));
            $finalMp = max(0, (int) round($manaData['current_mp']));

            // Возвращаем данные из Redis для HP и MP
            return [
                'current_hp' => $finalHp,
                'current_mp' => $finalMp,
                'is_hp_full' => $finalHp >= $healthData['max_hp'],
                'is_mp_full' => $finalMp >= $manaData['max_mp'],
                'seconds_elapsed' => max($secondsElapsedHP, $secondsElapsedMP)
            ];
        }

        // Если данные о здоровье есть в Redis, но данных о мане нет
        if ($healthDataExists && !$manaDataExists) {
            // Получаем данные о здоровье из Redis
            $healthData = json_decode(\Illuminate\Support\Facades\Redis::get($healthRedisKey), true);

            // Проверяем, изменился ли max_hp в базе данных
            if ($healthData['max_hp'] != $this->max_hp) {
                // Если max_hp изменился, обновляем его в Redis
                $oldMaxHp = $healthData['max_hp'];
                $healthData['max_hp'] = $this->max_hp;

                // Если текущее HP больше нового максимума, ограничиваем его
                if ($healthData['current_hp'] > $this->max_hp) {
                    $healthData['current_hp'] = $this->max_hp;

                    \Log::info("Ограничено текущее HP до нового максимума в getActualResources (случай 2)", [
                        'user_id' => $this->user_id,
                        'old_max_hp' => $oldMaxHp,
                        'new_max_hp' => $this->max_hp,
                        'current_hp' => $healthData['current_hp']
                    ]);
                }

                \Log::info("Обновлен максимум HP в Redis из базы данных в getActualResources (случай 2)", [
                    'user_id' => $this->user_id,
                    'old_max_hp' => $oldMaxHp,
                    'new_max_hp' => $this->max_hp
                ]);
            }

            // Применяем регенерацию HP на основе прошедшего времени
            $secondsElapsedHP = $now->timestamp - $healthData['last_update'];

            if ($secondsElapsedHP > 0 && $healthData['current_hp'] > 0 && $healthData['current_hp'] < $healthData['max_hp']) {
                // Вычисляем, сколько HP восстановилось за прошедшее время
                $hpToRegen = $healthData['recovery_rate'] * $secondsElapsedHP;

                // Обновляем HP, но не больше максимального
                $healthData['current_hp'] = min($healthData['max_hp'], $healthData['current_hp'] + $hpToRegen);

                // Обновляем время последнего обновления
                $healthData['last_update'] = $now->timestamp;

                // Сохраняем обновленные данные в Redis
                \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
                \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа
            } else {
                // Даже если регенерация не применялась, но max_hp изменился, сохраняем обновленные данные
                if ($healthData['max_hp'] != $this->max_hp) {
                    \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
                    \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа
                }
            }

            // Получаем актуальные значения MP из базы данных с учетом эффективного recovery
            $effectiveStats = $this->getEffectiveStats();
            $recovery = $effectiveStats['recovery'] ?? $this->recovery ?? 0;
            $mpPerSecond = ($recovery * 1.5) / 60;
            $lastRegen = Carbon::parse($this->last_regeneration_at ?: $now);
            $secondsElapsedForMp = $now->diffInSeconds($lastRegen);
            $mpToRegen = $mpPerSecond * $secondsElapsedForMp;
            $actualMp = min($this->max_mp, $this->current_mp + $mpToRegen);

            // Инициализируем данные о мане в Redis
            $manaData = [
                'current_mp' => $actualMp,
                'max_mp' => $this->max_mp,
                'last_update' => $now->timestamp,
                'recovery_rate' => $mpPerSecond
            ];

            // Сохраняем данные о мане в Redis
            \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
            \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа

            // Логируем для отладки
            \Log::debug("getActualResources из Redis для HP и из базы для MP для #{$this->user_id}", [
                'redis_hp' => $healthData['current_hp'],
                'redis_max_hp' => $healthData['max_hp'],
                'redis_hp_last_update' => date('Y-m-d H:i:s', $healthData['last_update']),
                'redis_hp_recovery_rate' => $healthData['recovery_rate'],
                'seconds_elapsed_hp' => $secondsElapsedHP,
                'hp_to_regen' => $hpToRegen ?? 0,
                'current_mp' => $actualMp,
                'max_mp' => $this->max_mp,
                'mp_recovery_rate' => $mpPerSecond,
                'seconds_elapsed_mp' => $secondsElapsedForMp,
                'mp_to_regen' => $mpToRegen
            ]);

            // Возвращаем данные из Redis для HP и вычисленные для MP
            return [
                'current_hp' => (int) round($healthData['current_hp']),
                'current_mp' => (int) round($actualMp),
                'is_hp_full' => $healthData['current_hp'] >= $healthData['max_hp'],
                'is_mp_full' => $actualMp >= $this->max_mp,
                'seconds_elapsed' => max($secondsElapsedHP, $secondsElapsedForMp)
            ];
        }

        // Если данные о мане есть в Redis, но данных о здоровье нет
        if (!$healthDataExists && $manaDataExists) {
            // Получаем данные о мане из Redis
            $manaData = json_decode(\Illuminate\Support\Facades\Redis::get($manaRedisKey), true);

            // Проверяем, изменился ли max_mp в базе данных
            if ($manaData['max_mp'] != $this->max_mp) {
                // Если max_mp изменился, обновляем его в Redis
                $oldMaxMp = $manaData['max_mp'];
                $manaData['max_mp'] = $this->max_mp;

                // Если текущее MP больше нового максимума, ограничиваем его
                if ($manaData['current_mp'] > $this->max_mp) {
                    $manaData['current_mp'] = $this->max_mp;

                    \Log::info("Ограничено текущее MP до нового максимума в getActualResources", [
                        'user_id' => $this->user_id,
                        'old_max_mp' => $oldMaxMp,
                        'new_max_mp' => $this->max_mp,
                        'current_mp' => $manaData['current_mp']
                    ]);
                }

                \Log::info("Обновлен максимум MP в Redis из базы данных в getActualResources", [
                    'user_id' => $this->user_id,
                    'old_max_mp' => $oldMaxMp,
                    'new_max_mp' => $this->max_mp
                ]);
            }

            // Применяем регенерацию MP на основе прошедшего времени
            $secondsElapsedMP = $now->timestamp - $manaData['last_update'];

            if ($secondsElapsedMP > 0 && $manaData['current_mp'] < $manaData['max_mp']) {
                // Вычисляем, сколько MP восстановилось за прошедшее время
                $mpToRegen = $manaData['recovery_rate'] * $secondsElapsedMP;

                // Обновляем MP, но не больше максимального
                $manaData['current_mp'] = min($manaData['max_mp'], $manaData['current_mp'] + $mpToRegen);

                // Обновляем время последнего обновления
                $manaData['last_update'] = $now->timestamp;

                // Сохраняем обновленные данные в Redis
                \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
                \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа
            } else {
                // Даже если регенерация не применялась, но max_mp изменился, сохраняем обновленные данные
                if ($manaData['max_mp'] != $this->max_mp) {
                    \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
                    \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа
                }
            }

            // Получаем актуальные значения HP из базы данных с учетом эффективного recovery
            $effectiveStats = $this->getEffectiveStats();
            $recovery = $effectiveStats['recovery'] ?? $this->recovery ?? 0;
            $hpPerSecond = ($recovery * 1.5) / 180;
            $lastRegen = Carbon::parse($this->last_regeneration_at ?: $now);
            $secondsElapsedForHp = $now->diffInSeconds($lastRegen);
            $hpToRegen = $hpPerSecond * $secondsElapsedForHp;
            $actualHp = min($this->max_hp, $this->current_hp + $hpToRegen);

            // Инициализируем данные о здоровье в Redis
            $healthData = [
                'current_hp' => $actualHp,
                'max_hp' => $this->max_hp,
                'last_update' => $now->timestamp,
                'recovery_rate' => $hpPerSecond
            ];

            // Сохраняем данные о здоровье в Redis
            \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
            \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа

            // Логируем для отладки
            \Log::debug("getActualResources из Redis для MP и из базы для HP для #{$this->user_id}", [
                'current_hp' => $actualHp,
                'max_hp' => $this->max_hp,
                'hp_recovery_rate' => $hpPerSecond,
                'seconds_elapsed_hp' => $secondsElapsedForHp,
                'hp_to_regen' => $hpToRegen,
                'redis_mp' => $manaData['current_mp'],
                'redis_max_mp' => $manaData['max_mp'],
                'redis_mp_last_update' => date('Y-m-d H:i:s', $manaData['last_update']),
                'redis_mp_recovery_rate' => $manaData['recovery_rate'],
                'seconds_elapsed_mp' => $secondsElapsedMP,
                'mp_to_regen' => $mpToRegen ?? 0
            ]);

            // Возвращаем данные из Redis для MP и вычисленные для HP
            return [
                'current_hp' => (int) round($actualHp),
                'current_mp' => (int) round($manaData['current_mp']),
                'is_hp_full' => $actualHp >= $this->max_hp,
                'is_mp_full' => $manaData['current_mp'] >= $manaData['max_mp'],
                'seconds_elapsed' => max($secondsElapsedForHp, $secondsElapsedMP)
            ];
        }

        // Если данных в Redis нет, используем стандартную логику вычисления из базы данных

        // Если последней регенерации не было, используем текущее время как базу
        if (!$this->last_regeneration_at) {
            $this->last_regeneration_at = $now;
            // Сразу сохраняем в базу для корректности будущих вызовов
            $this->save();

            // Если current_hp или current_mp не инициализированы, устанавливаем их равными max_hp и max_mp
            if ($this->current_hp === null) {
                $this->current_hp = $this->max_hp;
                $this->save();
            }

            if ($this->current_mp === null) {
                $this->current_mp = $this->max_mp;
                $this->save();
            }

            // Инициализируем данные о здоровье и мане в Redis
            $healthData = [
                'current_hp' => $this->current_hp,
                'max_hp' => $this->max_hp,
                'last_update' => $now->timestamp,
                'recovery_rate' => 0 // Пока recovery = 0, так как не вычислено
            ];

            $manaData = [
                'current_mp' => $this->current_mp,
                'max_mp' => $this->max_mp,
                'last_update' => $now->timestamp,
                'recovery_rate' => 0 // Пока recovery = 0, так как не вычислено
            ];

            // Сохраняем данные в Redis
            \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
            \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа

            \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
            \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа

            // Возвращаем текущие значения без вычислений с указанием, что они полные
            return [
                'current_hp' => (int) $this->current_hp,
                'current_mp' => (int) $this->current_mp,
                'is_hp_full' => $this->current_hp >= $this->max_hp,
                'is_mp_full' => $this->current_mp >= $this->max_mp,
                'seconds_elapsed' => 0
            ];
        }

        // Переводим timestamp в объект Carbon для вычисления разницы
        $lastRegen = Carbon::parse($this->last_regeneration_at);

        // Вычисляем, сколько секунд прошло с момента последней регенерации
        $secondsElapsed = $now->diffInSeconds($lastRegen);

        // Если прошло меньше секунды, возвращаем текущие значения без изменений
        if ($secondsElapsed < 1) {
            return [
                'current_hp' => (int) $this->current_hp,
                'current_mp' => (int) $this->current_mp,
                'is_hp_full' => $this->current_hp >= $this->max_hp,
                'is_mp_full' => $this->current_mp >= $this->max_mp,
                'seconds_elapsed' => $secondsElapsed
            ];
        }

        // Получаем эффективные характеристики персонажа
        $effectiveStats = $this->getEffectiveStats();
        $recovery = $effectiveStats['recovery'] ?? $this->recovery ?? 0;

        // Если recovery = 0, регенерации не будет
        if ($recovery <= 0) {
            return [
                'current_hp' => (int) $this->current_hp,
                'current_mp' => (int) $this->current_mp,
                'is_hp_full' => $this->current_hp >= $this->max_hp,
                'is_mp_full' => $this->current_mp >= $this->max_mp,
                'seconds_elapsed' => $secondsElapsed,
            ];
        }

        // Считаем, сколько HP и MP должно было восстановиться за прошедшее время
        // Формула: HP/сек = recovery * 1.5 / 180, MP/сек = recovery * 1.5 / 60
        // Если нужно изменить скорость регенерации, меняйте множитель 1.5
        $hpPerSecond = $recovery * 1.5 / 180;
        $mpPerSecond = $recovery * 1.5 / 60;

        // Применяем множитель регенерации HP из сессии, если он есть
        $activeMultipliers = session('active_hp_regen_multipliers', []);
        if (!empty($activeMultipliers)) {
            // Если есть несколько множителей, используем максимальный
            $maxMultiplier = max($activeMultipliers);
            $hpPerSecond *= $maxMultiplier;

            Log::info("Применен множитель регенерации HP в getActualResources", [
                'user_id' => $this->user_id,
                'base_hp_regen' => $recovery * 1.5 / 180,
                'multiplier' => $maxMultiplier,
                'new_hp_regen' => $hpPerSecond
            ]);
        }

        // Общее восстановление за прошедшее время
        $hpToRegen = $hpPerSecond * $secondsElapsed;
        $mpToRegen = $mpPerSecond * $secondsElapsed;

        // ВАЖНО: Берем значение current_hp, а не hp (как может быть в старом коде)
        // Новые значения HP/MP не могут превышать максимальные значения
        // Если HP = 0, не применяем регенерацию
        $actualHp = $this->current_hp <= 0 ? 0 : min($this->max_hp, $this->current_hp + $hpToRegen); // HP с округлением вниз
        $actualMp = min($this->max_mp, $this->current_mp + $mpToRegen); // MP с округлением вниз

        // Проверяем, заполнены ли HP/MP полностью
        $isHpFull = $actualHp >= $this->max_hp;
        $isMpFull = $actualMp >= $this->max_mp;

        // Логируем для отладки
        \Log::debug("getActualResources из БД для #{$this->user_id}", [
            'seconds_elapsed' => $secondsElapsed,
            'recovery' => $recovery,
            'hp_per_second' => $hpPerSecond,
            'mp_per_second' => $mpPerSecond,
            'hp_to_regen' => $hpToRegen,
            'mp_to_regen' => $mpToRegen,
            'old_hp' => $this->current_hp,
            'old_mp' => $this->current_mp,
            'new_hp' => $actualHp,
            'new_mp' => $actualMp,
            'is_hp_full' => $isHpFull,
            'is_mp_full' => $isMpFull
        ]);

        // Инициализируем данные о здоровье и мане в Redis
        // Убедимся, что текущее HP не превышает максимальное
        $actualHp = min($actualHp, $this->max_hp);

        $healthData = [
            'current_hp' => $actualHp,
            'max_hp' => $this->max_hp,
            'last_update' => $now->timestamp,
            'recovery_rate' => $hpPerSecond
        ];

        // Убедимся, что текущее MP не превышает максимальное
        $actualMp = min($actualMp, $this->max_mp);

        $manaData = [
            'current_mp' => $actualMp,
            'max_mp' => $this->max_mp,
            'last_update' => $now->timestamp,
            'recovery_rate' => $mpPerSecond
        ];

        // Сохраняем данные в Redis
        \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
        \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа

        \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
        \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа

        // Возвращаем массив с актуальными значениями (без сохранения в базу)
        return [
            'current_hp' => (int) round($actualHp), // Округляем до целого для интерфейса
            'current_mp' => (int) round($actualMp),
            'is_hp_full' => $isHpFull,
            'is_mp_full' => $isMpFull,
            'seconds_elapsed' => $secondsElapsed
        ];
    }



    /**
     * Устанавливает новое значение HP в Redis
     *
     * @param int $hp Новое значение HP
     * @return bool Успешно ли установлено новое значение
     */
    public function setRedisHp(int $hp): bool
    {
        $healthRedisKey = "player:health:{$this->user_id}";

        // Проверяем, есть ли данные в Redis
        if (!\Illuminate\Support\Facades\Redis::exists($healthRedisKey)) {
            // Если данных нет, инициализируем их через getActualResources
            $this->getActualResources();
        }

        // Получаем текущие данные из Redis
        $healthData = json_decode(\Illuminate\Support\Facades\Redis::get($healthRedisKey), true);

        // Запоминаем старое значение HP для логирования
        $oldHP = $healthData['current_hp'];

        // Устанавливаем новое значение HP, ограничивая его максимальным
        $healthData['current_hp'] = min($healthData['max_hp'], max(0, $hp));
        $healthData['last_update'] = now()->timestamp;

        // Сохраняем обновленные данные в Redis
        \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
        \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа

        // Логируем изменение HP
        \Log::info("Установлено новое значение HP игрока через Redis (setRedisHp)", [
            'user_id' => $this->user_id,
            'old_hp' => $oldHP,
            'new_hp' => $healthData['current_hp'],
            'is_max_hp' => $healthData['current_hp'] >= $healthData['max_hp']
        ]);

        return true;
    }

    /**
     * Устанавливает новое значение MP в Redis
     *
     * @param int $mp Новое значение MP
     * @return bool Успешно ли установлено новое значение
     */
    public function setRedisMp(int $mp): bool
    {
        $manaRedisKey = "player:mana:{$this->user_id}";

        // Проверяем, есть ли данные в Redis
        if (!\Illuminate\Support\Facades\Redis::exists($manaRedisKey)) {
            // Если данных нет, инициализируем их через getActualResources
            $this->getActualResources();
        }

        // Получаем текущие данные из Redis
        $manaData = json_decode(\Illuminate\Support\Facades\Redis::get($manaRedisKey), true);

        // Запоминаем старое значение MP для логирования
        $oldMP = $manaData['current_mp'];

        // Устанавливаем новое значение MP, ограничивая его максимальным
        $manaData['current_mp'] = min($manaData['max_mp'], max(0, $mp));
        $manaData['last_update'] = now()->timestamp;

        // Сохраняем обновленные данные в Redis
        \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
        \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа

        // Логируем изменение MP
        \Log::info("Установлено новое значение MP игрока через Redis (setRedisMp)", [
            'user_id' => $this->user_id,
            'old_mp' => $oldMP,
            'new_mp' => $manaData['current_mp'],
            'is_max_mp' => $manaData['current_mp'] >= $manaData['max_mp']
        ]);

        return true;
    }

    /**
     * Синхронизирует атрибут recovery между базой данных и Redis для HP и MP
     * Вызывается при изменении атрибута recovery в базе данных
     *
     * @return bool Успешно ли обновлены данные
     */
    public function syncRecoveryWithRedis(): bool
    {
        $healthRedisKey = "player:health:{$this->user_id}";
        $manaRedisKey = "player:mana:{$this->user_id}";

        // Получаем эффективное значение recovery с учетом предметов и эффектов
        $effectiveStats = $this->getEffectiveStats();
        $recovery = $effectiveStats['recovery'] ?? $this->recovery ?? 0;

        // Вычисляем новые скорости восстановления HP и MP
        $newHpRecoveryRate = $recovery * 1.5 / 180;
        $newMpRecoveryRate = $recovery * 1.5 / 60;

        // Применяем множитель регенерации HP из сессии, если он есть
        $activeMultipliers = session('active_hp_regen_multipliers', []);
        if (!empty($activeMultipliers)) {
            // Если есть несколько множителей, используем максимальный
            $maxMultiplier = max($activeMultipliers);
            $newHpRecoveryRate *= $maxMultiplier;
        }

        // Обновляем данные о здоровье в Redis
        $healthDataExists = \Illuminate\Support\Facades\Redis::exists($healthRedisKey);
        if ($healthDataExists) {
            // Получаем текущие данные из Redis
            $healthData = json_decode(\Illuminate\Support\Facades\Redis::get($healthRedisKey), true);

            // Запоминаем старое значение для логирования
            $oldHpRecoveryRate = $healthData['recovery_rate'];

            // Обновляем скорость восстановления даже если она не изменилась
            $healthData['recovery_rate'] = $newHpRecoveryRate;

            // Сохраняем обновленные данные в Redis
            \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
            \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа

            // Логируем только если значение изменилось
            if ($oldHpRecoveryRate != $newHpRecoveryRate) {
                \Log::info("Обновлена скорость восстановления HP в Redis для #{$this->user_id}", [
                    'old_recovery_rate' => $oldHpRecoveryRate,
                    'new_recovery_rate' => $newHpRecoveryRate,
                    'base_recovery' => $recovery
                ]);
            }
        } else {
            // Если данных о здоровье нет в Redis, инициализируем их
            $now = Carbon::now();
            $healthData = [
                'current_hp' => $this->current_hp,
                'max_hp' => $this->max_hp,
                'last_update' => $now->timestamp,
                'recovery_rate' => $newHpRecoveryRate
            ];

            // Сохраняем данные в Redis
            \Illuminate\Support\Facades\Redis::set($healthRedisKey, json_encode($healthData));
            \Illuminate\Support\Facades\Redis::expire($healthRedisKey, 86400); // TTL 24 часа

            \Log::info("Инициализированы данные о здоровье в Redis для #{$this->user_id} через syncRecoveryWithRedis", [
                'recovery' => $recovery,
                'recovery_rate' => $newHpRecoveryRate
            ]);
        }

        // Обновляем данные о мане в Redis
        $manaDataExists = \Illuminate\Support\Facades\Redis::exists($manaRedisKey);
        if ($manaDataExists) {
            // Получаем текущие данные из Redis
            $manaData = json_decode(\Illuminate\Support\Facades\Redis::get($manaRedisKey), true);

            // Запоминаем старое значение для логирования
            $oldMpRecoveryRate = $manaData['recovery_rate'];

            // Обновляем скорость восстановления даже если она не изменилась
            $manaData['recovery_rate'] = $newMpRecoveryRate;

            // Сохраняем обновленные данные в Redis
            \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
            \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа

            // Логируем только если значение изменилось
            if ($oldMpRecoveryRate != $newMpRecoveryRate) {
                \Log::info("Обновлена скорость восстановления MP в Redis для #{$this->user_id}", [
                    'old_recovery_rate' => $oldMpRecoveryRate,
                    'new_recovery_rate' => $newMpRecoveryRate,
                    'base_recovery' => $recovery
                ]);
            }
        } else {
            // Если данных о мане нет в Redis, инициализируем их
            $now = Carbon::now();
            $manaData = [
                'current_mp' => $this->current_mp,
                'max_mp' => $this->max_mp,
                'last_update' => $now->timestamp,
                'recovery_rate' => $newMpRecoveryRate
            ];

            // Сохраняем данные в Redis
            \Illuminate\Support\Facades\Redis::set($manaRedisKey, json_encode($manaData));
            \Illuminate\Support\Facades\Redis::expire($manaRedisKey, 86400); // TTL 24 часа

            \Log::info("Инициализированы данные о мане в Redis для #{$this->user_id} через syncRecoveryWithRedis", [
                'recovery' => $recovery,
                'recovery_rate' => $newMpRecoveryRate
            ]);
        }

        return true;
    }
}
{{-- Компонент для отображения отдельной награды в интерфейсе распределения --}}
@props([
    'reward',
    'currentUser',
    'partyMembers'
])

@php
    $isCurrentUserReward = $reward->current_assignee_id === $currentUser->id && $reward->status !== 'declined';
    $canRequest = $reward->canBeRequestedBy($currentUser->id);
    $isRequested = $reward->isRequestedBy($currentUser->id);
    $hasRequests = !empty($reward->requested_by);
    $requestCount = count($reward->requested_by ?? []);

    // Определяем цвет рамки в зависимости от статуса
    $borderColor = match($reward->status) {
        'pending' => 'border-[#3b3629]',
        'accepted' => 'border-[#2f473c]',
        'declined' => 'border-[#6e3f35]',
        'requested' => 'border-[#c1a96e]',
        'distributed' => 'border-[#998d66]',
        default => 'border-[#3b3629]'
    };

    // Определяем фон в зависимости от статуса
    $bgColor = match($reward->status) {
        'declined' => 'bg-gradient-to-r from-[#6e3f35]/20 to-[#59372d]/20',
        'requested' => 'bg-gradient-to-r from-[#c1a96e]/10 to-[#998d66]/10',
        default => 'bg-[#1a1814]'
    };
@endphp

<div class="reward-item {{ $bgColor }} border-2 {{ $borderColor }} rounded-lg p-3 transition-all duration-200">
    <div class="flex items-center justify-between">
        {{-- Информация о награде --}}
        <div class="flex items-center space-x-3 flex-1">
            {{-- Иконка награды --}}
            <div class="flex-shrink-0">
                @if($reward->display_icon)
                    @if(str_starts_with($reward->display_icon, '/') || str_starts_with($reward->display_icon, 'http'))
                        <img src="{{ $reward->display_icon }}" alt="{{ $reward->display_name }}"
                             class="w-10 h-10 rounded border border-[#3b3629] object-contain"
                             style="image-rendering: crisp-edges;">
                    @else
                        <div class="w-10 h-10 flex items-center justify-center text-2xl bg-[#2a2722] rounded border border-[#3b3629]">
                            {{ $reward->display_icon }}
                        </div>
                    @endif
                @else
                    <div class="w-10 h-10 flex items-center justify-center text-2xl bg-[#2a2722] rounded border border-[#3b3629]">
                        ❓
                    </div>
                @endif
            </div>

            {{-- Название и количество --}}
            <div class="flex-1 min-w-0">
                <div class="text-[#e4d7b0] font-medium truncate">
                    {{ $reward->display_name }}
                </div>
                @if($reward->display_quantity)
                    <div class="text-[#c1a96e] text-sm">
                        {{ $reward->display_quantity }}
                    </div>
                @endif
            </div>

            {{-- Информация о получателе --}}
            <div class="text-center min-w-0 flex-shrink-0">
                {{-- Текущий владелец --}}
                <div class="mb-2">
                    <div class="text-[#c1a96e] text-xs font-medium mb-1">
                        @if($reward->status === 'declined')
                            🚫 Отказано участником
                        @elseif($isCurrentUserReward)
                            🎯 Ваша награда
                        @else
                            👤 Получает:
                        @endif
                    </div>
                    @if($reward->status !== 'declined' && $reward->currentAssignee)
                        <div class="text-[#d4cbb0] text-sm font-medium">
                            {{ $reward->currentAssignee->name }}
                        </div>
                        @if($reward->original_assignee_id !== $reward->current_assignee_id)
                            <div class="text-[#998d66] text-xs">
                                (изначально: {{ $reward->originalAssignee->name }})
                            </div>
                        @endif
                    @elseif($reward->status === 'declined')
                        <div class="text-[#998d66] text-sm">
                            {{ $reward->originalAssignee->name }}
                        </div>
                        <div class="text-[#6e3f35] text-xs">
                            (отказался)
                        </div>
                    @endif
                </div>

                {{-- Статус награды --}}
                <div class="mt-1">
                    @switch($reward->status)
                        @case('pending')
                            <span class="inline-block px-2 py-1 text-xs bg-[#3b3629] text-[#d4cbb0] rounded">
                                ⏳ Ожидает решения
                            </span>
                            @break
                        @case('accepted')
                            <span class="inline-block px-2 py-1 text-xs bg-[#2f473c] text-[#f8eac2] rounded">
                                ✅ Принята
                            </span>
                            @break
                        @case('declined')
                            <span class="inline-block px-2 py-1 text-xs bg-[#6e3f35] text-[#f8eac2] rounded">
                                🚫 Отказано
                            </span>
                            @break
                        @case('requested')
                            <span class="inline-block px-2 py-1 text-xs bg-[#c1a96e] text-[#1a1814] rounded">
                                🔥 Спорная ({{ $requestCount }})
                            </span>
                            @break
                        @case('distributed')
                            <span class="inline-block px-2 py-1 text-xs bg-[#998d66] text-[#f8eac2] rounded">
                                📦 Получена
                            </span>
                            @break
                    @endswitch
                </div>
            </div>
        </div>

        {{-- Кнопки действий --}}
        <div class="flex flex-col items-center space-y-2 ml-4 min-w-[120px]">
            @if($reward->status !== 'distributed')
                {{-- Кнопка отказа (только для текущего получателя) --}}
                @if($isCurrentUserReward && !$reward->isCurrency() && !$reward->isExperience())
                    <form method="POST" action="{{ route('dungeons.rewards.decline', $reward) }}" class="w-full">
                        @csrf
                        <button type="submit"
                                class="w-full px-3 py-2 text-sm bg-gradient-to-b from-[#6e3f35] to-[#59372d]
                                       border border-[#3b3629] text-[#f8eac2] rounded hover:from-[#7a4a3e]
                                       hover:to-[#634039] transition-all duration-200 hover:shadow-[0_0_10px_rgba(193,169,110,0.4)]"
                                onclick="return confirm('Вы уверены, что хотите отказаться от этой награды? Она будет перераспределена другим игрокам.')"
                                title="Отказаться от награды и передать другим">
                            <span class="hidden sm:inline">🚫 Отказаться</span>
                            <span class="sm:hidden">🚫</span>
                        </button>
                    </form>
                @endif

                {{-- Кнопка запроса (для других игроков) --}}
                @if($canRequest && $reward->status !== 'declined')
                    <form method="POST" action="{{ route('dungeons.rewards.request', $reward) }}" class="w-full">
                        @csrf
                        <button type="submit"
                                class="w-full px-3 py-2 text-sm border border-[#3b3629] text-[#f8eac2] rounded
                                       transition-all duration-200 hover:shadow-[0_0_10px_rgba(193,169,110,0.4)]
                                       @if($isRequested)
                                           bg-gradient-to-b from-[#c1a96e] to-[#998d66] text-[#1a1814]
                                       @else
                                           bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5847] hover:to-[#243c2f]
                                       @endif"
                                title="{{ $isRequested ? 'Отменить запрос на награду' : 'Запросить эту награду себе' }}">
                            @if($isRequested)
                                <span class="hidden sm:inline">✋ Отменить запрос</span>
                                <span class="sm:hidden">✋</span>
                            @else
                                <span class="hidden sm:inline">🙋 Хочу получить</span>
                                <span class="sm:hidden">🙋</span>
                            @endif
                        </button>
                    </form>
                @endif

                {{-- Информация о том, что нельзя запросить --}}
                @if(!$canRequest && !$isCurrentUserReward && $reward->status !== 'distributed')
                    <div class="w-full text-center">
                        <div class="text-[#998d66] text-xs px-2 py-1 bg-[#2a2722] rounded border border-[#3b3629]">
                            @if($reward->status === 'declined')
                                🚫 Отказано
                            @elseif($reward->isCurrency() || $reward->isExperience())
                                💰 Автоматически
                            @else
                                🔒 Недоступно
                            @endif
                        </div>
                    </div>
                @endif
            @else
                {{-- Награда уже распределена --}}
                <div class="w-full text-center">
                    <div class="text-[#998d66] text-xs px-2 py-1 bg-[#2a2722] rounded border border-[#3b3629]">
                        @if($reward->status === 'declined')
                            🚫 Отказано
                        @else
                            ✅ Получена
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    {{-- Дополнительная информация о запросах --}}
    @if($hasRequests && $reward->status === 'requested')
        <div class="mt-3 pt-3 border-t border-[#3b3629]">
            <div class="text-[#c1a96e] text-sm font-medium mb-2 flex items-center">
                <span class="mr-2">⚔️</span>
                Конкуренты за награду:
            </div>
            <div class="flex flex-wrap gap-2 mb-3">
                @foreach($reward->requested_by as $requesterId)
                    @php
                        $requester = $partyMembers->firstWhere('id', $requesterId);
                    @endphp
                    @if($requester)
                        <span class="inline-flex items-center px-2 py-1 text-xs bg-[#c1a96e] text-[#1a1814] rounded">
                            @if($requester->id === $currentUser->id)
                                <span class="mr-1">👤</span>
                            @endif
                            {{ $requester->name }}
                        </span>
                    @endif
                @endforeach
            </div>
            <div class="bg-[#2a2722] border border-[#3b3629] rounded p-2">
                <div class="text-[#e4d7b0] text-xs font-medium mb-1 flex items-center">
                    <span class="mr-1">🏆</span>
                    Правила распределения:
                </div>
                <div class="text-[#d4cbb0] text-xs">
                    • Награда достанется игроку с <strong>наибольшей боевой активностью (БА)</strong><br>
                    • БА рассчитывается по урону, лечению и поддержке в подземелье<br>
                    • При равной БА награда остается у изначального получателя
                </div>
            </div>
        </div>
    @endif

    {{-- Информация для владельца награды --}}
    @if($isCurrentUserReward && $reward->status === 'pending')
        <div class="mt-3 pt-3 border-t border-[#3b3629]">
            <div class="bg-[#2f473c]/20 border border-[#2f473c] rounded p-2">
                <div class="text-[#e4d7b0] text-xs font-medium mb-1 flex items-center">
                    <span class="mr-1">💡</span>
                    Ваши действия:
                </div>
                <div class="text-[#d4cbb0] text-xs">
                    • <strong>Оставить себе</strong> - просто завершите подземелье<br>
                    • <strong>Отказаться</strong> - награда будет перераспределена другим игрокам<br>
                    • Другие игроки могут запросить эту награду
                </div>
            </div>
        </div>
    @endif
</div>

{{-- Стили для мобильной адаптивности --}}
<style>
@media (max-width: 768px) {
    .reward-item {
        padding: 0.75rem;
    }
    
    .reward-item .flex {
        flex-direction: column;
        align-items: stretch;
    }
    
    .reward-item .flex-1 {
        margin-bottom: 0.75rem;
    }
    
    .reward-item .ml-4 {
        margin-left: 0;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .reward-item .space-x-2 > * + * {
        margin-left: 0.25rem;
    }
    
    .reward-item button {
        padding: 0.5rem;
        font-size: 0.75rem;
    }
}
</style>

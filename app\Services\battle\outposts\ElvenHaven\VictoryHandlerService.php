<?php

namespace App\Services\battle\outposts\ElvenHaven;

use App\Models\User;
use App\Models\Mob;
use App\Models\Resource;
use App\Services\BattleLogService;
use App\Services\LogFormattingService;
use App\Services\RewardService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Сервис для обработки награды за победу над мобом в Эльфийской Гавани
 */
class VictoryHandlerService
{
    protected BattleLogService $battleLogService;
    protected LogFormattingService $logFormatter;
    protected RewardService $rewardService;

    /**
     * Конструктор сервиса
     *
     * @param BattleLogService $battleLogService Сервис для работы с боевыми логами
     * @param LogFormattingService $logFormatter Сервис для форматирования логов
     * @param RewardService $rewardService Сервис для генерации и выдачи наград
     */
    public function __construct(
        BattleLogService $battleLogService,
        LogFormattingService $logFormatter,
        RewardService $rewardService
    ) {
        $this->battleLogService = $battleLogService;
        $this->logFormatter = $logFormatter;
        $this->rewardService = $rewardService;
    }

    /**
     * Обрабатывает награду за победу над мобом
     *
     * @param User $user Игрок, победивший моба
     * @param Mob $mob Побежденный моб
     */
    public function handleVictory(User $user, Mob $mob)
    {
        // Обновляем время последней атаки PvE
        session(['last_attack_pve_time' => microtime(true)]);

        // Получаем ключ для логов
        $battleLogKey = $this->getBattleLogKey($user);

        // Проверяем, не умер ли игрок (например, от дебаффа)
        $this->checkDeathRedirect($user);

        // Отображаем сообщение о победе
        $victoryMessage = $this->logFormatter->formatGenericMessage(
            "🎖️", // Иконка
            "Вы победили", // Основное сообщение
            $mob->name, // Выделенный текст
            "text-orange-400 font-medium", // Стиль выделенного текста
            "!" // Дополнительный текст после выделенного
        );
        $this->battleLogService->addLog($battleLogKey, $victoryMessage, 'success');

        // Сбрасываем текущую цель
        $user->current_target_id = null;
        $user->current_target_type = null;
        $user->save();

        // ИСПРАВЛЕНИЕ КРИТИЧЕСКОГО БАГА: Убираем дублирование начисления опыта
        // Опыт уже начислен через ObeliskService при нанесении урона (25% от урона)
        // Дополнительное начисление опыта за победу создавало двойное начисление

        // Логируем информацию о том, что опыт уже был начислен
        Log::info("Опыт за победу над мобом уже начислен через ObeliskService при нанесении урона", [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'note' => 'Дублирование опыта исправлено - опыт начисляется только за урон'
        ]);

        // ИСПРАВЛЕНИЕ: Убираем дублирование валют - базовая бронза теперь начисляется через RewardService
        // Генерация наград (валюты, ресурсы, предметы, алхимия)
        $rewards = [];
        $rewards['currency'] = $this->rewardService->calculateCurrencyReward($mob);
        $rewards['resources'] = $this->rewardService->generateMobLoot($mob);
        $rewards['items'] = $this->rewardService->generateItemLoot($mob, $user->id);
        $rewards['alchemy_ingredients'] = $this->rewardService->generateAlchemyLoot($mob);
        // ИСПРАВЛЕНИЕ: Убираем опыт из наград, так как он уже начислен через ObeliskService
        $rewards['experience'] = 0; // Опыт не начисляется здесь - только через урон

        // Если валюты не выпали через настройки дропа, добавляем базовую бронзу
        if (empty($rewards['currency']) || array_sum($rewards['currency']) == 0) {
            $bronzeReward = rand(5, 15) * $mob->level;
            $rewards['currency']['bronze'] = $bronzeReward;

            \Log::info('Добавлена базовая бронза (валюты не выпали через дроп)', [
                'mob_id' => $mob->id,
                'bronze_amount' => $bronzeReward
            ]);
        }

        // Выдача наград через RewardService
        $rewardResults = $this->rewardService->giveRewards($user, $rewards);

        // Логируем выданные предметы
        if (isset($rewardResults['given']['items']) && !empty($rewardResults['given']['items'])) {
            foreach ($rewardResults['given']['items'] as $item) {
                // Определяем стиль для отображения в зависимости от редкости
                $rarityStyles = [
                    'common' => 'text-gray-300',
                    'uncommon' => 'text-green-400',
                    'rare' => 'text-blue-400',
                    'epic' => 'text-purple-400',
                    'legendary' => 'text-orange-400'
                ];

                $quality = $item['quality'] ?? 'common';
                $style = $rarityStyles[$quality] ?? 'text-white';

                // Отображаем сообщение о добытом предмете
                $itemMessage = $this->logFormatter->formatGenericMessage(
                    "🎁", // Иконка
                    "Вы нашли предмет:", // Основное сообщение
                    "{$item['name']}", // Выделенный текст (название предмета)
                    "{$style} font-medium", // Стиль в зависимости от редкости
                    "" // Без дополнительного текста
                );
                $this->battleLogService->addLog($battleLogKey, $itemMessage, 'success');

                Log::info("Добавлен лог о выпадении предмета в боевой лог", [
                    'user_id' => $user->id,
                    'item_id' => $item['id'],
                    'item_name' => $item['name']
                ]);
            }
        }

        // Логируем выданные алхимические ингредиенты
        if (isset($rewardResults['given']['alchemy_ingredients']) && !empty($rewardResults['given']['alchemy_ingredients'])) {
            foreach ($rewardResults['given']['alchemy_ingredients'] as $ingredientId => $quantity) {
                $ingredient = \App\Models\AlchemyIngredient::find($ingredientId);
                if ($ingredient) {
                    $ingredientLog = $this->logFormatter->formatGenericMessage(
                        "🧪",
                        "Получен алхимический ингредиент:",
                        "{$quantity} {$ingredient->name}",
                        "text-purple-400 font-medium",
                        ""
                    );
                    $this->battleLogService->addLog($battleLogKey, $ingredientLog, 'reward');
                }
            }
        }

        // Логируем выданные ресурсы - ТОЛЬКО те, которые РЕАЛЬНО были добавлены в инвентарь
        if (isset($rewardResults['given']['resources']) && !empty($rewardResults['given']['resources'])) {
            foreach ($rewardResults['given']['resources'] as $resourceId => $quantity) {
                // Получаем информацию о ресурсе
                $resource = Resource::find($resourceId);

                if ($resource) {
                    // Определяем стиль для отображения в зависимости от редкости
                    $rarityStyles = [
                        'common' => 'text-gray-300',
                        'uncommon' => 'text-green-400',
                        'rare' => 'text-blue-400',
                        'epic' => 'text-purple-400',
                        'legendary' => 'text-orange-400'
                    ];

                    $style = $rarityStyles[$resource->rarity] ?? 'text-white';

                    // Формируем иконку ресурса
                    $resourceIcon = '';
                    if ($resource->icon && file_exists(public_path($resource->icon))) {
                        $resourceIcon = "<img src='" . asset($resource->icon) . "' alt='{$resource->name}' class='w-4 h-4 inline-block mr-1'>";
                    }

                    // Отображаем сообщение о полученном ресурсе - ТОЛЬКО если он был успешно добавлен
                    $resourceMessage = $this->logFormatter->formatGenericMessage(
                        "💎", // Иконка
                        "Вы получили {$resourceIcon}", // Основное сообщение с иконкой ресурса
                        "{$quantity} {$resource->name}", // Выделенный текст (количество и название ресурса)
                        "{$style} font-medium", // Стиль в зависимости от редкости
                        "" // Без дополнительного текста
                    );
                    $this->battleLogService->addLog($battleLogKey, $resourceMessage, 'success');

                    Log::info("Добавлен лог о УСПЕШНОМ получении ресурса в боевой лог", [
                        'user_id' => $user->id,
                        'resource_id' => $resourceId,
                        'resource_name' => $resource->name,
                        'quantity' => $quantity,
                        'note' => 'Ресурс был успешно добавлен в инвентарь'
                    ]);
                }
            }
        }
    }

    /**
     * Проверяет, не мёртв ли игрок. Если HP <= 0 — редиректит на аванпосты с сообщением о проигрыше.
     * Вызывать после любого боевого действия!
     */
    private function checkDeathRedirect($user, $actualResources = null)
    {
        if (!$actualResources) {
            $actualResources = $user->profile->getActualResources();
        }
        if ($actualResources['current_hp'] <= 0) {
            // Проверяем, есть ли флаг недавней победы
            $hasRecentVictory = session('recent_victory_time') && 
                               (time() - session('recent_victory_time') < 10);
            
            // Если игрок недавно победил, не перенаправляем
            if ($hasRecentVictory) {
                Log::info("ElvenHaven VictoryHandlerService пропускает проверку HP для победителя", [
                    'user_id' => $user->id,
                    'hasRecentVictory' => $hasRecentVictory,
                    'recent_victory_time' => session('recent_victory_time'),
                    'current_time' => time()
                ]);
                return;
            }
            
            Log::warning("ElvenHaven VictoryHandlerService перенаправляет на battle.outposts.index", [
                'user_id' => $user->id,
                'current_hp' => $actualResources['current_hp'],
                'hasRecentVictory' => $hasRecentVictory,
                'recent_victory_time' => session('recent_victory_time'),
                'current_time' => time()
            ]);
            
            // Немедленно редиректим на аванпосты с сообщением о проигрыше
            abort(redirect()->route('battle.outposts.index')->with('error', 'Вы проиграли, восстановитесь'));
        }
    }

    /**
     * Получает ключ для логов с учетом текущей локации
     *
     * @param mixed $user Пользователь
     * @return string Ключ для логов
     */
    private function getBattleLogKey($user)
    {
        // Получаем текущую локацию пользователя
        $currentLocation = $user->statistics->current_location ?? 'Неизвестно';

        // Проверяем, есть ли логи из Тарнмора, сохраненные с специфическим ключом
        $tarnmoreKey = "location:tarnmore_quarry:{$user->id}";
        if (Cache::has("{$tarnmoreKey}:logs")) {
            Log::info("Найдены логи Тарнмора, будем использовать ключ {$tarnmoreKey}");
            return $tarnmoreKey;
        }

        // Используем общий ключ для всех боевых локаций, если специфический не найден
        return $this->battleLogService->getBattleLogKey($user->id);
    }
}

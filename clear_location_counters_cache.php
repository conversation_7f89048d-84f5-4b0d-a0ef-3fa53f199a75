<?php

require_once 'vendor/autoload.php';

use App\Models\Location;
use App\Models\MineLocation;
use App\Services\battle\UserLocationService;
use Illuminate\Support\Facades\Cache;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Очистка кеша счетчиков локаций ===\n\n";

$userLocationService = app(UserLocationService::class);

// Получаем все локации для очистки кеша
$mainLocations = Location::pluck('name');
$mineLocations = MineLocation::pluck('name');

echo "Очищаем кеш для основных локаций (" . $mainLocations->count() . ")...\n";
foreach ($mainLocations as $locationName) {
    $userLocationService->clearLocationCache($locationName);
    echo "  ✓ {$locationName}\n";
}

echo "\nОчищаем кеш для локаций рудников (" . $mineLocations->count() . ")...\n";
foreach ($mineLocations as $locationName) {
    $userLocationService->clearLocationCache($locationName);
    echo "  ✓ {$locationName}\n";
}

// Очищаем также специфические ключи
echo "\nОчищаем дополнительные кеши...\n";

// Очищаем кеш для специальной локации из бага
$userLocationService->clearLocationCache('аааааааааааа');
echo "  ✓ аааааааааааа\n";

// Очищаем общие кеши онлайн статусов
$cacheKeys = [
    'online_players_count',
    'faction_counts_global',
    'location_players_cache',
    'online_status_cache'
];

foreach ($cacheKeys as $key) {
    Cache::forget($key);
    echo "  ✓ {$key}\n";
}

// Очищаем Redis кеши если используется Redis
if (config('cache.default') === 'redis') {
    echo "\nОчищаем Redis кеши...\n";
    
    try {
        $redis = app('redis');
        
        // Очищаем ключи связанные с локациями
        $locationKeys = $redis->keys('location:*');
        if (!empty($locationKeys)) {
            $redis->del($locationKeys);
            echo "  ✓ Удалено " . count($locationKeys) . " ключей локаций\n";
        }
        
        // Очищаем ключи онлайн статусов
        $onlineKeys = $redis->keys('online:*');
        if (!empty($onlineKeys)) {
            $redis->del($onlineKeys);
            echo "  ✓ Удалено " . count($onlineKeys) . " ключей онлайн статуса\n";
        }
        
        // Очищаем ключи фракций
        $factionKeys = $redis->keys('faction:*');
        if (!empty($factionKeys)) {
            $redis->del($factionKeys);
            echo "  ✓ Удалено " . count($factionKeys) . " ключей фракций\n";
        }
        
    } catch (Exception $e) {
        echo "  ⚠ Ошибка при очистке Redis: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Кеш очищен ===\n";
echo "Теперь счетчики будут пересчитаны с использованием исправленной логики!\n";
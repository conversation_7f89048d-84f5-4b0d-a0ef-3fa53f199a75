<?php

namespace App\Jobs\Mine;

use App\Services\Mine\EnhancedMineBotService;
use App\Services\MineDetectionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Задача для обработки атак на обнаруженных игроков в рудниках
 * 
 * Эта задача запускается периодически и обеспечивает:
 * - Атаки мобов на игроков с дебафом 'Обнаружен'
 * - Очистку истекших дебафов обнаружения
 * - Умное распределение атак между целями
 */
class ProcessDetectedPlayersAttackJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected ?string $specificMine;
    protected bool $forceAttack;

    /**
     * Максимальное время выполнения задачи (секунды)
     */
    public int $timeout = 60;

    /**
     * Количество попыток выполнения при ошибке
     */
    public int $tries = 2;

    /**
     * Создание новой задачи
     */
    public function __construct(?string $specificMine = null, bool $forceAttack = false)
    {
        $this->specificMine = $specificMine;
        $this->forceAttack = $forceAttack;

        // Устанавливаем приоритетную очередь для атак на обнаруженных игроков
        $this->onQueue('mine_detection_attacks');
    }

    /**
     * Выполнение задачи
     */
    public function handle(EnhancedMineBotService $mineBotService, MineDetectionService $detectionService): void
    {
        $startTime = microtime(true);
        
        try {
            Log::info("ProcessDetectedPlayersAttackJob: Начало обработки атак на обнаруженных игроков", [
                'specific_mine' => $this->specificMine,
                'force_attack' => $this->forceAttack,
                'job_id' => $this->job->getJobId() ?? 'manual',
            ]);

            // Сначала очищаем истекшие дебафы
            $cleanedEffects = $detectionService->cleanupExpiredDetectionEffects();
            if ($cleanedEffects > 0) {
                Log::info("ProcessDetectedPlayersAttackJob: Очищено истекших дебафов: {$cleanedEffects}");
            }

            // Получаем статистику обнаружения
            $detectionStats = $detectionService->getDetectionStatistics();
            
            if (empty($detectionStats)) {
                Log::info("ProcessDetectedPlayersAttackJob: Нет обнаруженных игроков для атаки");
                return;
            }

            Log::info("ProcessDetectedPlayersAttackJob: Найдены обнаруженные игроки в локациях", [
                'locations_with_detected' => count($detectionStats),
                'total_detected_players' => array_sum(array_column($detectionStats, 'detected_players'))
            ]);

            $totalStats = [
                'locations_processed' => 0,
                'total_attacks_attempted' => 0,
                'total_attacks_successful' => 0,
                'total_detected_players' => 0,
                'errors' => 0,
            ];

            // Обрабатываем каждую локацию с обнаруженными игроками
            foreach ($detectionStats as $locationStat) {
                try {
                    // Пропускаем локации без обнаруженных игроков
                    if ($locationStat['detected_players'] <= 0) {
                        continue;
                    }

                    // Если указан конкретный рудник, обрабатываем только его
                    if ($this->specificMine && !str_contains($locationStat['location_name'], $this->specificMine)) {
                        continue;
                    }

                    $totalStats['locations_processed']++;
                    $totalStats['total_detected_players'] += $locationStat['detected_players'];

                    // Определяем slug рудника из названия локации
                    $mineSlug = $this->determineMineSlug($locationStat);
                    
                    if (!$mineSlug) {
                        Log::warning("ProcessDetectedPlayersAttackJob: Не удалось определить slug для локации", [
                            'location_stat' => $locationStat
                        ]);
                        continue;
                    }

                    Log::info("ProcessDetectedPlayersAttackJob: Обработка рудника с обнаруженными игроками", [
                        'mine_slug' => $mineSlug,
                        'detected_players' => $locationStat['detected_players'],
                        'avg_remaining_time' => $locationStat['avg_remaining_time']
                    ]);

                    // Выполняем атаки
                    if ($this->forceAttack) {
                        $attackStats = $mineBotService->forceAttackDetectedPlayers($mineSlug);
                    } else {
                        $attackStats = $mineBotService->processMineBots($mineSlug);
                    }

                    $totalStats['total_attacks_attempted'] += $attackStats['attacks_executed'] ?? 0;
                    $totalStats['total_attacks_successful'] += $attackStats['attacks_executed'] ?? 0;

                    Log::info("ProcessDetectedPlayersAttackJob: Завершена обработка рудника", [
                        'mine_slug' => $mineSlug,
                        'attack_stats' => $attackStats
                    ]);

                } catch (\Exception $e) {
                    Log::error("ProcessDetectedPlayersAttackJob: Ошибка обработки локации", [
                        'location_stat' => $locationStat,
                        'error' => $e->getMessage()
                    ]);
                    $totalStats['errors']++;
                }
            }

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::info("ProcessDetectedPlayersAttackJob: Завершена обработка атак на обнаруженных игроков", array_merge($totalStats, [
                'execution_time_ms' => $executionTime
            ]));

        } catch (\Exception $e) {
            Log::error("ProcessDetectedPlayersAttackJob: Критическая ошибка: " . $e->getMessage(), [
                'specific_mine' => $this->specificMine,
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Определяет slug рудника из статистики локации
     */
    protected function determineMineSlug(array $locationStat): ?string
    {
        // Используем mine_location_id для поиска slug
        if (isset($locationStat['mine_location_id'])) {
            $mineLocation = \App\Models\MineLocation::find($locationStat['mine_location_id']);
            if ($mineLocation && $mineLocation->slug) {
                return $mineLocation->slug;
            }
        }

        // Fallback: пытаемся найти по названию
        if (isset($locationStat['location_name'])) {
            $mineLocation = \App\Models\MineLocation::where('name', 'like', "%{$locationStat['location_name']}%")
                ->first();
            if ($mineLocation && $mineLocation->slug) {
                return $mineLocation->slug;
            }
        }

        return null;
    }

    /**
     * Обработка неудачного выполнения задачи
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ProcessDetectedPlayersAttackJob: Задача провалена после {$this->tries} попыток", [
            'specific_mine' => $this->specificMine,
            'force_attack' => $this->forceAttack,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }

    /**
     * Получает уникальный ID задачи для предотвращения дублирования
     */
    public function uniqueId(): string
    {
        if ($this->specificMine) {
            return 'process_detected_players_attack_' . md5($this->specificMine);
        }
        
        return 'process_detected_players_attack_all';
    }

    /**
     * Определяет, сколько секунд задача должна быть уникальной
     */
    public function uniqueFor(): int
    {
        return 20; // 20 секунд уникальности для предотвращения спама
    }

    /**
     * Создает задачу для атаки на всех обнаруженных игроков
     */
    public static function dispatchForAllMines(bool $forceAttack = false): void
    {
        static::dispatch(null, $forceAttack);
    }

    /**
     * Создает задачу для атаки в конкретном руднике
     */
    public static function dispatchForMine(string $mineName, bool $forceAttack = false): void
    {
        static::dispatch($mineName, $forceAttack);
    }

    /**
     * Создает задачу с задержкой для периодических атак
     */
    public static function dispatchDelayed(int $delaySeconds = 30, ?string $mineName = null): void
    {
        static::dispatch($mineName, false)->delay(now()->addSeconds($delaySeconds));
    }
}
@php use Illuminate\Support\Facades\Auth; @endphp {{-- Используем фасад Auth для проверки роли --}}
<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- Устанавливаем заголовок страницы --}}
    <title>Создать нового бота - Админка</title>

    {{-- Подключаем Vite для CSS и JS --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

{{-- Устанавливаем темный фон и основной шрифт для тела страницы (стиль из sample.blade.php) --}}
<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">

    {{-- Основной контейнер страницы админки, стилизованный под sample.blade.php --}}
    <div class="container max-w-2xl mx-auto px-4 py-8 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden my-5">


        {{-- Основной контент --}}
        <div class="flex-1 flex flex-col overflow-hidden">

            {{-- Главная область контента --}}
            <main class="flex-1 overflow-x-hidden overflow-y-auto"> {{-- Убрали bg-gray-200 --}}
                <div class="container mx-auto px-6 py-8">
                    {{-- Заголовок страницы, стилизованный под sample.blade.php --}}
                    <h3 class="block text-center text-white py-1.5 px-8 rounded-md shadow-lg bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] text-xl font-semibold mb-4">
                        Создать нового бота
                    </h3>

                    {{-- Хлебные крошки, стилизованные под темную тему --}}
                    <div class="mt-4 mb-6 text-sm text-[#d3c6a6]">
                        {{-- Ссылка на главную страницу админки --}}
                        <a href="{{ route('admin.dashboard') }}" class="text-[#e5b769] hover:text-[#f0d89e]">Админка</a>
                        <span class="mx-2 text-[#a6925e]">/</span> {{-- Разделитель --}}
                        {{-- Ссылка на управление ботами --}}
                        <a href="{{ route('admin.bots.index') }}" class="text-[#e5b769] hover:text-[#f0d89e]">Управление ботами</a>
                        <span class="mx-2 text-[#a6925e]">/</span> {{-- Разделитель --}}
                        <span class="text-[#f5f5f5]">Создать нового бота</span> {{-- Текущая страница --}}
                    </div>

                    {{-- Форма создания, стилизованная под темную тему --}}
                    {{-- Контейнер формы: темный фон, тень, скругление, отступы --}}
                    <div class="bg-[#211f1a] border border-[#514b3c] shadow-inner rounded px-8 pt-6 pb-8 mb-4">

                        {{-- Вывод ошибок валидации (стилизовано под темную тему) --}}
                        @if ($errors->any())
                            {{-- Контейнер для ошибок: красный фон, рамка, текст --}}
                            <div class="bg-[#5a2626] border border-[#e74c3c] text-[#f5c6cb] px-4 py-3 rounded relative mb-6"
                                role="alert">
                                {{-- Заголовок блока ошибок --}}
                                <strong class="font-bold text-white">Ошибка валидации!</strong>
                                {{-- Список ошибок --}}
                                <ul class="mt-3 list-disc list-inside text-sm text-[#f5c6cb]">
                                    {{-- Перебираем все ошибки --}}
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>{{-- Выводим текст ошибки --}}
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        {{-- Форма отправляет данные на метод store контроллера BotController --}}
                        <form action="{{ route('admin.bots.store') }}" method="POST">
                            @csrf {{-- Защита от CSRF-атак --}}

                            {{-- Блок полей формы --}}

                            {{-- Поле: Имя бота --}}
                            <div class="mb-6">
                                {{-- Метка для поля ввода имени бота --}}
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="name">
                                    Имя {{-- Текст метки --}}
                                </label>
                                {{-- Поле ввода для имени бота --}}
                                <input
                                    class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили поля ввода для темной темы --}}
                                    id="name" {{-- ID поля, связывает с меткой --}}
                                    name="name" {{-- Имя поля для отправки данных формы --}}
                                    type="text" {{-- Тип поля: текстовое --}}
                                    placeholder="Например, Страж Леса-5" {{-- Пример текста в поле --}}
                                    value="{{ old('name') }}" {{-- Восстанавливает старое значение при ошибке валидации, иначе пустое --}}
                                    required {{-- Поле обязательно для заполнения --}}
                                >
                                {{-- Подсказка под полем ввода имени --}}
                                <p class="text-[#a6925e] text-xs italic mt-1">Уникальное имя для нового бота.</p>
                            </div>

                            {{-- Поля: Раса и Класс (в одной строке) --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для полей расы и класса с отрицательными отступами --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Контейнер для поля расы (половина ширины на средних экранах) --}}
                                    {{-- Метка для выпадающего списка расы --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="race">
                                        Раса {{-- Текст метки --}}
                                    </label>
                                    <div class="relative"> {{-- Относительный контейнер для позиционирования иконки стрелки --}}
                                        {{-- Выпадающий список для выбора расы --}}
                                        <select
                                            class="block appearance-none w-full bg-[#38352c] border border-[#514b3c] text-[#f5f5f5] py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-[#4a452c] focus:border-[#a6925e]" {{-- Стили select для темной темы --}}
                                            id="race" {{-- ID поля --}}
                                            name="race" {{-- Имя поля --}}
                                            required {{-- Обязательно для выбора --}}
                                        >
                                            <option value="" class="text-[#7a7568]">-- Выберите расу --</option> {{-- Опция-заглушка по умолчанию --}}
                                            {{-- Опция Solarius --}}
                                            <option value="solarius" {{ old('race') == 'solarius' ? 'selected' : '' }} class="bg-[#38352c] text-[#f5f5f5]">
                                                Solarius
                                            </option>
                                            {{-- Опция Lunarius --}}
                                            <option value="lunarius" {{ old('race') == 'lunarius' ? 'selected' : '' }} class="bg-[#38352c] text-[#f5f5f5]">
                                                Lunarius
                                            </option>
                                            {{-- Другие расы можно добавить здесь --}}
                                        </select>
                                        {{-- Иконка стрелки для select --}}
                                        <div
                                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-[#a6925e]">
                                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20">
                                                <path
                                                    d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full md:w-1/2 px-3"> {{-- Контейнер для поля класса (половина ширины) --}}
                                    {{-- Метка для выпадающего списка класса --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="class">
                                        Класс {{-- Текст метки --}}
                                    </label>
                                    <div class="relative"> {{-- Относительный контейнер для иконки --}}
                                        {{-- Выпадающий список для выбора класса --}}
                                        <select
                                            class="block appearance-none w-full bg-[#38352c] border border-[#514b3c] text-[#f5f5f5] py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-[#4a452c] focus:border-[#a6925e]" {{-- Стили select --}}
                                            id="class" {{-- ID поля --}}
                                            name="class" {{-- Имя поля --}}
                                            required {{-- Обязательно для выбора --}}
                                        >
                                            <option value="" class="text-[#7a7568]">-- Выберите класс --</option> {{-- Опция по умолчанию --}}
                                            {{-- Опция Warrior --}}
                                            <option value="warrior" {{ old('class') == 'warrior' ? 'selected' : '' }} class="bg-[#38352c] text-[#f5f5f5]">
                                                Воин
                                            </option>
                                            {{-- Опция Mage --}}
                                            <option value="mage" {{ old('class') == 'mage' ? 'selected' : '' }} class="bg-[#38352c] text-[#f5f5f5]">
                                                Маг
                                            </option>
                                            {{-- Опция Priest (Жрец) --}}
                                            <option value="priest" {{ old('class') == 'priest' ? 'selected' : '' }} class="bg-[#38352c] text-[#f5f5f5]">
                                                Жрец
                                            </option>
                                            {{-- Место для добавления других классов --}}
                                        </select>
                                        {{-- Иконка стрелки для select --}}
                                        <div
                                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-[#a6925e]">
                                            <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20">
                                                <path
                                                    d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- Поле: Уровень бота --}}
                            <div class="mb-6"> {{-- Контейнер для поля уровня --}}
                                {{-- Метка для поля уровня --}}
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="level">
                                    Уровень {{-- Текст метки --}}
                                </label>
                                {{-- Поле ввода для уровня бота --}}
                                <input
                                    class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили поля ввода --}}
                                    id="level" {{-- ID поля --}}
                                    name="level" {{-- Имя поля --}}
                                    type="number" {{-- Тип поля: числовое --}}
                                    placeholder="1" {{-- Пример значения --}}
                                    value="{{ old('level', 1) }}" {{-- Старое значение или 1 по умолчанию --}}
                                    required {{-- Обязательно для заполнения --}}
                                    min="1" {{-- Минимальное значение 1 --}}
                                >
                            </div>

                            {{-- Поля: HP и Макс. HP (в одной строке) --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для полей HP --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Контейнер для текущего HP --}}
                                    {{-- Метка для текущего HP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="hp">
                                        Текущее HP {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для текущего HP --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="hp" {{-- ID --}}
                                        name="hp" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="100" {{-- Пример --}}
                                        value="{{ old('hp') }}" {{-- Старое значение --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                                <div class="w-full md:w-1/2 px-3"> {{-- Контейнер для максимального HP --}}
                                    {{-- Метка для максимального HP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="max_hp">
                                        Максимальное HP {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для максимального HP --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="max_hp" {{-- ID --}}
                                        name="max_hp" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="100" {{-- Пример --}}
                                        value="{{ old('max_hp') }}" {{-- Старое значение --}}
                                        required {{-- Обязательно --}}
                                        min="1" {{-- Мин. значение 1 --}}
                                    >
                                </div>
                            </div>

                            {{-- Поля: MP и Макс. MP (в одной строке) --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для полей MP --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Контейнер для текущего MP --}}
                                    {{-- Метка для текущего MP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="mp">
                                        Текущее MP {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для текущего MP --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="mp" {{-- ID --}}
                                        name="mp" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="50" {{-- Пример --}}
                                        value="{{ old('mp') }}" {{-- Старое значение --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                                <div class="w-full md:w-1/2 px-3"> {{-- Контейнер для максимального MP --}}
                                    {{-- Метка для максимального MP --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="max_mp">
                                        Максимальное MP {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для максимального MP --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="max_mp" {{-- ID --}}
                                        name="max_mp" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="50" {{-- Пример --}}
                                        value="{{ old('max_mp') }}" {{-- Старое значение --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                            </div>

                            {{-- Поля: Характеристики (Сила, Интеллект, Ловкость) --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для характеристик --}}
                                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0"> {{-- Контейнер для силы --}}
                                    {{-- Метка для силы --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="strength">
                                        Сила {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для силы --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="strength" {{-- ID --}}
                                        name="strength" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="10" {{-- Пример --}}
                                        value="{{ old('strength', 10) }}" {{-- Старое значение или 10 по умолчанию --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0"> {{-- Контейнер для интеллекта --}}
                                    {{-- Метка для интеллекта --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="intelligence">
                                        Интеллект {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для интеллекта --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="intelligence" {{-- ID --}}
                                        name="intelligence" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="10" {{-- Пример --}}
                                        value="{{ old('intelligence', 10) }}" {{-- Старое значение или 10 по умолчанию --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                                <div class="w-full md:w-1/3 px-3 mb-6 md:mb-0"> {{-- Контейнер для ловкости --}}
                                    {{-- Метка для ловкости --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="dexterity">
                                        Ловкость {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для ловкости --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="dexterity" {{-- ID --}}
                                        name="dexterity" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="10" {{-- Пример --}}
                                        value="{{ old('dexterity', 10) }}" {{-- Старое значение или 10 по умолчанию --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                            </div>

                            {{-- Поля: Защитные характеристики (Броня, Сопр. магии) --}}
                            <div class="flex flex-wrap -mx-3 mb-6"> {{-- Контейнер для защиты --}}
                                <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0"> {{-- Контейнер для брони --}}
                                    {{-- Метка для брони --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="armor">
                                        Броня {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для брони --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="armor" {{-- ID --}}
                                        name="armor" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="5" {{-- Пример --}}
                                        value="{{ old('armor', 5) }}" {{-- Старое значение или 5 по умолчанию --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                                <div class="w-full md:w-1/2 px-3"> {{-- Контейнер для сопротивления магии --}}
                                    {{-- Метка для сопротивления магии --}}
                                    <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="magic_resistance">
                                        Сопр. магии {{-- Текст метки --}}
                                    </label>
                                    {{-- Поле ввода для сопротивления магии --}}
                                    <input
                                        class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]" {{-- Стили --}}
                                        id="magic_resistance" {{-- ID --}}
                                        name="magic_resistance" {{-- Имя поля --}}
                                        type="number" {{-- Тип: число --}}
                                        placeholder="5" {{-- Пример --}}
                                        value="{{ old('magic_resistance', 5) }}" {{-- Старое значение или 5 по умолчанию --}}
                                        required {{-- Обязательно --}}
                                        min="0" {{-- Мин. значение 0 --}}
                                    >
                                </div>
                            </div>

                            {{-- Поле: Базовый урон (автоматически рассчитывается) --}}
                            <div class="mb-6"> {{-- Контейнер для базового урона --}}
                                {{-- Метка для базового урона --}}
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="base_damage_display">
                                    Базовый урон {{-- Текст метки --}}
                                </label>
                                {{-- Информационное поле (только для чтения) --}}
                                <div class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#3a3a3a] text-[#a6925e] leading-tight">
                                    <span id="base_damage_display">Будет рассчитан автоматически на основе силы</span>
                                </div>
                                {{-- Подсказка для поля базового урона --}}
                                <p class="text-[#a6925e] text-xs italic mt-1">
                                    Базовый урон рассчитывается автоматически по формуле: урон = сила бота.
                                    Это значение будет сохранено в базе данных и использоваться во время боя.
                                </p>
                            </div>

                            {{-- Поле: Локация (выпадающий список) --}}
                            <div class="mb-6"> {{-- Контейнер для локации --}}
                                {{-- Метка для локации --}}
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="location">
                                    Локация {{-- Текст метки --}}
                                </label>
                                <div class="relative"> {{-- Относительный контейнер для позиционирования иконки стрелки --}}
                                    {{-- Выпадающий список для выбора локации --}}
                                    <select
                                        class="block appearance-none w-full bg-[#38352c] border border-[#514b3c] text-[#f5f5f5] py-3 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-[#4a452c] focus:border-[#a6925e]" {{-- Стили select для темной темы --}}
                                        id="location" {{-- ID поля --}}
                                        name="location" {{-- Имя поля --}}
                                        required {{-- Обязательно для выбора --}}
                                    >
                                        <option value="" class="text-[#7a7568]">-- Выберите локацию --</option> {{-- Опция-заглушка по умолчанию --}}

                                        {{-- Группа: Рудники (для автоматизации ботов) --}}
                                        @if(isset($mineLocations) && $mineLocations->count() > 0)
                                            <optgroup label="🏭 Рудники (для автоматизации ботов)" class="bg-[#4a452c] text-[#e5b769] font-bold">
                                                @foreach($mineLocations as $mineLocation)
                                                    @if($mineLocation->baseLocation)
                                                        <option value="{{ $mineLocation->name }}"
                                                                data-mine-location-id="{{ $mineLocation->id }}"
                                                                {{ old('location') == $mineLocation->name ? 'selected' : '' }}
                                                                class="bg-[#38352c] text-[#f5f5f5]">
                                                            {{ $mineLocation->name }} ({{ $mineLocation->baseLocation->name }})
                                                        </option>
                                                    @endif
                                                @endforeach
                                            </optgroup>
                                        @endif

                                        {{-- Группа: Обычные локации --}}
                                        <optgroup label="🏛️ Обычные локации" class="bg-[#4a452c] text-[#e5b769] font-bold">
                                            @foreach($locations as $location)
                                                <option value="{{ $location->name }}" {{ old('location') == $location->name ? 'selected' : '' }} class="bg-[#38352c] text-[#f5f5f5]">
                                                    {{ $location->name }}
                                                </option>
                                            @endforeach
                                        </optgroup>
                                    </select>
                                    {{-- Иконка стрелки для select --}}
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-[#a6925e]">
                                        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                        </svg>
                                    </div>
                                </div>
                                {{-- Подсказка для поля локации --}}
                                <p class="text-[#a6925e] text-xs italic mt-1">
                                    <strong>Рудники:</strong> Для ботов, которые будут обрабатываться командой <code>bots:process-mines</code><br>
                                    <strong>Обычные локации:</strong> Для других типов ботов
                                </p>
                            </div>

                            {{-- Поле: Активность (радиокнопки) --}}
                            <div class="mb-6"> {{-- Контейнер для выбора активности --}}
                                {{-- Метка для группы радиокнопок --}}
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2">
                                    Активен {{-- Текст метки --}}
                                </label>
                                <div class="mt-2"> {{-- Контейнер для радиокнопок --}}
                                    {{-- Радиокнопка 'Да' --}}
                                    <label class="inline-flex items-center text-[#d3c6a6] hover:text-[#f5f5f5] cursor-pointer">
                                        {{-- Стилизованная радиокнопка --}}
                                        <input type="radio" class="form-radio h-4 w-4 text-[#e5b769] bg-[#38352c] border-[#514b3c] focus:ring-[#e5b769]" name="is_active" value="1" {{ old('is_active', 1) == 1 ? 'checked' : '' }}> {{-- Значение 1, проверка старого значения или 1 по умолчанию --}}
                                        <span class="ml-2 text-sm">Да (Бот будет появляться и действовать в игре)</span> {{-- Текст рядом с кнопкой --}}
                                    </label>
                                    {{-- Радиокнопка 'Нет' --}}
                                    <label class="inline-flex items-center ml-6 text-[#d3c6a6] hover:text-[#f5f5f5] cursor-pointer">
                                        {{-- Стилизованная радиокнопка --}}
                                        <input type="radio" class="form-radio h-4 w-4 text-[#e5b769] bg-[#38352c] border-[#514b3c] focus:ring-[#e5b769]" name="is_active" value="0" {{ old('is_active') === '0' ? 'checked' : '' }}> {{-- Значение 0, проверка старого значения (строка '0') --}}
                                        <span class="ml-2 text-sm">Нет (Бот временно неактивен)</span> {{-- Текст рядом с кнопкой --}}
                                    </label>
                                </div>
                            </div>

                            {{-- Поле: Время Возрождения (в секундах) --}}
                            <div class="mb-8">
                                <label class="block text-[#d3c6a6] text-sm font-bold mb-2" for="respawn_time_seconds">
                                    Интервал воскрешения (сек)
                                </label>
                                <input
                                    class="shadow appearance-none border border-[#514b3c] rounded w-full py-2 px-3 bg-[#2f2d2b] text-[#f5f5f5] leading-tight focus:outline-none focus:shadow-outline focus:border-[#a6925e] placeholder-[#7a7568]"
                                    id="respawn_time_seconds"
                                    name="respawn_time_seconds"
                                    type="number"
                                    placeholder="300"
                                    value="{{ old('respawn_time_seconds', 300) }}"
                                    min="0"
                                    required
                                >
                                <p class="text-[#a6925e] text-xs italic mt-1">Время в секундах до автоматического воскрешения бота после смерти. Каждый бот может иметь свой индивидуальный интервал.</p>
                            </div>

                            {{-- Скрытое поле для mine_location_id --}}
                            <input type="hidden" id="mine_location_id" name="mine_location_id" value="{{ old('mine_location_id') }}">

                            {{-- Блок кнопок управления формой --}}
                            <div class="flex items-center justify-between mt-8"> {{-- Контейнер для кнопок с выравниванием --}}
                                {{-- Кнопка 'Создать бота' для отправки формы --}}
                                <button
                                    class="bg-[#c4a76d] hover:bg-[#d4b781] text-[#2f2d2b] font-bold py-2 px-4 rounded shadow-lg focus:outline-none focus:shadow-outline transition duration-300" {{-- Стили кнопки отправки --}}
                                    type="submit"> {{-- Тип кнопки: submit --}}
                                    Создать бота {{-- Текст кнопки --}}
                                </button>
                                {{-- Ссылка 'Отмена' для возврата к списку ботов --}}
                                <a href="{{ route('admin.bots.index') }}" {{-- URL для отмены --}}
                                   class="inline-block align-baseline font-bold text-sm text-[#a6925e] hover:text-[#e5b769] transition duration-300"> {{-- Стили ссылки отмены --}}
                                    Отмена {{-- Текст ссылки --}}
                                </a>
                            </div>
                        </form>
                    </div> {{-- Закрытие контейнера формы --}}

                </div> {{-- Закрытие container mx-auto px-6 py-8 --}}
            </main>
        </div> {{-- Закрытие flex-1 flex flex-col overflow-hidden --}}
    </div> {{-- Закрытие основного контейнера страницы --}}

    {{-- Скрипт для динамического обновления базового урона и mine_location_id --}}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const strengthInput = document.getElementById('strength');
            const baseDamageDisplay = document.getElementById('base_damage_display');
            const locationSelect = document.getElementById('location');
            const mineLocationIdInput = document.getElementById('mine_location_id');

            // Функция для обновления отображения базового урона
            function updateBaseDamage() {
                const strength = parseInt(strengthInput.value) || 0;
                const baseDamage = strength; // Согласно требованиям: урон = сила
                baseDamageDisplay.textContent = baseDamage + ' (рассчитано автоматически)';
            }

            // Функция для обновления mine_location_id при выборе локации
            function updateMineLocationId() {
                const selectedOption = locationSelect.options[locationSelect.selectedIndex];
                const mineLocationId = selectedOption.getAttribute('data-mine-location-id');
                
                if (mineLocationId) {
                    mineLocationIdInput.value = mineLocationId;
                } else {
                    mineLocationIdInput.value = '';
                }
            }

            // Обновляем при изменении силы
            strengthInput.addEventListener('input', updateBaseDamage);
            
            // Обновляем mine_location_id при изменении локации
            locationSelect.addEventListener('change', updateMineLocationId);

            // Инициализируем при загрузке страницы
            updateBaseDamage();
            updateMineLocationId();
        });
    </script>
</body>

</html>
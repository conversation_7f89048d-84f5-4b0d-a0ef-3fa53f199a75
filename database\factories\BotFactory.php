<?php

namespace Database\Factories;

use App\Models\Bot;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * Фабрика для создания тестовых ботов
 * 
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Bot>
 */
class BotFactory extends Factory
{
    /**
     * Модель, связанная с фабрикой.
     *
     * @var string
     */
    protected $model = Bot::class;

    /**
     * Определение состояния модели по умолчанию.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $races = ['solarius', 'lunarius'];
        $classes = ['warrior', 'mage', 'priest'];
        $locations = ['Шахта Новичков', 'Глубокие Туннели', 'Кристальные Пещеры', 'Подземелье Теней'];

        $race = $this->faker->randomElement($races);
        $class = $this->faker->randomElement($classes);
        $level = $this->faker->numberBetween(1, 50);

        // Базовые характеристики в зависимости от уровня
        $baseHp = 100 + ($level * 10);
        $baseMp = 50 + ($level * 5);
        $baseStrength = 10 + ($level * 2);
        $baseIntelligence = 10 + ($level * 2);
        $baseDexterity = 10 + ($level * 2);

        // Модификаторы в зависимости от класса
        switch ($class) {
            case 'warrior':
                $baseHp *= 1.5;
                $baseStrength *= 1.3;
                $baseIntelligence *= 0.8;
                break;
            case 'mage':
                $baseMp *= 1.5;
                $baseIntelligence *= 1.3;
                $baseStrength *= 0.8;
                break;
            case 'priest':
                $baseMp *= 1.3;
                $baseHp *= 1.2;
                $baseIntelligence *= 1.1;
                $baseStrength *= 0.9;
                break;
        }

        $maxHp = (int) $baseHp;
        $maxMp = (int) $baseMp;
        $currentHp = $this->faker->numberBetween((int) ($maxHp * 0.5), $maxHp);
        $currentMp = $this->faker->numberBetween((int) ($maxMp * 0.3), $maxMp);

        return [
            'name' => $this->generateBotName($race, $class),
            'race' => $race,
            'class' => $class,
            'level' => $level,
            'hp' => $currentHp,
            'max_hp' => $maxHp,
            'mp' => $currentMp,
            'max_mp' => $maxMp,
            'strength' => (int) $baseStrength,
            'intelligence' => (int) $baseIntelligence,
            'dexterity' => (int) $baseDexterity,
            'armor' => $this->faker->numberBetween(0, $level * 2),
            'magic_resistance' => $this->faker->numberBetween(0, $level),
            'location' => $this->faker->randomElement($locations),
            'is_active' => $this->faker->boolean(80), // 80% активных ботов
            'current_target_id' => null,
            'current_target_type' => null,
            'last_attack_time' => $this->faker->optional(0.3)->dateTimeBetween('-1 hour', 'now'),
            'last_attacker_id' => null,
            'last_attacker_type' => null,
            'attack_interval' => $this->faker->numberBetween(2, 8),
            'target_change_interval' => $this->faker->numberBetween(20, 60),
            'last_hp_regen_time' => $this->faker->optional(0.4)->dateTimeBetween('-30 minutes', 'now'),
            'last_mp_regen_time' => $this->faker->optional(0.4)->dateTimeBetween('-30 minutes', 'now'),
            'can_use_skills' => $this->faker->boolean(70),
            'intelligent_targeting' => $this->faker->boolean(30),
            'can_retreat' => $this->faker->boolean(20),
            'respawn_time' => null,
            'next_action_time' => $this->faker->optional(0.4)->dateTimeBetween('now', '+5 minutes'),
            'last_regeneration_at' => $this->faker->optional(0.6)->dateTimeBetween('-30 minutes', 'now'),
            'death_time' => null,
            'respawn_interval' => $this->faker->numberBetween(180, 600), // 3-10 минут
            'is_peaceful' => $this->faker->boolean(10), // 10% мирных ботов
        ];
    }

    /**
     * Генерирует имя бота на основе расы и класса
     */
    private function generateBotName(string $race, string $class): string
    {
        $solarianNames = [
            'warrior' => ['Солнечный Страж', 'Златокрылый Воин', 'Рассветный Защитник', 'Огненный Клинок'],
            'mage' => ['Светоносный Маг', 'Солнечный Чародей', 'Заклинатель Зари', 'Мастер Света'],
            'priest' => ['Солнечный Жрец', 'Целитель Света', 'Священник Рассвета', 'Служитель Солнца']
        ];

        $lunarianNames = [
            'warrior' => ['Лунный Страж', 'Серебряный Воин', 'Ночной Защитник', 'Клинок Полумесяца'],
            'mage' => ['Лунный Маг', 'Чародей Ночи', 'Заклинатель Звезд', 'Мастер Тьмы'],
            'priest' => ['Лунный Жрец', 'Целитель Ночи', 'Священник Полумесяца', 'Служитель Луны']
        ];

        $names = $race === 'solarius' ? $solarianNames : $lunarianNames;
        $classNames = $names[$class] ?? ['Безымянный Боец'];

        $baseName = $this->faker->randomElement($classNames);
        $suffix = $this->faker->optional(0.3)->randomElement(['I', 'II', 'III', 'Младший', 'Старший']);

        return $suffix ? "{$baseName} {$suffix}" : $baseName;
    }

    /**
     * Состояние для активного бота
     */
    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => true,
            'hp' => $attributes['max_hp'],
            'mp' => $attributes['max_mp'],
            'death_time' => null,
            'respawn_time' => null,
        ]);
    }

    /**
     * Состояние для мертвого бота
     */
    public function dead(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
            'hp' => 0,
            'death_time' => Carbon::now()->subMinutes($this->faker->numberBetween(1, 30)),
            'respawn_time' => Carbon::now()->addMinutes($this->faker->numberBetween(1, 10)),
            'current_target_id' => null,
            'current_target_type' => null,
        ]);
    }

    /**
     * Состояние для бота с низким HP
     */
    public function lowHealth(): static
    {
        return $this->state(fn(array $attributes) => [
            'hp' => $this->faker->numberBetween(1, (int) ($attributes['max_hp'] * 0.2)),
            'is_active' => true,
        ]);
    }

    /**
     * Состояние для бота в конкретной локации
     */
    public function inLocation(string $location): static
    {
        return $this->state(fn(array $attributes) => [
            'location' => $location,
        ]);
    }

    /**
     * Состояние для бота конкретной расы
     */
    public function race(string $race): static
    {
        return $this->state(fn(array $attributes) => [
            'race' => $race,
        ]);
    }

    /**
     * Состояние для бота конкретного класса
     */
    public function ofClass(string $class): static
    {
        return $this->state(fn(array $attributes) => [
            'class' => $class,
        ]);
    }

    /**
     * Состояние для бота конкретного уровня
     */
    public function level(int $level): static
    {
        $baseHp = 100 + ($level * 10);
        $baseMp = 50 + ($level * 5);

        return $this->state(fn(array $attributes) => [
            'level' => $level,
            'max_hp' => (int) $baseHp,
            'max_mp' => (int) $baseMp,
            'hp' => (int) $baseHp,
            'mp' => (int) $baseMp,
            'strength' => 10 + ($level * 2),
            'intelligence' => 10 + ($level * 2),
            'dexterity' => 10 + ($level * 2),
        ]);
    }
}

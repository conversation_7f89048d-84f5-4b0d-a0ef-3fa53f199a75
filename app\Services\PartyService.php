<?php

namespace App\Services;

use App\Models\Party;
use App\Models\PartyMember;
use App\Models\PartyInvitation;
use App\Models\User;
use App\Events\PartyCreated;
use App\Events\PartyDisbanded;
use App\Events\MemberJoined;
use App\Events\MemberLeft;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Сервис для управления группами игроков
 * 
 * Обеспечивает оптимизированную бизнес-логику для системы групп
 * с использованием кэширования и событий для высокой производительности
 */
class PartyService
{
    /**
     * Время кэширования данных группы в секундах (5 минут)
     */
    private const CACHE_TTL = 300;

    /**
     * Проверяет, есть ли в группе игрока игроки противоположной расы
     *
     * @param User $user Пользователь для проверки
     * @return bool
     */
    public function hasOppositeRaceMembers(User $user): bool
    {
        $party = $this->getActiveParty($user);
        
        if (!$party) {
            return false;
        }

        $userRace = $user->profile->race ?? 'solarius';
        
        // Получаем всех активных участников группы
        $members = $party->members()
            ->where('status', 'active')
            ->with('user.profile')
            ->get();
        
        // Проверяем, есть ли участники с противоположной расой
        foreach ($members as $member) {
            if ($member->user->id !== $user->id) {
                $memberRace = $member->user->profile->race ?? 'solarius';
                if ($memberRace !== $userRace) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Получает активную группу пользователя
     *
     * @param User $user Пользователь
     * @return Party|null
     */
    public function getActiveParty(User $user): ?Party
    {
        return $user->parties()
            ->wherePivot('status', PartyMember::STATUS_ACTIVE)
            ->first();
    }

    /**
     * Создание новой группы
     *
     * @param User $leader Лидер группы
     * @param array $data Данные группы
     * @return Party
     * @throws \Exception
     */
    public function createParty(User $leader, array $data): Party
    {
        // Проверяем, не состоит ли пользователь уже в активной группе
        if ($this->hasActiveParty($leader)) {
            throw new \Exception('Пользователь уже состоит в активной группе');
        }

        DB::beginTransaction();

        try {
            // Создаем группу
            $party = Party::create([
                'leader_id' => $leader->id,
                'name' => $data['name'] ?? null,
                'max_members' => $data['max_members'] ?? Party::DEFAULT_MAX_MEMBERS,
                'is_public' => $data['is_public'] ?? true,
                'auto_accept' => $data['auto_accept'] ?? false,
            ]);

            // Добавляем создателя как лидера группы
            $membership = PartyMember::create([
                'party_id' => $party->id,
                'user_id' => $leader->id,
                'role' => PartyMember::ROLE_LEADER,
                'status' => PartyMember::STATUS_ACTIVE,
                'joined_at' => now(),
                'is_ready' => true,
            ]);

            DB::commit();

            // Очищаем кэш пользователя
            $this->clearUserPartyCache($leader->id);

            // Запускаем событие создания группы
            event(new PartyCreated($party, $leader));

            Log::info('Группа создана', [
                'party_id' => $party->id,
                'leader_id' => $leader->id,
                'name' => $party->name
            ]);

            return $party;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка создания группы', [
                'leader_id' => $leader->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Расформирование группы
     *
     * @param Party $party
     * @param User $leader
     * @return bool
     * @throws \Exception
     */
    public function disbandParty(Party $party, User $leader): bool
    {
        if (!$party->isLeader($leader->id)) {
            throw new \Exception('Только лидер может расформировать группу');
        }

        DB::beginTransaction();

        try {
            // Получаем всех активных участников
            $activeMembers = $party->activeMembers()->with('user')->get();

            // Отмечаем всех участников как покинувших группу
            $party->members()->where('status', PartyMember::STATUS_ACTIVE)
                ->update([
                    'status' => PartyMember::STATUS_LEFT,
                    'left_at' => now()
                ]);

            // Меняем статус группы
            $party->update(['status' => Party::STATUS_DISBANDED]);

            DB::commit();

            // Очищаем кэш для всех участников
            foreach ($activeMembers as $member) {
                $this->clearUserPartyCache($member->user_id);
            }

            // Запускаем событие расформирования группы
            event(new PartyDisbanded($party, $leader, $activeMembers->pluck('user')));

            Log::info('Группа расформирована', [
                'party_id' => $party->id,
                'leader_id' => $leader->id
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка расформирования группы', [
                'party_id' => $party->id,
                'leader_id' => $leader->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Присоединение к группе
     *
     * @param Party $party
     * @param User $user
     * @return PartyMember
     * @throws \Exception
     */
    public function joinParty(Party $party, User $user): PartyMember
    {
        // Проверяем состояние пользователя в группе
        if ($this->hasActiveParty($user)) {
            // Если пользователь в многопользовательской группе, блокируем присоединение
            if ($user->isInMultiPlayerParty()) {
                throw new \Exception('Пользователь уже состоит в группе с другими игроками');
            }

            // Если пользователь в соло-группе, автоматически покидаем её
            if ($user->isInSoloParty()) {
                $user->leaveSoloParty();
                Log::info('Пользователь автоматически покинул соло-группу для присоединения к группе', [
                    'user_id' => $user->id,
                    'new_party_id' => $party->id
                ]);
            }
        }

        if (!$party->canAcceptNewMember()) {
            throw new \Exception('Группа заполнена');
        }

        if (!$party->isActive()) {
            throw new \Exception('Группа неактивна');
        }

        DB::beginTransaction();

        try {
            $membership = PartyMember::create([
                'party_id' => $party->id,
                'user_id' => $user->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_ACTIVE,
                'joined_at' => now(),
                'is_ready' => false,
            ]);

            DB::commit();

            // Очищаем кэш пользователя
            $this->clearUserPartyCache($user->id);

            // Запускаем событие присоединения к группе
            event(new MemberJoined($party, $user));

            Log::info('Пользователь присоединился к группе', [
                'party_id' => $party->id,
                'user_id' => $user->id
            ]);

            return $membership;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка присоединения к группе', [
                'party_id' => $party->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Покидание группы
     *
     * @param User $user
     * @return bool
     * @throws \Exception
     */
    public function leaveParty(User $user): bool
    {
        $membership = $user->partyMemberships()
            ->where('status', PartyMember::STATUS_ACTIVE)
            ->with('party')
            ->first();

        if (!$membership) {
            throw new \Exception('Пользователь не состоит в активной группе');
        }

        $party = $membership->party;

        DB::beginTransaction();

        try {
            // Если это лидер и в группе есть другие участники
            if ($membership->isLeader() && $party->getActiveMembersCount() > 1) {
                // Передаем лидерство первому доступному участнику
                $newLeader = $party->activeMembers()
                    ->where('user_id', '!=', $user->id)
                    ->first();

                if ($newLeader) {
                    $newLeader->update(['role' => PartyMember::ROLE_LEADER]);
                    $party->update(['leader_id' => $newLeader->user_id]);
                }
            }

            // Отмечаем участника как покинувшего группу
            $membership->markAsLeft();

            // Если в группе не осталось активных участников, расформировываем её
            if ($party->getActiveMembersCount() === 0) {
                $party->update(['status' => Party::STATUS_DISBANDED]);
            }

            DB::commit();

            // Очищаем кэш пользователя
            $this->clearUserPartyCache($user->id);

            // Очищаем приглашения пользователя
            PartyInvitation::cleanupUserInvitations($user->id);

            // Запускаем событие покидания группы
            event(new MemberLeft($party, $user));

            Log::info('Пользователь покинул группу', [
                'party_id' => $party->id,
                'user_id' => $user->id
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка покидания группы', [
                'party_id' => $party->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Приглашение игрока в группу
     *
     * @param Party $party
     * @param User $inviter
     * @param User $invitee
     * @return PartyMember
     * @throws \Exception
     */
    public function invitePlayer(Party $party, User $inviter, User $invitee): PartyMember
    {
        if (!$party->isLeader($inviter->id)) {
            throw new \Exception('Только лидер может приглашать игроков');
        }

        // Проверяем, состоит ли приглашаемый в группе с другими игроками
        // Если он в соло-группе (группа из 1 человека), то можно приглашать
        if ($this->hasActiveParty($invitee)) {
            $inviteeActiveParty = $this->getUserActiveParty($invitee);
            if ($inviteeActiveParty && $inviteeActiveParty->activeUsers()->count() > 1) {
                throw new \Exception('Игрок уже состоит в группе');
            }
        }

        if (!$party->canAcceptNewMember()) {
            throw new \Exception('Группа заполнена');
        }

        // Проверяем, нет ли уже активного приглашения
        $existingInvitation = PartyMember::where('party_id', $party->id)
            ->where('user_id', $invitee->id)
            ->where('status', PartyMember::STATUS_INVITED)
            ->first();

        if ($existingInvitation) {
            throw new \Exception('Игрок уже приглашен в эту группу');
        }

        try {
            $invitation = PartyMember::create([
                'party_id' => $party->id,
                'user_id' => $invitee->id,
                'role' => PartyMember::ROLE_MEMBER,
                'status' => PartyMember::STATUS_INVITED,
                'is_ready' => false,
            ]);

            Log::info('Игрок приглашен в группу', [
                'party_id' => $party->id,
                'inviter_id' => $inviter->id,
                'invitee_id' => $invitee->id
            ]);

            return $invitation;

        } catch (\Exception $e) {
            Log::error('Ошибка приглашения в группу', [
                'party_id' => $party->id,
                'inviter_id' => $inviter->id,
                'invitee_id' => $invitee->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Проверяет, состоит ли пользователь в активной группе
     *
     * @param User $user
     * @return bool
     */
    public function hasActiveParty(User $user): bool
    {
        $cacheKey = "user_active_party_{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user) {
            return $user->partyMemberships()
                ->where('status', PartyMember::STATUS_ACTIVE)
                ->exists();
        });
    }

    /**
     * Получает активную группу пользователя
     *
     * @param User $user
     * @return Party|null
     */
    public function getUserActiveParty(User $user): ?Party
    {
        $cacheKey = "user_party_data_{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user) {
            return $user->parties()
                ->wherePivot('status', PartyMember::STATUS_ACTIVE)
                ->with(['activeUsers.profile', 'leader.profile'])
                ->first();
        });
    }

    /**
     * Очищает кэш группы пользователя
     *
     * @param int $userId
     */
    private function clearUserPartyCache(int $userId): void
    {
        Cache::forget("user_active_party_{$userId}");
        Cache::forget("user_party_data_{$userId}");
    }

    /**
     * Отправить приглашение игроку в группу (новая логика WoW/L2 стиля)
     *
     * @param User $inviter
     * @param User $invitee
     * @param string|null $message
     * @return PartyInvitation
     * @throws \Exception
     */
    public function sendInvitation(User $inviter, User $invitee, ?string $message = null): PartyInvitation
    {
        // Используем метод из модели User для проверки и отправки приглашения
        return $inviter->inviteToParty($invitee, $message);
    }

    /**
     * Принять приглашение в группу (новая логика)
     *
     * @param PartyInvitation $invitation
     * @return Party
     * @throws \Exception
     */
    public function acceptInvitation(PartyInvitation $invitation): Party
    {
        DB::beginTransaction();

        try {
            $inviter = $invitation->inviter;
            $invitee = $invitation->invitee;

            // Принимаем приглашение
            $invitee->acceptPartyInvitation($invitation);

            // Проверяем, есть ли у приглашающего активная группа
            $party = $inviter->activeParty;

            if (!$party) {
                // Если у приглашающего нет группы, создаем новую
                $party = $this->createParty($inviter, [
                    'name' => null,
                    'max_members' => Party::DEFAULT_MAX_MEMBERS,
                    'is_public' => false,
                    'auto_accept' => false,
                ]);
            }

            // Добавляем приглашенного игрока в группу
            $this->joinParty($party, $invitee);

            DB::commit();

            // Очищаем кэш пользователей
            $this->clearUserPartyCache($inviter->id);
            $this->clearUserPartyCache($invitee->id);

            Log::info('Приглашение принято, игрок добавлен в группу', [
                'invitation_id' => $invitation->id,
                'party_id' => $party->id,
                'inviter_id' => $inviter->id,
                'invitee_id' => $invitee->id
            ]);

            return $party;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка принятия приглашения', [
                'invitation_id' => $invitation->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Отклонить приглашение в группу
     *
     * @param PartyInvitation $invitation
     * @return bool
     * @throws \Exception
     */
    public function declineInvitation(PartyInvitation $invitation): bool
    {
        $invitee = $invitation->invitee;
        return $invitee->declinePartyInvitation($invitation);
    }

    /**
     * Очистить истекшие приглашения
     *
     * @return int Количество очищенных приглашений
     */
    public function cleanupExpiredInvitations(): int
    {
        $expiredInvitations = PartyInvitation::expired()->get();

        foreach ($expiredInvitations as $invitation) {
            $invitation->markAsExpired();
        }

        Log::info('Очищены истекшие приглашения', [
            'count' => $expiredInvitations->count()
        ]);

        return $expiredInvitations->count();
    }
}

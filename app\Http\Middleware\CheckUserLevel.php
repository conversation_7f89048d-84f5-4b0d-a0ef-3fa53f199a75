<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use App\Services\LocationAccessService;

class CheckUserLevel
{
    /**
     * Сервис для проверки доступа к локациям
     */
    protected $locationAccessService;

    /**
     * Конструктор middleware
     * 
     * @param LocationAccessService $locationAccessService
     */
    public function __construct(LocationAccessService $locationAccessService)
    {
        $this->locationAccessService = $locationAccessService;
    }

    /**
     * Обрабатывает запрос и проверяет уровень пользователя для доступа к локации
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $locationName Название локации для проверки
     * @return mixed
     */
    public function handle($request, Closure $next, $locationName = null)
    {
        // Получаем текущего пользователя
        $user = Auth::user();

        // Если пользователь не авторизован, пропускаем проверку
        if (!$user) {
            return $next($request);
        }

        // Если локация не указана, пытаемся определить её из маршрута
        if (!$locationName) {
            // Определяем локацию на основе маршрута
            $route = $request->route()->getName();
            
            if (strpos($route, 'battle.outposts.elven_haven') !== false) {
                $locationName = 'Эльфийская Гавань';
            } elseif (strpos($route, 'battle.outposts.sandy_stronghold') !== false) {
                $locationName = 'Песчаный Оплот';
            }
            // Здесь можно добавить другие локации
        }

        // Если локация определена, проверяем доступ
        if ($locationName) {
            // Проверяем, может ли пользователь получить доступ к локации
            if (!$this->locationAccessService->canAccessLocation($user, $locationName)) {
                // Проверяем, есть ли флаг недавней победы
                $hasRecentVictory = session('recent_victory_time') && 
                                   (time() - session('recent_victory_time') < 10);
                
                // Если игрок недавно победил, пропускаем проверку доступа
                if ($hasRecentVictory) {
                    Log::info("CheckUserLevel пропускает проверку доступа для победителя", [
                        'user_id' => $user->id,
                        'locationName' => $locationName,
                        'hasRecentVictory' => $hasRecentVictory,
                        'recent_victory_time' => session('recent_victory_time'),
                        'current_time' => time()
                    ]);
                    return $next($request);
                }
                
                // Получаем причину отказа
                $reason = $this->locationAccessService->getAccessDeniedReason($user, $locationName);
                
                Log::warning("CheckUserLevel перенаправляет на battle.outposts.index", [
                    'user_id' => $user->id,
                    'locationName' => $locationName,
                    'reason' => $reason,
                    'hasRecentVictory' => $hasRecentVictory,
                    'recent_victory_time' => session('recent_victory_time'),
                    'current_time' => time()
                ]);
                
                // Перенаправляем на страницу аванпостов с сообщением
                return redirect()->route('battle.outposts.index')
                    ->with('error', $reason ?: "У вас нет доступа к локации '{$locationName}'.");
            }
        }

        return $next($request);
    }
}

<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Выполнение миграции для полей поражения ===\n\n";

// Проверяем, есть ли уже поле is_defeated
try {
    $hasDefeatedField = Schema::hasColumn('user_profiles', 'is_defeated');
    
    if ($hasDefeatedField) {
        echo "✓ Поле 'is_defeated' уже существует в таблице user_profiles\n";
    } else {
        echo "⚠ Поле 'is_defeated' не найдено. Выполняем миграцию...\n";
        
        // Выполняем конкретную миграцию
        $migrationFile = '2025_07_17_000001_add_defeat_fields_to_user_profiles_table';
        
        try {
            // Проверяем, не выполнялась ли уже эта миграция
            $migrationExists = DB::table('migrations')
                ->where('migration', $migrationFile)
                ->exists();
                
            if ($migrationExists) {
                echo "⚠ Миграция {$migrationFile} уже выполнялась, но поле отсутствует\n";
                echo "Это может означать проблему с БД или откат миграции\n";
            } else {
                echo "Выполняем миграцию {$migrationFile}...\n";
                Artisan::call('migrate', [
                    '--path' => 'database/migrations/' . $migrationFile . '.php',
                    '--force' => true
                ]);
                echo "✓ Миграция выполнена\n";
            }
        } catch (Exception $e) {
            echo "❌ Ошибка при выполнении миграции: " . $e->getMessage() . "\n";
            echo "Пытаемся выполнить миграцию вручную...\n";
            
            // Выполняем SQL вручную
            try {
                DB::statement("
                    ALTER TABLE user_profiles 
                    ADD COLUMN IF NOT EXISTS is_defeated BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS defeated_by_type VARCHAR(255),
                    ADD COLUMN IF NOT EXISTS defeated_by_id BIGINT,
                    ADD COLUMN IF NOT EXISTS defeated_at TIMESTAMP
                ");
                
                // Создаем индексы
                DB::statement("CREATE INDEX IF NOT EXISTS idx_user_profiles_is_defeated ON user_profiles (is_defeated)");
                DB::statement("CREATE INDEX IF NOT EXISTS idx_user_profiles_defeated_by ON user_profiles (defeated_by_type, defeated_by_id)");
                DB::statement("CREATE INDEX IF NOT EXISTS idx_user_profiles_defeated_at ON user_profiles (defeated_at)");
                
                echo "✓ Поля добавлены вручную\n";
            } catch (Exception $e2) {
                echo "❌ Ошибка при добавлении полей вручную: " . $e2->getMessage() . "\n";
                return;
            }
        }
    }
    
    // Проверяем результат
    $hasDefeatedFieldAfter = Schema::hasColumn('user_profiles', 'is_defeated');
    $hasDefeatedByTypeField = Schema::hasColumn('user_profiles', 'defeated_by_type');
    $hasDefeatedByIdField = Schema::hasColumn('user_profiles', 'defeated_by_id');
    $hasDefeatedAtField = Schema::hasColumn('user_profiles', 'defeated_at');
    
    echo "\n=== Проверка результата ===\n";
    echo "is_defeated: " . ($hasDefeatedFieldAfter ? "✓" : "❌") . "\n";
    echo "defeated_by_type: " . ($hasDefeatedByTypeField ? "✓" : "❌") . "\n";
    echo "defeated_by_id: " . ($hasDefeatedByIdField ? "✓" : "❌") . "\n";
    echo "defeated_at: " . ($hasDefeatedAtField ? "✓" : "❌") . "\n";
    
    if ($hasDefeatedFieldAfter) {
        echo "\n✓ Все необходимые поля добавлены успешно!\n";
        echo "Теперь можно обновить UserLocationService для использования is_defeated\n";
    } else {
        echo "\n❌ Не удалось добавить необходимые поля\n";
        echo "Проверьте права доступа к БД и повторите попытку\n";
    }
    
} catch (Exception $e) {
    echo "❌ Общая ошибка: " . $e->getMessage() . "\n";
}

echo "\n=== Завершено ===\n";
-- АГРЕССИВНЫЙ ПОИСК ВСЕХ СКРЫТЫХ БОТОВ
-- Находит всех ботов, которые могут атаковать в подлокации xzxzxzx

-- 1. Найдем ID подлокации xzxzxzx
SELECT id, name, location_id, is_active 
FROM mine_locations 
WHERE name = 'xzxzxzx';

-- 2. Найдем ВСЕХ ботов по именам атакующих
SELECT 
    id,
    name,
    location,
    mine_location_id,
    is_active,
    created_by_admin,
    hp,
    max_hp,
    CASE 
        WHEN location = 'xzxzxzx' THEN 'В АДМИНКЕ'
        ELSE 'СКРЫТ'
    END as admin_status,
    CASE 
        WHEN location REGEXP '^[0-9]+$' THEN 'ID ЛОКАЦИИ'
        ELSE 'НОРМАЛЬНАЯ ЛОКАЦИЯ'
    END as location_type
FROM bots 
WHERE name IN ('nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм')
ORDER BY name;

-- 3. Найдем ботов с ID локации для рудника xzxzxzx
SELECT 
    b.id,
    b.name,
    b.location,
    b.mine_location_id,
    b.is_active,
    b.created_by_admin,
    ml.name as correct_location,
    ml.id as correct_mine_id
FROM bots b
INNER JOIN mine_locations ml ON b.location = CAST(ml.id AS CHAR)
WHERE ml.name = 'xzxzxzx'
ORDER BY b.name;

-- 4. Найдем ботов в базовой локации рудника
SELECT 
    b.id,
    b.name,
    b.location,
    b.mine_location_id,
    b.is_active,
    b.created_by_admin,
    ml.name as should_be_location,
    ml.id as should_be_mine_id
FROM bots b
INNER JOIN mine_locations ml ON b.location = (
    SELECT l.name FROM locations l WHERE l.id = ml.location_id
)
WHERE ml.name = 'xzxzxzx'
ORDER BY b.name;

-- 5. Найдем ботов с частичным совпадением названия
SELECT 
    id,
    name,
    location,
    mine_location_id,
    is_active,
    created_by_admin,
    'ЧАСТИЧНОЕ СОВПАДЕНИЕ' as found_by
FROM bots 
WHERE location LIKE '%xzxzx%'
   OR location LIKE '%xzxz%'
   OR location LIKE '%zxzx%'
ORDER BY name;

-- 6. Найдем ботов с подозрительными локациями (только цифры)
SELECT 
    id,
    name,
    location,
    mine_location_id,
    is_active,
    created_by_admin,
    'ID ЛОКАЦИИ' as issue_type
FROM bots 
WHERE location REGEXP '^[0-9]+$'
  AND created_by_admin = true
ORDER BY CAST(location AS UNSIGNED);

-- 7. Найдем ботов с несоответствующими mine_location_id
SELECT 
    b.id,
    b.name,
    b.location,
    b.mine_location_id,
    b.is_active,
    b.created_by_admin,
    ml.name as mine_location_name,
    'НЕСООТВЕТСТВИЕ mine_location_id' as issue_type
FROM bots b
LEFT JOIN mine_locations ml ON b.mine_location_id = ml.id
WHERE b.created_by_admin = true
  AND (
    (b.mine_location_id IS NOT NULL AND ml.name != b.location)
    OR (b.mine_location_id IS NULL AND EXISTS(
        SELECT 1 FROM mine_locations ml2 WHERE ml2.name = b.location
    ))
  )
ORDER BY b.name;

-- 8. ИСПРАВЛЕНИЕ: Все боты с именами атакующих должны быть в подлокации xzxzxzx
UPDATE bots 
SET location = 'xzxzxzx',
    mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx' LIMIT 1),
    next_action_time = NULL
WHERE name IN ('nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм')
  AND location != 'xzxzxzx';

-- 9. ИСПРАВЛЕНИЕ: Все боты с ID локации для рудника xzxzxzx
UPDATE bots b
INNER JOIN mine_locations ml ON b.location = CAST(ml.id AS CHAR)
SET b.location = ml.name,
    b.mine_location_id = ml.id,
    b.next_action_time = NULL
WHERE ml.name = 'xzxzxzx'
  AND b.created_by_admin = true;

-- 10. ИСПРАВЛЕНИЕ: Все боты с несоответствующими mine_location_id
UPDATE bots b
INNER JOIN mine_locations ml ON b.location = ml.name
SET b.mine_location_id = ml.id,
    b.next_action_time = NULL
WHERE b.created_by_admin = true
  AND (b.mine_location_id IS NULL OR b.mine_location_id != ml.id);

-- 11. ПРОВЕРКА РЕЗУЛЬТАТА: Сколько ботов теперь в админке для подлокации xzxzxzx
SELECT 
    COUNT(*) as total_bots_in_admin,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_bots,
    COUNT(CASE WHEN hp > 0 THEN 1 END) as alive_bots
FROM bots 
WHERE location = 'xzxzxzx' 
  AND created_by_admin = true;

-- 12. СПИСОК ВСЕХ БОТОВ В ПОДЛОКАЦИИ xzxzxzx
SELECT 
    id,
    name,
    location,
    mine_location_id,
    is_active,
    hp,
    max_hp,
    race,
    class,
    next_action_time,
    CASE 
        WHEN is_active = true AND hp > 0 THEN '✅ АКТИВЕН'
        WHEN is_active = false THEN '❌ НЕАКТИВЕН'
        WHEN hp <= 0 THEN '💀 МЕРТВ'
        ELSE '❓ НЕИЗВЕСТНО'
    END as status
FROM bots 
WHERE location = 'xzxzxzx' 
  AND created_by_admin = true
ORDER BY name;
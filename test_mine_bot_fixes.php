<?php

/**
 * Тест исправлений функциональности ботов в локациях рудников
 * 
 * Этот скрипт проверяет:
 * 1. Исправление дублирования ботов в счетчиках фракций
 * 2. Корректность сброса целей при переходах между подлокациями
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\Bot;
use App\Models\MineLocation;
use App\Services\battle\UserLocationService;
use App\Services\battle\FactionCountService;
use App\Services\MineTargetResetService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// Загружаем конфигурацию Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== ТЕСТ ИСПРАВЛЕНИЙ БОТОВ В РУДНИКАХ ===\n\n";

// Тест 1: Проверка исправления дублирования ботов
echo "1. Тестирование исправления дублирования ботов в счетчиках фракций...\n";

try {
    $userLocationService = app(UserLocationService::class);
    $factionCountService = app(FactionCountService::class);
    
    // Найдем первую доступную локацию рудника
    $mineLocation = MineLocation::with('baseLocation')
        ->where('is_active', true)
        ->first();
    
    if (!$mineLocation) {
        echo "❌ Не найдена активная локация рудника для тестирования\n";
        exit(1);
    }
    
    echo "   Тестируем локацию: {$mineLocation->name}\n";
    
    // Подсчитаем ботов до исправления (симуляция)
    $botsInLocation = Bot::where(function($q) use ($mineLocation) {
        $q->where('location', $mineLocation->baseLocation->name ?? $mineLocation->name)
          ->orWhere('mine_location_id', $mineLocation->id);
    })->where('is_active', true)->get();
    
    $originalCount = $botsInLocation->count();
    echo "   Найдено ботов в локации (с потенциальным дублированием): $originalCount\n";
    
    // Тестируем исправленный метод
    $fixedCount = $userLocationService->getBotsCountInLocation(
        $mineLocation->baseLocation->name ?? $mineLocation->name,
        'solarius',
        'warrior'
    );
    
    echo "   Исправленный подсчет ботов-воинов Солариус: $fixedCount\n";
    
    // Проверяем, что нет дублирования
    $distinctBots = Bot::where(function($q) use ($mineLocation) {
        $q->where('mine_location_id', $mineLocation->id)
          ->orWhere(function($subQ) use ($mineLocation) {
              $subQ->where('location', $mineLocation->baseLocation->name ?? $mineLocation->name)
                   ->whereNull('mine_location_id');
          });
    })->where('is_active', true)
      ->where('race', 'solarius')
      ->where('class', 'warrior')
      ->count();
    
    echo "   Реальное количество ботов-воинов Солариус: $distinctBots\n";
    
    if ($fixedCount === $distinctBots) {
        echo "✅ Исправление дублирования работает корректно!\n\n";
    } else {
        echo "❌ Обнаружено расхождение в подсчете ботов\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка при тестировании подсчета ботов: " . $e->getMessage() . "\n\n";
}

// Тест 2: Проверка сброса целей при переходах
echo "2. Тестирование сброса целей при переходах между подлокациями...\n";

try {
    $mineTargetResetService = app(MineTargetResetService::class);
    
    // Найдем тестового пользователя
    $testUser = User::with(['profile', 'statistics'])
        ->where('email', '<EMAIL>')
        ->orWhere('name', 'admin')
        ->first();
    
    if (!$testUser) {
        echo "❌ Не найден тестовый пользователь для проверки\n";
    } else {
        echo "   Тестируем с пользователем: {$testUser->name}\n";
        
        // Найдем две разные подлокации рудника
        $locations = MineLocation::where('is_active', true)->take(2)->get();
        
        if ($locations->count() >= 2) {
            $location1 = $locations[0];
            $location2 = $locations[1];
            
            echo "   Переход из: {$location1->name} -> {$location2->name}\n";
            
            // Устанавливаем цель игрока
            $testUser->current_target_type = 'player';
            $testUser->current_target_id = 999; // Несуществующий игрок
            $testUser->save();
            
            echo "   Установлена цель: player #999\n";
            
            // Тестируем сброс целей
            $wasReset = $mineTargetResetService->checkAndResetTargetsOnMineLocationChange(
                $testUser,
                $location2,
                $location1
            );
            
            if ($wasReset) {
                echo "✅ Цель корректно сброшена при переходе между подлокациями!\n\n";
            } else {
                echo "⚠️  Цель не была сброшена (возможно, игрок доступен в новой локации)\n\n";
            }
            
        } else {
            echo "❌ Недостаточно локаций рудника для тестирования переходов\n\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка при тестировании сброса целей: " . $e->getMessage() . "\n\n";
}

// Тест 3: Проверка работы счетчиков фракций
echo "3. Тестирование счетчиков фракций...\n";

try {
    $factionCountService = app(FactionCountService::class);
    
    $mineLocation = MineLocation::with('baseLocation')
        ->where('is_active', true)
        ->first();
    
    if ($mineLocation) {
        echo "   Тестируем счетчики для локации: {$mineLocation->name}\n";
        
        $factionCounts = $factionCountService->getMineFactionCounts($mineLocation->id);
        
        echo "   Солариус: Воины={$factionCounts['total_counts']['solarius']['warriors']}, ";
        echo "Маги={$factionCounts['total_counts']['solarius']['mages']}, ";
        echo "Жрецы={$factionCounts['total_counts']['solarius']['knights']}\n";
        
        echo "   Лунариус: Воины={$factionCounts['total_counts']['lunarius']['warriors']}, ";
        echo "Маги={$factionCounts['total_counts']['lunarius']['mages']}, ";
        echo "Жрецы={$factionCounts['total_counts']['lunarius']['knights']}\n";
        
        echo "✅ Счетчики фракций работают!\n\n";
    } else {
        echo "❌ Локация рудника не найдена\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка при тестировании счетчиков фракций: " . $e->getMessage() . "\n\n";
}

echo "=== ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===\n";
echo "\nДля полного тестирования:\n";
echo "1. Зайдите в админку /admin/bots/create\n";
echo "2. Создайте бота для локации рудника\n";
echo "3. Проверьте счетчики фракций в игре\n";
echo "4. Переходите между подлокациями и проверяйте сброс целей\n";
<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\Location;
use App\Services\battle\UserLocationService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Поиск застрявшего Лунариус Мага ===\n\n";

// Ищем всех Лунариус Магов (ботов и игроков)
echo "=== ПОИСК БОТОВ ЛУНАРИУС МАГОВ ===\n";
$lunariusMageBots = Bot::where('race', 'lunarius')
    ->where('class', 'mage')
    ->where('is_active', true)
    ->get();

echo "Найдено активных ботов Лунариус Магов: " . $lunariusMageBots->count() . "\n\n";

foreach ($lunariusMageBots as $bot) {
    echo "Бот ID: {$bot->id}, Имя: {$bot->name}\n";
    echo "  Локация: {$bot->location}\n";
    echo "  Mine Location ID: " . ($bot->mine_location_id ?? 'NULL') . "\n";
    echo "  HP: {$bot->hp}/{$bot->max_hp}\n";
    echo "  Последняя атака: " . ($bot->last_attack_time ?? 'Никогда') . "\n";
    echo "  Цель: " . ($bot->current_target_type ?? 'Нет') . " ID:" . ($bot->current_target_id ?? 'Нет') . "\n";
    
    // Проверяем, в какой именно локации рудника находится
    if ($bot->mine_location_id) {
        $mineLocation = MineLocation::find($bot->mine_location_id);
        if ($mineLocation) {
            echo "  Локация рудника: {$mineLocation->name} (активна: " . ($mineLocation->is_active ? 'ДА' : 'НЕТ') . ")\n";
        }
    }
    echo "\n";
}

echo "=== ПОИСК ИГРОКОВ ЛУНАРИУС МАГОВ ===\n";
$lunariusMagePlayers = User::whereHas('profile', function ($q) {
    $q->where('race', 'lunarius')->where('class', 'mage');
})
->with(['profile', 'statistics'])
->get();

echo "Найдено игроков Лунариус Магов: " . $lunariusMagePlayers->count() . "\n\n";

foreach ($lunariusMagePlayers as $player) {
    $isOnline = $player->last_activity_timestamp >= (now()->subMinutes(5)->timestamp);
    $currentLocation = $player->statistics->current_location ?? 'Неизвестно';
    
    echo "Игрок ID: {$player->id}, Имя: {$player->name}\n";
    echo "  Онлайн: " . ($isOnline ? 'ДА' : 'НЕТ') . "\n";
    echo "  Последняя активность: " . date('Y-m-d H:i:s', $player->last_activity_timestamp) . "\n";
    echo "  Текущая локация: {$currentLocation}\n";
    echo "  HP: " . ($player->profile->hp ?? 'N/A') . "/" . ($player->profile->max_hp ?? 'N/A') . "\n";
    echo "  Цель: " . ($player->current_target_type ?? 'Нет') . " ID:" . ($player->current_target_id ?? 'Нет') . "\n";
    echo "\n";
}

echo "=== ПРОВЕРКА КОНКРЕТНОЙ ЛОКАЦИИ 'аааааааааааа' ===\n";

// Проверяем игроков в локации "аааааааааааа"
$playersInLocationA = User::whereHas('statistics', function ($q) {
    $q->where('current_location', 'аааааааааааа');
})
->with(['profile', 'statistics'])
->get();

echo "Игроков в локации 'аааааааааааа': " . $playersInLocationA->count() . "\n";
foreach ($playersInLocationA as $player) {
    $race = $player->profile->race ?? 'unknown';
    $class = $player->profile->class ?? 'unknown';
    $isOnline = $player->last_activity_timestamp >= (now()->subMinutes(5)->timestamp);
    
    echo "  {$player->name} - {$race} {$class} (онлайн: " . ($isOnline ? 'ДА' : 'НЕТ') . ")\n";
}

// Проверяем ботов в локации "аааааааааааа" 
$botsInLocationA = Bot::where('location', 'аааааааааааа')
    ->where('is_active', true)
    ->get();

echo "\nБотов в локации 'аааааааааааа': " . $botsInLocationA->count() . "\n";
foreach ($botsInLocationA as $bot) {
    echo "  {$bot->name} - {$bot->race} {$bot->class} (HP: {$bot->hp}/{$bot->max_hp})\n";
}

// Проверяем рудники с таким названием
$mineLocationA = MineLocation::where('name', 'аааааааааааа')->first();
if ($mineLocationA) {
    echo "\nРудник 'аааааааааааа' найден (ID: {$mineLocationA->id})\n";
    echo "Активен: " . ($mineLocationA->is_active ? 'ДА' : 'НЕТ') . "\n";
    echo "Является подлокацией: " . ($mineLocationA->isSubLocation() ? 'ДА' : 'НЕТ') . "\n";
    
    // Проверяем ботов с mine_location_id
    $botsInMineLocationA = Bot::where('mine_location_id', $mineLocationA->id)
        ->where('is_active', true)
        ->get();
    
    echo "Ботов с mine_location_id={$mineLocationA->id}: " . $botsInMineLocationA->count() . "\n";
    foreach ($botsInMineLocationA as $bot) {
        echo "  {$bot->name} - {$bot->race} {$bot->class} (HP: {$bot->hp}/{$bot->max_hp})\n";
    }
}

echo "\n=== ПРОВЕРКА КЕША И ОНЛАЙН СТАТУСА ===\n";

// Проверяем, есть ли проблемы с онлайн статусом
$userLocationService = app(UserLocationService::class);

echo "Проверяем нормализацию названия локации:\n";
$normalizedName = $userLocationService->normalizeLocationName('аааааааааааа');
echo "Оригинал: 'аааааааааааа'\n";
echo "Нормализованное: '{$normalizedName}'\n";

// Проверяем игроков с нормализованным именем
if ($normalizedName !== 'аааааааааааа') {
    $playersInNormalizedLocation = User::whereHas('statistics', function ($q) use ($normalizedName) {
        $q->where('current_location', $normalizedName);
    })
    ->with(['profile', 'statistics'])
    ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
    ->get();
    
    echo "\nИгроков в нормализованной локации '{$normalizedName}': " . $playersInNormalizedLocation->count() . "\n";
}

echo "\n=== ПОТЕНЦИАЛЬНЫЕ ПРОБЛЕМЫ ===\n";

// Проверяем игроков с устаревшими данными активности
$staleActivePlayers = User::whereHas('profile', function ($q) {
    $q->where('race', 'lunarius')->where('class', 'mage');
})
->where('last_activity_timestamp', '<', now()->subHours(1)->timestamp)
->where('last_activity_timestamp', '>', now()->subDays(1)->timestamp)
->with(['profile', 'statistics'])
->get();

echo "Лунариус Магов с устаревшей активностью (1 час - 1 день): " . $staleActivePlayers->count() . "\n";
foreach ($staleActivePlayers as $player) {
    $lastActivity = date('Y-m-d H:i:s', $player->last_activity_timestamp);
    $currentLocation = $player->statistics->current_location ?? 'Неизвестно';
    echo "  {$player->name} - последняя активность: {$lastActivity}, локация: {$currentLocation}\n";
}

// Проверяем ботов с проблемами
$problematicBots = Bot::where('race', 'lunarius')
    ->where('class', 'mage')
    ->where('is_active', true)
    ->where(function ($q) {
        $q->where('hp', '<=', 0)
          ->orWhereNull('location')
          ->orWhere('location', '')
          ->orWhereNotNull('death_time');
    })
    ->get();

echo "\nПроблематичных ботов Лунариус Магов: " . $problematicBots->count() . "\n";
foreach ($problematicBots as $bot) {
    echo "  {$bot->name} - проблемы: ";
    $problems = [];
    if ($bot->hp <= 0) $problems[] = "мертв (HP: {$bot->hp})";
    if (!$bot->location || $bot->location === '') $problems[] = "нет локации";
    if ($bot->death_time) $problems[] = "есть death_time";
    echo implode(', ', $problems) . "\n";
}

echo "\n=== ЗАВЕРШЕНО ===\n";
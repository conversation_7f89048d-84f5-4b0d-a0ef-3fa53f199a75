<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\Bot;
use App\Models\MineLocation;
use App\Models\Location;
use App\Services\battle\LocationPlayerCacheService;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Log;

/**
 * Тест для проверки строгой изоляции PvP атак в рудниках
 * Проверяет, что игроки не могут атаковать друг друга между разными уровнями локаций
 */
class MinePvPIsolationTest
{
    private $cacheService;
    private $baseLocation;
    private $subLocation;
    private $playerInBase;
    private $playerInSub;
    private $botInBase;
    private $botInSub;

    public function __construct()
    {
        $this->cacheService = new LocationPlayerCacheService();
        $this->setupTestData();
    }

    /**
     * Настройка тестовых данных
     */
    private function setupTestData()
    {
        // Создаем базовую локацию рудника
        $this->baseLocation = Location::firstOrCreate([
            'name' => 'Тестовый рудник',
            'slug' => 'test-mine'
        ]);

        // Создаем подлокацию рудника
        $this->subLocation = MineLocation::firstOrCreate([
            'name' => 'Тестовая подлокация рудника',
            'slug' => 'test-mine-sub',
            'base_location_id' => $this->baseLocation->id,
            'is_active' => true
        ]);

        // Создаем игрока в базовой локации (раса Solarius)
        $this->playerInBase = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => 'PlayerInBase', 'password' => bcrypt('password')]
        );
        
        // Устанавливаем расу и локацию для игрока в базовой локации
        $this->playerInBase->profile()->updateOrCreate([], [
            'race' => 'solarius',
            'hp' => 100,
            'current_hp' => 100,
            'max_hp' => 100
        ]);
        
        $this->playerInBase->statistics()->updateOrCreate([], [
            'current_location' => $this->baseLocation->name
        ]);
        
        $this->playerInBase->update(['last_activity_timestamp' => now()->timestamp]);

        // Создаем игрока в подлокации (раса Lunarius)
        $this->playerInSub = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => 'PlayerInSub', 'password' => bcrypt('password')]
        );
        
        // Устанавливаем расу и локацию для игрока в подлокации
        $this->playerInSub->profile()->updateOrCreate([], [
            'race' => 'lunarius',
            'hp' => 100,
            'current_hp' => 100,
            'max_hp' => 100
        ]);
        
        $this->playerInSub->statistics()->updateOrCreate([], [
            'current_location' => $this->subLocation->name
        ]);
        
        $this->playerInSub->update(['last_activity_timestamp' => now()->timestamp]);

        // Создаем бота в базовой локации (раса Lunarius)
        $this->botInBase = Bot::firstOrCreate([
            'name' => 'BotInBase',
            'race' => 'lunarius',
            'location' => $this->baseLocation->name,
            'mine_location_id' => null, // Важно: нет привязки к подлокации
            'is_active' => true,
            'hp' => 100,
            'max_hp' => 100
        ]);

        // Создаем бота в подлокации (раса Solarius)
        $this->botInSub = Bot::firstOrCreate([
            'name' => 'BotInSub',
            'race' => 'solarius',
            'location' => $this->subLocation->name,
            'mine_location_id' => $this->subLocation->id, // Важно: привязка к подлокации
            'is_active' => true,
            'hp' => 100,
            'max_hp' => 100
        ]);

        echo "✅ Тестовые данные созданы:\n";
        echo "   - Базовая локация: {$this->baseLocation->name}\n";
        echo "   - Подлокация: {$this->subLocation->name}\n";
        echo "   - Игрок в базовой (Solarius): {$this->playerInBase->name}\n";
        echo "   - Игрок в подлокации (Lunarius): {$this->playerInSub->name}\n";
        echo "   - Бот в базовой (Lunarius): {$this->botInBase->name}\n";
        echo "   - Бот в подлокации (Solarius): {$this->botInSub->name}\n\n";
    }

    /**
     * Тест 1: Игрок в базовой локации НЕ должен видеть игроков из подлокации
     */
    public function testPlayerInBaseCannotSeePlayersInSub()
    {
        echo "🧪 Тест 1: Игрок в базовой локации НЕ должен видеть игроков из подлокации\n";
        
        // Игрок в базовой локации (Solarius) ищет врагов (Lunarius)
        $enemyPlayers = $this->cacheService->getCachedPlayersInLocation(
            $this->baseLocation->name,
            'lunarius',
            $this->playerInBase->id
        );
        
        $foundPlayerInSub = $enemyPlayers->contains('id', $this->playerInSub->id);
        
        echo "   Детали поиска:\n";
        echo "   - Игрок в базовой: {$this->playerInBase->name} ({$this->baseLocation->name})\n";
        echo "   - Игрок в подлокации: {$this->playerInSub->name} ({$this->subLocation->name})\n";
        echo "   - Найдено врагов: {$enemyPlayers->count()}\n";
        
        if (!$foundPlayerInSub) {
            echo "   ✅ ПРОЙДЕН: Игрок в базовой локации НЕ видит игрока из подлокации\n";
            return true;
        } else {
            echo "   ❌ ПРОВАЛЕН: Игрок в базовой локации видит игрока из подлокации (БАГ!)\n";
            return false;
        }
    }

    /**
     * Тест 2: Игрок в подлокации НЕ должен видеть игроков из базовой локации
     */
    public function testPlayerInSubCannotSeePlayersInBase()
    {
        echo "🧪 Тест 2: Игрок в подлокации НЕ должен видеть игроков из базовой локации\n";
        
        // Игрок в подлокации (Lunarius) ищет врагов (Solarius)
        $enemyPlayers = $this->cacheService->getCachedPlayersInLocation(
            $this->subLocation->name,
            'solarius',
            $this->playerInSub->id
        );
        
        $foundPlayerInBase = $enemyPlayers->contains('id', $this->playerInBase->id);
        
        if (!$foundPlayerInBase) {
            echo "   ✅ ПРОЙДЕН: Игрок в подлокации НЕ видит игрока из базовой локации\n";
            return true;
        } else {
            echo "   ❌ ПРОВАЛЕН: Игрок в подлокации видит игрока из базовой локации (БАГ!)\n";
            return false;
        }
    }

    /**
     * Тест 3: Игрок в базовой локации НЕ должен видеть ботов из подлокации
     */
    public function testPlayerInBaseCannotSeeBotsInSub()
    {
        echo "🧪 Тест 3: Игрок в базовой локации НЕ должен видеть ботов из подлокации\n";
        
        // Игрок в базовой локации (Solarius) ищет ботов-врагов (Lunarius)
        $enemyBots = $this->cacheService->getCachedBotsInLocation(
            $this->baseLocation->name,
            'lunarius'
        );
        
        $foundBotInSub = $enemyBots->contains('id', $this->botInSub->id);
        
        if (!$foundBotInSub) {
            echo "   ✅ ПРОЙДЕН: Игрок в базовой локации НЕ видит бота из подлокации\n";
            return true;
        } else {
            echo "   ❌ ПРОВАЛЕН: Игрок в базовой локации видит бота из подлокации (БАГ!)\n";
            return false;
        }
    }

    /**
     * Тест 4: Игрок в подлокации НЕ должен видеть ботов из базовой локации
     */
    public function testPlayerInSubCannotSeeBotsInBase()
    {
        echo "🧪 Тест 4: Игрок в подлокации НЕ должен видеть ботов из базовой локации\n";
        
        // Игрок в подлокации (Lunarius) ищет ботов-врагов (Solarius)
        $enemyBots = $this->cacheService->getCachedBotsInLocation(
            $this->subLocation->name,
            'solarius'
        );
        
        $foundBotInBase = $enemyBots->contains('id', $this->botInBase->id);
        
        if (!$foundBotInBase) {
            echo "   ✅ ПРОЙДЕН: Игрок в подлокации НЕ видит бота из базовой локации\n";
            return true;
        } else {
            echo "   ❌ ПРОВАЛЕН: Игрок в подлокации видит бота из базовой локации (БАГ!)\n";
            return false;
        }
    }

    /**
     * Тест 5: Игрок в базовой локации ДОЛЖЕН видеть ботов в той же базовой локации
     */
    public function testPlayerInBaseCanSeeBotsInSameBase()
    {
        echo "🧪 Тест 5: Игрок в базовой локации ДОЛЖЕН видеть ботов в той же базовой локации\n";
        
        // Игрок в базовой локации (Solarius) ищет ботов-врагов (Lunarius)
        $enemyBots = $this->cacheService->getCachedBotsInLocation(
            $this->baseLocation->name,
            'lunarius'
        );
        
        $foundBotInBase = $enemyBots->contains('id', $this->botInBase->id);
        
        if ($foundBotInBase) {
            echo "   ✅ ПРОЙДЕН: Игрок в базовой локации видит бота в той же базовой локации\n";
            return true;
        } else {
            echo "   ❌ ПРОВАЛЕН: Игрок в базовой локации НЕ видит бота в той же базовой локации (БАГ!)\n";
            return false;
        }
    }

    /**
     * Тест 6: Игрок в подлокации ДОЛЖЕН видеть ботов в той же подлокации
     */
    public function testPlayerInSubCanSeeBotsInSameSub()
    {
        echo "🧪 Тест 6: Игрок в подлокации ДОЛЖЕН видеть ботов в той же подлокации\n";
        
        // Игрок в подлокации (Lunarius) ищет ботов-врагов (Solarius)
        $enemyBots = $this->cacheService->getCachedBotsInLocation(
            $this->subLocation->name,
            'solarius'
        );
        
        $foundBotInSub = $enemyBots->contains('id', $this->botInSub->id);
        
        if ($foundBotInSub) {
            echo "   ✅ ПРОЙДЕН: Игрок в подлокации видит бота в той же подлокации\n";
            return true;
        } else {
            echo "   ❌ ПРОВАЛЕН: Игрок в подлокации НЕ видит бота в той же подлокации (БАГ!)\n";
            return false;
        }
    }

    /**
     * Запуск всех тестов
     */
    public function runAllTests()
    {
        echo "🚀 Запуск тестов строгой изоляции PvP в рудниках\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
        
        $results = [];
        
        $results[] = $this->testPlayerInBaseCannotSeePlayersInSub();
        $results[] = $this->testPlayerInSubCannotSeePlayersInBase();
        $results[] = $this->testPlayerInBaseCannotSeeBotsInSub();
        $results[] = $this->testPlayerInSubCannotSeeBotsInBase();
        $results[] = $this->testPlayerInBaseCanSeeBotsInSameBase();
        $results[] = $this->testPlayerInSubCanSeeBotsInSameSub();
        
        $passed = array_sum($results);
        $total = count($results);
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 Результаты тестов: {$passed}/{$total} пройдено\n";
        
        if ($passed === $total) {
            echo "🎉 Все тесты пройдены! Строгая изоляция PvP в рудниках работает корректно.\n";
        } else {
            echo "⚠️  Некоторые тесты не прошли. Необходимо дополнительное исправление.\n";
        }
        
        echo "\n";
        
        return $passed === $total;
    }

    /**
     * Очистка тестовых данных
     */
    public function cleanup()
    {
        echo "🧹 Очистка тестовых данных...\n";
        
        // Не удаляем данные в production environment
        if (app()->environment('production')) {
            echo "   ⚠️  Пропущена очистка в production среде\n";
            return;
        }
        
        // Удаляем тестовые записи
        $this->playerInBase->delete();
        $this->playerInSub->delete();
        $this->botInBase->delete();
        $this->botInSub->delete();
        $this->subLocation->delete();
        $this->baseLocation->delete();
        
        echo "   ✅ Тестовые данные очищены\n";
    }
}

// Запуск тестов
if (php_sapi_name() === 'cli') {
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();
    
    $test = new MinePvPIsolationTest();
    $success = $test->runAllTests();
    
    if (isset($argv[1]) && $argv[1] === '--cleanup') {
        $test->cleanup();
    }
    
    exit($success ? 0 : 1);
}
-- Скрипт для исправления локаций ботов в рудниках
-- Запускать только после диагностики!

-- 1. Проверяем текущее состояние
SELECT 
    'ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ' as info,
    COUNT(*) as total_bots,
    COUNT(CASE WHEN mine_location_id IS NOT NULL THEN 1 END) as mine_bots,
    COUNT(CASE WHEN mine_location_id IS NOT NULL AND location != (SELECT name FROM mine_locations WHERE id = mine_location_id) THEN 1 END) as broken_bots
FROM bots;

-- 2. Показываем проблемные боты
SELECT 
    'ПРОБЛЕМНЫЕ БОТЫ' as info,
    b.id,
    b.name,
    b.race,
    b.location as current_location,
    ml.name as should_be_location,
    b.mine_location_id
FROM bots b
JOIN mine_locations ml ON b.mine_location_id = ml.id
WHERE b.location != ml.name;

-- 3. ИСПРАВЛЕНИЕ: Обновляем локации ботов в рудниках
UPDATE bots b
JOIN mine_locations ml ON b.mine_location_id = ml.id
SET b.location = ml.name
WHERE b.mine_location_id IS NOT NULL 
  AND b.location != ml.name;

-- 4. Проверяем результат
SELECT 
    'РЕЗУЛЬТАТ ИСПРАВЛЕНИЯ' as info,
    COUNT(*) as total_bots,
    COUNT(CASE WHEN mine_location_id IS NOT NULL THEN 1 END) as mine_bots,
    COUNT(CASE WHEN mine_location_id IS NOT NULL AND location != (SELECT name FROM mine_locations WHERE id = mine_location_id) THEN 1 END) as still_broken
FROM bots;

-- 5. Показываем распределение ботов по локациям
SELECT 
    'РАСПРЕДЕЛЕНИЕ ПО ЛОКАЦИЯМ' as info,
    b.location,
    COUNT(*) as bot_count,
    GROUP_CONCAT(CONCAT(b.name, ' (', b.race, ')') SEPARATOR ', ') as bots
FROM bots b
WHERE b.location LIKE '%рудник%' OR b.mine_location_id IS NOT NULL
GROUP BY b.location
ORDER BY b.location;

-- 6. Проверяем соответствие mine_location_id и location
SELECT 
    'ПРОВЕРКА СООТВЕТСТВИЯ' as info,
    b.id,
    b.name,
    b.location,
    ml.name as mine_location_name,
    CASE 
        WHEN b.location = ml.name THEN 'OK'
        ELSE 'ПРОБЛЕМА'
    END as status
FROM bots b
JOIN mine_locations ml ON b.mine_location_id = ml.id
ORDER BY status DESC, b.location;
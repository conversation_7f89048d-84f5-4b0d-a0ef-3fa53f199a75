<?php

namespace App\Services;

use App\Models\User;
use App\Models\Location;
use Illuminate\Support\Facades\Cache;
use Illuminate\Contracts\Auth\Authenticatable;

class LocationAccessService
{
    /**
     * Кеш локаций для оптимизации запросов
     */
    protected $locationsCache = null;

    /**
     * Конфигурация заблокированных локаций
     * Ключ - название локации, значение - массив ролей, которым разрешен доступ
     */
    private array $blockedLocations = [
        // 'Рудники' => ['admin'], // Рудники открыты для всех пользователей
    ];

    /**
     * Конструктор сервиса
     */
    public function __construct()
    {
        // ИСПРАВЛЕНО: Убираем загрузку локаций из конструктора
        // Теперь локации загружаются лениво при первом обращении
    }

    /**
     * Загружает все локации из базы данных или кеша
     */
    protected function loadLocations(): void
    {
        // Если локации уже загружены, не загружаем повторно
        if ($this->locationsCache !== null) {
            return;
        }

        try {
            // Проверяем, есть ли данные в кеше
            $cachedData = Cache::get('locations_data');

            if ($cachedData) {
                $this->locationsCache = $cachedData;
                return;
            }

            // Загружаем данные из базы
            $locations = Location::where('is_active', true)->orderBy('order')->get();

            // Преобразуем в массив с ключами по названию
            $this->locationsCache = [];

            foreach ($locations as $location) {
                // Определяем тип локации для фильтрации
                $locationType = $location->location_type ?? 'main';

                // Добавляем все необходимые поля
                $this->locationsCache[$location->name] = [
                    'id' => $location->id,
                    'name' => $location->name,
                    'description' => $location->description,
                    'image_path' => $location->image_path ?: 'default-location.jpg', // Замена на изображение по умолчанию, если нет
                    'min_level' => $location->min_level ?: 1,
                    'max_level' => $location->max_level ?: 0,
                    'route_name' => $location->route_name ?: 'battle.outposts.index', // Маршрут по умолчанию
                    'is_active' => $location->is_active ?? true,
                    'order' => $location->order ?: 0,
                    'gs_requirement' => $location->gs_requirement ?: 0,
                    'location_type' => $locationType,
                    'parent_id' => $location->parent_id,
                    'slug' => $location->slug // Используем только slug из базы данных, без генерации из названия
                ];
            }

            // Сохраняем в кеш на короткое время, чтобы изменения быстрее отображались
            Cache::put('locations_data', $this->locationsCache, now()->addMinutes(5));

        } catch (\Exception $e) {
            // ИСПРАВЛЕНО: Если БД недоступна, используем пустой массив
            // Это предотвращает падение приложения при проблемах с БД
            $this->locationsCache = [];

            // Логируем ошибку для отладки
            \Illuminate\Support\Facades\Log::warning('LocationAccessService: Не удалось загрузить локации из БД', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Проверяет, имеет ли пользователь доступ к указанной локации
     *
     * @param Authenticatable $user Пользователь
     * @param string $locationName Название локации
     * @return bool Имеет ли пользователь доступ
     */
    public function canAccessLocation(Authenticatable $user, string $locationName): bool
    {
        // Сначала проверяем блокировку локации
        if ($this->isLocationBlocked($locationName, $user)) {
            return false;
        }

        // Проверяем ограничения для смешанных рас в группе
        if ($this->isRestrictedForMixedRaceParty($user, $locationName)) {
            return false;
        }

        // Если локации нет в кеше, обновляем кеш
        if ($this->locationsCache === null || !isset($this->locationsCache[$locationName])) {
            $this->loadLocations();

            // Если после обновления кеша локация всё равно не найдена,
            // но она не заблокирована, разрешаем доступ (для совместимости)
            if (!isset($this->locationsCache[$locationName])) {
                return true; // Изменено с false на true
            }
        }

        $userLevel = $user->profile->level ?? 1;
        $location = $this->locationsCache[$locationName];

        // Проверяем минимальный уровень
        if ($userLevel < $location['min_level']) {
            return false;
        }

        // Проверяем максимальный уровень (если он установлен)
        if ($location['max_level'] > 0 && $userLevel > $location['max_level']) {
            return false;
        }

        // Проверяем требование к боевой силе (GearScore)
        if ($location['gs_requirement'] > 0) {
            $userGearScore = $user->profile->gs ?? 0;
            if ($userGearScore < $location['gs_requirement']) {
                return false;
            }
        }

        return true;
    }

    /**
     * Проверяет, ограничена ли локация для пользователя в группе со смешанными расами
     *
     * @param Authenticatable $user Пользователь
     * @param string $locationName Название локации
     * @return bool Ограничена ли локация
     */
    private function isRestrictedForMixedRaceParty(Authenticatable $user, string $locationName): bool
    {
        // Список локаций, доступных для смешанных рас (только подземелья)
        $allowedMixedRaceLocations = [
            'Подземелье',
            'Подземелье новичков',
            'Подземелье опытных',
            'Подземелье мастеров',
            'Подземелье легенд',
            'Данж',
            'Dungeon',
            'Катакомбы',
            'Склеп',
            'Лабиринт',
            'Пещера',
            'Грот'
        ];

        // Список локаций, запрещенных для смешанных рас
        $forbiddenMixedRaceLocations = [
            'Аванпост',
            'Аванпосты',
            'Рудник',
            'Рудники',
            'Шахта',
            'Шахты',
            'Форт',
            'Крепость',
            'Застава',
            'Поселение',
            'Деревня',
            'Город',
            'Столица',
            'Арена',
            'Колизей',
            'Поле боя',
            'Лес',
            'Равнина',
            'Степь',
            'Болото',
            'Пустыня',
            'Горы',
            'Холмы'
        ];

        // Проверяем, есть ли в группе игроки противоположной расы
        $partyService = app(\App\Services\PartyService::class);
        if (!$partyService->hasOppositeRaceMembers($user)) {
            return false; // Нет ограничений для одной расы
        }

        // Сначала проверяем, является ли локация подземельем (разрешенная)
        foreach ($allowedMixedRaceLocations as $allowedLocation) {
            if (stripos($locationName, $allowedLocation) !== false) {
                return false; // Разрешено
            }
        }

        // Затем проверяем, является ли локация запрещенной
        foreach ($forbiddenMixedRaceLocations as $forbiddenLocation) {
            if (stripos($locationName, $forbiddenLocation) !== false) {
                return true; // Запрещено
            }
        }

        // Если локация не найдена в списках, по умолчанию запрещаем доступ для смешанных рас
        return true;
    }

    /**
     * Проверяет, заблокирована ли локация для пользователя
     *
     * @param string $locationName Название локации
     * @param Authenticatable $user Пользователь
     * @return bool
     */
    public function isLocationBlocked(string $locationName, Authenticatable $user): bool
    {
        // Проверяем, есть ли локация в списке заблокированных
        if (!isset($this->blockedLocations[$locationName])) {
            return false;
        }

        $allowedRoles = $this->blockedLocations[$locationName];

        // Если массив пустой, значит локация заблокирована для всех
        if (empty($allowedRoles)) {
            return true;
        }

        // Проверяем, есть ли роль пользователя в списке разрешенных
        return !in_array($user->role, $allowedRoles);
    }

    /**
     * Получает список всех доступных локаций для пользователя
     *
     * @param Authenticatable $user Пользователь
     * @return array Массив доступных локаций
     */
    public function getAccessibleLocations(Authenticatable $user): array
    {
        if ($this->locationsCache === null) {
            $this->loadLocations();
        }

        $userLevel = $user->profile->level ?? 1;
        $userGearScore = $user->profile->gear_score ?? 0;
        $accessibleLocations = [];

        foreach ($this->locationsCache as $locationName => $location) {
            // Проверяем только ограничения по уровню, а требование по GS является рекомендательным
            if (
                $userLevel >= $location['min_level'] &&
                ($location['max_level'] === 0 || $userLevel <= $location['max_level'])
            ) {
                $accessibleLocations[] = $locationName;
            }
        }

        return $accessibleLocations;
    }

    /**
     * Получает причину, почему локация недоступна
     *
     * @param Authenticatable $user Пользователь
     * @param string $locationName Название локации
     * @return string|null Причина недоступности или null, если локация доступна
     */
    public function getAccessDeniedReason(Authenticatable $user, string $locationName): ?string
    {
        // Сначала проверяем блокировку локации
        if ($this->isLocationBlocked($locationName, $user)) {
            return $this->getBlockedLocationReason($locationName);
        }

        // Проверяем ограничения для смешанных рас в группе
        if ($this->isRestrictedForMixedRaceParty($user, $locationName)) {
            return "Вы не можете попасть в данную локацию находясь в группе с игроком противоположной расы. Для входа в эту локацию необходимо покинуть группу или войти в подземелье.";
        }

        if ($this->locationsCache === null || !isset($this->locationsCache[$locationName])) {
            $this->loadLocations();

            // Если после обновления кеша локация всё равно не найдена,
            // но она не заблокирована, разрешаем доступ
            if (!isset($this->locationsCache[$locationName])) {
                return null; // Изменено - не блокируем доступ к несуществующим локациям
            }
        }

        $userLevel = $user->profile->level ?? 1;
        $userGearScore = $user->profile->gear_score ?? 0;
        $location = $this->locationsCache[$locationName];

        if ($userLevel < $location['min_level']) {
            return "Для входа в локацию '{$locationName}' требуется минимум {$location['min_level']} уровень.";
        }

        // Если max_level = 0, это означает, что ограничения по максимальному уровню нет
        if ($location['max_level'] > 0 && $userLevel > $location['max_level']) {
            return "Локация '{$locationName}' доступна только до {$location['max_level']} уровня.";
        }

        // Требование по боевой силе является рекомендательным, а не обязательным
        // Не блокируем доступ к локации по требованию GS

        return null;
    }

    /**
     * Получает все активные локации
     *
     * @param string|null $type Тип локаций для фильтрации (outpost, mine и т.д.)
     * @return array Массив всех активных локаций
     */
    public function getAllLocations(string $type = null): array
    {
        if ($this->locationsCache === null) {
            $this->loadLocations();
        }

        // Фильтруем только активные локации
        return array_filter($this->locationsCache, function ($location) use ($type) {
            // Проверяем, что локация активна
            $isActive = $location['is_active'] ?? true;

            // Если тип не указан, возвращаем все активные локации
            if ($type === null) {
                return $isActive;
            }

            // Фильтруем по типу
            $locationType = $location['location_type'] ?? 'main';

            // Специальная обработка для аванпостов
            if ($type === 'outpost') {
                return $isActive && $locationType === 'outpost';
            }

            return $isActive && $locationType === $type;
        });
    }

    /**
     * Получает все активные аванпосты
     *
     * @return array Массив всех активных аванпостов
     */
    public function getOutposts(): array
    {
        // Получаем все локации типа 'outpost'
        $outposts = $this->getAllLocations('outpost');

        // Получаем список location_id активных аванпостов из таблицы OutpostLocation
        $activeOutpostLocationIds = \App\Models\OutpostLocation::where('is_active', true)->pluck('location_id')->toArray();

        // Фильтруем аванпосты, оставляя только те, которые существуют в таблице OutpostLocation и активны
        return array_filter($outposts, function ($outpost) use ($activeOutpostLocationIds) {
            return isset($outpost['id']) && in_array($outpost['id'], $activeOutpostLocationIds);
        });
    }

    /**
     * Очищает кеш локаций
     *
     * @return void
     */
    public function clearCache(): void
    {
        Cache::forget('locations_data');
        $this->locationsCache = null;
    }

    /**
     * Получает причину блокировки локации
     *
     * @param string $locationName Название локации
     * @return string
     */
    private function getBlockedLocationReason(string $locationName): string
    {
        return "Локация '{$locationName}' находится на доработке и временно недоступна.";
    }

    /**
     * Получает список заблокированных локаций
     *
     * @return array
     */
    public function getBlockedLocations(): array
    {
        return $this->blockedLocations;
    }

    /**
     * Добавляет локацию в список заблокированных
     *
     * @param string $locationName Название локации
     * @param array $allowedRoles Роли, которым разрешен доступ
     * @return void
     */
    public function blockLocation(string $locationName, array $allowedRoles = []): void
    {
        $this->blockedLocations[$locationName] = $allowedRoles;
    }

    /**
     * Убирает локацию из списка заблокированных
     *
     * @param string $locationName Название локации
     * @return void
     */
    public function unblockLocation(string $locationName): void
    {
        unset($this->blockedLocations[$locationName]);
    }
}

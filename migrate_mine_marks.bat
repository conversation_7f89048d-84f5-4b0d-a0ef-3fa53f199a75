@echo off
echo Запуск миграции для системы автоатак в рудниках...
echo ================================================

echo 1. Проверка статуса миграций...
php artisan migrate:status

echo.
echo 2. Запуск миграции mine_marks...
php artisan migrate --path=database/migrations/2025_07_19_120000_create_mine_marks_table.php --force

echo.
echo 3. Проверка созданной таблицы...
php artisan tinker --execute="
echo 'Таблица mine_marks существует: ' . (Schema::hasTable('mine_marks') ? 'ДА' : 'НЕТ') . PHP_EOL;
if (Schema::hasTable('mine_marks')) {
    echo 'Колонки: ' . implode(', ', Schema::getColumnListing('mine_marks')) . PHP_EOL;
}
"

echo.
echo 4. Тестирование настройки...
php setup_mine_detection_system.php

echo.
echo ================================================
echo Миграция завершена! Теперь можно тестировать систему.
pause
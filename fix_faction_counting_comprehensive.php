<?php

/**
 * КОМПЛЕКСНОЕ ИСПРАВЛЕНИЕ ПРОБЛЕМ СЧЕТЧИКА СТАТУСА ФРАКЦИЙ
 * 
 * Проблема: В счетчике фракций отображается 1 лунариус маг, но игроки других 
 * рас не могут его видеть в бою. Также нужно убрать игроков/ботов которые 
 * покинули локацию или имеют 0 HP.
 * 
 * Решение:
 * 1. Очистка мертвых/неактивных ботов из всех локаций
 * 2. Удаление игроков с 0 HP из онлайн статуса
 * 3. Исправление некорректных локаций ботов
 * 4. Синхронизация данных между админкой и компонентом статуса фракций
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Services\battle\FactionCountService;
use App\Services\battle\UserLocationService;
use App\Services\RedisOnlineLocationService;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 КОМПЛЕКСНОЕ ИСПРАВЛЕНИЕ СЧЕТЧИКА ФРАКЦИЙ\n";
echo "============================================\n\n";

// Инициализируем сервисы
$factionCountService = app(FactionCountService::class);
$userLocationService = app(UserLocationService::class);
$redisOnlineService = app(RedisOnlineLocationService::class);

$fixedIssues = [];
$totalFixed = 0;

// 1. ПОИСК И УДАЛЕНИЕ МЕРТВЫХ/НЕАКТИВНЫХ БОТОВ
echo "1. ОЧИСТКА МЕРТВЫХ И НЕАКТИВНЫХ БОТОВ\n";
echo "===================================\n";

// Находим всех мертвых ботов
$deadBots = Bot::where(function($query) {
    $query->where('hp', '<=', 0)
          ->orWhere('is_active', false)
          ->orWhereNotNull('death_time');
})->get();

echo "Найдено мертвых/неактивных ботов: " . $deadBots->count() . "\n";

$deadBotsFixed = 0;
foreach ($deadBots as $bot) {
    // Логируем удаление мертвого бота
    echo "  🗑️ Удаляем мертвого бота: {$bot->name} (HP: {$bot->hp}, активен: " . ($bot->is_active ? 'да' : 'нет') . ")\n";
    
    // Мягкое удаление - просто деактивируем
    $bot->update([
        'is_active' => false,
        'hp' => 0,
        'death_time' => now(),
        'location' => null,
        'mine_location_id' => null
    ]);
    
    $deadBotsFixed++;
}

$fixedIssues[] = "Деактивировано мертвых ботов: $deadBotsFixed";
$totalFixed += $deadBotsFixed;

echo "✅ Очищено мертвых ботов: $deadBotsFixed\n\n";

// 2. ОЧИСТКА ИГРОКОВ С 0 HP ИЗ ОНЛАЙН СТАТУСА
echo "2. ОЧИСТКА ИГРОКОВ С 0 HP ИЗ ОНЛАЙН СТАТУСА\n";
echo "==========================================\n";

$deadPlayers = User::whereHas('profile', function($query) {
    $query->where('current_hp', '<=', 0);
})->where('last_activity_timestamp', '>=', now()->subMinutes(15)->timestamp)
->get();

echo "Найдено мертвых игроков в онлайне: " . $deadPlayers->count() . "\n";

$deadPlayersFixed = 0;
foreach ($deadPlayers as $player) {
    echo "  💀 Игрок {$player->name} мертв (HP: {$player->profile->current_hp}), убираем из онлайна\n";
    
    // Убираем из Redis онлайн статуса
    $redisOnlineService->setUserOffline($player->id);
    
    // Обновляем время последней активности (делаем старым)
    $player->update([
        'last_activity_timestamp' => now()->subHours(1)->timestamp
    ]);
    
    $deadPlayersFixed++;
}

$fixedIssues[] = "Убрано мертвых игроков из онлайна: $deadPlayersFixed";
$totalFixed += $deadPlayersFixed;

echo "✅ Очищено мертвых игроков из онлайна: $deadPlayersFixed\n\n";

// 3. ПОИСК И ИСПРАВЛЕНИЕ НЕКОРРЕКТНЫХ ЛОКАЦИЙ БОТОВ
echo "3. ИСПРАВЛЕНИЕ НЕКОРРЕКТНЫХ ЛОКАЦИЙ БОТОВ\n";
echo "========================================\n";

// Найти ботов с числовыми локациями (ID вместо названий)
$botsWithNumericLocations = Bot::where('is_active', true)
    ->where('hp', '>', 0)
    ->whereRaw('location REGEXP "^[0-9]+$"')
    ->get();

echo "Найдено ботов с числовыми локациями: " . $botsWithNumericLocations->count() . "\n";

$numericLocationFixed = 0;
foreach ($botsWithNumericLocations as $bot) {
    $locationId = (int) $bot->location;
    
    // Ищем MineLocation с таким ID
    $mineLocation = MineLocation::find($locationId);
    if ($mineLocation) {
        echo "  🔧 Исправляем бота {$bot->name}: локация '{$bot->location}' → '{$mineLocation->name}'\n";
        
        $bot->update([
            'location' => $mineLocation->name,
            'mine_location_id' => $mineLocation->id
        ]);
        
        $numericLocationFixed++;
    } else {
        // Если не нашли соответствующую локацию, деактивируем бота
        echo "  ❌ Деактивируем бота {$bot->name}: не найдена локация с ID {$locationId}\n";
        
        $bot->update([
            'is_active' => false,
            'location' => null,
            'mine_location_id' => null
        ]);
        
        $numericLocationFixed++;
    }
}

$fixedIssues[] = "Исправлено ботов с числовыми локациями: $numericLocationFixed";
$totalFixed += $numericLocationFixed;

echo "✅ Исправлено ботов с числовыми локациями: $numericLocationFixed\n\n";

// 4. ПОИСК И ИСПРАВЛЕНИЕ БОТОВ БЕЗ MINE_LOCATION_ID В РУДНИКАХ
echo "4. ИСПРАВЛЕНИЕ БОТОВ БЕЗ MINE_LOCATION_ID В РУДНИКАХ\n";
echo "=================================================\n";

$botsInMinesWithoutMineId = Bot::where('is_active', true)
    ->where('hp', '>', 0)
    ->whereNull('mine_location_id')
    ->whereExists(function($query) {
        $query->select(DB::raw(1))
              ->from('mine_locations')
              ->whereRaw('mine_locations.name = bots.location');
    })
    ->get();

echo "Найдено ботов в рудниках без mine_location_id: " . $botsInMinesWithoutMineId->count() . "\n";

$mineIdFixed = 0;
foreach ($botsInMinesWithoutMineId as $bot) {
    $mineLocation = MineLocation::where('name', $bot->location)->first();
    if ($mineLocation) {
        echo "  🔧 Добавляем mine_location_id боту {$bot->name} в локации '{$bot->location}'\n";
        
        $bot->update([
            'mine_location_id' => $mineLocation->id
        ]);
        
        $mineIdFixed++;
    }
}

$fixedIssues[] = "Исправлено ботов без mine_location_id: $mineIdFixed";
$totalFixed += $mineIdFixed;

echo "✅ Исправлено ботов без mine_location_id: $mineIdFixed\n\n";

// 5. ПОИСК И ОЧИСТКА ИГРОКОВ, ПОКИНУВШИХ ЛОКАЦИИ
echo "5. ОЧИСТКА ИГРОКОВ, ПОКИНУВШИХ ЛОКАЦИИ\n";
echo "=====================================\n";

// Находим игроков, которые давно не были активны, но все еще в онлайне
$inactivePlayers = User::where('last_activity_timestamp', '<', now()->subMinutes(15)->timestamp)
    ->get();

echo "Найдено неактивных игроков для очистки: " . $inactivePlayers->count() . "\n";

$inactivePlayersFixed = 0;
foreach ($inactivePlayers as $player) {
    // Убираем из Redis онлайн статуса
    $redisOnlineService->setUserOffline($player->id);
    $inactivePlayersFixed++;
}

$fixedIssues[] = "Убрано неактивных игроков из онлайна: $inactivePlayersFixed";
$totalFixed += $inactivePlayersFixed;

echo "✅ Очищено неактивных игроков: $inactivePlayersFixed\n\n";

// 6. ПОИСК ПРОБЛЕМНЫХ ЛОКАЦИЙ С НЕОБЫЧНЫМИ ИМЕНАМИ
echo "6. ПОИСК ПРОБЛЕМНЫХ ЛОКАЦИЙ\n";
echo "===========================\n";

// Ищем локации с подозрительными именами
$suspiciousLocations = DB::table('mine_locations')
    ->where(function($query) {
        $query->where('name', 'like', '%aaaa%')
              ->orWhere('name', 'like', '%ааа%')
              ->orWhere('name', 'like', '%xxx%')
              ->orWhere('name', 'like', '%zzz%')
              ->orWhereRaw('CHAR_LENGTH(name) > 15');
    })
    ->get();

echo "Найдено подозрительных локаций: " . $suspiciousLocations->count() . "\n";

foreach ($suspiciousLocations as $location) {
    echo "  ⚠️ Подозрительная локация: '{$location->name}' (ID: {$location->id})\n";
    
    // Проверяем, есть ли в ней боты
    $botsInSuspiciousLocation = Bot::where('location', $location->name)
        ->orWhere('mine_location_id', $location->id)
        ->get();
    
    if ($botsInSuspiciousLocation->count() > 0) {
        echo "    🤖 В локации найдено ботов: " . $botsInSuspiciousLocation->count() . "\n";
        
        foreach ($botsInSuspiciousLocation as $bot) {
            echo "      - {$bot->name} (race: {$bot->race}, class: {$bot->class}, HP: {$bot->hp})\n";
            
            // Если локация явно проблемная (много повторяющихся символов), деактивируем ботов
            if (preg_match('/^(.)\1{5,}/', $location->name)) {
                echo "      ❌ Деактивируем бота из проблемной локации\n";
                
                $bot->update([
                    'is_active' => false,
                    'location' => null,
                    'mine_location_id' => null
                ]);
                
                $totalFixed++;
            }
        }
    }
}

// 7. ПРОВЕРКА РЕЗУЛЬТАТА - ТЕСТИРОВАНИЕ НЕСКОЛЬКИХ ЛОКАЦИЙ
echo "\n7. ПРОВЕРКА РЕЗУЛЬТАТА\n";
echo "=====================\n";

$testLocations = [
    'Шахта Новичков',
    'Рудник Солариус', 
    'Рудник Лунариус',
    'Городская Площадь'
];

foreach ($testLocations as $location) {
    echo "\nТестирование локации: {$location}\n";
    echo str_repeat('-', 40) . "\n";
    
    $factionCounts = $factionCountService->getLocationFactionCounts($location);
    
    $solariumTotal = $factionCounts['total_counts']['solarius']['total'];
    $lunariumTotal = $factionCounts['total_counts']['lunarius']['total'];
    
    echo "Солариус: {$solariumTotal} (";
    echo "воины: {$factionCounts['total_counts']['solarius']['warriors']}, ";
    echo "маги: {$factionCounts['total_counts']['solarius']['mages']}, ";
    echo "жрецы: {$factionCounts['total_counts']['solarius']['knights']})";
    echo "\n";
    
    echo "Лунариус: {$lunariumTotal} (";
    echo "воины: {$factionCounts['total_counts']['lunarius']['warriors']}, ";
    echo "маги: {$factionCounts['total_counts']['lunarius']['mages']}, ";
    echo "жрецы: {$factionCounts['total_counts']['lunarius']['knights']})";
    echo "\n";
    
    // Проверяем соответствие игроков и ботов
    $playersInLocation = $userLocationService->getPlayersInLocation($location);
    echo "Онлайн игроков в локации: " . $playersInLocation->count() . "\n";
    
    // Проверяем, что все найденные игроки здоровы
    $deadPlayersInLocation = $playersInLocation->filter(function($player) {
        return $player->profile->current_hp <= 0;
    });
    
    if ($deadPlayersInLocation->count() > 0) {
        echo "⚠️ ПРОБЛЕМА: Найдено мертвых игроков в онлайне: " . $deadPlayersInLocation->count() . "\n";
    } else {
        echo "✅ Все онлайн игроки в локации здоровы\n";
    }
}

// 8. ИТОГОВЫЙ ОТЧЕТ
echo "\n8. ИТОГОВЫЙ ОТЧЕТ\n";
echo "================\n";

echo "Всего исправлений: {$totalFixed}\n\n";

echo "Детализация исправлений:\n";
foreach ($fixedIssues as $issue) {
    echo "✅ {$issue}\n";
}

echo "\nРекомендации:\n";
echo "1. Регулярно запускать этот скрипт для поддержания чистоты данных\n";
echo "2. Добавить автоматическую очистку мертвых ботов в scheduler\n";
echo "3. Проверить логику респавна ботов, чтобы избежать создания в некорректных локациях\n";
echo "4. Добавить валидацию при создании ботов через админку\n";

echo "\nВремя выполнения: " . now()->format('H:i:s d.m.Y') . "\n";
echo "\n🎉 ИСПРАВЛЕНИЕ ЗАВЕРШЕНО!\n";
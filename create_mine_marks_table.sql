-- Создание таблицы mine_marks для системы автоатак в рудниках
-- Выполните этот скрипт в PostgreSQL если миграция не работает

-- Проверяем и создаем таблицу только если она не существует
CREATE TABLE IF NOT EXISTS mine_marks (
    id BIGSERIAL PRIMARY KEY,
    
    -- Основные поля
    player_id BIGINT NOT NULL,
    mine_location_id BIGINT NOT NULL,
    location_id BIGINT NOT NULL,
    location_name VARCHAR(255) NOT NULL,
    
    -- Временные поля
    expires_at TIMESTAMP NOT NULL,
    last_attack_at TIMESTAMP NULL,
    
    -- Статусные поля
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    attack_count INTEGER NOT NULL DEFAULT 0,
    
    -- Системные поля Laravel
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);

-- Добавляем комментарии
COMMENT ON TABLE mine_marks IS 'Метки обнаружения игроков в рудниках';
COMMENT ON COLUMN mine_marks.player_id IS 'ID игрока с меткой';
COMMENT ON COLUMN mine_marks.mine_location_id IS 'ID локации рудника';
COMMENT ON COLUMN mine_marks.location_id IS 'ID основной локации';
COMMENT ON COLUMN mine_marks.location_name IS 'Название локации рудника';
COMMENT ON COLUMN mine_marks.expires_at IS 'Время истечения метки';
COMMENT ON COLUMN mine_marks.last_attack_at IS 'Время последней атаки';
COMMENT ON COLUMN mine_marks.is_active IS 'Активна ли метка';
COMMENT ON COLUMN mine_marks.attack_count IS 'Количество атак';

-- Создаем индексы для оптимизации (только если они не существуют)
CREATE INDEX IF NOT EXISTS mine_marks_player_location_idx ON mine_marks (player_id, mine_location_id);
CREATE INDEX IF NOT EXISTS mine_marks_location_active_idx ON mine_marks (mine_location_id, is_active, expires_at);
CREATE INDEX IF NOT EXISTS mine_marks_active_expires_idx ON mine_marks (is_active, expires_at);
CREATE INDEX IF NOT EXISTS mine_marks_location_id_idx ON mine_marks (location_id);
CREATE INDEX IF NOT EXISTS mine_marks_expires_idx ON mine_marks (expires_at);

-- Добавляем внешние ключи (только если таблицы существуют и связи не созданы)
DO $$
BEGIN
    -- Проверяем и добавляем внешний ключ на users
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                      WHERE table_name = 'mine_marks' 
                      AND constraint_name = 'mine_marks_player_id_foreign') THEN
        ALTER TABLE mine_marks 
        ADD CONSTRAINT mine_marks_player_id_foreign 
        FOREIGN KEY (player_id) REFERENCES users(id) ON DELETE CASCADE;
    END IF;
    
    -- Проверяем и добавляем внешний ключ на mine_locations
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'mine_locations')
       AND NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                      WHERE table_name = 'mine_marks' 
                      AND constraint_name = 'mine_marks_mine_location_id_foreign') THEN
        ALTER TABLE mine_marks 
        ADD CONSTRAINT mine_marks_mine_location_id_foreign 
        FOREIGN KEY (mine_location_id) REFERENCES mine_locations(id) ON DELETE CASCADE;
    END IF;
    
    -- Проверяем и добавляем внешний ключ на locations
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'locations')
       AND NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                      WHERE table_name = 'mine_marks' 
                      AND constraint_name = 'mine_marks_location_id_foreign') THEN
        ALTER TABLE mine_marks 
        ADD CONSTRAINT mine_marks_location_id_foreign 
        FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Проверяем результат
SELECT 
    'mine_marks' as table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'mine_marks'
ORDER BY ordinal_position;
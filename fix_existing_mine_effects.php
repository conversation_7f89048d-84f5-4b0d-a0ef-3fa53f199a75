<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 ИСПРАВЛЕНИЕ СУЩЕСТВУЮЩИХ ЭФФЕКТОВ MINE_DETECTION\n";
echo "=" . str_repeat("=", 55) . "\n\n";

// 1. Находим все эффекты с неправильным target_type
echo "1️⃣ Поиск эффектов с неправильным target_type...\n";
$wrongEffects = ActiveEffect::where('effect_type', 'mine_detection')
    ->where('target_type', 'App\\Models\\User')
    ->get();

echo "Найдено эффектов для исправления: {$wrongEffects->count()}\n\n";

if ($wrongEffects->count() > 0) {
    echo "2️⃣ Исправление target_type...\n";
    
    foreach ($wrongEffects as $effect) {
        echo "- Исправляем эффект ID: {$effect->id}\n";
        echo "  Было: '{$effect->target_type}'\n";
        
        $effect->target_type = 'player';
        $effect->save();
        
        echo "  Стало: '{$effect->target_type}'\n\n";
    }
    
    echo "✅ Все эффекты исправлены!\n\n";
} else {
    echo "ℹ️ Нет эффектов для исправления\n\n";
}

// 3. Проверяем результат
echo "3️⃣ Проверка результата...\n";
$allMineEffects = ActiveEffect::where('effect_type', 'mine_detection')->get();
echo "Всего эффектов mine_detection: {$allMineEffects->count()}\n";

foreach ($allMineEffects as $effect) {
    echo "- ID: {$effect->id}, target_type: '{$effect->target_type}', target_id: {$effect->target_id}\n";
}

echo "\n✅ ИСПРАВЛЕНИЕ ЗАВЕРШЕНО!\n";

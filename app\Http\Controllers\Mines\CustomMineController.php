<?php

namespace App\Http\Controllers\Mines;

use App\Http\Controllers\Controller;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\Skill;
use App\Models\SpawnedResource;
use App\Models\User;
use App\Models\UserResource;
use App\Services\BattleLogService;
use App\Services\ExperienceService;
use App\Services\LocationResourceService;
use App\Services\SkillService;
use App\Services\LogFormattingService;
use App\Contracts\DamageCalculator;
use App\Services\CombatFormulaService;
use App\Services\battle\FactionCountService;
use App\Services\MineTargetResetService;
use App\Services\MineDetectionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CustomMineController extends Controller
{
    /**
     * Сервис для работы с логами боя
     */
    protected BattleLogService $battleLogService;

    /**
     * Сервис для работы с опытом
     */
    protected ExperienceService $experienceService;

    /**
     * Сервис для работы с ресурсами локаций
     */
    protected LocationResourceService $resourceService;

    /**
     * Сервис для работы с умениями
     */
    protected SkillService $skillService;

    /**
     * Сервис для расчета урона
     */
    protected DamageCalculator $damageCalculator;

    /**
     * Сервис для форматирования логов
     */
    protected LogFormattingService $logFormatter;

    /**
     * Сервис для расчета урона по формулам
     */
    protected CombatFormulaService $combatFormulaService;

    /**
     * Сервис для подсчета фракций
     */
    protected FactionCountService $factionCountService;

    /**
     * Сервис для сброса целей в рудниках
     */
    protected MineTargetResetService $mineTargetResetService;

    /**
     * Сервис для обнаружения в рудниках
     */
    protected MineDetectionService $mineDetectionService;

    /**
     * Конструктор контроллера
     *
     * @param BattleLogService $battleLogService Сервис для работы с логами боя
     * @param ExperienceService $experienceService Сервис для работы с опытом
     * @param LocationResourceService $resourceService Сервис для работы с ресурсами локаций
     * @param SkillService $skillService Сервис для работы с умениями
     * @param DamageCalculator $damageCalculator Сервис для расчета урона
     * @param LogFormattingService $logFormatter Сервис для форматирования логов
     * @param CombatFormulaService $combatFormulaService Сервис для расчета урона по формулам
     * @param FactionCountService $factionCountService Сервис для подсчета фракций
     * @param MineTargetResetService $mineTargetResetService Сервис для сброса целей в рудниках
     * @param MineDetectionService $mineDetectionService Сервис для обнаружения в рудниках
     */
    public function __construct(
        BattleLogService $battleLogService,
        ExperienceService $experienceService,
        LocationResourceService $resourceService,
        SkillService $skillService,
        DamageCalculator $damageCalculator,
        LogFormattingService $logFormatter,
        CombatFormulaService $combatFormulaService,
        FactionCountService $factionCountService,
        MineTargetResetService $mineTargetResetService,
        MineDetectionService $mineDetectionService
    ) {
        $this->battleLogService = $battleLogService;
        $this->experienceService = $experienceService;
        $this->resourceService = $resourceService;
        $this->skillService = $skillService;
        $this->damageCalculator = $damageCalculator;
        $this->logFormatter = $logFormatter;
        $this->combatFormulaService = $combatFormulaService;
        $this->factionCountService = $factionCountService;
        $this->mineTargetResetService = $mineTargetResetService;
        $this->mineDetectionService = $mineDetectionService;
    }

    /**
     * Отображение страницы локации рудника
     *
     * @param string $slug Идентификатор локации
     * @return \Illuminate\View\View
     */
    public function index($slug)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы просматривать эту страницу.');
        }

        // Загружаем необходимые связи для корректной работы компонентов
        $user->load(['profile', 'statistics']);

        // Проверка актуального HP из Redis
        try {
            $userActualResources = $user->profile->getActualResources();
            $currentHp = $userActualResources['current_hp'];
            $currentMp = $userActualResources['current_mp'];
        } catch (\Exception $e) {
            // При ошибке используем значения из БД
            $currentHp = $user->profile->current_hp ?? $user->profile->hp;
            $currentMp = $user->profile->current_mp ?? $user->profile->mp;
        }

        if ($currentHp <= 0) {
            return redirect()->route('battle.mines.index')
                ->with('error', 'У вас слишком мало здоровья. Восстановитесь!');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;

        // Получаем ID локации
        $locationId = $baseLocation->id;

        // ИСПРАВЛЕНО: Используем централизованный сервис для обновления локации
        // Устанавливаем точную локацию рудника для строгой изоляции
        $locationService = app(\App\Services\battle\UserLocationService::class);
        $locationService->updateUserLocation($user, $mineLocation->name);

        // Логируем обновление локации для отладки
        \Log::debug("CustomMineController: обновление локации игрока (ИСПРАВЛЕНО)", [
            'user_id' => $user->id,
            'mine_location_name' => $mineLocation->name,
            'base_location_name' => $baseLocation->name,
            'set_location' => $mineLocation->name,
            'fix_note' => 'Теперь используется точная локация рудника для строгой изоляции'
        ]);

        // Проверяем и сбрасываем цели при переходе между подлокациями рудника
        $previousMineLocation = $this->mineTargetResetService->getPreviousMineLocationFromSessionById();
        $targetWasReset = $this->mineTargetResetService->checkAndResetTargetsOnMineLocationChange(
            $user,
            $mineLocation,
            $previousMineLocation
        );

        // Сохраняем текущую подлокацию в сессии для следующего перехода
        $this->mineTargetResetService->storeMineLocationInSession($mineLocation);

        // Если цель была сброшена, обновляем объект пользователя
        if ($targetWasReset) {
            $user->refresh();
        }

        // Получаем ресурсы в локации
        // Если это подлокация рудника, используем специальный метод
        if ($mineLocation->isSubLocation()) {
            $resourcesInLocation = $this->resourceService->getActiveResourcesForMineLocation($mineLocation->id);
        } else {
            // Для основной локации рудника получаем только ресурсы с mine_location_id = NULL
            $resourcesInLocation = $this->resourceService->getActiveResourcesForMainMineLocation($locationId);
        }

        // Получаем мобов в локации
        // Если это подлокация рудника, ищем мобов по mine_location_id
        if ($mineLocation->isSubLocation()) {
            $mobsInLocation = Mob::where('mine_location_id', $mineLocation->id)
                ->where('hp', '>', 0)
                ->get();
        } else {
            // Иначе используем обычную логику по названию локации
            $mobsInLocation = Mob::where('location', $mineLocation->name)
                ->where('hp', '>', 0)
                ->get();
        }

        // Получаем ботов противоположной фракции в локации
        // ИСПРАВЛЕНИЕ: Упрощаем логику определения фракций - используем прямой поиск по полю race
        $userRace = $user->profile->race ?? null;

        // Валидация расы пользователя
        if (!$userRace || !in_array($userRace, ['solarius', 'lunarius'])) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'У вашего персонажа не определена раса. Обратитесь к администратору.');
        }

        // Определяем противоположную расу
        $enemyRace = ($userRace === 'solarius') ? 'lunarius' : 'solarius';

        // Получаем ботов для данной локации рудника
        // Учитываем как базовую локацию, так и конкретную локацию рудника
        $botsQuery = \App\Models\Bot::where('is_active', true)
            ->where('hp', '>', 0)
            ->where('race', $enemyRace); // Только боты противоположной фракции

        // Если это подлокация, фильтруем по mine_location_id
        if ($mineLocation->isSubLocation()) {
            $botsQuery->where('mine_location_id', $mineLocation->id);
        } else {
            // Для базовой локации ищем по имени базовой локации
            $botsQuery->where('location', $baseLocation->name)
                ->where(function($query) use ($mineLocation) {
                    $query->whereNull('mine_location_id')
                          ->orWhere('mine_location_id', $mineLocation->id);
                });
        }

        $botsInLocation = $botsQuery->get();

        // Получаем подлокации
        $subLocations = $mineLocation->sublocations()->where('is_active', true)->get();

        // ИСПРАВЛЕНО: Всегда используем getMineFactionCounts для единообразия подсчета
        // Это обеспечивает одинаковую логику для всех типов локаций рудников
        $factionCounts = $this->factionCountService->getMineFactionCounts($mineLocation->id, $userRace);

        \Log::debug("CustomMineController: подсчет фракций", [
            'mine_location_name' => $mineLocation->name,
            'base_location_name' => $baseLocation->name,
            'user_race' => $userRace,
            'is_sub_location' => $mineLocation->isSubLocation(),
            'bots_found' => $botsInLocation->count(),
            'faction_counts' => $factionCounts
        ]);

        // ОТЛАДКА: Логируем результат FactionCountService
        \Log::debug("CustomMineController: результат FactionCountService", [
            'mine_location' => $mineLocation->name,
            'base_location' => $baseLocation->name,
            'search_location' => $baseLocation->name,
            'faction_counts' => $factionCounts
        ]);

        // Извлекаем данные из результата FactionCountService (используем total_counts для суммы игроков и ботов)
        $solWarriors = $factionCounts['total_counts']['solarius']['warriors'];
        $solMages = $factionCounts['total_counts']['solarius']['mages'];
        $solKnights = $factionCounts['total_counts']['solarius']['knights'];
        $lunWarriors = $factionCounts['total_counts']['lunarius']['warriors'];
        $lunMages = $factionCounts['total_counts']['lunarius']['mages'];
        $lunKnights = $factionCounts['total_counts']['lunarius']['knights'];

        // Получаем текущую цель пользователя
        $targetResource = null;
        $targetMob = null;
        $targetBot = null;
        $target = null;

        if ($user->current_target_type === 'resource' && $user->current_target_id) {
            $targetResource = SpawnedResource::with('resource')
                ->where('id', $user->current_target_id)
                ->where('is_active', true)
                ->first();
        } elseif ($user->current_target_type === 'mob' && $user->current_target_id) {
            $targetMob = Mob::find($user->current_target_id);
        } elseif ($user->current_target_type === 'bot' && $user->current_target_id) {
            // Получаем выбранного бота с проверкой фракции и локации
            $targetBot = \App\Models\Bot::where('id', $user->current_target_id)
                ->where('location', $baseLocation->name) // Проверяем локацию
                ->where('race', $enemyRace) // Только боты противоположной фракции
                ->where('is_active', true)
                ->where('hp', '>', 0)
                ->first();

            // Логируем для отладки
            Log::info('Получение выбранного бота', [
                'user_id' => $user->id,
                'target_bot_id' => $user->current_target_id,
                'base_location' => $baseLocation->name,
                'enemy_race' => $enemyRace,
                'bot_found' => $targetBot ? true : false,
                'bot_name' => $targetBot ? $targetBot->name : null
            ]);

            // Если бот не найден, сбрасываем цель
            if (!$targetBot) {
                Log::warning('Выбранный бот не найден, сбрасываем цель', [
                    'user_id' => $user->id,
                    'target_bot_id' => $user->current_target_id,
                    'base_location' => $baseLocation->name
                ]);

                $user->current_target_type = null;
                $user->current_target_id = null;
                $user->save();
            }
        } elseif ($user->current_target_type === 'player' && $user->current_target_id) {
            $target = User::with('profile')->find($user->current_target_id);
        }

        // Получаем логи боя
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $mineLocation->slug);
        $battleLogs = $this->battleLogService->getLogs($battleLogKey);

        // Получаем умения пользователя
        $userSkills = $this->skillService->getUserSkills($user);

        // Получаем все эффекты пользователя с связанными моделями, а затем фильтруем активные
        $allUserEffects = $user->activeEffects()->with('skill')->get();
        $userEffects = $allUserEffects->filter(fn($effect) => $effect->isActive());
        
        // Логирование для отладки активных эффектов
        Log::info('CustomMineController: загрузка активных эффектов', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'total_effects' => $allUserEffects->count(),
            'active_effects' => $userEffects->count(),
            'effect_types' => $userEffects->pluck('effect_type')->toArray(),
            'effect_names' => $userEffects->pluck('effect_name')->toArray()
        ]);

        // Определяем, оглушен ли пользователь
        $isStunned = $userEffects->contains(function ($effect) {
            return $effect->skill_id == 14 && $effect->isActive();
        });

        // Получаем данные о последнем атакующем для корректного отображения кнопки "Бить в ответ"
        $lastAttacker = null;
        $lastAttackerResources = null;

        // Логируем начальное состояние для отладки
        Log::info('CustomMineController: проверка последнего атакующего', [
            'user_id' => $user->id,
            'last_attacker_id' => $user->last_attacker_id,
            'last_attacker_type' => $user->last_attacker_type,
            'current_location' => $mineLocation->name,
            'base_location' => $baseLocation->name
        ]);

        if ($user->last_attacker_id) {
            if ($user->last_attacker_type === 'player') {
                // Используем ту же логику, что и в методе retaliate для поиска последнего атакующего игрока
                $locationService = app(\App\Services\battle\UserLocationService::class);
                $normalizedLocation = $locationService->normalizeLocationName($mineLocation->name);

                $lastAttacker = User::where('id', $user->last_attacker_id)
                    ->whereHas('statistics', function ($query) use ($normalizedLocation) {
                        // ИСПРАВЛЕНО: Строгая изоляция - проверяем только точную локацию
                        $query->where('current_location', $normalizedLocation);
                    })
                    ->whereHas('profile', function ($query) {
                        $query->where('hp', '>', 0);
                    })
                    ->with('profile', 'statistics')
                    ->first();

                // Если атакующий найден, получаем его актуальные ресурсы из Redis
                if ($lastAttacker) {
                    try {
                        $lastAttackerActualResources = $lastAttacker->profile->getActualResources();
                        $lastAttackerResources = [
                            'current_hp' => $lastAttackerActualResources['current_hp'],
                            'max_hp' => $lastAttacker->profile->max_hp,
                            'current_mp' => $lastAttackerActualResources['current_mp'],
                            'max_mp' => $lastAttacker->profile->max_mp
                        ];
                    } catch (\Exception $e) {
                        Log::error('Ошибка получения актуальных ресурсов последнего атакующего в CustomMineController', [
                            'attacker_id' => $lastAttacker->id,
                            'error' => $e->getMessage()
                        ]);
                        // Fallback к значениям из БД
                        $lastAttackerResources = [
                            'current_hp' => $lastAttacker->profile->current_hp ?? $lastAttacker->profile->hp,
                            'max_hp' => $lastAttacker->profile->max_hp,
                            'current_mp' => $lastAttacker->profile->current_mp ?? $lastAttacker->profile->mp,
                            'max_mp' => $lastAttacker->profile->max_mp
                        ];
                    }

                    Log::info('CustomMineController: найден последний атакующий игрок', [
                        'attacker_id' => $lastAttacker->id,
                        'attacker_name' => $lastAttacker->name,
                        'attacker_hp' => $lastAttacker->profile->hp,
                        'attacker_location' => $lastAttacker->statistics->current_location ?? 'unknown'
                    ]);
                } else {
                    // Если атакующий не найден в локации, сбрасываем информацию о нем
                    Log::info('CustomMineController: сброс last_attacker_id для игрока - атакующий не найден в локации', [
                        'user_id' => $user->id,
                        'old_attacker_id' => $user->last_attacker_id,
                        'searched_location' => $normalizedLocation,
                        'base_location' => $mineLocation->baseLocation ? $mineLocation->baseLocation->name : null
                    ]);

                    $user->last_attacker_id = null;
                    $user->last_attacker_type = null;
                    $user->save();
                }
            } elseif ($user->last_attacker_type === 'mob') {
                // Обработка мобов как последних атакующих
                $locationService = app(\App\Services\battle\UserLocationService::class);
                $normalizedLocation = $locationService->normalizeLocationName($mineLocation->name);

                // ИСПРАВЛЕНО: Строгая изоляция - ищем моба только в точной локации
                $lastAttacker = Mob::where('id', $user->last_attacker_id)
                    ->where('location', $normalizedLocation)
                    ->where('hp', '>', 0)
                    ->first();

                // Если моб найден, получаем его ресурсы (для совместимости с компонентом аванпостов)
                if ($lastAttacker) {
                    $lastAttackerResources = [
                        'current_hp' => $lastAttacker->hp,
                        'max_hp' => $lastAttacker->max_hp,
                        'current_mp' => $lastAttacker->mp ?? 0,
                        'max_mp' => $lastAttacker->max_mp ?? 0
                    ];
                } else {
                    // Если моб не найден в локации, сбрасываем информацию о нем
                    $user->last_attacker_id = null;
                    $user->last_attacker_type = null;
                    $user->save();
                }
            }
        }

        // Хлебные крошки для навигации
        $breadcrumbs = [
            ['label' => 'Главная', 'url' => route('home')],
            ['label' => 'Битва', 'url' => route('battle.index')],
            ['label' => 'Рудники', 'url' => route('battle.mines.index')],
        ];

        // Если это подлокация, добавляем родительскую локацию
        if ($mineLocation->isSubLocation()) {
            $parentLocation = $mineLocation->parent;
            $breadcrumbs[] = ['label' => $parentLocation->name, 'url' => route('battle.mines.custom.index', $parentLocation->slug)];
        }

        // Добавляем текущую локацию
        $breadcrumbs[] = ['label' => $mineLocation->name, 'url' => '#'];

        // Формируем название локации с учетом иерархии
        $locationTitle = $mineLocation->isSubLocation()
            ? $mineLocation->parent->name . '-' . $mineLocation->name
            : $mineLocation->name;

        // Используем уже полученные актуальные ресурсы пользователя из начала метода
        // (избегаем дублирования вызова getActualResources())

        // Формируем данные для представления
        $viewData = [
            'user' => $user,
            'userProfile' => $user->profile,
            'actualResources' => [
                'hp' => $currentHp, // Используем уже полученное значение из начала метода
                'max_hp' => $user->profile->max_hp,
                'mp' => $currentMp, // Используем уже полученное значение из начала метода
                'max_mp' => $user->profile->max_mp
            ],
            'experienceProgress' => [
                'current' => $user->profile->experience,
                'max' => $user->profile->experience_to_next_level,
                'percent' => ($user->profile->experience_to_next_level > 0)
                    ? ($user->profile->experience / $user->profile->experience_to_next_level) * 100
                    : 100 // Если опыт до следующего уровня равен 0, считаем, что шкала заполнена на 100%
            ],
            'onlineCount' => User::where('updated_at', '>=', now()->subMinutes(15))->count(),
            'hasBrokenItems' => false, // Заглушка, можно реализовать позже
            'brokenItemsCount' => 0, // Заглушка, можно реализовать позже
            'resourcesInLocation' => $resourcesInLocation,
            'mobsInLocation' => $mobsInLocation,
            'botsInLocation' => $botsInLocation, // Боты в данной локации рудника
            'targetResource' => $targetResource,
            'targetMob' => $targetMob,
            'targetBot' => $targetBot, // Добавляем выбранного бота
            'target' => $target,
            'battleLogs' => $battleLogs,
            'userEffects' => $userEffects,
            'isStunned' => $isStunned,
            'lastAttacker' => $lastAttacker, // Добавляем данные о последнем атакующем
            'lastAttackerResources' => $lastAttackerResources, // Добавляем ресурсы последнего атакующего
            'solWarriors' => $solWarriors,
            'solMages' => $solMages,
            'solKnights' => $solKnights,
            'lunWarriors' => $lunWarriors,
            'lunMages' => $lunMages,
            'lunKnights' => $lunKnights,
            'userSkills' => $userSkills,
            'mineLocation' => $mineLocation,
            'subLocations' => $subLocations,
            'breadcrumbs' => $breadcrumbs,
            'locationTitle' => $locationTitle
        ];

        // Добавляем имя локации для отображения в заголовке шаблона
        $viewData['locationName'] = $mineLocation->name;

        // Формируем правильный префикс маршрута для использования в компоненте skills-panel
        // Используем формат 'battle.mines.custom', без добавления slug в префикс
        $viewData['routePrefix'] = 'battle.mines.custom';

        // Выбор шаблона
        $templatePath = 'battle.mines.locations.' . $mineLocation->slug;

        // Проверяем, существует ли шаблон
        if (!view()->exists($templatePath)) {
            // Если шаблон не существует, логируем ошибку
            Log::error('Шаблон для локации рудника не найден', [
                'location_id' => $mineLocation->id,
                'location_name' => $mineLocation->name,
                'location_slug' => $mineLocation->slug,
                'template_path' => $templatePath
            ]);

            // Создаем шаблон для локации
            $templateService = app(\App\Services\MineTemplateService::class);

            // Если это подлокация, создаем шаблон для подлокации
            if ($mineLocation->parent_id) {
                $templateCreated = $templateService->createSubLocationTemplate($mineLocation);
            } else {
                // Иначе создаем шаблон для базовой локации
                $templateCreated = $templateService->createTemplate($mineLocation);
            }

            // Логируем результат создания шаблона
            if ($templateCreated) {
                Log::info('Шаблон для локации рудника успешно создан', [
                    'location_id' => $mineLocation->id,
                    'location_name' => $mineLocation->name,
                    'location_slug' => $mineLocation->slug,
                    'template_path' => $templatePath
                ]);
            } else {
                // Если не удалось создать шаблон, возвращаем ошибку
                abort(500, 'Не удалось создать шаблон для локации рудника');
            }
        }

        return view($templatePath, $viewData);
    }

    /**
     * Получает ключ для логов боя
     *
     * @param User $user Пользователь
     * @param string $locationName Название локации
     * @param string|null $slug Slug локации
     * @return string Ключ логов
     */
    protected function getBattleLogKey($user, $locationName, $slug = null)
    {
        // Используем унифицированный ключ для всех боевых локаций рудников
        $unifiedKey = "battle_logs:mines:{$user->id}";

        Log::info("Используем унифицированный ключ для логов боя в рудниках", [
            'user_id' => $user->id,
            'location_name' => $locationName,
            'slug' => $slug,
            'unified_key' => $unifiedKey
        ]);

        return $unifiedKey;
    }

    /**
     * Выбор ресурса в качестве цели
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @param int $id ID ресурса
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectResource(Request $request, $slug, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;

        // Получаем ID локации
        $locationId = $baseLocation->id;

        // Находим ресурс с учетом подлокации
        $resourceQuery = SpawnedResource::with('resource')
            ->where('id', $id)
            ->where('location_id', $locationId)
            ->where('is_active', true);

        // Если это подлокация рудника, ищем ресурс с соответствующим mine_location_id
        if ($mineLocation->isSubLocation()) {
            $resourceQuery->where('mine_location_id', $mineLocation->id);
        } else {
            // Для основной локации ищем ресурсы с mine_location_id = NULL
            $resourceQuery->whereNull('mine_location_id');
        }

        $resource = $resourceQuery->first();

        if (!$resource) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Ресурс не найден или уже исчерпан.');
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'resource';
        $user->current_target_id = $resource->id;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $mineLocation->slug);

        // Добавляем запись в боевой лог с контекстом локации
        $this->battleLogService->addLog(
            $battleLogKey,
            "🎯 Вы выбрали {$resource->resource->name} как цель в {$mineLocation->name}",
            'info'
        );

        return redirect()->route('battle.mines.custom.index', $slug)
            ->with('success', 'Ресурс выбран как цель');
    }

    /**
     * Добыча ресурса
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function hitResource(Request $request, $slug)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'resource' || !$user->current_target_id) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Сначала выберите ресурс для добычи.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;

        // Получаем ID локации
        $locationId = $baseLocation->id;

        // Находим ресурс с учетом подлокации
        $resourceQuery = SpawnedResource::with('resource')
            ->where('id', $user->current_target_id)
            ->where('location_id', $locationId)
            ->where('is_active', true);

        // Если это подлокация рудника, ищем ресурс с соответствующим mine_location_id
        if ($mineLocation->isSubLocation()) {
            $resourceQuery->where('mine_location_id', $mineLocation->id);
        } else {
            // Для основной локации ищем ресурсы с mine_location_id = NULL
            $resourceQuery->whereNull('mine_location_id');
        }

        $resource = $resourceQuery->first();

        if (!$resource) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Ресурс не найден или уже исчерпан.');
        }

        // Уменьшаем прочность ресурса через сервис
        $this->resourceService->decreaseDurability($resource);

        // Применяем дебаф обнаружения при добыче ресурса
        Log::info('🎯 [MINE DEBUG] Начало применения дебафа обнаружения в CustomMineController', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'mine_location_id' => $mineLocation->id,
            'mine_location_name' => $mineLocation->name,
            'location_id' => $mineLocation->location_id,
            'resource_id' => $resource->id
        ]);
        
        try {
            $detectionEffect = null;
            
            // Проверяем, существует ли таблица mine_marks
            if (\Illuminate\Support\Facades\Schema::hasTable('mine_marks')) {
                Log::info('🎯 [MINE DEBUG] Используем MineDetectionService (mine_marks)');
                $detectionEffect = $this->mineDetectionService->applyDetectionDebuff($user, $mineLocation);
                Log::info('🎯 [MINE DEBUG] Результат MineDetectionService', [
                    'effect_created' => $detectionEffect ? 'YES' : 'NO',
                    'effect_type' => $detectionEffect ? get_class($detectionEffect) : 'NULL',
                    'effect_id' => $detectionEffect ? $detectionEffect->id : 'NULL'
                ]);
            } else {
                Log::info('🎯 [MINE DEBUG] Используем MineDetectionServiceFallback (active_effects)');
                $mineDetectionServiceFallback = app(\App\Services\MineDetectionServiceFallback::class);
                $detectionEffect = $mineDetectionServiceFallback->applyDetectionDebuff($user, $mineLocation);
                Log::info('🎯 [MINE DEBUG] Результат MineDetectionServiceFallback', [
                    'effect_created' => $detectionEffect ? 'YES' : 'NO',
                    'effect_type' => $detectionEffect ? get_class($detectionEffect) : 'NULL',
                    'effect_id' => $detectionEffect ? $detectionEffect->id : 'NULL'
                ]);
            }
            
            if ($detectionEffect) {
                Log::info('🎯 [MINE DEBUG] Дебаф успешно применен в CustomMineController');
            } else {
                Log::warning('🎯 [MINE DEBUG] Дебаф НЕ был применен в CustomMineController!');
            }
        } catch (\Exception $e) {
            Log::error('🎯 [MINE DEBUG] Ошибка при применении дебафа обнаружения в CustomMineController', [
                'user_id' => $user->id,
                'mine_location_id' => $mineLocation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $mineLocation->slug);

        // Если ресурс исчерпан, добавляем его пользователю и сбрасываем цель
        if ($resource->durability <= 0) {
            // ИСПРАВЛЕНИЕ: Используем addResourceWithLimit для корректной обработки переполнения инвентаря
            $lostQuantity = UserResource::addResourceWithLimit(
                $user->id,
                $resource->resource_id,
                1, // Добываем 1 единицу ресурса
                UserResource::LOCATION_INVENTORY
            );

            $resourceName = $resource->resource->name;

            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Формируем сообщение и лог с учетом переполнения инвентаря
            if ($lostQuantity > 0) {
                // Ресурс не поместился в инвентарь
                $logMessage = "❌ Вы добыли {$resourceName} в {$mineLocation->name}, но инвентарь переполнен!";
                $successMessage = "Вы добыли {$resourceName}, но инвентарь переполнен и ресурс потерян!";
                
                // Добавляем информацию о дебафе
                if ($detectionEffect ?? false) {
                    $successMessage .= " Вы были замечены при добыче ресурса!";
                }

                // Логируем потерю ресурса
                \Log::warning('Ресурс потерян из-за переполнения инвентаря при добыче в шахте', [
                    'user_id' => $user->id,
                    'resource_id' => $resource->resource_id,
                    'resource_name' => $resourceName,
                    'mine_location' => $mineLocation->name,
                    'lost_amount' => $lostQuantity
                ]);

                $this->battleLogService->addLog($battleLogKey, $logMessage, 'warning');
                return redirect()->route('battle.mines.custom.index', $slug)
                    ->with('warning', $successMessage);
            } else {
                // Ресурс успешно добавлен
                $logMessage = "✅ Вы добыли {$resourceName} в {$mineLocation->name}!";
                $successMessage = "Вы успешно добыли {$resourceName}!";
                
                // Добавляем информацию о дебафе
                if ($detectionEffect ?? false) {
                    $successMessage .= " Вы были замечены при добыче ресурса!";
                }

                $this->battleLogService->addLog($battleLogKey, $logMessage, 'success');
                return redirect()->route('battle.mines.custom.index', $slug)
                    ->with('success', $successMessage);
            }
        } else {
            // Логируем событие удара по ресурсу
            $logMessage = sprintf(
                '<div class="flex items-center">
                    <span class="flex items-center text-xs leading-tight">
                        <span style="color: #d4af37;">Кирка →</span>
                        <img src="%s" alt="%s" class="w-4 h-4 inline-block mx-1">
                        <span style="color: #32cd32;">%d</span>
                    </span>
                </div>',
                asset($resource->resource->icon),
                $resource->resource->name,
                $resource->durability
            );

            $this->battleLogService->addLog($battleLogKey, $logMessage, 'info');

            // Формируем сообщение об успешном ударе
            $successMessage = "Вы продолжаете добывать {$resource->resource->name}.";
            
            // Добавляем информацию о дебафе
            if ($detectionEffect ?? false) {
                $successMessage .= " Вы были замечены при добыче ресурса!";
            }

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('success', $successMessage);
        }
    }

    /**
     * Выбор моба в качестве цели
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @param int $id ID моба
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectMob(Request $request, $slug, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Логируем информацию о выборе моба для отладки
        Log::info('Выбор моба в шахте', [
            'user_id' => $user->id,
            'mob_id' => $id,
            'location_slug' => $slug,
            'location_id' => $mineLocation->id,
            'is_sublocation' => $mineLocation->parent_id !== null
        ]);

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;

        // Получаем ID локации
        $locationId = $baseLocation->id;

        // Находим моба - учитываем привязку к подлокации
        if ($mineLocation->isSubLocation()) {
            $mob = Mob::where('id', $id)
                ->where('mine_location_id', $mineLocation->id)
                ->where('hp', '>', 0)
                ->first();
        } else {
            $mob = Mob::where('id', $id)
                ->where('location', $mineLocation->name)
                ->where('hp', '>', 0)
                ->first();
        }

        if (!$mob) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Моб не найден или уже убит.');
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'mob';
        $user->current_target_id = $mob->id;
        $user->save();

        // Логируем успешный выбор моба
        Log::info('Моб успешно выбран как цель', [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'location_slug' => $slug,
            'location_name' => $mineLocation->name,
            'is_sublocation' => $mineLocation->parent_id !== null
        ]);

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $mineLocation->slug);

        // Добавляем запись в боевой лог с контекстом локации
        $this->battleLogService->addLog(
            $battleLogKey,
            "🎯 Вы выбрали {$mob->name} как цель в {$mineLocation->name}",
            'info'
        );

        return redirect()->route('battle.mines.custom.index', $slug)
            ->with('success', 'Моб выбран как цель');
    }

    /**
     * Выбор бота в качестве цели
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @param int $id ID бота
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectBot(Request $request, $slug, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;
        $locationName = $baseLocation->name;

        // Логируем информацию о выборе бота для отладки
        Log::info('Выбор бота в шахте', [
            'user_id' => $user->id,
            'bot_id' => $id,
            'location_slug' => $slug,
            'location_name' => $locationName,
            'mine_location_id' => $mineLocation->id,
            'is_sublocation' => $mineLocation->parent_id !== null
        ]);

        // ИСПРАВЛЕНИЕ: Упрощаем логику определения фракций - используем прямой поиск по полю race
        $userRace = $user->profile->race ?? null;

        // Валидация расы пользователя
        if (!$userRace || !in_array($userRace, ['solarius', 'lunarius'])) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'У вашего персонажа не определена раса. Обратитесь к администратору.');
        }

        // Определяем противоположную расу
        $enemyRace = ($userRace === 'solarius') ? 'lunarius' : 'solarius';

        // Находим бота с учетом изоляции между локациями и подлокациями
        // Проверяем, что бот принадлежит противоположной фракции (как в аванпостах)
        $botQuery = \App\Models\Bot::where('id', $id)
            ->where('race', $enemyRace) // Только боты противоположной фракции
            ->where('is_active', true)
            ->where('hp', '>', 0);

        // ИСПРАВЛЕНО: Изоляция между подлокациями и базовой локацией
        if ($mineLocation->isSubLocation()) {
            // Если игрок в подлокации - может атаковать только ботов В ЭТОЙ подлокации
            $botQuery->where('mine_location_id', $mineLocation->id);
        } else {
            // Если игрок в базовой локации - может атаковать только ботов базовой локации 
            // (у которых mine_location_id = NULL или равен ID базовой локации)
            $botQuery->where('location', $locationName)
                ->where(function($query) use ($mineLocation) {
                    $query->whereNull('mine_location_id')
                          ->orWhere('mine_location_id', $mineLocation->id);
                });
        }

        $bot = $botQuery->first();

        if (!$bot) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Бот не найден, недоступен или принадлежит вашей фракции.');
        }

        // Дополнительная проверка фракций для безопасности
        if ($bot->race === $userRace) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Нельзя атаковать ботов своей фракции.');
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'bot';
        $user->current_target_id = $bot->id;
        $user->save();

        // Логируем успешный выбор бота
        Log::info('Бот успешно выбран как цель', [
            'user_id' => $user->id,
            'bot_id' => $bot->id,
            'bot_name' => $bot->name,
            'location_slug' => $slug,
            'location_name' => $locationName,
            'is_sublocation' => $mineLocation->parent_id !== null
        ]);

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $mineLocation->slug);

        // Добавляем запись в боевой лог с контекстом локации
        $this->battleLogService->addLog(
            $battleLogKey,
            "🎯 Вы выбрали бота {$bot->name} как цель в {$mineLocation->name}",
            'info'
        );

        return redirect()->route('battle.mines.custom.index', $slug)
            ->with('success', 'Бот выбран как цель');
    }

    /**
     * Атака бота
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackBot(Request $request, $slug)
    {
        $user = Auth::user();
        $isCrit = false; // Инициализируем флаг крита

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'bot' || !$user->current_target_id) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Сначала выберите бота для атаки.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;
        $locationName = $baseLocation->name;

        // Логируем информацию для отладки
        Log::info('Атака бота в шахте', [
            'user_id' => $user->id,
            'bot_id' => $user->current_target_id,
            'location_slug' => $slug,
            'location_name' => $locationName,
            'mine_location_id' => $mineLocation->id,
            'is_sublocation' => $mineLocation->parent_id !== null
        ]);

        // ИСПРАВЛЕНИЕ: Упрощаем логику определения фракций - используем прямой поиск по полю race
        $userRace = $user->profile->race ?? null;

        // Валидация расы пользователя
        if (!$userRace || !in_array($userRace, ['solarius', 'lunarius'])) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'У вашего персонажа не определена раса. Обратитесь к администратору.');
        }

        // Определяем противоположную расу
        $enemyRace = ($userRace === 'solarius') ? 'lunarius' : 'solarius';

        // Находим бота с проверкой фракции и изоляции между локациями
        $botQuery = \App\Models\Bot::where('id', $user->current_target_id)
            ->where('race', $enemyRace) // Только боты противоположной фракции
            ->where('is_active', true)
            ->where('hp', '>', 0);

        // ИСПРАВЛЕНО: Изоляция между подлокациями и базовой локацией
        if ($mineLocation->isSubLocation()) {
            // Если игрок в подлокации - может атаковать только ботов В ЭТОЙ подлокации
            $botQuery->where('mine_location_id', $mineLocation->id);
        } else {
            // Если игрок в базовой локации - может атаковать только ботов базовой локации 
            // (у которых mine_location_id = NULL или равен ID базовой локации)
            $botQuery->where('location', $locationName)
                ->where(function($query) use ($mineLocation) {
                    $query->whereNull('mine_location_id')
                          ->orWhere('mine_location_id', $mineLocation->id);
                });
        }

        $bot = $botQuery->first();

        if (!$bot) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Бот не найден, уже уничтожен или принадлежит вашей фракции.');
        }

        // Дополнительная проверка фракций для безопасности
        if ($bot->race === $userRace) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Нельзя атаковать ботов своей фракции.');
        }

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $mineLocation->slug);

        // Проверяем, жив ли бот
        if ($bot->hp <= 0) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            $this->battleLogService->addLog(
                $battleLogKey,
                "❌ Бот {$bot->name} уже уничтожен",
                'error'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Бот уже уничтожен.');
        }

        // Рассчитываем урон игрока
        $userDamage = $user->profile->strength;
        $critChance = 10; // 10% шанс крита

        // Проверяем критический удар
        if (rand(1, 100) <= $critChance) {
            $userDamage *= 2;
            $isCrit = true;
        }

        // Применяем урон к боту
        $bot->hp = max(0, $bot->hp - $userDamage);
        $bot->save();

        // Формируем сообщение о результате атаки
        $critText = $isCrit ? ' (КРИТИЧЕСКИЙ УДАР!)' : '';
        $logMessage = sprintf(
            '<div class="flex items-center">
                <span class="flex items-center text-xs leading-tight">
                    <span style="color: #ff6b6b;">⚔️ →</span>
                    <span class="mx-1">%s</span>
                    <span style="color: #ff6b6b;">-%d</span>
                    <span style="color: #ffd700;">%s</span>
                </span>
            </div>',
            $bot->name,
            $userDamage,
            $critText
        );

        $this->battleLogService->addLog($battleLogKey, $logMessage, 'success');

        // Проверяем, убит ли бот
        if ($bot->hp <= 0) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Деактивируем бота
            $bot->is_active = false;
            $bot->save();

            // Добавляем опыт за победу над ботом
            $experienceGain = $bot->level * 10; // Опыт зависит от уровня бота
            $user->profile->experience += $experienceGain;
            $user->profile->save();

            $this->battleLogService->addLog(
                $battleLogKey,
                "🏆 Вы уничтожили бота {$bot->name} в {$mineLocation->name}! Получено {$experienceGain} опыта.",
                'success'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('success', "Вы уничтожили бота {$bot->name}! Получено {$experienceGain} опыта.");
        }

        return redirect()->route('battle.mines.custom.index', $slug)
            ->with('success', "Вы нанесли {$userDamage} урона боту {$bot->name}{$critText}");
    }

    /**
     * Атака моба
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackMob(Request $request, $slug)
    {
        $user = Auth::user();
        $isCrit = false; // Инициализируем флаг крита

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'mob' || !$user->current_target_id) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Сначала выберите моба для атаки.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $mineLocation->name;

        // Получаем базовую локацию
        $baseLocation = $mineLocation->baseLocation;

        // Получаем ID локации
        $locationId = $baseLocation->id;

        // Логируем информацию для отладки
        Log::info('Атака моба в шахте', [
            'user_id' => $user->id,
            'mob_id' => $user->current_target_id,
            'location_slug' => $slug,
            'location_id' => $mineLocation->id,
            'location_name' => $mineLocation->name,
            'base_location_id' => $locationId,
            'is_sublocation' => $mineLocation->parent_id !== null
        ]);

        // Находим моба - учитываем привязку к подлокации
        if ($mineLocation->isSubLocation()) {
            $mob = Mob::where('id', $user->current_target_id)
                ->where('mine_location_id', $mineLocation->id)
                ->where('hp', '>', 0)
                ->first();
        } else {
            $mob = Mob::where('id', $user->current_target_id)
                ->where('location', $mineLocation->name)
                ->where('hp', '>', 0)
                ->first();
        }

        if (!$mob) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Моб не найден или уже убит.');
        }

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $mineLocation->slug);

        // Проверяем, жив ли моб
        if ($mob->hp <= 0) {
            $this->battleLogService->addLog($battleLogKey, "Цель {$mob->name} уже побеждена!", 'warning');
            return redirect()->back()->with('error', 'Моб уже побежден!');
        }

        // Получаем силу атакующего (игрока)
        $userStrength = $user->profile->getEffectiveStats()['strength'] ?? 1;
        // Получаем броню цели (моба)
        $mobArmor = $mob->armor ?? 0;
        // Используем универсальную формулу урона
        $damage = $this->combatFormulaService->calculateDamage($userStrength, $mobArmor);

        // Проверяем, был ли удар критическим (5% шанс)
        if (rand(1, 100) <= 5) {
            $damage = ceil($damage * 1.5);
            $isCrit = true;
        }

        // Логируем расчетный урон
        Log::info("Рассчитанный урон по мобу", [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'damage' => $damage,
            'mob_hp_before' => $mob->hp,
            'is_crit' => $isCrit
        ]);

        // Сохраняем текущее HP моба для расчета фактического урона
        $mobCurrentHp = $mob->hp;

        // Рассчитываем фактический урон (не больше текущего HP моба)
        $effectiveDamage = min($damage, $mobCurrentHp);

        // Наносим урон мобу
        $mob->hp = max(0, $mob->hp - $damage);
        $mob->save();

        Log::info("Урон нанесен", [
            'mob_hp_after' => $mob->hp,
            'calculated_damage' => $damage,
            'effective_damage' => $effectiveDamage
        ]);

        // Получаем ключ боевого лога с учетом slug для подлокаций
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

        // Используем LogFormattingService для записи лога атаки
        $attackLog = $this->logFormatter->formatPlayerAttack($user, $mob, $damage, $isCrit);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'info');

        // Начисляем опыт за фактически нанесенный урон через DamageService
        $this->damageCalculator->awardExperienceForDamage($user, $effectiveDamage, $location);

        if ($mob->hp <= 0) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Логируем событие убийства моба с контекстом локации
            $this->battleLogService->addLog(
                $battleLogKey,
                "💀 Вы убили {$mob->name} в {$mineLocation->name} и получили опыт!",
                'success'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('success', "Вы убили {$mob->name}!");
        } else {
            // Моб может атаковать в ответ
            $this->mobAttackPlayer($mob, $user, $mineLocation);

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('success', "Вы атаковали {$mob->name} и нанесли {$damage} урона.");
        }
    }

    /**
     * Атака моба по игроку
     *
     * @param Mob $mob Моб, который атакует
     * @param User $target Игрок, которого атакуют
     * @param MineLocation $mineLocation Локация рудника
     * @return void
     */
    protected function mobAttackPlayer($mob, $target, $mineLocation)
    {
        // Получаем силу моба и броню игрока
        $mobStrength = $mob->strength ?? 1;
        $playerArmor = $target->profile->getEffectiveStats()['armor'] ?? 0;

        // Используем CombatFormulaService для расчета урона
        $damage = $this->combatFormulaService->calculateDamage($mobStrength, $playerArmor);

        // Проверяем, был ли удар критическим (5% шанс)
        $isCrit = rand(1, 100) <= 5;
        if ($isCrit) {
            $damage = ceil($damage * 1.5);
        }

        // Получаем сервис для работы со здоровьем игрока
        $playerHealthService = app(\App\Services\PlayerHealthService::class);

        // Применяем урон через Redis (синхронизация с БД происходит автоматически)
        $damageResult = $playerHealthService->applyDamage($target, $damage, "mob:{$mob->id}");

        $oldHp = $damageResult['old_hp'];
        $newHp = $damageResult['new_hp'];
        $isDead = $damageResult['is_dead'];

        // Обновляем информацию о последнем атакующем
        // Для мобов используем ID моба и тип 'mob'
        $target->update([
            'last_attacker_id' => $mob->id,
            'last_attacker_type' => 'mob'
        ]);

        // Получаем ключ боевого лога с учетом slug для подлокаций
        $battleLogKey = $this->getBattleLogKey($target, $mineLocation->name, $mineLocation->slug);

        // Логируем атаку моба
        $mobAttackLog = $this->logFormatter->formatMobAttack($mob, $target, $damage, $isCrit);
        $this->battleLogService->addLog($battleLogKey, $mobAttackLog, 'danger');

        // Логируем для отладки
        Log::info("Моб атаковал игрока через PlayerHealthService", [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'target_id' => $target->id,
            'old_hp' => $oldHp,
            'damage' => $damage,
            'new_hp' => $newHp,
            'is_crit' => $isCrit,
            'is_dead' => $isDead
        ]);
    }

    /**
     * Атака случайного игрока противоположной фракции
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackAnyPlayer(Request $request, $slug)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // ИСПРАВЛЕНИЕ: Используем централизованный UserLocationService для единообразия
        $locationService = app(\App\Services\battle\UserLocationService::class);

        // ИСПРАВЛЕНО: Используем точную локацию рудника для строгой изоляции
        // Нормализуем название конкретной локации (подлокации или базовой)
        $normalizedLocation = $locationService->normalizeLocationName($mineLocation->name);

        // ИСПРАВЛЕНИЕ: Упрощаем логику поиска врагов - используем прямой поиск по полю race
        // Определяем расу текущего игрока
        $userRace = $user->profile->race ?? null;

        // Валидация расы пользователя
        if (!$userRace || !in_array($userRace, ['solarius', 'lunarius'])) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'У вашего персонажа не определена раса. Обратитесь к администратору.');
        }

        // Определяем противоположную расу
        $enemyRace = ($userRace === 'solarius') ? 'lunarius' : 'solarius';

        // Логируем детали поиска для отладки
        \Log::info('CustomMineController: attackAnyPlayer - начало поиска врагов', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_race_from_profile' => $user->profile->race ?? 'null',
            'determined_user_race' => $userRace,
            'enemy_race' => $enemyRace,
            'mine_location_name' => $mineLocation->name,
            'normalized_location' => $normalizedLocation,
            'slug' => $slug,
            'is_sublocation' => $mineLocation->isSubLocation()
        ]);

        // ОПТИМИЗАЦИЯ: Используем новый сервис кеширования для быстрого поиска игроков
        $cacheService = app(\App\Services\battle\LocationPlayerCacheService::class);

        // Получаем кешированных игроков-врагов в локации
        $enemyPlayers = $cacheService->getCachedPlayersInLocation($normalizedLocation, $enemyRace, $user->id);

        // Логируем детали поиска игроков для диагностики
        \Log::info('CustomMineController: attackAnyPlayer - детали поиска игроков (оптимизированный)', [
            'user_id' => $user->id,
            'normalized_location' => $normalizedLocation,
            'enemy_race' => $enemyRace,
            'cached_enemy_players_found' => $enemyPlayers->count(),
            'found_players' => $enemyPlayers->map(function ($p) {
                return [
                    'id' => $p->id,
                    'name' => $p->name,
                    'last_activity' => $p->last_activity_timestamp ? date('Y-m-d H:i:s', $p->last_activity_timestamp) : 'unknown'
                ];
            })->toArray()
        ]);

        // ОПТИМИЗАЦИЯ: Используем кеш для поиска ботов-врагов
        $enemyBots = $cacheService->getCachedBotsInLocation($normalizedLocation, $enemyRace);

        // Логируем результаты поиска
        \Log::info('CustomMineController: attackAnyPlayer - результаты поиска', [
            'enemy_players_count' => $enemyPlayers->count(),
            'enemy_bots_count' => $enemyBots->count(),
            'enemy_players' => $enemyPlayers->map(function ($p) {
                return ['id' => $p->id, 'name' => $p->name, 'race' => $p->profile->race ?? 'unknown'];
            })->toArray(),
            'enemy_bots' => $enemyBots->map(function ($b) {
                return ['id' => $b->id, 'name' => $b->name, 'race' => $b->race];
            })->toArray()
        ]);

        // Объединяем игроков и ботов
        $allEnemies = $enemyPlayers->merge($enemyBots);

        // Получаем ключ боевого лога с учетом slug для подлокаций
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

        // Если нет врагов, сбрасываем цель и сообщаем об этом
        if ($allEnemies->isEmpty()) {
            // Сбрасываем текущую цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Логируем отсутствие врагов для отладки с дополнительной информацией
            \Log::warning('CustomMineController: attackAnyPlayer - враги не найдены', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_race' => $userRace,
                'enemy_race' => $enemyRace,
                'normalized_location' => $normalizedLocation,
                'mine_location_name' => $mineLocation->name,
                'is_sublocation' => $mineLocation->isSubLocation(),
                'enemy_players_found' => $enemyPlayers->count(),
                'enemy_bots_found' => $enemyBots->count(),
                'search_details' => [
                    'searched_location' => $normalizedLocation,
                    'searched_race' => $enemyRace,
                    'active_filter' => true,
                    'hp_filter' => '> 0'
                ]
            ]);

            // Добавляем запись в боевой лог
            $this->battleLogService->addLog(
                $battleLogKey,
                "🔍 В локации нет игроков или ботов противоположной фракции",
                'info'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'В локации нет игроков или ботов противоположной фракции');
        }

        // Выбираем случайного врага
        $enemy = $allEnemies->random();

        // Логируем информацию о выбранном враге
        Log::info('Выбран случайный враг', [
            'user_id' => $user->id,
            'enemy_type' => get_class($enemy),
            'enemy_id' => $enemy->id,
            'enemy_name' => $enemy->name,
            'location' => $normalizedLocation,
            'total_enemies' => $allEnemies->count()
        ]);

        // Определяем тип цели и сразу атакуем
        if ($enemy instanceof \App\Models\User) {
            $user->current_target_type = 'player';
            $user->current_target_id = $enemy->id;
            $user->save();
            
            // Логируем выбор и атаку игрока
            Log::info('Выбран и атакован игрок', [
                'user_id' => $user->id,
                'target_id' => $enemy->id,
                'target_name' => $enemy->name
            ]);
            
            // Добавляем запись в боевой лог
            $this->battleLogService->addLog(
                $battleLogKey,
                "🎯 Вы выбрали игрока {$enemy->name} и атакуете!",
                'info'
            );
            
            // Перенаправляем на атаку игрока с мгновенным выполнением
            return $this->attackPlayer($request, $slug);
            
        } else {
            $user->current_target_type = 'bot';
            $user->current_target_id = $enemy->id;
            $user->save();
            
            // Логируем выбор и атаку бота
            Log::info('Выбран и атакован бот', [
                'user_id' => $user->id,
                'target_id' => $enemy->id,
                'target_name' => $enemy->name
            ]);
            
            // Добавляем запись в боевой лог
            $this->battleLogService->addLog(
                $battleLogKey,
                "🎯 Вы выбрали бота {$enemy->name} и атакуете!",
                'info'
            );
            
            // Перенаправляем на атаку бота с мгновенным выполнением
            return $this->attackBot($request, $slug);
        }
    }

    /**
     * Атака игрока
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackPlayer(Request $request, $slug)
    {
        $user = Auth::user();
        $damageCoefficient = min(1, max(0, floatval($request->input('damageCoefficient', 1))));

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'player' || !$user->current_target_id) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Сначала выберите игрока для атаки.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $mineLocation->name;

        // Проверка кулдауна
        $lastAttackTime = session('last_pvp_attack_time', 0);
        $currentTime = time();
        if ($currentTime - $lastAttackTime < 4) {
            return redirect()->back()->with('error', 'Подождите перед следующей атакой!');
        }

        // Получаем цель - ищем как в шахте, так и в базовой локации
        $targetQuery = User::whereHas('profile', function ($q) {
            $q->where('hp', '>', 0);
        })->where('id', $user->current_target_id);

        // ИСПРАВЛЕНО: Строгая изоляция - игрок может атаковать только в ТОЧНО той же локации
        $targetQuery->whereHas('statistics', function ($q) use ($location) {
            $q->where('current_location', $location);
        });

        $target = $targetQuery->first();

        // Логируем детали поиска цели для отладки
        \Log::info('CustomMineController: attackPlayer - поиск цели', [
            'user_id' => $user->id,
            'target_id' => $user->current_target_id,
            'mine_location_name' => $location,
            'base_location_name' => $mineLocation->baseLocation ? $mineLocation->baseLocation->name : null,
            'target_found' => $target ? true : false,
            'target_details' => $target ? [
                'id' => $target->id,
                'name' => $target->name,
                'hp' => $target->profile->hp ?? 0,
                'location' => $target->statistics->current_location ?? 'unknown'
            ] : null
        ]);

        if (!$target) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Игрок не найден или покинул локацию.');
        }

        // Получаем ключ боевого лога с учетом slug для подлокаций
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

        // Рассчитываем урон
        $damage = round($this->calculatePvPDamage($user, $target) * $damageCoefficient);

        // Получаем сервис для работы со здоровьем игрока
        $playerHealthService = app(\App\Services\PlayerHealthService::class);

        // Применяем урон через Redis (синхронизация с БД происходит автоматически)
        $damageResult = $playerHealthService->applyDamage($target, $damage, "player:{$user->id}");

        $oldHp = $damageResult['old_hp'];
        $newHp = $damageResult['new_hp'];
        $isDead = $damageResult['is_dead'];

        // Логируем для отладки урон
        Log::info("PvP Урон применен через PlayerHealthService в рудниках", [
            'attacker_id' => $user->id,
            'defender_id' => $target->id,
            'old_hp' => $oldHp,
            'damage' => $damage,
            'new_hp' => $newHp,
            'is_dead' => $isDead,
        ]);

        // Проверяем, является ли атакующий целью атакованного игрока
        $isTargetedByDefender = ($target->current_target_type === 'player' && $target->current_target_id == $user->id);

        // Обновляем информацию о последнем атакующем
        $target->update([
            'last_attacker_id' => $user->id,
            'last_attacker_type' => 'player'
        ]);

        // Логируем событие для отладки
        Log::info('PvP Attack', [
            'attacker_id' => $user->id,
            'attacker_name' => $user->name,
            'attacker_hp' => $user->profile->hp,
            'defender_id' => $target->id,
            'defender_name' => $target->name,
            'defender_hp' => $target->profile->hp,
            'damage' => $damage,
            'location' => $location,
            'is_targeted_by_defender' => $isTargetedByDefender
        ]);

        // Обновляем время последней атаки
        session(['last_pvp_attack_time' => $currentTime]);

        // Рассчитываем фактический урон для начисления опыта
        $effectiveDamage = min($damage, $oldHp);

        // Начисляем опыт за фактически нанесенный урон через DamageService
        $this->damageCalculator->awardExperienceForDamage($user, $effectiveDamage, $location);

        // Используем LogFormattingService для форматирования логов атаки
        $attackLog = $this->logFormatter->formatPlayerAttack($user, $target, $damage, false);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'success');

        // Логируем для цели с учетом slug для подлокаций
        $targetLogKey = $this->getBattleLogKey($target, $mineLocation->name, $slug);
        $defendLog = $this->logFormatter->formatPlayerDefend($target, $user, $damage, false);
        $this->battleLogService->addLog($targetLogKey, $defendLog, 'danger');

        // Проверяем смерть цели используя результат от PlayerHealthService
        if ($isDead) {
            $this->handlePvPDeath($target, $user, $mineLocation);
        }

        return redirect()->route('battle.mines.custom.index', $slug)
            ->with('success', "Вы атаковали игрока {$target->name} и нанесли {$damage} урона");
    }

    /**
     * Расчет урона в PvP
     *
     * @param User $attacker Атакующий игрок
     * @param User $defender Защищающийся игрок
     * @return int Рассчитанный урон
     */
    protected function calculatePvPDamage($attacker, $defender)
    {
        // Получаем эффективные характеристики
        $attackerStrength = $attacker->profile->getEffectiveStats()['strength'] ?? 1;
        $defenderArmor = $defender->profile->getEffectiveStats()['armor'] ?? 0;

        // Используем CombatFormulaService для расчета урона
        $baseDamage = $this->combatFormulaService->calculateDamage($attackerStrength, $defenderArmor);

        // Применяем случайность (±3%)
        $randomFactor = rand(97, 103) / 100;
        $damage = (int) round($baseDamage * $randomFactor);

        // Определяем критический удар (5% шанс)
        $isCritPvp = false;
        if (rand(1, 100) <= 5) {
            $damage = round($damage * 1.5);
            $isCritPvp = true;
        }

        // Логирование расчета урона
        Log::info('PvP Damage Calculation', [
            'attacker_id' => $attacker->id,
            'defender_id' => $defender->id,
            'attacker_strength' => $attackerStrength,
            'defender_armor' => $defenderArmor,
            'base_damage' => $baseDamage,
            'random_factor' => $randomFactor,
            'is_crit' => $isCritPvp,
            'final_damage' => $damage
        ]);

        return max(1, $damage); // Минимальный урон 1
    }

    /**
     * Обработка смерти игрока в PvP
     *
     * @param User $deadPlayer Погибший игрок
     * @param User $killer Убийца
     * @param MineLocation $mineLocation Локация рудника
     * @return void
     */
    protected function handlePvPDeath($deadPlayer, $killer, $mineLocation)
    {
        // Обновляем статистику
        $deadPlayer->statistics->increment('pvp_losses');
        $killer->statistics->increment('pvp_wins');

        // Сбрасываем цель убийцы
        $killer->current_target_id = null;
        $killer->current_target_type = null;
        $killer->save();

        // Телепортируем погибшего в безопасную зону
        $deadPlayer->statistics->current_location = 'Таверна';
        $deadPlayer->statistics->save();

        // Восстанавливаем здоровье погибшего
        $deadPlayer->profile->hp = $deadPlayer->profile->max_hp;
        $deadPlayer->profile->save();

        // Логи для убийцы с учетом slug для подлокаций
        $killerLogKey = $this->getBattleLogKey($killer, $mineLocation->name, $mineLocation->slug);
        $this->battleLogService->addLog(
            $killerLogKey,
            "🏆 Вы победили игрока {$deadPlayer->name}!",
            'success'
        );

        // Логи для погибшего с учетом slug для подлокаций
        $deadLogKey = $this->getBattleLogKey($deadPlayer, $mineLocation->name, $mineLocation->slug);
        $this->battleLogService->addLog(
            $deadLogKey,
            "💀 Вы были побеждены игроком {$killer->name} и телепортированы в Таверну",
            'danger'
        );

        // Логирование события смерти
        Log::info('PvP Death Event', [
            'killer_id' => $killer->id,
            'dead_player_id' => $deadPlayer->id,
            'location' => $mineLocation->name
        ]);
    }

    /**
     * Ответная атака на последнего атаковавшего
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function retaliate(Request $request, $slug)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Загружаем необходимые связи для корректной работы
        $user->load(['profile', 'statistics']);

        // Проверяем, есть ли последний атаковавший
        if (!$user->last_attacker_id) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Нет информации о последнем атаковавшем.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем тип последнего атакующего
        $lastAttackerType = $user->last_attacker_type ?? 'player';
        $lastAttacker = null;

        // В зависимости от типа атакующего, ищем его
        if ($lastAttackerType === 'player') {
            // Находим атакующего игрока
            $lastAttacker = User::where('id', $user->last_attacker_id)
                ->whereHas('profile', function ($query) {
                    // Проверяем актуальное HP из Redis или БД
                    $query->where(function ($q) {
                        $q->whereRaw('
                            CASE
                                WHEN current_hp IS NOT NULL THEN current_hp > 0
                                ELSE hp > 0
                            END
                        ');
                    });
                })
                ->with('profile', 'statistics')
                ->first();

            if (!$lastAttacker) {
                // Сбрасываем информацию о последнем атакующем, если он не найден или мертв
                $user->update([
                    'last_attacker_id' => null,
                    'last_attacker_type' => null
                ]);

                return redirect()->route('battle.mines.custom.index', $slug)
                    ->with('error', 'Последний атаковавший игрок не найден или был побежден.');
            }

            // Проверяем, находится ли атакующий в той же локации, используя улучшенную логику
            $locationService = app(\App\Services\battle\UserLocationService::class);
            $areInSameLocation = $locationService->arePlayersInSameLocation($user, $lastAttacker);

            if (!$areInSameLocation) {
                // Сбрасываем информацию о последнем атакующем, если он не в локации
                $user->update([
                    'last_attacker_id' => null,
                    'last_attacker_type' => null
                ]);

                return redirect()->route('battle.mines.custom.index', $slug)
                    ->with('error', 'Последний атаковавший игрок покинул локацию.');
            }

            // Устанавливаем цель пользователя
            $user->current_target_type = 'player';
            $user->current_target_id = $lastAttacker->id;
            $user->save();

            // Получаем ключ боевого лога с учетом slug для подлокаций
            $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

            // Добавляем запись в боевой лог
            $this->battleLogService->addLog(
                $battleLogKey,
                "🎯 Вы выбрали игрока {$lastAttacker->name} как цель для ответной атаки",
                'info'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('success', "Вы выбрали игрока {$lastAttacker->name} как цель для ответной атаки");
        } elseif ($lastAttackerType === 'mob') {
            // Если атакующий - моб, проверяем, что он находится в той же локации
            // Используем централизованный UserLocationService для единообразия
            $locationService = app(\App\Services\battle\UserLocationService::class);
            $normalizedLocation = $locationService->normalizeLocationName($mineLocation->name);

            // ИСПРАВЛЕНО: Строгая изоляция - ищем моба только в точной локации
            $lastAttacker = Mob::where('id', $user->last_attacker_id)
                ->where('location', $normalizedLocation)
                ->where('hp', '>', 0)
                ->first();

            if (!$lastAttacker) {
                // Сбрасываем информацию о последнем атакующем, если он не в локации
                $user->update([
                    'last_attacker_id' => null,
                    'last_attacker_type' => null
                ]);

                return redirect()->route('battle.mines.custom.index', $slug)
                    ->with('error', 'Последний атаковавший моб покинул локацию или был побежден.');
            }

            // Устанавливаем цель пользователя
            $user->current_target_type = 'mob';
            $user->current_target_id = $lastAttacker->id;
            $user->save();

            // Получаем ключ боевого лога с учетом slug для подлокаций
            $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

            // Добавляем запись в боевой лог
            $this->battleLogService->addLog(
                $battleLogKey,
                "🎯 Вы выбрали моба {$lastAttacker->name} как цель для ответной атаки",
                'info'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('success', "Вы выбрали моба {$lastAttacker->name} как цель для ответной атаки");
        } else {
            // Неизвестный тип атакующего
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Неизвестный тип последнего атаковавшего.');
        }
    }

    /**
     * Смена цели на случайного врага с исключением текущей цели
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeTarget(Request $request, $slug)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по slug
        $mineLocation = MineLocation::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

        // Проверяем, есть ли у пользователя текущая цель
        if (!$user->current_target_type || !$user->current_target_id) {
            $this->battleLogService->addLog(
                $battleLogKey,
                "❌ У вас нет активной цели для смены",
                'error'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'У вас нет активной цели для смены');
        }

        // ИСПРАВЛЕНИЕ: Сбрасываем информацию о последнем атакующем при смене цели
        // Это решает проблему с отображением кнопки "Бить в ответ" после смены цели
        $user->last_attacker_id = null;
        $user->last_attacker_type = null;

        // Сохраняем информацию о текущей цели для исключения
        $currentTargetType = $user->current_target_type;
        $currentTargetId = $user->current_target_id;

        // Используем централизованный UserLocationService для единообразия
        $locationService = app(\App\Services\battle\UserLocationService::class);

        // Нормализуем название локации так же, как в FactionCountService
        $normalizedLocation = $locationService->normalizeLocationName($mineLocation->name);

        // Определяем расу текущего игрока
        $userRace = $user->profile->race ?? null;

        // Валидация расы пользователя
        if (!$userRace || !in_array($userRace, ['solarius', 'lunarius'])) {
            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'У вашего персонажа не определена раса. Обратитесь к администратору.');
        }

        // Определяем противоположную расу
        $enemyRace = ($userRace === 'solarius') ? 'lunarius' : 'solarius';

        // Логируем детали поиска для отладки
        \Log::info('CustomMineController: changeTarget - начало поиска новой цели', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_race' => $userRace,
            'enemy_race' => $enemyRace,
            'current_target_type' => $currentTargetType,
            'current_target_id' => $currentTargetId,
            'mine_location_name' => $mineLocation->name,
            'normalized_location' => $normalizedLocation,
            'slug' => $slug
        ]);

        // Ищем игроков-врагов в текущей локации по полю race, исключая текущую цель
        $enemyPlayersQuery = User::whereHas('profile', function ($q) use ($enemyRace) {
            $q->where('race', $enemyRace)
                ->where(function ($hpQuery) {
                    // Проверяем актуальное HP из Redis или БД
                    $hpQuery->whereRaw('
                        CASE
                            WHEN current_hp IS NOT NULL THEN current_hp > 0
                            ELSE hp > 0
                        END
                    ');
                });
        })->where('id', '!=', $user->id)
            ->where('last_activity_timestamp', '>=', now()->subMinutes(10)->timestamp);

        // ИСПРАВЛЕНО: Строгая изоляция - игроки могут атаковать только в точно той же локации
        // Убираем поиск по базовой локации для игроков, чтобы избежать атак между разными уровнями
        $enemyPlayersQuery->whereHas('statistics', function ($q) use ($normalizedLocation) {
            $q->where('current_location', $normalizedLocation);
        });

        // Исключаем текущую цель, если это игрок
        if ($currentTargetType === 'player') {
            $enemyPlayersQuery->where('id', '!=', $currentTargetId);
        }

        $enemyPlayers = $enemyPlayersQuery->get();

        // Логируем результаты поиска игроков для отладки
        \Log::info('CustomMineController: changeTarget - поиск игроков-врагов', [
            'normalized_location' => $normalizedLocation,
            'base_location' => $mineLocation->baseLocation ? $mineLocation->baseLocation->name : null,
            'enemy_race' => $enemyRace,
            'found_players_count' => $enemyPlayers->count(),
            'found_players' => $enemyPlayers->map(function ($p) {
                return [
                    'id' => $p->id,
                    'name' => $p->name,
                    'race' => $p->profile->race ?? 'unknown',
                    'hp' => $p->profile->hp ?? 0,
                    'location' => $p->statistics->current_location ?? 'unknown'
                ];
            })->toArray()
        ]);

        // Ищем ботов-врагов используя как нормализованное название, так и название базовой локации
        $enemyBotsQuery = \App\Models\Bot::where('race', $enemyRace)
            ->where('is_active', true)
            ->where('hp', '>', 0);

        // ИСПРАВЛЕНО: Строгая изоляция - боты могут атаковать только в точно той же локации
        // Убираем поиск по базовой локации для ботов, чтобы избежать атак между разными уровнями
        $enemyBotsQuery->where('location', $normalizedLocation);

        // Исключаем текущую цель, если это бот
        if ($currentTargetType === 'bot') {
            $enemyBotsQuery->where('id', '!=', $currentTargetId);
        }

        $enemyBots = $enemyBotsQuery->get();

        // Логируем результаты поиска
        \Log::info('CustomMineController: changeTarget - результаты поиска', [
            'enemy_players_count' => $enemyPlayers->count(),
            'enemy_bots_count' => $enemyBots->count(),
            'excluded_target_type' => $currentTargetType,
            'excluded_target_id' => $currentTargetId
        ]);

        // Объединяем игроков и ботов
        $allEnemies = $enemyPlayers->merge($enemyBots);

        // Если нет доступных целей для смены, сообщаем об этом
        if ($allEnemies->isEmpty()) {
            $this->battleLogService->addLog(
                $battleLogKey,
                "🔍 Нет доступных целей для смены в локации",
                'info'
            );

            return redirect()->route('battle.mines.custom.index', $slug)
                ->with('error', 'Нет доступных целей для смены в локации');
        }

        // Выбираем случайного врага
        $newTarget = $allEnemies->random();

        // Определяем тип новой цели и устанавливаем её
        if ($newTarget instanceof \App\Models\User) {
            $user->current_target_type = 'player';
            $user->current_target_id = $newTarget->id;
            $targetTypeName = 'игрока';
        } else {
            $user->current_target_type = 'bot';
            $user->current_target_id = $newTarget->id;
            $targetTypeName = 'бота';
        }
        $user->save();

        // Логируем смену цели
        Log::info('Цель успешно сменена', [
            'user_id' => $user->id,
            'old_target_type' => $currentTargetType,
            'old_target_id' => $currentTargetId,
            'new_target_type' => $user->current_target_type,
            'new_target_id' => $user->current_target_id,
            'new_target_name' => $newTarget->name,
            'location' => $normalizedLocation
        ]);

        // Добавляем запись в боевой лог о смене цели
        $this->battleLogService->addLog(
            $battleLogKey,
            "🎯 Вы сменили цель на {$targetTypeName} {$newTarget->name}",
            'info'
        );

        // Мгновенно наносим урон новой цели
        $attackResult = $this->performInstantAttackAfterTargetChange($user, $newTarget, $mineLocation, $slug);

        return redirect()->route('battle.mines.custom.index', $slug)
            ->with('success', "Цель сменена на {$targetTypeName} {$newTarget->name}. " . $attackResult['message']);
    }

    /**
     * Использование умения игроком
     *
     * @param Request $request Запрос
     * @param string $slug Идентификатор локации
     * @param int $skillId ID умения
     * @return \Illuminate\Http\RedirectResponse
     */
    public function useSkill(Request $request, $slug, $skillId)
    {
        // Логируем вызов метода
        \Log::info('Вызов useSkill в CustomMineController', [
            'skillId' => $skillId,
            'slug' => $slug,
            'request' => $request->all(),
        ]);

        // Проверяем, что $skillId — это число
        if (!is_numeric($skillId)) {
            \Log::error('Skill ID не является числом', ['skillId' => $skillId]);
            return back()->with('error', 'Некорректный ID умения');
        }

        try {
            $user = Auth::user();

            // Находим локацию по slug
            $mineLocation = MineLocation::where('slug', $slug)
                ->where('is_active', true)
                ->firstOrFail();

            // Получаем ключ для логов боя с учетом slug для подлокаций
            $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

            // Получение умения
            $skill = Skill::find($skillId);

            if (!$skill) {
                \Log::error('Умение не найдено', ['skillId' => $skillId]);
                return back()->with('error', 'Умение не найдено');
            }

            // Проверяем, что $skill - это объект Skill, а не коллекция
            if ($skill instanceof \Illuminate\Database\Eloquent\Collection) {
                \Log::error('$skill является коллекцией, а не объектом', [
                    'skillId' => $skillId,
                    'count' => $skill->count()
                ]);

                // Если это коллекция, возьмем первый элемент
                if ($skill->count() > 0) {
                    $skill = $skill->first();
                    \Log::info('Извлечен первый элемент из коллекции умений', [
                        'skill_id' => $skill->id,
                        'skill_name' => $skill->name
                    ]);
                } else {
                    return back()->with('error', 'Умение не найдено (пустая коллекция)');
                }
            }

            $targetType = $request->input('target_type'); // 'user' или 'mob'
            $targetId = $request->input('target_id');
            $location = $mineLocation->name;

            // Определяем цель
            $target = null;
            if ($skill->target_type !== 'all_allies' && $skill->target_type !== 'all_enemies') {
                if ($targetType === 'user') {
                    $target = User::findOrFail($targetId);
                } elseif ($targetType === 'mob') {
                    $target = Mob::findOrFail($targetId);
                } elseif ($skill->target_type === 'self') {
                    $target = $user;
                }
            }

            // Логируем типы данных перед вызовом useSkill
            \Log::info('Перед вызовом useSkill', [
                'caster_type' => get_class($user),
                'target_type' => $target ? get_class($target) : 'null',
                'skill_type' => get_class($skill),
                'skill_id' => $skill->id,
                'location' => $location
            ]);

            // Отключаем логирование баффов в SkillService, т.к. мы будем логировать результат здесь
            $this->skillService->setShouldLogBuffEffects(false);

            // Применяем скилл
            $result = $this->skillService->useSkill($user, $target, $skill, $location);

            if (!$result['success']) {
                \Log::warning('Неудачное использование умения', [
                    'skillName' => $skill->name,
                    'message' => $result['message']
                ]);
                return back()->with('error', $result['message']);
            }

            \Log::info('Успешное использование умения', [
                'skillName' => $skill->name,
                'message' => $result['message']
            ]);

            // Логируем результат в боевой лог
            $logMessage = '';
            if (isset($result['damage']) && $result['damage'] > 0) {
                $logMessage = sprintf(
                    '<div class="flex items-center">
                        <span class="flex items-center text-xs leading-tight">
                            <span style="color: #ff6b6b;">%s →</span>
                            <span class="mx-1">%s</span>
                            <span style="color: #ff6b6b;">-%d</span>
                        </span>
                    </div>',
                    $skill->name,
                    $target ? ($target->name ?? 'Цель') : 'Все цели',
                    $result['damage']
                );
            } else {
                $logMessage = sprintf(
                    '<div class="flex items-center">
                        <span class="flex items-center text-xs leading-tight">
                            <span style="color: #4ecdc4;">%s</span>
                            <span class="mx-1">→</span>
                            <span>%s</span>
                        </span>
                    </div>',
                    $skill->name,
                    $target ? ($target->name ?? 'Цель') : 'Все цели'
                );
            }

            $this->battleLogService->addLog($battleLogKey, $logMessage, $result['success'] ? 'success' : 'error');

            // Возвращаем пользователя назад с сообщением об успехе или ошибке
            return back()->with('success', $result['message']);

        } catch (\Exception $e) {
            \Log::error('Ошибка при использовании умения', [
                'skillId' => $skillId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return back()->with('error', 'Произошла ошибка: ' . $e->getMessage());
        }
    }

    /**
     * Выполняет мгновенную атаку после смены цели
     *
     * @param User $user Атакующий игрок
     * @param mixed $target Цель атаки (игрок или бот)
     * @param MineLocation $mineLocation Локация рудника
     * @param string $slug Slug локации
     * @return array Результат атаки
     */
    protected function performInstantAttackAfterTargetChange($user, $target, $mineLocation, $slug)
    {
        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $mineLocation->name, $slug);

        try {
            // Проверяем тип цели и выполняем соответствующую атаку
            if ($target instanceof \App\Models\User) {
                // Атака игрока
                return $this->performPlayerAttackAfterTargetChange($user, $target, $mineLocation, $battleLogKey);
            } else {
                // Атака бота
                return $this->performBotAttackAfterTargetChange($user, $target, $mineLocation, $battleLogKey);
            }
        } catch (\Exception $e) {
            // Логируем ошибку
            Log::error('Ошибка при мгновенной атаке после смены цели', [
                'user_id' => $user->id,
                'target_type' => get_class($target),
                'target_id' => $target->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Добавляем запись в боевой лог об ошибке
            $this->battleLogService->addLog(
                $battleLogKey,
                "❌ Произошла ошибка при атаке новой цели",
                'error'
            );

            return [
                'success' => false,
                'message' => 'Произошла ошибка при атаке новой цели'
            ];
        }
    }

    /**
     * Выполняет атаку игрока после смены цели
     *
     * @param User $attacker Атакующий игрок
     * @param User $target Цель атаки
     * @param MineLocation $mineLocation Локация рудника
     * @param string $battleLogKey Ключ боевого лога
     * @return array Результат атаки
     */
    protected function performPlayerAttackAfterTargetChange($attacker, $target, $mineLocation, $battleLogKey)
    {
        // Получаем актуальные данные о HP цели из Redis
        $targetActualResources = $target->profile->getActualResources();

        // Проверяем, что цель жива и находится в локации
        if ($targetActualResources['current_hp'] <= 0) {
            return [
                'success' => false,
                'message' => 'Цель уже побеждена'
            ];
        }

        // Рассчитываем урон
        $damage = $this->calculatePvPDamage($attacker, $target);

        // Получаем сервис для работы со здоровьем игрока
        $playerHealthService = app(\App\Services\PlayerHealthService::class);

        // Применяем урон через Redis (синхронизация с БД происходит автоматически)
        $damageResult = $playerHealthService->applyDamage($target, $damage, "player:{$attacker->id}");

        $oldHp = $damageResult['old_hp'];
        $newHp = $damageResult['new_hp'];
        $isDead = $damageResult['is_dead'];

        // Логируем для отладки урон
        Log::info("PvP Урон применен через PlayerHealthService при смене цели в рудниках", [
            'attacker_id' => $attacker->id,
            'defender_id' => $target->id,
            'old_hp' => $oldHp,
            'damage' => $damage,
            'new_hp' => $newHp,
            'is_dead' => $isDead,
        ]);

        // Обновляем информацию о последнем атакующем
        $target->update([
            'last_attacker_id' => $attacker->id,
            'last_attacker_type' => 'player'
        ]);

        // Рассчитываем фактический урон для начисления опыта
        $effectiveDamage = min($damage, $oldHp);

        // Начисляем опыт за фактически нанесенный урон
        $this->damageCalculator->awardExperienceForDamage($attacker, $effectiveDamage, $mineLocation->name);

        // Логируем атаку
        $attackLog = $this->logFormatter->formatPlayerAttack($attacker, $target, $damage, false);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'success');

        // Логируем для цели
        $targetLogKey = $this->getBattleLogKey($target, $mineLocation->name, $mineLocation->slug);
        $defendLog = $this->logFormatter->formatPlayerDefend($target, $attacker, $damage, false);
        $this->battleLogService->addLog($targetLogKey, $defendLog, 'danger');

        // Проверяем смерть цели
        if ($target->profile->hp <= 0) {
            $this->handlePvPDeath($target, $attacker, $mineLocation);
            return [
                'success' => true,
                'message' => "Вы атаковали {$target->name} и нанесли {$damage} урона. Цель побеждена!"
            ];
        }

        return [
            'success' => true,
            'message' => "Вы атаковали {$target->name} и нанесли {$damage} урона"
        ];
    }

    /**
     * Выполняет атаку бота после смены цели
     *
     * @param User $attacker Атакующий игрок
     * @param \App\Models\Bot $target Цель атаки
     * @param MineLocation $mineLocation Локация рудника
     * @param string $battleLogKey Ключ боевого лога
     * @return array Результат атаки
     */
    protected function performBotAttackAfterTargetChange($attacker, $target, $mineLocation, $battleLogKey)
    {
        // Проверяем, что бот жив
        if ($target->hp <= 0) {
            return [
                'success' => false,
                'message' => 'Бот уже уничтожен'
            ];
        }

        // Рассчитываем урон игрока
        $userDamage = $attacker->profile->strength;
        $critChance = 10; // 10% шанс крита

        // Проверяем критический удар
        $isCrit = false;
        if (rand(1, 100) <= $critChance) {
            $userDamage *= 2;
            $isCrit = true;
        }

        // Применяем урон к боту
        $oldHp = $target->hp;
        $effectiveDamage = min($userDamage, $oldHp); // Фактический урон не больше оставшегося HP
        $target->hp = max(0, $target->hp - $userDamage);
        $target->save();

        // Начисляем опыт за фактически нанесенный урон
        $this->damageCalculator->awardExperienceForDamage($attacker, $effectiveDamage, $mineLocation->name);

        // Логируем атаку
        $attackLog = $this->logFormatter->formatPlayerAttack($attacker, $target, $userDamage, $isCrit);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'success');

        // Проверяем смерть бота
        if ($target->hp <= 0) {
            // Сбрасываем цель пользователя
            $attacker->current_target_type = null;
            $attacker->current_target_id = null;
            $attacker->save();

            // Логируем событие убийства бота
            $this->battleLogService->addLog(
                $battleLogKey,
                "💀 Вы уничтожили бота {$target->name} и получили опыт!",
                'success'
            );

            return [
                'success' => true,
                'message' => "Вы атаковали {$target->name} и нанесли {$userDamage} урона. Бот уничтожен!"
            ];
        }

        return [
            'success' => true,
            'message' => "Вы атаковали {$target->name} и нанесли {$userDamage} урона"
        ];
    }
}

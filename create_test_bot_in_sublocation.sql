-- SQL скрипт для создания тестового бота в подлокации testPod

-- Сначала найдем ID подлокации testPod
SELECT 
    ml.id as mine_location_id,
    ml.name as mine_location_name,
    ml.parent_id,
    ml.location_id,
    l.name as base_location_name
FROM mine_locations ml
LEFT JOIN locations l ON ml.location_id = l.id
WHERE ml.name = 'testPod' AND ml.is_active = true;

-- Создаем тестового бота в подлокации testPod
-- ВНИМАНИЕ: Замените {MINE_LOCATION_ID} на реальный ID подлокации testPod из запроса выше

INSERT INTO bots (
    name,
    level,
    hp,
    max_hp,
    mp,
    max_mp,
    attack,
    defense,
    race,
    class,
    location,
    mine_location_id,
    experience_reward,
    gold_reward,
    is_active,
    created_by_admin,
    created_at,
    updated_at
) VALUES (
    'Тестовый Лунный Воин',
    5,
    100,
    100,
    50,
    50,
    25,
    15,
    'lunarius',  -- Если игрок solarius, то бот должен быть lunarius (и наоборот)
    'warrior',
    'Шахта Тестовая',  -- Название базовой локации (замените на реальную)
    NULL,  -- ЗАМЕНИТЕ на ID подлокации testPod
    10,
    5,
    true,
    true,
    NOW(),
    NOW()
);

-- Если нужно создать бота для солярисов, используйте этот вариант:
INSERT INTO bots (
    name,
    level,
    hp,
    max_hp,
    mp,
    max_mp,
    attack,
    defense,
    race,
    class,
    location,
    mine_location_id,
    experience_reward,
    gold_reward,
    is_active,
    created_by_admin,
    created_at,
    updated_at
) VALUES (
    'Тестовый Солнечный Маг',
    5,
    80,
    80,
    100,
    100,
    20,
    10,
    'solarius',  -- Если игрок lunarius, то бот должен быть solarius
    'mage',
    'Шахта Тестовая',  -- Название базовой локации (замените на реальную)
    NULL,  -- ЗАМЕНИТЕ на ID подлокации testPod
    10,
    5,
    true,
    true,
    NOW(),
    NOW()
);

-- Проверяем созданных ботов
SELECT 
    b.id,
    b.name,
    b.race,
    b.class,
    b.hp,
    b.location,
    b.mine_location_id,
    ml.name as mine_location_name
FROM bots b
LEFT JOIN mine_locations ml ON b.mine_location_id = ml.id
WHERE b.mine_location_id = (
    SELECT id FROM mine_locations WHERE name = 'testPod' AND is_active = true LIMIT 1
)
AND b.is_active = true;
-- Быстрая проверка состояния ботов и их локаций

-- 1. Проверяем боты с ID локации вместо названия
SELECT 
    id,
    name,
    location,
    mine_location_id,
    race,
    is_active,
    created_by_admin
FROM bots 
WHERE created_by_admin = true 
  AND location REGEXP '^[0-9]+$'
ORDER BY location;

-- 2. Проверяем активные подлокации рудников
SELECT 
    ml.id,
    ml.name as mine_name,
    ml.location_id,
    l.name as base_location,
    ml.is_active,
    COUNT(b.id) as bots_count
FROM mine_locations ml
LEFT JOIN locations l ON ml.location_id = l.id
LEFT JOIN bots b ON b.location = ml.name AND b.created_by_admin = true
WHERE ml.is_active = true
GROUP BY ml.id, ml.name, ml.location_id, l.name, ml.is_active
ORDER BY ml.name;

-- 3. Проверяем онлайн игроков и их локации
SELECT 
    u.id,
    u.name,
    up.race,
    us.current_location,
    u.is_online,
    u.last_activity_timestamp,
    up.current_hp
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN user_statistics us ON u.id = us.user_id
WHERE u.is_online = true 
   OR u.last_activity_timestamp > UNIX_TIMESTAMP() - 1200  -- 20 минут назад
ORDER BY u.is_online DESC, u.last_activity_timestamp DESC;

-- 4. Проверяем ботов в подлокациях рудников
SELECT 
    b.id,
    b.name as bot_name,
    b.location,
    b.mine_location_id,
    b.race,
    b.is_active,
    b.hp,
    b.max_hp,
    b.next_action_time,
    ml.name as mine_location_name,
    ml.id as correct_mine_id
FROM bots b
LEFT JOIN mine_locations ml ON b.location = ml.name
WHERE b.created_by_admin = true
  AND b.is_active = true
ORDER BY b.location, b.name;

-- 5. Находим потенциальные проблемы
SELECT 
    'Боты с ID локации' as problem_type,
    COUNT(*) as count
FROM bots 
WHERE created_by_admin = true 
  AND location REGEXP '^[0-9]+$'

UNION ALL

SELECT 
    'Боты без mine_location_id в рудниках' as problem_type,
    COUNT(*) as count
FROM bots b
INNER JOIN mine_locations ml ON b.location = ml.name
WHERE b.created_by_admin = true
  AND (b.mine_location_id IS NULL OR b.mine_location_id != ml.id)

UNION ALL

SELECT 
    'Боты с кулдауном' as problem_type,
    COUNT(*) as count
FROM bots 
WHERE created_by_admin = true 
  AND next_action_time IS NOT NULL
  AND next_action_time > NOW();
<?php
/**
 * Простая проверка исправления атак ботов в рудниках
 * Запускается без Laravel для быстрой проверки логики
 */

echo "🔧 Проверка исправления атак ботов в рудниках\n";
echo "============================================\n\n";

// Проверяем наличие исправленных файлов
$filesToCheck = [
    'app/Services/BotBehaviorService.php',
    'app/Traits/OutpostLocationTrait.php', 
    'app/Console/Commands/TestMineBotsAttackFix.php'
];

$fixesFound = 0;

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        echo "📁 Проверка файла: {$file}\n";
        
        switch ($file) {
            case 'app/Services/BotBehaviorService.php':
                $checks = [
                    'isMineLocation' => 'Проверка рудников',
                    'areInSameMineSubLocation' => 'Проверка подлокаций', 
                    'areEnemyRaces' => 'Проверка враждебных рас',
                    'ИСПРАВЛЕНИЕ ПОДЛОКАЦИЙ' => 'Комментарий об исправлении'
                ];
                break;
                
            case 'app/Traits/OutpostLocationTrait.php':
                $checks = [
                    'isMineLocation' => 'Метод проверки рудников'
                ];
                break;
                
            case 'app/Console/Commands/TestMineBotsAttackFix.php':
                $checks = [
                    'подлокации' => 'Описание с подлокациями',
                    'sameSubLocation' => 'Проверка подлокаций в тесте'
                ];
                break;
        }
        
        foreach ($checks as $search => $description) {
            if (strpos($content, $search) !== false) {
                echo "  ✅ {$description}: НАЙДЕНО\n";
                $fixesFound++;
            } else {
                echo "  ❌ {$description}: НЕ НАЙДЕНО\n";
            }
        }
        echo "\n";
    } else {
        echo "❌ Файл не найден: {$file}\n\n";
    }
}

// Проверяем MineBotCacheService
if (file_exists('app/Services/Mine/MineBotCacheService.php')) {
    $content = file_get_contents('app/Services/Mine/MineBotCacheService.php');
    echo "📁 Проверка файла: app/Services/Mine/MineBotCacheService.php\n";
    
    if (strpos($content, 'protected string $prefix;') !== false) {
        echo "  ✅ Исправление prefix: НАЙДЕНО\n";
        $fixesFound++;
    } else {
        echo "  ❌ Исправление prefix: НЕ НАЙДЕНО\n";
    }
    
    if (strpos($content, "config('mine_bots', [])") !== false) {
        echo "  ✅ Безопасная загрузка конфигурации: НАЙДЕНО\n";
        $fixesFound++;
    } else {
        echo "  ❌ Безопасная загрузка конфигурации: НЕ НАЙДЕНО\n";
    }
    echo "\n";
}

// Итоги
echo "📊 РЕЗУЛЬТАТЫ ПРОВЕРКИ\n";
echo "=====================\n";
echo "Найдено исправлений: {$fixesFound}\n";

if ($fixesFound >= 6) {
    echo "🎉 Исправление выглядит ПОЛНЫМ! Все ключевые компоненты на месте.\n";
    echo "\n🔥 ОСНОВНЫЕ ИЗМЕНЕНИЯ:\n";
    echo "1. Боты атакуют враждебных игроков только в своей подлокации\n";
    echo "2. Добавлена проверка точного совпадения локаций\n";
    echo "3. Исправлена ошибка MineBotCacheService\n";
    echo "4. Создана команда для тестирования\n";
} else {
    echo "⚠️ Исправление выглядит НЕПОЛНЫМ. Требуется дополнительная проверка.\n";
}

echo "\n🛠️ Для полного тестирования запустите:\n";
echo "php artisan test:mine-bots-attack-fix --verbose\n";
?>
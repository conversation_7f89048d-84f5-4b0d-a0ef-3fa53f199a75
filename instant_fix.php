<?php

/**
 * МГНОВЕННОЕ ИСПРАВЛЕНИЕ ПРОБЛЕМЫ С АТАКАМИ БОТОВ
 * Исправляет всех ботов с неправильными локациями ПРЯМО СЕЙЧАС
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "⚡ МГНОВЕННОЕ ИСПРАВЛЕНИЕ БОТОВ\n";
echo "==============================\n\n";

// Исправляем ВСЕХ ботов с ID локации
$mineLocations = MineLocation::where('is_active', true)->get();
$totalFixed = 0;

foreach ($mineLocations as $mine) {
    $bots = Bot::where('location', (string) $mine->id)->get();
    
    foreach ($bots as $bot) {
        $bot->location = $mine->name;
        $bot->mine_location_id = $mine->id;
        $bot->next_action_time = null;
        $bot->save();
        
        echo "✅ {$bot->name}: ID:{$mine->id} → {$mine->name}\n";
        $totalFixed++;
    }
}

// Сбрасываем ВСЕ кулдауны
Bot::where('created_by_admin', true)->update(['next_action_time' => null]);

// Очищаем кэш
try {
    \Illuminate\Support\Facades\Artisan::call('cache:clear');
    echo "✅ Кэш очищен\n";
} catch (\Exception $e) {
    echo "⚠️ Ошибка очистки кэша: {$e->getMessage()}\n";
}

echo "\n🎉 ИСПРАВЛЕНО: {$totalFixed} ботов\n";
echo "⚔️ Теперь боты атакуют только в своих подлокациях!\n";

// Показываем текущее состояние
echo "\n📊 ТЕКУЩЕЕ СОСТОЯНИЕ:\n";
$problemBots = Bot::whereRaw('location REGEXP \'^[0-9]+$\'')->count();
echo "Проблемных ботов осталось: {$problemBots}\n";

if ($problemBots === 0) {
    echo "✅ ВСЕ ПРОБЛЕМЫ РЕШЕНЫ!\n";
} else {
    echo "⚠️ Требуется повторный запуск\n";
}

echo "\nВремя: " . now()->format('H:i:s d.m.Y') . "\n";
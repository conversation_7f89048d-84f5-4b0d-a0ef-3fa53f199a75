<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\ActiveEffect;
use App\Services\MineDetectionService;
use App\Services\MineDetectionServiceFallback;

class TestMineDetection extends Command
{
    protected $signature = 'mine:test-detection {--user=admin}';
    protected $description = 'Тестирование системы обнаружения в рудниках';

    public function handle()
    {
        $this->info('🧪 ТЕСТИРОВАНИЕ СИСТЕМЫ ОБНАРУЖЕНИЯ В РУДНИКАХ');
        $this->info('===============================================');
        $this->newLine();

        // 1. Проверяем основные компоненты
        $this->info('1️⃣ Проверка системных компонентов...');
        
        $hasMineMarks = Schema::hasTable('mine_marks');
        $hasActiveEffects = Schema::hasTable('active_effects');
        
        $this->line("   Таблица mine_marks: " . ($hasMineMarks ? '✅ Есть' : '❌ Нет'));
        $this->line("   Таблица active_effects: " . ($hasActiveEffects ? '✅ Есть' : '❌ Нет'));

        // 2. Находим тестовые данные
        $this->newLine();
        $this->info('2️⃣ Поиск тестовых данных...');
        
        $userName = $this->option('user');
        $user = User::where('name', $userName)->first() ?? User::with('profile')->first();
        
        if (!$user) {
            $this->error('❌ Пользователи не найдены!');
            return 1;
        }
        
        $this->line("   Пользователь: {$user->name} (ID: {$user->id})");
        
        $mineLocation = MineLocation::where('is_active', true)->first() ?? MineLocation::first();
        
        if (!$mineLocation) {
            $this->error('❌ Локации рудников не найдены!');
            return 1;
        }
        
        $this->line("   Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})");
        $this->line("   Основная локация: ID {$mineLocation->location_id}");

        // 3. Проверяем текущие дебафы
        $this->newLine();
        $this->info('3️⃣ Проверка текущих дебафов...');
        
        if ($hasMineMarks) {
            $activeMarks = MineMark::where('player_id', $user->id)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->count();
            $this->line("   Активных меток (mine_marks): {$activeMarks}");
        }
        
        $activeEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $user->id)
            ->where('effect_type', 'mine_detection_debuff')
            ->where('ends_at', '>', now())
            ->count();
        $this->line("   Активных дебафов (active_effects): {$activeEffects}");

        // 4. Тестируем создание дебафа
        $this->newLine();
        $this->info('4️⃣ ТЕСТИРОВАНИЕ СОЗДАНИЯ ДЕБАФА...');
        
        try {
            if ($hasMineMarks) {
                $this->line('   🎯 Использование MineDetectionService...');
                $service = app(MineDetectionService::class);
                $result = $service->applyDetectionDebuff($user, $mineLocation);
            } else {
                $this->line('   🎯 Использование MineDetectionServiceFallback...');
                $service = app(MineDetectionServiceFallback::class);
                $result = $service->applyDetectionDebuff($user, $mineLocation);
            }
            
            if ($result) {
                $this->info("   ✅ Дебаф создан успешно! (ID: {$result->id})");
                $this->line("   ⏰ Истекает: " . ($result->expires_at ?? $result->ends_at));
            } else {
                $this->error('   ❌ Не удалось создать дебаф!');
            }
            
        } catch (\Exception $e) {
            $this->error('   ❌ ОШИБКА: ' . $e->getMessage());
            $this->line('   📍 ' . $e->getFile() . ':' . $e->getLine());
        }

        // 5. Проверяем обнаружение
        $this->newLine();
        $this->info('5️⃣ Проверка обнаружения игрока...');
        
        try {
            if ($hasMineMarks) {
                $isDetected = app(MineDetectionService::class)->hasActiveMark($user->id, $mineLocation->location_id, $mineLocation->id);
            } else {
                $isDetected = app(MineDetectionServiceFallback::class)->isPlayerDetected($user->id, $mineLocation->location_id, $mineLocation->id);
            }
            
            $this->line("   Игрок обнаружен: " . ($isDetected ? '✅ Да' : '❌ Нет'));
            
        } catch (\Exception $e) {
            $this->error('   ❌ Ошибка проверки: ' . $e->getMessage());
        }

        // 6. Финальная статистика
        $this->newLine();
        $this->info('6️⃣ Финальная статистика...');
        
        if ($hasMineMarks) {
            $totalMarks = MineMark::where('player_id', $user->id)->count();
            $activeMarks = MineMark::where('player_id', $user->id)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->count();
            $this->line("   Всего меток: {$totalMarks}, активных: {$activeMarks}");
        }
        
        $totalEffects = ActiveEffect::where('target_id', $user->id)
            ->where('effect_type', 'mine_detection_debuff')
            ->count();
        $activeEffectsTotal = ActiveEffect::where('target_id', $user->id)
            ->where('effect_type', 'mine_detection_debuff')
            ->where('ends_at', '>', now())
            ->count();
        $this->line("   Всего дебафов: {$totalEffects}, активных: {$activeEffectsTotal}");

        // 7. Рекомендации
        $this->newLine();
        $this->info('7️⃣ РЕКОМЕНДАЦИИ...');
        
        if (!$hasMineMarks) {
            $this->warn('💡 Создайте таблицу mine_marks для оптимальной работы:');
            $this->line('   php artisan migrate --path=database/migrations/2025_07_21_160000_create_mine_marks_table_fixed.php');
        }
        
        $this->info('💡 Для проверки в игре:');
        $this->line('   1. Зайдите в рудник как ' . $user->name);
        $this->line('   2. Выберите ресурс и нажмите "Добыть"');
        $this->line('   3. Проверьте компонент активных эффектов');
        
        $this->info('💡 Для запуска автоатак:');
        $this->line('   php artisan mine:auto-attack');
        
        $this->newLine();
        $this->info('✅ Тестирование завершено!');
        
        return 0;
    }
}
<?php

/**
 * Быстрый тест с очисткой логов для диагностики дебафа "Замечен"
 */

require_once __DIR__ . '/vendor/autoload.php';

// Инициализируем Laravel приложение
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 БЫСТРЫЙ ТЕСТ СИСТЕМЫ ОБНАРУЖЕНИЯ В РУДНИКАХ\n";
echo "============================================\n\n";

// Очищаем лог для чистого тестирования
$logFile = storage_path('logs/laravel.log');
if (file_exists($logFile)) {
    // Создаем бэкап и очищаем лог
    $backupFile = storage_path('logs/laravel_backup_' . date('Y-m-d_H-i-s') . '.log');
    copy($logFile, $backupFile);
    file_put_contents($logFile, '');
    echo "✅ Лог очищен (бэкап: " . basename($backupFile) . ")\n\n";
}

echo "📋 ИНСТРУКЦИИ ДЛЯ ТЕСТИРОВАНИЯ:\n";
echo "==============================\n";
echo "1. Зайдите в игру под пользователем admin\n";
echo "2. Перейдите в любой рудник\n";
echo "3. Выберите ресурс для добычи\n";
echo "4. Нажмите 'Добыть' ОДИН РАЗ\n";
echo "5. Вернитесь в терминал и нажмите Enter\n\n";

echo "Ожидание действий пользователя... (Нажмите Enter после теста)\n";
fgets(STDIN);

echo "\n🔍 АНАЛИЗ ЛОГОВ ПОСЛЕ ТЕСТИРОВАНИЯ:\n";
echo "===================================\n";

if (file_exists($logFile) && filesize($logFile) > 0) {
    echo "📊 Содержимое логов:\n";
    $logContent = file_get_contents($logFile);
    
    // Ищем записи о дебафе обнаружения
    $lines = explode("\n", $logContent);
    $found = false;
    
    foreach ($lines as $line) {
        if (strpos($line, '[MINE DEBUG]') !== false || 
            strpos($line, 'mine_detection') !== false ||
            strpos($line, 'замечен') !== false) {
            echo "   🎯 " . trim($line) . "\n";
            $found = true;
        }
    }
    
    if (!$found) {
        echo "   ❌ Записей о системе обнаружения НЕ НАЙДЕНО!\n";
        echo "   💡 Возможные причины:\n";
        echo "      - Вы не нажимали кнопку 'Добыть'\n";
        echo "      - Ошибка в пути до контроллера\n";
        echo "      - Проблема с маршрутизацией\n\n";
        
        echo "   📋 Все записи в логе:\n";
        foreach ($lines as $line) {
            if (!empty(trim($line))) {
                echo "      " . trim($line) . "\n";
            }
        }
    } else {
        echo "\n✅ Записи о системе обнаружения найдены!\n";
    }
} else {
    echo "❌ Лог пуст или не существует!\n";
    echo "💡 Это означает, что код контроллера не выполнялся\n";
}

echo "\n📋 ДОПОЛНИТЕЛЬНАЯ ДИАГНОСТИКА:\n";
echo "==============================\n";

// Проверяем активные эффекты
use App\Models\ActiveEffect;
use App\Models\MineMark;
use App\Models\User;
use Illuminate\Support\Facades\Schema;

try {
    $user = User::where('name', 'admin')->first();
    if ($user) {
        echo "👤 Пользователь admin найден (ID: {$user->id})\n";
        
        // Проверяем mine_marks
        if (Schema::hasTable('mine_marks')) {
            $marks = MineMark::where('player_id', $user->id)->count();
            echo "📊 Всего меток в mine_marks: {$marks}\n";
            
            $activeMarks = MineMark::where('player_id', $user->id)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->count();
            echo "📊 Активных меток в mine_marks: {$activeMarks}\n";
        } else {
            echo "❌ Таблица mine_marks не существует\n";
        }
        
        // Проверяем active_effects
        $effects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $user->id)
            ->where('effect_type', 'mine_detection_debuff')
            ->count();
        echo "📊 Всего дебафов в active_effects: {$effects}\n";
        
        $activeEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $user->id)
            ->where('effect_type', 'mine_detection_debuff')
            ->where('ends_at', '>', now())
            ->count();
        echo "📊 Активных дебафов в active_effects: {$activeEffects}\n";
        
    } else {
        echo "❌ Пользователь admin не найден!\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка при проверке БД: " . $e->getMessage() . "\n";
}

echo "\n🎯 СЛЕДУЮЩИЕ ШАГИ:\n";
echo "==================\n";

if (!$found) {
    echo "1. Убедитесь, что вы нажимали кнопку 'Добыть' в рудниках\n";
    echo "2. Проверьте маршруты: php artisan route:list | grep mine\n";
    echo "3. Запустите полную диагностику: php debug_mine_detection_real.php\n";
} else {
    echo "1. Проанализируйте записи в логах выше\n";
    echo "2. Если дебаф не применяется, проверьте ошибки в логах\n";
    echo "3. Убедитесь, что планировщик запущен: php artisan schedule:work\n";
}

echo "\nТест завершен!\n";
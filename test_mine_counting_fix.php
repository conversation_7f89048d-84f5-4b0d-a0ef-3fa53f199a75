<?php

require_once 'vendor/autoload.php';

use App\Models\Bot;
use App\Models\User;
use App\Models\MineLocation;
use App\Services\battle\FactionCountService;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Тестирование исправления подсчета в рудниках ===\n\n";

// Получаем сервис
$factionCountService = app(\App\Services\battle\FactionCountService::class);

// Тестируем несколько локаций рудников
$testMineLocations = MineLocation::where('is_active', true)->take(3)->get();

foreach ($testMineLocations as $mineLocation) {
    echo "=== Тестирование: {$mineLocation->name} (ID: {$mineLocation->id}) ===\n";
    echo "Тип: " . ($mineLocation->isSubLocation() ? 'Подлокация' : 'Базовая локация') . "\n";
    
    // Тестируем единообразный метод getMineFactionCounts
    echo "\n--- Результат getMineFactionCounts ---\n";
    $factionCounts = $factionCountService->getMineFactionCounts($mineLocation->id);
    
    echo "Солариус (всего): ";
    echo "Воины={$factionCounts['total_counts']['solarius']['warriors']}, ";
    echo "Маги={$factionCounts['total_counts']['solarius']['mages']}, ";
    echo "Жрецы={$factionCounts['total_counts']['solarius']['knights']}, ";
    echo "Итого={$factionCounts['total_counts']['solarius']['total']}\n";
    
    echo "Лунариус (всего): ";
    echo "Воины={$factionCounts['total_counts']['lunarius']['warriors']}, ";
    echo "Маги={$factionCounts['total_counts']['lunarius']['mages']}, ";
    echo "Жрецы={$factionCounts['total_counts']['lunarius']['knights']}, ";
    echo "Итого={$factionCounts['total_counts']['lunarius']['total']}\n";
    
    echo "\nРазделение игроки/боты:\n";
    echo "Игроки Солариус: {$factionCounts['player_counts']['solarius']['total']}\n";
    echo "Боты Солариус: {$factionCounts['bot_counts']['solarius']['total']}\n";
    echo "Игроки Лунариус: {$factionCounts['player_counts']['lunarius']['total']}\n";
    echo "Боты Лунариус: {$factionCounts['bot_counts']['lunarius']['total']}\n";
    
    // Проверяем ботов вручную для сравнения
    echo "\n--- Проверка ботов вручную ---\n";
    $manualBots = Bot::where('mine_location_id', $mineLocation->id)
        ->where('is_active', true)
        ->where('hp', '>', 0)
        ->get();
    
    echo "Ботов найдено вручную: " . $manualBots->count() . "\n";
    
    if ($manualBots->count() > 0) {
        $manualBotsByRace = $manualBots->groupBy('race');
        foreach ($manualBotsByRace as $race => $bots) {
            echo "  {$race}: " . $bots->count() . " ботов\n";
        }
    }
    
    // Проверяем игроков вручную
    echo "\n--- Проверка игроков вручную ---\n";
    $manualPlayers = User::whereHas('statistics', function ($q) use ($mineLocation) {
        $q->where('current_location', $mineLocation->name);
    })
    ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
    ->with(['profile', 'statistics'])
    ->get();
    
    echo "Игроков найдено вручную: " . $manualPlayers->count() . "\n";
    
    if ($manualPlayers->count() > 0) {
        $manualPlayersByRace = $manualPlayers->groupBy(function ($player) {
            return $player->profile->race ?? 'unknown';
        });
        foreach ($manualPlayersByRace as $race => $players) {
            echo "  {$race}: " . $players->count() . " игроков\n";
        }
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

echo "=== Проверка ошибок в логах ===\n";
echo "Рекомендуется проверить логи Laravel на наличие warning сообщений:\n";
echo "tail -f storage/logs/laravel.log | grep -i 'FactionCountService.*неподдерживаем'\n\n";

echo "=== Завершено ===\n";
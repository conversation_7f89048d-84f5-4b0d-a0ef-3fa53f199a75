<?php

namespace App\Services\Mine;

use App\Models\Bot;
use App\Models\User;
use App\Models\Location;
use App\Models\MineLocation;
use App\Services\PlayerHealthService;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Collection;

/**
 * Улучшенный сервис для управления ботами рудников с системой обнаружения
 * 
 * Расширяет базовый MineBotService дополнительной логикой:
 * - Интеграция с системой обнаружения игроков
 * - Приоритетное нацеливание на обнаруженных игроков
 * - Улучшенная статистика и логирование
 */
class EnhancedMineBotService extends MineBotService
{
    protected MineDetectionService $detectionService;
    protected EnhancedMineBotAttackService $enhancedAttackService;

    public function __construct(
        MineBotCacheService $cacheService,
        EnhancedMineBotAttackService $enhancedAttackService,
        MineBotHealingService $healingService,
        PlayerHealthService $playerHealthService,
        MineDetectionService $detectionService
    ) {
        // Используем enhanced attack service вместо стандартного
        parent::__construct($cacheService, $enhancedAttackService, $healingService, $playerHealthService);
        $this->enhancedAttackService = $enhancedAttackService;
        $this->detectionService = $detectionService;
    }

    /**
     * Обрабатывает всех ботов в руднике с учетом системы обнаружения
     */
    public function processMineBots(string $mineName): array
    {
        $startTime = microtime(true);
        $stats = [
            'mine' => $mineName,
            'bots_processed' => 0,
            'attacks_executed' => 0,
            'heals_executed' => 0,
            'errors' => 0,
            'execution_time' => 0,
            'detected_players_found' => 0,
            'detection_based_attacks' => 0,
        ];

        try {
            Log::info("EnhancedMineBotService: Начало обработки ботов в руднике {$mineName}");

            // Проверяем, что это действительно рудник
            if (!$this->isValidMine($mineName)) {
                throw new \Exception("Локация {$mineName} не является активным рудником");
            }

            // Получаем информацию о рудничной локации
            $mineLocation = MineLocation::where('slug', $mineName)->first();
            if (!$mineLocation) {
                throw new \Exception("Рудничная локация {$mineName} не найдена");
            }

            // Получаем статистику обнаруженных игроков
            $detectedPlayers = $this->detectionService->getDetectedPlayers(
                $mineLocation->location_id, 
                $mineLocation->id
            );
            $stats['detected_players_found'] = count($detectedPlayers);

            if ($stats['detected_players_found'] > 0) {
                Log::info("EnhancedMineBotService: Найдено обнаруженных игроков в {$mineName}", [
                    'detected_count' => $stats['detected_players_found'],
                    'detected_players' => array_column($detectedPlayers, 'player_name')
                ]);
            }

            // Кэшируем данные локации
            $this->cacheLocationData($mineName);

            // Получаем активных ботов в руднике
            $bots = $this->getActiveMineBots($mineName);

            if ($bots->isEmpty()) {
                Log::info("EnhancedMineBotService: Нет активных ботов в руднике {$mineName}");
                return $stats;
            }

            Log::info("EnhancedMineBotService: Найдено {$bots->count()} активных ботов в руднике {$mineName}");

            // Обрабатываем ботов пакетами для оптимизации
            $batchSize = $this->config['processing']['batch_size'];
            $batches = $bots->chunk($batchSize);

            foreach ($batches as $batch) {
                $batchStats = $this->processEnhancedBotBatch($batch, $mineName, $mineLocation);
                $stats['bots_processed'] += $batchStats['bots_processed'];
                $stats['attacks_executed'] += $batchStats['attacks_executed'];
                $stats['heals_executed'] += $batchStats['heals_executed'];
                $stats['errors'] += $batchStats['errors'];
                $stats['detection_based_attacks'] += $batchStats['detection_based_attacks'];

                // Проверяем лимиты памяти и времени
                if ($this->shouldStopProcessing($startTime)) {
                    Log::warning("EnhancedMineBotService: Остановка обработки из-за лимитов времени/памяти");
                    break;
                }
            }

        } catch (\Exception $e) {
            Log::error("EnhancedMineBotService: Ошибка обработки ботов в руднике {$mineName}: " . $e->getMessage());
            $stats['errors']++;
        }

        $stats['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);

        Log::info("EnhancedMineBotService: Завершена обработка ботов в руднике {$mineName}", $stats);

        return $stats;
    }

    /**
     * Обрабатывает пакет ботов с улучшенной логикой обнаружения
     */
    protected function processEnhancedBotBatch(Collection $bots, string $mineName, MineLocation $mineLocation): array
    {
        $stats = [
            'bots_processed' => 0,
            'attacks_executed' => 0,
            'heals_executed' => 0,
            'errors' => 0,
            'detection_based_attacks' => 0,
        ];

        foreach ($bots as $bot) {
            try {
                $stats['bots_processed']++;

                // Проверяем здоровье бота
                if ($bot->current_hp <= 0) {
                    Log::debug("EnhancedMineBotService: Бот {$bot->id} мертв, пропускаем");
                    continue;
                }

                // Проверяем, нужно ли лечение (приоритет)
                if ($this->botNeedsHealing($bot)) {
                    if ($this->healingService->processHealing($bot, $mineName)) {
                        $stats['heals_executed']++;
                        Log::debug("EnhancedMineBotService: Бот {$bot->id} получил лечение");
                    }
                    continue; // Пропускаем атаку если лечились
                }

                // Проверяем наличие обнаруженных игроков для приоритетной атаки
                $detectedPlayers = $this->detectionService->getDetectedPlayers(
                    $mineLocation->location_id, 
                    $mineLocation->id
                );

                $wasDetectionBasedAttack = false;
                if (!empty($detectedPlayers)) {
                    // Увеличиваем шанс атаки если есть обнаруженные игроки
                    $attackChance = 85; // Повышенный шанс атаки для обнаруженных целей
                    $wasDetectionBasedAttack = true;
                } else {
                    $attackChance = 35; // Стандартный шанс атаки
                }

                // Проверяем шанс атаки
                if (rand(1, 100) <= $attackChance) {
                    if ($this->enhancedAttackService->processAttack($bot, $mineName)) {
                        $stats['attacks_executed']++;
                        if ($wasDetectionBasedAttack) {
                            $stats['detection_based_attacks']++;
                        }
                        
                        Log::debug("EnhancedMineBotService: Бот {$bot->id} выполнил атаку", [
                            'detection_based' => $wasDetectionBasedAttack,
                            'detected_players_available' => count($detectedPlayers)
                        ]);
                    }
                }

            } catch (\Exception $e) {
                Log::error("EnhancedMineBotService: Ошибка обработки бота {$bot->id}: " . $e->getMessage());
                $stats['errors']++;
            }
        }

        return $stats;
    }

    /**
     * Получить статистику системы обнаружения
     */
    public function getDetectionStatistics(): array
    {
        return $this->detectionService->getDetectionStatistics();
    }

    /**
     * Очистить истекшие дебафы обнаружения
     */
    public function cleanupExpiredDetectionEffects(): int
    {
        return $this->detectionService->cleanupExpiredDetectionEffects();
    }

    /**
     * Проверить, нужно ли боту лечение
     */
    protected function botNeedsHealing(Bot $bot): bool
    {
        $hpPercent = $bot->current_hp / $bot->max_hp;
        return $hpPercent < 0.7; // Лечимся если HP ниже 70%
    }

    /**
     * Получить расширенную статистику работы сервиса
     */
    public function getEnhancedStats(string $mineName): array
    {
        $mineLocation = MineLocation::where('slug', $mineName)->first();
        if (!$mineLocation) {
            return [];
        }

        $stats = [
            'mine_name' => $mineName,
            'mine_location_id' => $mineLocation->id,
            'base_location_id' => $mineLocation->location_id,
            'active_bots' => $this->getActiveMineBots($mineName)->count(),
            'detected_players' => count($this->detectionService->getDetectedPlayers(
                $mineLocation->location_id, 
                $mineLocation->id
            )),
            'cache_hits' => $this->cacheService->getCacheHitRate(),
            'last_processed' => now()->toISOString()
        ];

        return $stats;
    }

    /**
     * Форсированная атака на обнаруженных игроков (для тестирования/админских команд)
     */
    public function forceAttackDetectedPlayers(string $mineName): array
    {
        $results = [
            'mine' => $mineName,
            'attacks_attempted' => 0,
            'attacks_successful' => 0,
            'detected_players' => 0,
            'errors' => []
        ];

        try {
            $mineLocation = MineLocation::where('slug', $mineName)->first();
            if (!$mineLocation) {
                throw new \Exception("Рудничная локация не найдена");
            }

            $detectedPlayers = $this->detectionService->getDetectedPlayers(
                $mineLocation->location_id, 
                $mineLocation->id
            );
            $results['detected_players'] = count($detectedPlayers);

            if (empty($detectedPlayers)) {
                return $results;
            }

            $bots = $this->getActiveMineBots($mineName);
            foreach ($bots as $bot) {
                if ($bot->current_hp <= 0) continue;

                $results['attacks_attempted']++;
                
                if ($this->enhancedAttackService->processAttack($bot, $mineName)) {
                    $results['attacks_successful']++;
                } else {
                    $results['errors'][] = "Бот {$bot->id} не смог атаковать";
                }
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }
}
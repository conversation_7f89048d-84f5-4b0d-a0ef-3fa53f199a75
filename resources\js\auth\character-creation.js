/**
 * Система создания персонажа с выбором расы и класса
 * Включает интерактивность и иконки
 */

document.addEventListener('DOMContentLoaded', function() {
    const raceSelect = document.getElementById('race');
    const classSelect = document.getElementById('class');
    
    // Данные о расах и классах с иконками
    const raceData = {
        'solarius': {
            name: 'Солариус',
            icon: '/assets/race/Racesolarius.png',
            description: 'Дети света',
            classes: {
                'warrior': {
                    name: 'Воин',
                    icon: '/assets/race/raceSolarWarrior.png'
                },
                'priest': {
                    name: 'Жрец',
                    icon: '/assets/race/raceSolarPriest.png'
                },
                'mage': {
                    name: '<PERSON><PERSON>г',
                    icon: '/assets/race/raceSolarMage.png'
                }
            }
        },
        'lunarius': {
            name: 'Лу<PERSON>риу<PERSON>',
            icon: '/assets/race/Racelunarius.png',
            description: 'Дети тьмы',
            classes: {
                'warrior': {
                    name: 'Воин',
                    icon: '/assets/race/raceLunarWarrior.png'
                },
                'priest': {
                    name: 'Жрец',
                    icon: '/assets/race/raceLunarPriest.png'
                },
                'mage': {
                    name: 'Маг',
                    icon: '/assets/race/raceLunarMage.png'
                }
            }
        }
    };

    // Инициализация - блокируем выбор класса
    classSelect.disabled = true;
    classSelect.innerHTML = '<option value="">Сначала выберите расу</option>';

    // Обработчик изменения расы
    raceSelect.addEventListener('change', function() {
        const selectedRace = this.value;
        
        if (selectedRace === '') {
            // Если раса не выбрана, блокируем класс
            classSelect.disabled = true;
            classSelect.innerHTML = '<option value="">Сначала выберите расу</option>';
            return;
        }

        // Разблокируем выбор класса
        classSelect.disabled = false;
        classSelect.innerHTML = '<option value="">Выберите класс</option>';
        
        // Добавляем доступные классы для выбранной расы
        const race = raceData[selectedRace];
        if (race && race.classes) {
            Object.keys(race.classes).forEach(classKey => {
                const classData = race.classes[classKey];
                const option = document.createElement('option');
                option.value = classKey;
                option.textContent = classData.name;
                option.dataset.icon = classData.icon;
                classSelect.appendChild(option);
            });
        }
    });

    // Функция для создания кастомного селекта с иконками
    function createCustomSelect(selectElement, data, placeholder = 'Выберите опцию') {
        // Создаем контейнер для кастомного селекта
        const container = document.createElement('div');
        container.className = 'custom-select-container relative';
        
        // Создаем отображаемую кнопку
        const button = document.createElement('button');
        button.type = 'button';
        button.className = `custom-select-button w-full p-3 border-2 border-[#3b3629] bg-[#1a1814] text-[#d4cbb0] rounded-lg 
                           focus:outline-none focus:border-[#c1a96e] transition-all duration-200 flex items-center justify-between`;
        button.style.textShadow = '1px 1px 2px rgba(0,0,0,0.8)';
        
        const buttonContent = document.createElement('div');
        buttonContent.className = 'flex items-center';
        
        const buttonIcon = document.createElement('img');
        buttonIcon.className = 'w-5 h-5 mr-2 hidden';
        buttonIcon.alt = '';
        
        const buttonText = document.createElement('span');
        buttonText.textContent = placeholder;
        
        const buttonArrow = document.createElement('svg');
        buttonArrow.className = 'w-4 h-4 transition-transform duration-200';
        buttonArrow.setAttribute('fill', 'none');
        buttonArrow.setAttribute('stroke', 'currentColor');
        buttonArrow.setAttribute('viewBox', '0 0 24 24');
        buttonArrow.innerHTML = `
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
        `;
        
        buttonContent.appendChild(buttonIcon);
        buttonContent.appendChild(buttonText);
        button.appendChild(buttonContent);
        button.appendChild(buttonArrow);
        
        // Создаем выпадающий список
        const dropdown = document.createElement('div');
        dropdown.className = `custom-select-dropdown absolute top-full left-0 right-0 z-50 bg-[#1a1814] border-2 border-[#3b3629] 
                             rounded-lg shadow-lg mt-1 max-h-48 overflow-y-auto hidden`;
        
        // Функция для обновления содержимого dropdown
        function updateDropdown(options) {
            dropdown.innerHTML = '';
            
            options.forEach(option => {
                const item = document.createElement('div');
                item.className = 'custom-select-item flex items-center p-3 hover:bg-[#2a2722] cursor-pointer transition-colors duration-200';
                
                if (option.icon) {
                    const icon = document.createElement('img');
                    icon.src = option.icon;
                    icon.className = 'w-5 h-5 mr-2';
                    icon.alt = option.name;
                    icon.onerror = function() {
                        this.style.display = 'none';
                    };
                    item.appendChild(icon);
                }
                
                const text = document.createElement('span');
                text.textContent = option.name;
                text.className = 'text-[#d4cbb0]';
                text.style.textShadow = '1px 1px 2px rgba(0,0,0,0.8)';
                item.appendChild(text);
                
                if (option.description) {
                    const desc = document.createElement('span');
                    desc.textContent = ` - ${option.description}`;
                    desc.className = 'text-[#998d66] text-sm ml-1';
                    item.appendChild(desc);
                }
                
                item.addEventListener('click', function() {
                    selectElement.value = option.value;
                    
                    // Обновляем отображение кнопки
                    if (option.icon) {
                        buttonIcon.src = option.icon;
                        buttonIcon.classList.remove('hidden');
                        buttonIcon.onerror = function() {
                            this.classList.add('hidden');
                        };
                    } else {
                        buttonIcon.classList.add('hidden');
                    }
                    
                    buttonText.textContent = option.name;
                    dropdown.classList.add('hidden');
                    buttonArrow.style.transform = 'rotate(0deg)';
                    
                    // Вызываем событие изменения
                    const changeEvent = new Event('change', { bubbles: true });
                    selectElement.dispatchEvent(changeEvent);
                });
                
                dropdown.appendChild(item);
            });
        }
        
        // Обработчик клика по кнопке
        button.addEventListener('click', function() {
            if (selectElement.disabled) return;
            
            const isOpen = !dropdown.classList.contains('hidden');
            
            if (isOpen) {
                dropdown.classList.add('hidden');
                buttonArrow.style.transform = 'rotate(0deg)';
            } else {
                dropdown.classList.remove('hidden');
                buttonArrow.style.transform = 'rotate(180deg)';
            }
        });
        
        // Закрытие при клике вне селекта
        document.addEventListener('click', function(e) {
            if (!container.contains(e.target)) {
                dropdown.classList.add('hidden');
                buttonArrow.style.transform = 'rotate(0deg)';
            }
        });
        
        container.appendChild(button);
        container.appendChild(dropdown);
        
        // Заменяем оригинальный селект
        selectElement.style.display = 'none';
        selectElement.parentNode.insertBefore(container, selectElement.nextSibling);
        
        // Функция для обновления состояния disabled
        function updateDisabledState() {
            if (selectElement.disabled) {
                container.classList.add('disabled');
                button.classList.add('opacity-50', 'cursor-not-allowed');
                button.classList.remove('hover:border-[#c1a96e]');
            } else {
                container.classList.remove('disabled');
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                button.classList.add('hover:border-[#c1a96e]');
            }
        }
        
        // Следим за изменениями disabled состояния
        new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'disabled') {
                    updateDisabledState();
                }
            });
        }).observe(selectElement, { attributes: true });
        
        // Инициализируем состояние
        updateDisabledState();
        
        // Возвращаем функцию для обновления опций
        return {
            updateOptions: updateDropdown,
            reset: function() {
                buttonIcon.classList.add('hidden');
                buttonText.textContent = placeholder;
                dropdown.classList.add('hidden');
                buttonArrow.style.transform = 'rotate(0deg)';
            },
            setDisabled: function(disabled) {
                selectElement.disabled = disabled;
                updateDisabledState();
            }
        };
    }
    
    // Создаем кастомные селекты
    const raceOptions = Object.keys(raceData).map(key => ({
        value: key,
        name: raceData[key].name,
        icon: raceData[key].icon,
        description: raceData[key].description
    }));
    
    const customRaceSelect = createCustomSelect(raceSelect, raceOptions, 'Выберите расу');
    const customClassSelect = createCustomSelect(classSelect, [], 'Сначала выберите расу');
    
    // Обновляем обработчик изменения расы для кастомных селектов
    raceSelect.addEventListener('change', function() {
        const selectedRace = this.value;
        
        if (selectedRace === '') {
            customClassSelect.reset();
            customClassSelect.setDisabled(true);
            return;
        }
        
        customClassSelect.setDisabled(false);
        classSelect.value = '';
        
        // Обновляем опции класса
        const race = raceData[selectedRace];
        if (race && race.classes) {
            const classOptions = Object.keys(race.classes).map(classKey => ({
                value: classKey,
                name: race.classes[classKey].name,
                icon: race.classes[classKey].icon
            }));
            
            customClassSelect.updateOptions(classOptions);
            customClassSelect.reset();
        }
    });
});
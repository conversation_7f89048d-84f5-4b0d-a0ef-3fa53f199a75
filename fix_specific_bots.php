<?php

/**
 * ИСПРАВЛЕНИЕ КОНКРЕТНЫХ БОТОВ, КОТОРЫЕ АТАКУЮТ
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Bot;
use App\Models\MineLocation;
use Illuminate\Support\Facades\DB;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 ИСПРАВЛЕНИЕ КОНКРЕТНЫХ БОТОВ\n";
echo "==============================\n\n";

// Имена ботов, которые атакуют
$attackingBots = ['nvvng2', 'cccccc2', 'nvvng', 'иииииииииииии2222'];
$targetLocation = 'xzxzxzx';

echo "Цель: привязать ботов к подлокации '{$targetLocation}'\n";
echo "Боты для исправления: " . implode(', ', $attackingBots) . "\n\n";

// Найдем подлокацию
$mineLocation = MineLocation::where('name', $targetLocation)->first();

if (!$mineLocation) {
    echo "❌ Подлокация '{$targetLocation}' не найдена!\n";
    echo "Доступные подлокации:\n";
    
    $availableLocations = MineLocation::where('is_active', true)->get();
    foreach ($availableLocations as $loc) {
        echo "  - {$loc->name} (ID: {$loc->id})\n";
    }
    exit;
}

echo "✅ Подлокация найдена: {$mineLocation->name} (ID: {$mineLocation->id})\n\n";

// Исправляем каждого бота
$fixedCount = 0;
$notFoundCount = 0;

foreach ($attackingBots as $botName) {
    $bot = Bot::where('name', $botName)->first();
    
    if ($bot) {
        $oldLocation = $bot->location;
        $oldMineId = $bot->mine_location_id;
        
        // Исправляем локацию
        $bot->location = $mineLocation->name;
        $bot->mine_location_id = $mineLocation->id;
        $bot->next_action_time = null; // Сбрасываем кулдаун
        $bot->save();
        
        echo "✅ {$botName}:\n";
        echo "   location: '{$oldLocation}' → '{$bot->location}'\n";
        echo "   mine_location_id: {$oldMineId} → {$bot->mine_location_id}\n";
        echo "   Кулдаун сброшен\n\n";
        
        $fixedCount++;
    } else {
        echo "❌ {$botName}: не найден в базе данных\n\n";
        $notFoundCount++;
    }
}

// Дополнительно исправляем всех ботов с ID подлокации
echo "🔍 ПОИСК ДОПОЛНИТЕЛЬНЫХ БОТОВ С ID ЛОКАЦИИ\n";
echo "=========================================\n";

$additionalBots = Bot::where('location', (string) $mineLocation->id)
    ->where('created_by_admin', true)
    ->get();

if ($additionalBots->count() > 0) {
    echo "Найдено дополнительных ботов с ID локации: {$additionalBots->count()}\n";
    
    foreach ($additionalBots as $bot) {
        $bot->location = $mineLocation->name;
        $bot->mine_location_id = $mineLocation->id;
        $bot->next_action_time = null;
        $bot->save();
        
        echo "✅ {$bot->name}: ID:{$mineLocation->id} → {$mineLocation->name}\n";
        $fixedCount++;
    }
} else {
    echo "✅ Дополнительных ботов с ID локации не найдено\n";
}

// Итоговая статистика
echo "\n📊 ИТОГОВАЯ СТАТИСТИКА\n";
echo "=====================\n";
echo "Исправлено ботов: {$fixedCount}\n";
echo "Не найдено ботов: {$notFoundCount}\n";

// Проверяем результат в админке
echo "\n🔍 ПРОВЕРКА АДМИНКИ\n";
echo "==================\n";

$adminBots = Bot::where('location', $targetLocation)
    ->where('created_by_admin', true)
    ->get();

echo "Ботов в админке теперь: {$adminBots->count()}\n";
foreach ($adminBots as $bot) {
    $active = $bot->is_active ? '✅' : '❌';
    echo "{$active} {$bot->name} (HP: {$bot->hp}/{$bot->max_hp})\n";
}

// Проверяем, остались ли проблемные боты
echo "\n⚠️  ПРОВЕРКА ПРОБЛЕМНЫХ БОТОВ\n";
echo "============================\n";

$problemBots = Bot::where('created_by_admin', true)
    ->whereRaw('location REGEXP \'^[0-9]+$\'')
    ->get();

if ($problemBots->count() > 0) {
    echo "❌ Осталось ботов с ID локации: {$problemBots->count()}\n";
    foreach ($problemBots as $bot) {
        echo "  - {$bot->name} (location: '{$bot->location}')\n";
    }
} else {
    echo "✅ Все боты исправлены!\n";
}

echo "\n🎉 ИСПРАВЛЕНИЕ ЗАВЕРШЕНО\n";
echo "=======================\n";
echo "Время: " . now()->format('H:i:s d.m.Y') . "\n";
echo "Теперь в админке должны отображаться все боты из подлокации!\n";
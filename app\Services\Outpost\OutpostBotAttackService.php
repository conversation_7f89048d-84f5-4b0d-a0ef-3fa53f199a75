<?php

namespace App\Services\Outpost;

use App\Models\Bot;
use App\Models\User;
use App\Services\PlayerHealthService;
use App\Services\BotTargetDistributionService;
use App\Events\Outpost\BotAttackEvent;
use App\Events\BotTargetSwitchEvent;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Collection;

/**
 * Сервис для обработки атак ботов в аванпостах
 * Реализует умное переключение целей и рандомизированные интервалы атак
 */
class OutpostBotAttackService
{
    protected OutpostBotCacheService $cacheService;
    protected PlayerHealthService $playerHealthService;
    protected BotTargetDistributionService $targetDistributionService;
    protected array $config;

    public function __construct(
        OutpostBotCacheService $cacheService,
        PlayerHealthService $playerHealthService,
        BotTargetDistributionService $targetDistributionService
    ) {
        $this->cacheService = $cacheService;
        $this->playerHealthService = $playerHealthService;
        $this->targetDistributionService = $targetDistributionService;
        $this->config = config('outpost_bots');
    }

    /**
     * Обрабатывает атаку бота
     */
    public function processAttack(Bot $bot, string $outpostName): bool
    {
        try {
            // Проверяем кулдаун атаки
            if ($this->cacheService->hasAttackCooldown($bot->id)) {
                return false;
            }

            // Ищем цель для атаки
            $target = $this->findTarget($bot, $outpostName);

            if (!$target) {
                Log::debug("OutpostBotAttackService: Бот {$bot->id} не нашел цель для атаки в {$outpostName}");
                return false;
            }

            // Выполняем атаку
            $attackResult = $this->executeAttack($bot, $target);

            if ($attackResult['success']) {
                // Устанавливаем кулдаун атаки
                $cooldown = rand(
                    $this->config['attacks']['interval_min'],
                    $this->config['attacks']['interval_max']
                );
                $this->cacheService->setAttackCooldown($bot->id, $cooldown);

                // Обновляем информацию о последней атаке
                $this->updateBotAttackInfo($bot, $target, $attackResult);

                // Генерируем событие атаки
                event(new BotAttackEvent($bot, $target, $attackResult));

                Log::info("OutpostBotAttackService: Бот {$bot->name} атаковал {$target->name} на {$attackResult['damage']} урона");

                return true;
            }

        } catch (\Exception $e) {
            Log::error("OutpostBotAttackService: Ошибка атаки бота {$bot->id}: " . $e->getMessage());
        }

        return false;
    }

    /**
     * Ищет цель для атаки с умным распределением и переключением
     */
    protected function findTarget(Bot $bot, string $outpostName): ?User
    {
        // Проверяем, нужно ли сменить текущую цель (динамическое переключение)
        if ($this->shouldSwitchCurrentTarget($bot, $outpostName)) {
            $this->releaseCurrentTarget($bot, $outpostName);
        }

        // Получаем потенциальных целей (игроки враждебной расы)
        $enemyRace = $this->getEnemyRace($bot->race);
        $potentialTargets = $this->getPotentialTargets($outpostName, $enemyRace);

        if ($potentialTargets->isEmpty()) {
            return null;
        }

        // Используем сервис умного распределения целей
        return $this->targetDistributionService->getOptimalTarget(
            $bot,
            $potentialTargets,
            'outpost',
            $outpostName
        );
    }

    /**
     * Получает потенциальных целей для атаки
     */
    protected function getPotentialTargets(string $outpostName, string $enemyRace): Collection
    {
        // Определяем онлайн-статус по активности в последние 20 минут
        $onlineThreshold = now()->timestamp - (20 * 60); // 20 минут назад

        // Исправлено: используем правильную связь через user_statistics.current_location и profile.race
        return User::whereHas('statistics', function ($query) use ($outpostName) {
            $query->where('current_location', $outpostName);
        })
            ->whereHas('profile', function ($query) use ($enemyRace) {
                $query->where('race', $enemyRace);
            })
            ->where(function ($query) use ($onlineThreshold) {
                // Игрок считается онлайн если:
                // 1. is_online = true ИЛИ
                // 2. last_activity_timestamp больше порога (активность в последние 20 минут)
                $query->where('is_online', true)
                    ->orWhere('last_activity_timestamp', '>', $onlineThreshold);
            })
            ->with(['profile', 'statistics'])
            ->get()
            ->filter(function ($user) {
                // Проверяем, что у игрока есть HP
                $currentHP = $this->playerHealthService->getCurrentHP($user);
                return $currentHP > 0;
            });
    }

    /**
     * Выбирает цель с умным переключением на основе недавнего урона
     */
    protected function selectTargetWithIntelligentSwitching(Bot $bot, Collection $targets): ?User
    {
        $now = Carbon::now();
        $damageRecencyThreshold = $this->config['attacks']['target_switching']['damage_recency_threshold'];
        $maxTargetsToConsider = $this->config['attacks']['target_switching']['max_targets_to_consider'];

        // Сортируем цели по приоритету
        $prioritizedTargets = $targets->sortBy(function ($target) use ($bot, $now, $damageRecencyThreshold) {
            $currentHP = $this->playerHealthService->getCurrentHP($target);
            $maxHP = $target->profile->max_hp;
            $hpPercent = ($currentHP / $maxHP) * 100;

            // Приоритет 1: Игроки с низким HP (менее 30%)
            if ($hpPercent < 30) {
                return 1;
            }

            // Приоритет 2: Текущая цель бота (если она еще жива)
            if ($bot->current_target_id == $target->id && $bot->current_target_type == 'user') {
                return 2;
            }

            // Приоритет 3: Игроки, которые недавно получили урон от этого бота
            if (
                $bot->last_attacker_id == $target->id &&
                $bot->last_attack_time &&
                $bot->last_attack_time->diffInSeconds($now) <= $damageRecencyThreshold
            ) {
                return 3;
            }

            // Приоритет 4: Игроки с низким уровнем
            return 4 + $target->level;
        });

        // Берем топ-N целей для случайного выбора
        $topTargets = $prioritizedTargets->take($maxTargetsToConsider);

        if ($topTargets->isEmpty()) {
            return null;
        }

        // Случайно выбираем из топ-целей
        return $topTargets->random();
    }

    /**
     * Выполняет атаку
     */
    protected function executeAttack(Bot $bot, User $target): array
    {
        // Рассчитываем урон
        $damage = $this->calculateDamage($bot, $target);

        // Применяем урон через PlayerHealthService (автоматически обновляет Redis и БД)
        $damageResult = $this->playerHealthService->applyDamage($target, $damage, "bot:{$bot->id}");

        // Обновляем кэш
        $this->cacheService->updatePlayerHP($target->id, $damageResult['new_hp']);

        return [
            'success' => true,
            'damage' => $damageResult['damage'],
            'target_hp_before' => $damageResult['old_hp'],
            'target_hp_after' => $damageResult['new_hp'],
            'is_death' => $damageResult['is_dead'],
            'timestamp' => Carbon::now(),
        ];
    }

    /**
     * Рассчитывает урон атаки
     */
    protected function calculateDamage(Bot $bot, User $target): int
    {
        // Базовый урон на основе характеристик бота
        $baseDamage = $this->getBaseDamage($bot);

        // Получаем броню цели
        $targetArmor = $target->profile->armor ?? 0;

        // Рассчитываем снижение урона от брони (формула из CombatFormulaService)
        $armorReduction = 0;
        if ($targetArmor > 0) {
            $armorReduction = (91.77 * $targetArmor) / ($targetArmor + 669.64) / 100;
            $armorReduction = min($armorReduction, 0.9177); // Максимум 91.77%
        }

        // Применяем снижение от брони
        $finalDamage = $baseDamage * (1 - $armorReduction);

        // Добавляем случайность ±10%
        $randomFactor = rand(90, 110) / 100;
        $finalDamage *= $randomFactor;

        return max(1, intval($finalDamage));
    }

    /**
     * Получает базовый урон бота
     */
    protected function getBaseDamage(Bot $bot): int
    {
        $multipliers = $this->config['attacks']['damage_multipliers'];

        switch ($bot->class) {
            case 'warrior':
                return intval($bot->strength * $multipliers['warrior']);
            case 'mage':
                return intval($bot->intelligence * $multipliers['mage']);
            case 'priest':
                return intval($bot->intelligence * $multipliers['priest']);
            default:
                return intval($bot->strength * 2.0);
        }
    }

    /**
     * Обновляет информацию о последней атаке бота
     */
    protected function updateBotAttackInfo(Bot $bot, User $target, array $attackResult): void
    {
        $bot->update([
            'current_target_id' => $target->id,
            'current_target_type' => 'user',
            'last_attack_time' => $attackResult['timestamp'],
            'last_attacker_id' => $target->id,
            'last_attacker_type' => 'user',
        ]);
    }

    /**
     * Получает враждебную расу
     */
    protected function getEnemyRace(string $botRace): string
    {
        return $botRace === 'solarius' ? 'lunarius' : 'solarius';
    }

    /**
     * Проверяет, нужно ли боту сменить текущую цель
     */
    protected function shouldSwitchCurrentTarget(Bot $bot, string $outpostName): bool
    {
        // Если у бота нет текущей цели, смена не нужна
        if (!$bot->current_target_id || $bot->current_target_type !== 'player') {
            return false;
        }

        // Проверяем через сервис распределения целей
        return $this->targetDistributionService->shouldSwitchTarget($bot, 'outpost', $outpostName);
    }

    /**
     * Освобождает текущую цель бота
     */
    protected function releaseCurrentTarget(Bot $bot, string $outpostName): void
    {
        $previousTarget = null;

        if ($bot->current_target_id && $bot->current_target_type === 'player') {
            $previousTarget = User::find($bot->current_target_id);
        }

        // Освобождаем назначение в сервисе распределения
        $this->targetDistributionService->releaseTargetAssignment($bot, 'outpost', $outpostName);

        // Сбрасываем цель в БД
        $bot->current_target_id = null;
        $bot->current_target_type = null;
        $bot->save();

        // Генерируем событие смены цели
        if ($previousTarget) {
            event(new BotTargetSwitchEvent(
                $bot,
                $previousTarget,
                null,
                'outpost',
                $outpostName,
                'dynamic_switch'
            ));
        }

        Log::debug("OutpostBotAttackService: Бот {$bot->name} освободил текущую цель для динамического переключения");
    }

    /**
     * Проверяет, может ли бот атаковать цель
     */
    public function canAttackTarget(Bot $bot, User $target): bool
    {
        // Проверяем расовый конфликт
        if (!$this->config['security']['check_race_conflicts']) {
            return true;
        }

        $enemyRace = $this->getEnemyRace($bot->race);

        // ИСПРАВЛЕНО: Проверяем расу через profile
        if (!$target->profile || $target->profile->race !== $enemyRace) {
            return false;
        }

        // Проверяем, что цель жива
        $currentHP = $this->playerHealthService->getCurrentHP($target);
        if ($currentHP <= 0) {
            return false;
        }

        // Проверяем, что цель онлайн
        if (!$target->is_online) {
            return false;
        }

        return true;
    }

    /**
     * Получает статистику атак для аванпоста
     */
    public function getAttackStats(string $outpostName): array
    {
        $bots = Bot::where('location', $outpostName)
            ->where('is_active', true)
            ->get();

        $totalAttacks = 0;
        $recentAttacks = 0;
        $now = Carbon::now();

        foreach ($bots as $bot) {
            if ($bot->last_attack_time) {
                $totalAttacks++;
                if ($bot->last_attack_time->diffInMinutes($now) <= 5) {
                    $recentAttacks++;
                }
            }
        }

        return [
            'outpost' => $outpostName,
            'total_attacks' => $totalAttacks,
            'recent_attacks' => $recentAttacks,
            'active_attackers' => $bots->where('is_active', true)->where('hp', '>', 0)->count(),
        ];
    }
}

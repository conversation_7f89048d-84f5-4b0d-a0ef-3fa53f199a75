<?php

/**
 * Простейший тест системы обнаружения через artisan команду
 */

// Запускаем через artisan для правильной инициализации <PERSON>
if (php_sapi_name() !== 'cli') {
    die('Этот скрипт должен запускаться из командной строки');
}

// Вместо инициализации <PERSON>, просто выполним artisan команды
echo "🧪 ПРОСТОЙ ТЕСТ СИСТЕМЫ ОБНАРУЖЕНИЯ\n";
echo "===================================\n\n";

echo "1️⃣ Проверка таблицы mine_marks...\n";
$checkTable = shell_exec('php artisan tinker --execute="echo Schema::hasTable(\'mine_marks\') ? \'YES\' : \'NO\';" 2>&1');
echo "   Таблица mine_marks: " . trim($checkTable) . "\n\n";

echo "2️⃣ Проверка пользователей...\n";
$userCheck = shell_exec('php artisan tinker --execute="echo User::count() . \' пользователей\';" 2>&1');
echo "   Пользователи: " . trim($userCheck) . "\n\n";

echo "3️⃣ Проверка локаций рудников...\n";
$mineCheck = shell_exec('php artisan tinker --execute="echo App\\Models\\MineLocation::count() . \' локаций\';" 2>&1');
echo "   Локации рудников: " . trim($mineCheck) . "\n\n";

echo "4️⃣ Тест создания дебафа...\n";
$testCode = '
$user = User::first();
$mine = App\\Models\\MineLocation::first();
if ($user && $mine) {
    try {
        if (Schema::hasTable("mine_marks")) {
            $service = app(App\\Services\\MineDetectionService::class);
            $result = $service->applyDetectionDebuff($user, $mine);
            echo $result ? "SUCCESS: Дебаф создан (ID: " . $result->id . ")" : "FAIL: Дебаф не создан";
        } else {
            $service = app(App\\Services\\MineDetectionServiceFallback::class);
            $result = $service->applyDetectionDebuff($user, $mine);
            echo $result ? "SUCCESS: Дебаф создан (ID: " . $result->id . ")" : "FAIL: Дебаф не создан";
        }
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage();
    }
} else {
    echo "FAIL: Нет данных для теста";
}
';

$testResult = shell_exec("php artisan tinker --execute=\"$testCode\" 2>&1");
echo "   Результат: " . trim($testResult) . "\n\n";

echo "5️⃣ Проверка активных дебафов...\n";
$activeCheck = shell_exec('php artisan tinker --execute="echo App\\Models\\ActiveEffect::where(\'effect_type\', \'mine_detection_debuff\')->where(\'ends_at\', \'>\', now())->count() . \' активных дебафов\';" 2>&1');
echo "   Активные дебафы: " . trim($activeCheck) . "\n\n";

if (strpos(trim($checkTable), 'YES') !== false) {
    $marksCheck = shell_exec('php artisan tinker --execute="echo App\\Models\\MineMark::where(\'is_active\', true)->where(\'expires_at\', \'>\', now())->count() . \' активных меток\';" 2>&1');
    echo "   Активные метки: " . trim($marksCheck) . "\n\n";
}

echo "✅ Тест завершен!\n\n";

echo "📋 СЛЕДУЮЩИЕ ДЕЙСТВИЯ:\n";
echo "=====================\n";
echo "1. Если тест прошел успешно - проблема в планировщике\n";
echo "2. Если дебаф не создается - проблема в сервисах\n";
echo "3. Если нет данных - нужно создать пользователей/локации\n\n";

echo "🔧 КОМАНДЫ ДЛЯ РУЧНОЙ ПРОВЕРКИ:\n";
echo "==============================\n";
echo "php artisan tinker\n";
echo ">>> \$user = User::first();\n";
echo ">>> \$mine = App\\Models\\MineLocation::first();\n";
echo ">>> \$service = app(App\\Services\\MineDetectionService::class);\n";
echo ">>> \$result = \$service->applyDetectionDebuff(\$user, \$mine);\n";
echo ">>> echo \$result ? 'Дебаф создан' : 'Дебаф не создан';\n";
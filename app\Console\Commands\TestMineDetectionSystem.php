<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MineDetectionService;
use App\Services\Mine\EnhancedMineBotService;
use App\Models\User;
use App\Models\MineLocation;
use App\Jobs\Mine\ProcessDetectedPlayersAttackJob;

class TestMineDetectionSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mine:test-detection 
                            {action=stats : Действие (stats|simulate|attack|cleanup)}
                            {--mine= : Конкретный рудник для обработки}
                            {--user= : ID пользователя для симуляции}
                            {--force : Принудительная атака}';

    /**
     * The console command description.
     */
    protected $description = 'Тестирование системы обнаружения в рудниках';

    /**
     * Execute the console command.
     */
    public function handle(
        MineDetectionService $detectionService,
        EnhancedMineBotService $mineBotService
    ): int {
        $action = $this->argument('action');
        
        $this->info("=== Тестирование системы обнаружения в рудниках ===");
        $this->info("Действие: {$action}");
        
        switch ($action) {
            case 'stats':
                return $this->showDetectionStats($detectionService);
                
            case 'simulate':
                return $this->simulateDetection($detectionService);
                
            case 'attack':
                return $this->testAttackSystem($mineBotService);
                
            case 'cleanup':
                return $this->cleanupExpiredEffects($detectionService);
                
            default:
                $this->error("Неизвестное действие: {$action}");
                $this->info("Доступные действия: stats, simulate, attack, cleanup");
                return 1;
        }
    }

    /**
     * Показать статистику обнаружения
     */
    protected function showDetectionStats(MineDetectionService $detectionService): int
    {
        $this->info("\n--- Статистика системы обнаружения ---");
        
        $stats = $detectionService->getDetectionStatistics();
        
        if (empty($stats)) {
            $this->info("Нет обнаруженных игроков");
            return 0;
        }
        
        $this->table(
            ['Локация', 'Обнаружено игроков', 'Среднее время до окончания (сек)'],
            array_map(function ($stat) {
                return [
                    $stat['location_name'],
                    $stat['detected_players'],
                    $stat['avg_remaining_time']
                ];
            }, $stats)
        );
        
        $totalDetected = array_sum(array_column($stats, 'detected_players'));
        $this->info("\nВсего обнаруженных игроков: {$totalDetected}");
        
        return 0;
    }

    /**
     * Симулировать обнаружение игрока
     */
    protected function simulateDetection(MineDetectionService $detectionService): int
    {
        $userId = $this->option('user');
        $mineSlug = $this->option('mine');
        
        if (!$userId) {
            $this->error("Необходимо указать ID пользователя с помощью --user=ID");
            return 1;
        }
        
        if (!$mineSlug) {
            $this->error("Необходимо указать рудник с помощью --mine=slug");
            return 1;
        }
        
        $user = User::find($userId);
        if (!$user) {
            $this->error("Пользователь с ID {$userId} не найден");
            return 1;
        }
        
        $mineLocation = MineLocation::where('slug', $mineSlug)->first();
        if (!$mineLocation) {
            $this->error("Рудник с slug '{$mineSlug}' не найден");
            return 1;
        }
        
        $this->info("\n--- Симуляция обнаружения ---");
        $this->info("Пользователь: {$user->name} (ID: {$user->id})");
        $this->info("Рудник: {$mineLocation->name} (slug: {$mineSlug})");
        
        // Применяем дебаф обнаружения
        $effect = $detectionService->applyDetectionDebuff($user, $mineLocation);
        
        if ($effect) {
            $this->info("✅ Дебаф обнаружения успешно применен");
            $this->info("ID эффекта: {$effect->id}");
            $this->info("Длительность: " . MineDetectionService::DETECTION_DURATION . " секунд");
            $this->info("Истекает: {$effect->ends_at}");
            
            // Проверяем, обнаружен ли игрок
            $isDetected = $detectionService->isPlayerDetected(
                $user->id, 
                $mineLocation->location_id, 
                $mineLocation->id
            );
            
            $this->info("Проверка обнаружения: " . ($isDetected ? "✅ Обнаружен" : "❌ Не обнаружен"));
            
        } else {
            $this->error("❌ Не удалось применить дебаф обнаружения");
            return 1;
        }
        
        return 0;
    }

    /**
     * Тестировать систему атак
     */
    protected function testAttackSystem(EnhancedMineBotService $mineBotService): int
    {
        $mineSlug = $this->option('mine');
        $forceAttack = $this->option('force');
        
        $this->info("\n--- Тестирование системы атак ---");
        
        if ($mineSlug) {
            $this->info("Рудник: {$mineSlug}");
            
            // Получаем статистику рудника
            $stats = $mineBotService->getEnhancedStats($mineSlug);
            $this->info("Активных ботов: {$stats['active_bots']}");
            $this->info("Обнаруженных игроков: {$stats['detected_players']}");
            
            if ($stats['detected_players'] == 0) {
                $this->warn("В руднике нет обнаруженных игроков для атаки");
            }
            
            // Запускаем атаку
            if ($forceAttack) {
                $this->info("Принудительная атака на обнаруженных игроков...");
                $attackStats = $mineBotService->forceAttackDetectedPlayers($mineSlug);
                
                $this->table(
                    ['Метрика', 'Значение'],
                    [
                        ['Попыток атак', $attackStats['attacks_attempted']],
                        ['Успешных атак', $attackStats['attacks_successful']],
                        ['Обнаружено игроков', $attackStats['detected_players']],
                        ['Ошибки', count($attackStats['errors'])]
                    ]
                );
                
                if (!empty($attackStats['errors'])) {
                    $this->error("Ошибки:");
                    foreach ($attackStats['errors'] as $error) {
                        $this->error("  - {$error}");
                    }
                }
            } else {
                $this->info("Обычная обработка ботов...");
                ProcessDetectedPlayersAttackJob::dispatchForMine($mineSlug);
                $this->info("Задача добавлена в очередь");
            }
        } else {
            $this->info("Обработка всех рудников...");
            
            if ($forceAttack) {
                $this->warn("Принудительная атака для всех рудников не поддерживается");
                return 1;
            } else {
                ProcessDetectedPlayersAttackJob::dispatchForAllMines();
                $this->info("Задача для всех рудников добавлена в очередь");
            }
        }
        
        return 0;
    }

    /**
     * Очистить истекшие эффекты
     */
    protected function cleanupExpiredEffects(MineDetectionService $detectionService): int
    {
        $this->info("\n--- Очистка истекших дебафов обнаружения ---");
        
        $cleanedCount = $detectionService->cleanupExpiredDetectionEffects();
        
        if ($cleanedCount > 0) {
            $this->info("✅ Очищено истекших дебафов: {$cleanedCount}");
        } else {
            $this->info("Нет истекших дебафов для очистки");
        }
        
        return 0;
    }
}
<?php

namespace App\Services\battle;

use App\Models\Bot;
use App\Models\MineLocation;
use App\Services\RedisKeyService;

/**
 * Сервис для подсчета количества игроков и ботов каждой фракции в локациях
 */
class FactionCountService
{
    protected RedisKeyService $redisKeyService;
    protected array $ttlConfig;

    public function __construct(RedisKeyService $redisKeyService)
    {
        $this->redisKeyService = $redisKeyService;
        $this->ttlConfig = config('cache_ttl') ?? [];
    }
    /**
     * Получает количество игроков и ботов по фракциям и классам в указанной локации
     * В счетчике отображаются только ОНЛАЙН игроки (активность за последние 5 минут)
     * и ВСЕ боты (включая союзных), но в блоке действий
     * для атаки доступны только боты противоположной расы
     *
     * @param string $location Название локации
     * @param string|null $userRace Раса текущего пользователя (не используется для фильтрации в счетчике)
     * @return array Массив с данными о количестве игроков и ботов
     */
    public function getLocationFactionCounts(string $location, ?string $userRace = null): array
    {
        // Логируем начало подсчета для отладки
        \Log::debug("FactionCountService: начало подсчета для локации", [
            'location' => $location,
            'user_race' => $userRace,
            'timestamp' => now()->toDateTimeString()
        ]);

        // Используем централизованный сервис для работы с локациями
        $locationService = app(\App\Services\battle\UserLocationService::class);

        // Нормализуем название локации
        $normalizedLocation = $locationService->normalizeLocationName($location);

        \Log::debug("FactionCountService: нормализовано название локации", [
            'original_location' => $location,
            'normalized_location' => $normalizedLocation
        ]);

        // Подсчет ОНЛАЙН игроков каждой фракции в локации с группировкой по классу
        // ИСПРАВЛЕНИЕ: Используем прямой поиск по полю race для единообразия с CustomMineController

        // Получаем всех онлайн игроков в локации
        $allPlayersInLocation = $locationService->getPlayersInLocation($normalizedLocation);

        // Функция для определения фракции игрока (только поддерживаемые расы)
        $getPlayerFaction = function ($player) {
            $userRace = $player->profile->race ?? null;

            if ($userRace === 'solarius') {
                return 'solarius';
            } elseif ($userRace === 'lunarius') {
                return 'lunarius';
            } else {
                // Если раса не определена или неподдерживаемая, логируем предупреждение
                \Log::warning('FactionCountService: игрок с неопределенной или неподдерживаемой расой', [
                    'player_id' => $player->id,
                    'player_name' => $player->name,
                    'race' => $userRace,
                    'supported_races' => ['solarius', 'lunarius']
                ]);
                return null; // Возвращаем null для исключения из подсчета
            }
        };

        // Разделяем игроков по фракциям и подсчитываем по классам
        $solWarriors = $allPlayersInLocation->filter(function ($player) use ($getPlayerFaction) {
            return $getPlayerFaction($player) === 'solarius' && $player->profile->class === 'warrior';
        })->count();

        $solMages = $allPlayersInLocation->filter(function ($player) use ($getPlayerFaction) {
            return $getPlayerFaction($player) === 'solarius' && $player->profile->class === 'mage';
        })->count();

        $solKnights = $allPlayersInLocation->filter(function ($player) use ($getPlayerFaction) {
            return $getPlayerFaction($player) === 'solarius' && $player->profile->class === 'priest';
        })->count();

        $lunWarriors = $allPlayersInLocation->filter(function ($player) use ($getPlayerFaction) {
            return $getPlayerFaction($player) === 'lunarius' && $player->profile->class === 'warrior';
        })->count();

        $lunMages = $allPlayersInLocation->filter(function ($player) use ($getPlayerFaction) {
            return $getPlayerFaction($player) === 'lunarius' && $player->profile->class === 'mage';
        })->count();

        $lunKnights = $allPlayersInLocation->filter(function ($player) use ($getPlayerFaction) {
            return $getPlayerFaction($player) === 'lunarius' && $player->profile->class === 'priest';
        })->count();

        \Log::debug("FactionCountService: игроки разделены между фракциями по полю race", [
            'reason' => 'Единообразие с CustomMineController::attackAnyPlayer',
            'location' => $normalizedLocation,
            'total_players_in_location' => $allPlayersInLocation->count(),
            'solarius_split' => ['warriors' => $solWarriors, 'mages' => $solMages, 'knights' => $solKnights],
            'lunarius_split' => ['warriors' => $lunWarriors, 'mages' => $lunMages, 'knights' => $lunKnights]
        ]);

        // ИСПРАВЛЕНО: Используем строгую изоляцию для подсчета ботов
        // Прямой подсчет ботов без использования потенциально некорректного метода
        $solBotWarriors = $this->getStrictBotsCountInLocation($normalizedLocation, 'solarius', 'warrior');
        $solBotMages = $this->getStrictBotsCountInLocation($normalizedLocation, 'solarius', 'mage');
        $solBotKnights = $this->getStrictBotsCountInLocation($normalizedLocation, 'solarius', 'priest');

        // Подсчет ботов Лунариус по классам
        $lunBotWarriors = $this->getStrictBotsCountInLocation($normalizedLocation, 'lunarius', 'warrior');
        $lunBotMages = $this->getStrictBotsCountInLocation($normalizedLocation, 'lunarius', 'mage');
        $lunBotKnights = $this->getStrictBotsCountInLocation($normalizedLocation, 'lunarius', 'priest');

        // Суммируем игроков и ботов для общего подсчета
        $totalSolWarriors = $solWarriors + $solBotWarriors;
        $totalSolMages = $solMages + $solBotMages;
        $totalSolKnights = $solKnights + $solBotKnights;
        $totalLunWarriors = $lunWarriors + $lunBotWarriors;
        $totalLunMages = $lunMages + $lunBotMages;
        $totalLunKnights = $lunKnights + $lunBotKnights;

        // Логируем результаты подсчета для отладки
        \Log::debug("FactionCountService: результаты подсчета", [
            'location' => $location,
            'player_counts_detailed' => [
                'solarius_warriors' => $solWarriors,
                'solarius_mages' => $solMages,
                'solarius_knights' => $solKnights,
                'lunarius_warriors' => $lunWarriors,
                'lunarius_mages' => $lunMages,
                'lunarius_knights' => $lunKnights,
                'solarius_total' => $solWarriors + $solMages + $solKnights,
                'lunarius_total' => $lunWarriors + $lunMages + $lunKnights
            ],
            'bot_counts' => [
                'solarius_total' => $solBotWarriors + $solBotMages + $solBotKnights,
                'lunarius_total' => $lunBotWarriors + $lunBotMages + $lunBotKnights
            ],
            'total_counts' => [
                'solarius_total' => $totalSolWarriors + $totalSolMages + $totalSolKnights,
                'lunarius_total' => $totalLunWarriors + $totalLunMages + $totalLunKnights
            ]
        ]);

        // Возвращаем массив с результатами
        return [
            // Отдельные данные для игроков
            'player_counts' => [
                'solarius' => [
                    'warriors' => $solWarriors,
                    'mages' => $solMages,
                    'knights' => $solKnights,
                    'total' => $solWarriors + $solMages + $solKnights
                ],
                'lunarius' => [
                    'warriors' => $lunWarriors,
                    'mages' => $lunMages,
                    'knights' => $lunKnights,
                    'total' => $lunWarriors + $lunMages + $lunKnights
                ]
            ],
            // Отдельные данные для ботов
            'bot_counts' => [
                'solarius' => [
                    'warriors' => $solBotWarriors,
                    'mages' => $solBotMages,
                    'knights' => $solBotKnights,
                    'total' => $solBotWarriors + $solBotMages + $solBotKnights
                ],
                'lunarius' => [
                    'warriors' => $lunBotWarriors,
                    'mages' => $lunBotMages,
                    'knights' => $lunBotKnights,
                    'total' => $lunBotWarriors + $lunBotMages + $lunBotKnights
                ]
            ],
            // Общие данные (игроки + боты)
            'total_counts' => [
                'solarius' => [
                    'warriors' => $totalSolWarriors,
                    'mages' => $totalSolMages,
                    'knights' => $totalSolKnights,
                    'total' => $totalSolWarriors + $totalSolMages + $totalSolKnights
                ],
                'lunarius' => [
                    'warriors' => $totalLunWarriors,
                    'mages' => $totalLunMages,
                    'knights' => $totalLunKnights,
                    'total' => $totalLunWarriors + $totalLunMages + $totalLunKnights
                ]
            ]
        ];
    }

    /**
     * Определяет правильное название локации для поиска ботов
     * ИСПРАВЛЕНО: Используется централизованный UserLocationService::normalizeLocationName()
     *
     * @param string $location Название локации (обычно из mine_locations)
     * @return string Название локации для поиска ботов
     */
    private function resolveBotLocationName(string $location): string
    {
        // ИСПРАВЛЕНИЕ: Используем централизованный сервис для нормализации
        $locationService = app(\App\Services\battle\UserLocationService::class);
        $normalizedLocation = $locationService->normalizeLocationName($location);

        \Log::debug("FactionCountService: нормализация локации через UserLocationService", [
            'original_location' => $location,
            'normalized_location' => $normalizedLocation
        ]);

        return $normalizedLocation;
    }

    /**
     * Получает количество игроков и ботов по фракциям для конкретной локации рудника
     * 
     * @param int $mineLocationId ID локации рудника
     * @param string|null $userRace Раса пользователя
     * @return array
     */
    public function getMineFactionCounts(int $mineLocationId, ?string $userRace = null): array
    {
        \Log::debug("FactionCountService: подсчет для рудника", [
            'mine_location_id' => $mineLocationId,
            'user_race' => $userRace
        ]);

        // Получаем локацию рудника
        $mineLocation = MineLocation::with('baseLocation')->find($mineLocationId);
        if (!$mineLocation) {
            \Log::warning('FactionCountService: Локация рудника не найдена', [
                'mine_location_id' => $mineLocationId
            ]);
            return $this->getEmptyFactionCounts();
        }
        
        // ИСПРАВЛЕНИЕ: Не требуем обязательного наличия baseLocation
        // Базовая локация может отсутствовать для некоторых рудников
        if (!$mineLocation->baseLocation) {
            \Log::debug('FactionCountService: Базовая локация отсутствует, продолжаем с локацией рудника', [
                'mine_location_id' => $mineLocationId,
                'mine_location_name' => $mineLocation->name
            ]);
        }

        // ИСПРАВЛЕНИЕ: Используем сервис для получения игроков в локации рудника, а не в базовой локации
        // Это обеспечивает единообразие с getLocationFactionCounts
        $locationService = app(\App\Services\battle\UserLocationService::class);
        $normalizedLocation = $locationService->normalizeLocationName($mineLocation->name);
        $allPlayersInLocation = $locationService->getPlayersInLocation($normalizedLocation);
        
        \Log::debug("FactionCountService: поиск игроков для рудника", [
            'mine_location_id' => $mineLocationId,
            'mine_location_name' => $mineLocation->name,
            'base_location_name' => $mineLocation->baseLocation->name ?? 'null',
            'normalized_location' => $normalizedLocation,
            'players_found' => $allPlayersInLocation->count(),
            'note' => 'ИСПРАВЛЕНИЕ: Используем имя локации рудника для единообразия с getLocationFactionCounts'
        ]);

        // Функция для определения фракции игрока
        $getPlayerFaction = function ($player) {
            $race = $player->profile->race ?? null;
            return in_array($race, ['solarius', 'lunarius']) ? $race : null;
        };

        // Подсчет игроков
        $playerCounts = $this->calculatePlayerCounts($allPlayersInLocation, $getPlayerFaction);

        // Подсчет ботов для данной локации рудника
        $botCounts = $this->calculateMineBotCounts($mineLocationId);

        // Подсчет общих значений
        $totalCounts = $this->calculateTotalCounts($playerCounts, $botCounts);

        return [
            'player_counts' => $playerCounts,
            'bot_counts' => $botCounts,
            'total_counts' => $totalCounts
        ];
    }

    /**
     * Подсчитывает ботов для конкретной локации рудника
     */
    private function calculateMineBotCounts(int $mineLocationId): array
    {
        // ЗАЩИТА: Получаем только корректных активных ботов в данной локации рудника
        $botsInMineLocation = Bot::where('mine_location_id', $mineLocationId)
            ->where('is_active', true)
            ->where('hp', '>', 0) // Исключаем мертвых ботов из подсчета
            ->whereNull('death_time') // Исключаем ботов с временем смерти
            ->whereNotNull('location') // Исключаем ботов без локации
            ->where('location', '!=', '') // Исключаем ботов с пустой локацией
            ->get();

        \Log::debug("FactionCountService: найдено ботов в руднике", [
            'mine_location_id' => $mineLocationId,
            'bots_count' => $botsInMineLocation->count()
        ]);

        return $this->calculateBotCounts($botsInMineLocation);
    }

    /**
     * Возвращает пустые счетчики фракций
     */
    private function getEmptyFactionCounts(): array
    {
        $empty = [
            'warriors' => 0,
            'mages' => 0,
            'knights' => 0,
            'total' => 0
        ];

        return [
            'player_counts' => [
                'solarius' => $empty,
                'lunarius' => $empty
            ],
            'bot_counts' => [
                'solarius' => $empty,
                'lunarius' => $empty
            ],
            'total_counts' => [
                'solarius' => $empty,
                'lunarius' => $empty
            ]
        ];
    }

    /**
     * Подсчитывает игроков по фракциям и классам
     */
    private function calculatePlayerCounts($players, $getFactionCallback): array
    {
        $playerCounts = [
            'solarius' => ['warriors' => 0, 'mages' => 0, 'knights' => 0, 'total' => 0],
            'lunarius' => ['warriors' => 0, 'mages' => 0, 'knights' => 0, 'total' => 0]
        ];

        foreach ($players as $player) {
            $faction = $getFactionCallback($player);
            if (!$faction) continue;

            $class = $player->profile->class ?? null;
            $playerCounts[$faction]['total']++;

            switch ($class) {
                case 'warrior':
                    $playerCounts[$faction]['warriors']++;
                    break;
                case 'mage':
                    $playerCounts[$faction]['mages']++;
                    break;
                case 'priest':
                    $playerCounts[$faction]['knights']++;
                    break;
                default:
                    // Логируем неподдерживаемые классы игроков
                    \Log::warning('FactionCountService: Игрок с неподдерживаемым классом', [
                        'player_id' => $player->id,
                        'player_name' => $player->name,
                        'class' => $class,
                        'race' => $player->profile->race ?? 'unknown',
                        'supported_classes' => ['warrior', 'mage', 'priest']
                    ]);
                    break;
            }
        }

        return $playerCounts;
    }

    /**
     * Подсчитывает ботов по фракциям и классам
     */
    private function calculateBotCounts($bots): array
    {
        $botCounts = [
            'solarius' => ['warriors' => 0, 'mages' => 0, 'knights' => 0, 'total' => 0],
            'lunarius' => ['warriors' => 0, 'mages' => 0, 'knights' => 0, 'total' => 0]
        ];

        foreach ($bots as $bot) {
            $faction = $bot->race;
            
            // Проверяем поддерживаемые расы
            if (!in_array($faction, ['solarius', 'lunarius'])) {
                \Log::warning('FactionCountService: Бот с неподдерживаемой расой', [
                    'bot_id' => $bot->id,
                    'bot_name' => $bot->name,
                    'race' => $faction,
                    'supported_races' => ['solarius', 'lunarius']
                ]);
                continue;
            }

            $class = $bot->class;
            $botCounts[$faction]['total']++;

            switch ($class) {
                case 'warrior':
                    $botCounts[$faction]['warriors']++;
                    break;
                case 'mage':
                    $botCounts[$faction]['mages']++;
                    break;
                case 'priest':
                    $botCounts[$faction]['knights']++;
                    break;
                default:
                    // Логируем неподдерживаемые классы ботов
                    \Log::warning('FactionCountService: Бот с неподдерживаемым классом', [
                        'bot_id' => $bot->id,
                        'bot_name' => $bot->name,
                        'class' => $class,
                        'race' => $faction,
                        'supported_classes' => ['warrior', 'mage', 'priest']
                    ]);
                    // Не увеличиваем счетчик класса, но общий total уже увеличен
                    break;
            }
        }

        return $botCounts;
    }

    /**
     * Подсчитывает общие значения (игроки + боты)
     */
    private function calculateTotalCounts($playerCounts, $botCounts): array
    {
        $totalCounts = [
            'solarius' => ['warriors' => 0, 'mages' => 0, 'knights' => 0, 'total' => 0],
            'lunarius' => ['warriors' => 0, 'mages' => 0, 'knights' => 0, 'total' => 0]
        ];

        foreach (['solarius', 'lunarius'] as $faction) {
            foreach (['warriors', 'mages', 'knights', 'total'] as $type) {
                $totalCounts[$faction][$type] = 
                    $playerCounts[$faction][$type] + $botCounts[$faction][$type];
            }
        }

        return $totalCounts;
    }

    /**
     * ИСПРАВЛЕНИЕ: Строгий подсчет ботов в локации без пересечений
     * Обеспечивает полную изоляцию между базовыми локациями и подлокациями рудников
     *
     * @param string $locationName Название локации
     * @param string $race Раса ботов
     * @param string $class Класс ботов
     * @return int Количество ботов
     */
    private function getStrictBotsCountInLocation(string $locationName, string $race, string $class): int
    {
        // Проверяем, является ли локация подлокацией рудника
        $mineLocation = MineLocation::where('name', $locationName)->first();

        // ЗАЩИТА: Фильтруем только корректных ботов
        $query = Bot::where('race', $race)
                   ->where('class', $class)
                   ->where('is_active', true)
                   ->where('hp', '>', 0)
                   ->whereNull('death_time') // Исключаем ботов с временем смерти
                   ->whereNotNull('location') // Исключаем ботов без локации
                   ->where('location', '!=', ''); // Исключаем ботов с пустой локацией

        if ($mineLocation) {
            // Для подлокаций рудника ищем только ботов с конкретным mine_location_id
            $query->where('mine_location_id', $mineLocation->id);
            
            \Log::debug("FactionCountService: строгий подсчет ботов в подлокации рудника", [
                'location' => $locationName,
                'mine_location_id' => $mineLocation->id,
                'race' => $race,
                'class' => $class
            ]);
        } else {
            // Для обычных локаций ищем ботов без mine_location_id
            $query->where('location', $locationName)
                  ->whereNull('mine_location_id');
                  
            \Log::debug("FactionCountService: строгий подсчет ботов в обычной локации", [
                'location' => $locationName,
                'race' => $race,
                'class' => $class
            ]);
        }

        $count = $query->count();

        \Log::debug("FactionCountService: результат строгого подсчета ботов", [
            'location' => $locationName,
            'race' => $race,
            'class' => $class,
            'count' => $count,
            'is_mine_location' => $mineLocation ? true : false
        ]);

        return $count;
    }
}
<?php

namespace App\Http\Controllers\Outposts;

use App\Http\Controllers\Controller;
use App\Models\OutpostLocation;
use App\Models\Location;
use App\Models\Mob;
use App\Models\Bot;
use App\Models\Skill;
use App\Models\User;
use App\Models\ActiveEffect;
use App\Services\BattleLogService;
use App\Services\ExperienceService;
use App\Services\SkillService;
use App\Services\LogFormattingService;
use App\Contracts\DamageCalculator;
use App\Services\CombatFormulaService;
use App\Services\ObeliskService;
use App\Services\DamageService;
use App\Services\OutpostTemplateService;
use App\Services\battle\outposts\Custom\VillageService;
use App\Services\battle\outposts\Custom\VillageAttackService;
use App\Services\battle\outposts\Custom\LeaveLocationService;
use App\Services\battle\FactionCountService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class CustomOutpostController extends Controller
{
    /**
     * Сервис для работы с логами боя
     */
    protected BattleLogService $battleLogService;

    /**
     * Сервис для работы с опытом
     */
    protected ExperienceService $experienceService;

    /**
     * Сервис для работы с умениями
     */
    protected SkillService $skillService;

    /**
     * Сервис для расчета урона
     */
    protected DamageCalculator $damageCalculator;

    /**
     * Сервис для форматирования логов
     */
    protected LogFormattingService $logFormatter;

    /**
     * Сервис для расчета урона по формулам
     */
    protected CombatFormulaService $combatFormulaService;

    /**
     * Сервис для работы с обелисками
     */
    protected ObeliskService $obeliskService;

    /**
     * Сервис для работы с уроном
     */
    protected DamageService $damageService;

    /**
     * Сервис для работы с деревнями
     */
    protected VillageService $villageService;

    /**
     * Сервис для атаки на деревни
     */
    protected VillageAttackService $villageAttackService;

    /**
     * Сервис для выхода из локации
     */
    protected LeaveLocationService $leaveLocationService;

    /**
     * Сервис для подсчета фракций
     */
    protected FactionCountService $factionCountService;

    /**
     * Базовый множитель урона для PvP
     */
    protected const PVP_DAMAGE_MULTIPLIER = 0.7;

    /**
     * Конструктор контроллера
     *
     * @param BattleLogService $battleLogService Сервис для работы с логами боя
     * @param ExperienceService $experienceService Сервис для работы с опытом
     * @param SkillService $skillService Сервис для работы с умениями
     * @param DamageCalculator $damageCalculator Сервис для расчета урона
     * @param LogFormattingService $logFormatter Сервис для форматирования логов
     * @param CombatFormulaService $combatFormulaService Сервис для расчета урона по формулам
     * @param ObeliskService $obeliskService Сервис для работы с обелисками
     * @param DamageService $damageService Сервис для работы с уроном
     * @param VillageService $villageService Сервис для работы с деревнями
     * @param VillageAttackService $villageAttackService Сервис для атаки на деревни
     * @param LeaveLocationService $leaveLocationService Сервис для выхода из локации
     * @param FactionCountService $factionCountService Сервис для подсчета фракций
     */
    public function __construct(
        BattleLogService $battleLogService,
        ExperienceService $experienceService,
        SkillService $skillService,
        DamageCalculator $damageCalculator,
        LogFormattingService $logFormatter,
        CombatFormulaService $combatFormulaService,
        ObeliskService $obeliskService,
        DamageService $damageService,
        VillageService $villageService,
        VillageAttackService $villageAttackService,
        LeaveLocationService $leaveLocationService,
        FactionCountService $factionCountService
    ) {
        $this->battleLogService = $battleLogService;
        $this->experienceService = $experienceService;
        $this->skillService = $skillService;
        $this->damageCalculator = $damageCalculator;
        $this->logFormatter = $logFormatter;
        $this->combatFormulaService = $combatFormulaService;
        $this->obeliskService = $obeliskService;
        $this->damageService = $damageService;
        $this->villageService = $villageService;
        $this->villageAttackService = $villageAttackService;
        $this->leaveLocationService = $leaveLocationService;
        $this->factionCountService = $factionCountService;
    }

    /**
     * Выбор моба в качестве цели
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации (может быть из outpost_locations или locations)
     * @param int $mobId ID моба
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectMob(Request $request, $id, $mobId)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию - сначала пытаемся найти в outpost_locations, затем в locations
        $outpostLocationRecord = OutpostLocation::where('id', $id)->where('is_active', true)->first();

        if ($outpostLocationRecord && $outpostLocationRecord->location_id) {
            // Если найдена запись в outpost_locations, получаем связанную локацию
            $outpostLocation = Location::where('id', $outpostLocationRecord->location_id)
                ->where('is_active', true)
                ->firstOrFail();
        } else {
            // Если не найдена в outpost_locations, ищем напрямую в locations (для обратной совместимости)
            $outpostLocation = Location::where('id', $id)
                ->where('is_active', true)
                ->firstOrFail();
        }

        // Логируем информацию о выборе моба для отладки
        Log::info('Выбор моба в аванпосте', [
            'user_id' => $user->id,
            'mob_id' => $mobId,
            'location_id' => $outpostLocation->id,
            'location_name' => $outpostLocation->name,
            'is_sublocation' => $outpostLocation->parent_id !== null
        ]);

        // Находим моба (ищем по location_id для кастомных локаций)
        $mob = Mob::where('id', $mobId)
            ->where('location_id', $outpostLocation->id)
            ->where('hp', '>', 0)
            ->where(function ($query) {
                $query->whereNull('death_time')
                    ->orWhereRaw('death_time <= NOW() - (respawn_time * INTERVAL \'1 minute\')');
            })
            ->first();

        if (!$mob) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Моб не найден, уже побежден или восстанавливается после смерти.');
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'mob';
        $user->current_target_id = $mob->id;
        $user->save();

        // Логируем успешный выбор моба
        Log::info('Моб успешно выбран как цель', [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'location_id' => $outpostLocation->id,
            'location_name' => $outpostLocation->name,
            'is_sublocation' => $outpostLocation->parent_id !== null
        ]);

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Добавляем запись в боевой лог
        $this->battleLogService->addLog(
            $battleLogKey,
            " Вы выбрали {$mob->name} как цель",
            'info'
        );

        // Сохраняем информацию о выбранном мобе в сессии для корректного отображения интерфейса
        session(['selected_mob' => $mob]);

        return redirect()->route('battle.outposts.show', $id);
    }

    /**
     * Атака цели (универсальный метод)
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attack(Request $request, $id)
    {
        // Проверяем тип цели и вызываем соответствующий метод
        $user = Auth::user();

        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        if ($user->current_target_type === 'mob') {
            return $this->attackMob($request, $id);
        } elseif ($user->current_target_type === 'player') {
            return $this->attackPlayer($request, $id);
        } elseif ($user->current_target_type === 'bot') {
            return $this->attackBot($request, $id);
        } else {
            // Определяем правильный ID локации для записи в боевой журнал
            $locationId = $this->resolveLocationId($id);
            $outpostLocation = Location::where('id', $locationId)
                ->where('is_active', true)
                ->firstOrFail();
                
            // Получаем ключ боевого лога для записи сообщения
            $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
            
            // Добавляем запись в боевой журнал
            $this->battleLogService->addLog(
                $battleLogKey,
                "Сначала выберите цель для атаки",
                'warning'
            );

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Сначала выберите цель для атаки.');
        }
    }

    /**
     * Атака моба
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackMob(Request $request, $id)
    {
        $user = Auth::user();
        $isCrit = false; // Инициализируем флаг крита

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Сбрасываем флаг возможности ответной атаки при атаке моба
        Session::forget('can_retaliate');

        // Находим локацию - сначала пытаемся найти в outpost_locations, затем в locations
        $outpostLocationRecord = OutpostLocation::where('id', $id)->where('is_active', true)->first();

        if ($outpostLocationRecord && $outpostLocationRecord->location_id) {
            // Если найдена запись в outpost_locations, получаем связанную локацию
            $outpostLocation = Location::where('id', $outpostLocationRecord->location_id)
                ->where('is_active', true)
                ->firstOrFail();
        } else {
            // Если не найдена в outpost_locations, ищем напрямую в locations (для обратной совместимости)
            $outpostLocation = Location::where('id', $id)
                ->where('is_active', true)
                ->firstOrFail();
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'mob' || !$user->current_target_id) {
            // Получаем ключ боевого лога для записи сообщения
            $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
            
            // Добавляем запись в боевой журнал
            $this->battleLogService->addLog(
                $battleLogKey,
                "🏹 Сначала выберите моба для атаки",
                'warning'
            );

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Сначала выберите моба для атаки.');
        }

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при атаке моба', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id
        ]);

        // Проверяем, находится ли пользователь в этой локации
        // Если локация пользователя не совпадает с локацией аванпоста,
        // обновляем локацию пользователя на локацию аванпоста
        if ($user->statistics->current_location !== $location) {
            // Обновляем локацию пользователя
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя при атаке моба', [
                'user_id' => $user->id,
                'old_location' => $user->statistics->current_location,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Проверка кулдауна
        $lastAttackTime = session('last_attack_time', 0);
        $currentTime = time();
        if ($currentTime - $lastAttackTime < 2) {
            return redirect()->back()->with('error', 'Подождите перед следующей атакой!');
        }
        session(['last_attack_time' => $currentTime]);

        // Логируем информацию о поиске моба для отладки
        Log::info('Поиск моба для атаки', [
            'user_id' => $user->id,
            'target_mob_id' => $user->current_target_id,
            'outpost_location_id' => $outpostLocation->id,
            'outpost_location_name' => $outpostLocation->name
        ]);

        // Находим моба (ищем по location_id для кастомных локаций)
        $mob = Mob::where('id', $user->current_target_id)
            ->where('location_id', $outpostLocation->id)
            ->where('hp', '>', 0)
            ->where(function ($query) {
                $query->whereNull('death_time')
                    ->orWhereRaw('death_time <= NOW() - (respawn_time * INTERVAL \'1 minute\')');
            })
            ->first();

        // Дополнительная отладка: проверяем, есть ли моб с таким ID вообще
        if (!$mob) {
            $mobExists = Mob::where('id', $user->current_target_id)->first();

            // Проверяем, можно ли атаковать моба (учитывая время респауна)
            $canAttackMob = false;
            $respawnStatus = 'неизвестно';

            if ($mobExists) {
                if ($mobExists->death_time === null) {
                    $canAttackMob = $mobExists->hp > 0;
                    $respawnStatus = 'жив';
                } else {
                    $respawnTime = $mobExists->respawn_time ?? 3;
                    // ИСПРАВЛЕНИЕ: Обеспечиваем правильное приведение death_time к Carbon объекту
                    $deathTime = $mobExists->death_time instanceof \Carbon\Carbon
                        ? $mobExists->death_time
                        : \Carbon\Carbon::parse($mobExists->death_time);
                    $respawnDeadline = $deathTime->addMinutes($respawnTime);
                    $canAttackMob = now()->gte($respawnDeadline) && $mobExists->hp > 0;
                    $respawnStatus = now()->gte($respawnDeadline) ? 'респаунился' : 'ожидает респауна';
                }
            }

            Log::warning('Моб не найден для атаки', [
                'user_id' => $user->id,
                'target_mob_id' => $user->current_target_id,
                'outpost_location_id' => $outpostLocation->id,
                'mob_exists' => $mobExists ? true : false,
                'mob_location_id' => $mobExists ? $mobExists->location_id : null,
                'mob_location' => $mobExists ? $mobExists->location : null,
                'mob_hp' => $mobExists ? $mobExists->hp : null,
                'mob_death_time' => $mobExists ? $mobExists->death_time : null,
                'mob_respawn_time' => $mobExists ? ($mobExists->respawn_time ?? 3) : null,
                'can_attack_mob' => $canAttackMob,
                'respawn_status' => $respawnStatus,
                'current_time' => now()->toDateTimeString()
            ]);

            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Моб не найден или уже побежден.');
        }

        // Сбрасываем информацию о последнем атаковавшем при атаке моба
        $user->last_attacker_id = null;
        $user->save();

        // Получаем силу атакующего (игрока)
        $userStrength = $user->profile->getEffectiveStats()['strength'] ?? 1;
        // Получаем броню цели (моба)
        $mobArmor = $mob->armor ?? 0;
        // Используем универсальную формулу урона
        $damage = $this->combatFormulaService->calculateDamage($userStrength, $mobArmor);

        // Проверяем, был ли удар критическим (5% шанс)
        if (rand(1, 100) <= 5) {
            $damage = ceil($damage * 1.5);
            $isCrit = true;
        }

        // Логируем расчетный урон
        Log::info("Рассчитанный урон по мобу", [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'damage' => $damage,
            'mob_hp_before' => $mob->hp,
            'is_crit' => $isCrit
        ]);

        // Сохраняем текущее HP моба для корректного расчета БА
        $mobCurrentHp = $mob->hp;

        // Рассчитываем фактический урон (не больше текущего HP моба)
        $effectiveDamage = $this->damageService->calculateEffectiveDamage($damage, $mobCurrentHp);

        // Наносим урон мобу
        $mob->hp = max(0, $mob->hp - $damage);
        $mob->save();

        // Обновляем информацию о мобе в сессии, если он там есть
        $selectedMob = session('selected_mob');
        if ($selectedMob && $selectedMob->id == $mob->id) {
            session(['selected_mob' => $mob]);
        }

        Log::info("Урон нанесен", [
            'mob_hp_after' => $mob->hp,
            'calculated_damage' => $damage,
            'effective_damage' => $effectiveDamage
        ]);

        // Регистрируем действие в обелиске (урон игрока по мобу)
        $this->obeliskService->registerAction(
            $outpostLocation->name,
            'damage',
            $damage,
            'player',
            (string) $user->id,
            'mob',
            (string) $mob->id,
            $mobCurrentHp
        );

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Используем LogFormattingService для записи лога атаки
        $attackLog = $this->logFormatter->formatPlayerAttack($user, $mob, $damage, $isCrit);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'info');

        // Опыт за урон начисляется автоматически в ObeliskService при регистрации действия

        // Проверяем, побежден ли моб
        if ($mob->hp <= 0) {
            // Устанавливаем время смерти моба
            $mob->death_time = now();
            $mob->save();

            // Инвалидируем кэш мобов для локации, так как моб побежден
            app(\App\Services\GameCacheService::class)->invalidateLocationMobsCache($location);
            app(\App\Services\GameCacheService::class)->invalidateMobCache($mob->id, $location);

            // Получаем время респауна из модели моба или используем значение по умолчанию (3 минуты)
            $respawnTime = $mob->respawn_time ?? 3;

            // Запускаем задачу на респаун моба через указанное время
            \App\Jobs\RespawnSingleMob::dispatch($mob->id)->delay(now()->addMinutes($respawnTime));

            // Логируем информацию о запланированном респауне
            Log::info("Задача на респаун моба ID {$mob->id} запланирована через {$respawnTime} минут.", [
                'mob_name' => $mob->name,
                'location' => $outpostLocation->name,
                'death_time' => $mob->death_time,
                'respawn_time' => $respawnTime
            ]);

            // Логируем победу над мобом
            $this->battleLogService->addLog(
                $battleLogKey,
                "🏆 Вы победили {$mob->name}!",
                'success'
            );

            // ИСПРАВЛЕНИЕ КРИТИЧЕСКОГО БАГА: Убираем дублирование начисления опыта
            // Опыт уже начислен через ObeliskService при нанесении урона (100% от урона - 1:1)
            // Дополнительное начисление опыта за победу создавало двойное начисление
            Log::info("Опыт за победу над мобом уже начислен через ObeliskService при нанесении урона", [
                'user_id' => $user->id,
                'mob_id' => $mob->id,
                'mob_name' => $mob->name,
                'note' => 'Дублирование опыта исправлено - опыт начисляется только за урон (1:1 с уроном)'
            ]);

            // ДОБАВЛЯЕМ ОБРАБОТКУ НАГРАД ИЗ ДРОПА
            $this->handleMobVictoryRewards($user, $mob, $battleLogKey);

            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Удаляем информацию о мобе из сессии
            session()->forget('selected_mob');

            return redirect()->route('battle.outposts.show', $id)
                ->with('success', "Вы победили {$mob->name}!");
        }

        // Моб наносит ответный удар
        $mobStrength = $mob->strength ?? 1;
        $userArmor = $user->profile->getEffectiveStats()['armor'] ?? 0;
        $mobDamage = $this->combatFormulaService->calculateDamage($mobStrength, $userArmor);

        // Проверяем, был ли удар моба критическим (5% шанс)
        $mobCrit = false;
        if (rand(1, 100) <= 5) {
            $mobDamage = ceil($mobDamage * 1.5);
            $mobCrit = true;
        }

        // Получаем сервис для работы со здоровьем игрока
        $playerHealthService = app(\App\Services\PlayerHealthService::class);

        // Сохраняем текущее HP игрока для корректного расчета БА
        $userCurrentHp = $user->profile->hp;

        // Применяем урон через Redis
        $damageResult = $playerHealthService->applyDamage($user, $mobDamage, "mob:{$mob->id}");

        // Обновляем значение в базе данных для синхронизации
        $user->profile->hp = max(0, $user->profile->hp - $mobDamage);
        $user->profile->save();

        // Регистрируем действие в обелиске (урон моба по игроку)
        $this->obeliskService->registerAction(
            $outpostLocation->name,
            'damage',
            $mobDamage,
            'mob',
            (string) $mob->id,
            'player',
            (string) $user->id,
            $userCurrentHp
        );

        // Логируем информацию о нанесенном уроне
        Log::info('Получен урон от моба', [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'damage' => $mobDamage,
            'user_hp_db_before' => $user->profile->hp + $mobDamage,
            'user_hp_db_after' => $user->profile->hp,
            'user_hp_redis' => $damageResult['new_hp'],
            'is_dead' => $damageResult['is_dead'],
            'is_crit' => $mobCrit
        ]);

        // Записываем лог атаки моба
        $mobAttackLog = $this->logFormatter->formatMobAttack($mob, $user, $mobDamage, $mobCrit);
        $this->battleLogService->addLog($battleLogKey, $mobAttackLog, 'danger');

        // Проверяем, побежден ли игрок, используя актуальные данные из Redis
        if ($damageResult['is_dead'] || $user->profile->hp <= 0) {
            // Логируем поражение игрока
            $this->battleLogService->addLog(
                $battleLogKey,
                "💀 Вы были побеждены {$mob->name}!",
                'danger'
            );

            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Удаляем информацию о мобе из сессии
            session()->forget('selected_mob');

            return redirect()->route('battle.defeat')
                ->with('error', "Вы были побеждены {$mob->name}!");
        }

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Вы атаковали {$mob->name} и нанесли {$damage} урона!");
    }

    /**
     * Внесение вклада в обелиск
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function contributeObelisk(Request $request, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию - сначала пытаемся найти в outpost_locations, затем в locations
        $outpostLocationRecord = OutpostLocation::where('id', $id)->where('is_active', true)->first();

        if ($outpostLocationRecord && $outpostLocationRecord->location_id) {
            // Если найдена запись в outpost_locations, получаем связанную локацию
            $outpostLocation = Location::where('id', $outpostLocationRecord->location_id)
                ->where('is_active', true)
                ->firstOrFail();
        } else {
            // Если не найдена в outpost_locations, ищем напрямую в locations (для обратной совместимости)
            $outpostLocation = Location::where('id', $id)
                ->where('is_active', true)
                ->firstOrFail();
        }

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при вкладе в обелиск', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id
        ]);

        // Проверяем, находится ли пользователь в этой локации
        // Если локация пользователя не совпадает с локацией аванпоста,
        // обновляем локацию пользователя на локацию аванпоста
        if ($user->statistics->current_location !== $location) {
            // Обновляем локацию пользователя
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя при вкладе в обелиск', [
                'user_id' => $user->id,
                'old_location' => $user->statistics->current_location,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Проверка кулдауна
        $lastContributionTime = session('last_contribution_time', 0);
        $currentTime = time();
        if ($currentTime - $lastContributionTime < 3) {
            return redirect()->back()->with('error', 'Подождите перед следующим вкладом!');
        }
        session(['last_contribution_time' => $currentTime]);

        // Получаем обелиск
        $obelisk = $this->obeliskService->getObelisk($location);

        if (!$obelisk) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Обелиск не найден.');
        }

        // Рассчитываем вклад на основе уровня и силы игрока
        $contribution = $user->profile->level * 5 + ($user->profile->getEffectiveStats()['strength'] ?? 1);

        // Вносим вклад в обелиск (с проверкой контроля деревень)
        $contributionResult = $this->obeliskService->contributeToObelisk($user, $location, $contribution);

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Проверяем, был ли принят вклад в обелиск
        if (!$contributionResult) {
            // Получаем информацию о том, почему вклад был отклонен
            $contributionCheck = $this->obeliskService->canContributeToObelisk($user, $location);

            Log::info('Вклад в обелиск отклонен при ручном вкладе', [
                'user_id' => $user->id,
                'user_race' => $user->profile->race ?? 'не определена',
                'location' => $location,
                'contribution' => $contribution,
                'reason' => $contributionCheck['reason'] ?? 'неизвестная причина'
            ]);

            // Добавляем запись в боевой лог о том, что вклад не был принят
            $this->battleLogService->addLog(
                $battleLogKey,
                "❌ Вклад в обелиск отклонен: " . ($contributionCheck['reason'] ?? 'неизвестная причина'),
                'warning'
            );

            return redirect()->route('battle.outposts.show', $id)
                ->with('warning', 'Вклад в обелиск отклонен: ' . ($contributionCheck['reason'] ?? 'неизвестная причина'));
        }

        // Добавляем запись в боевой лог об успешном вкладе
        $this->battleLogService->addLog(
            $battleLogKey,
            "🔮 Вы внесли вклад в обелиск: +{$contribution} энергии",
            'info'
        );

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Вы внесли вклад в обелиск: +{$contribution} энергии");
    }

    /**
     * Выбор случайного моба в качестве цели
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации (может быть из outpost_locations или locations)
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectRandomMob(Request $request, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию - сначала пытаемся найти в outpost_locations, затем в locations
        $outpostLocationRecord = OutpostLocation::where('id', $id)->where('is_active', true)->first();

        if ($outpostLocationRecord && $outpostLocationRecord->location_id) {
            // Если найдена запись в outpost_locations, получаем связанную локацию
            $outpostLocation = Location::where('id', $outpostLocationRecord->location_id)
                ->where('is_active', true)
                ->firstOrFail();
        } else {
            // Если не найдена в outpost_locations, ищем напрямую в locations (для обратной совместимости)
            $outpostLocation = Location::where('id', $id)
                ->where('is_active', true)
                ->firstOrFail();
        }

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при выборе случайного моба', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id
        ]);

        // Проверяем, находится ли пользователь в этой локации
        // Если локация пользователя не совпадает с локацией аванпоста,
        // обновляем локацию пользователя на локацию аванпоста
        if ($user->statistics->current_location !== $location) {
            // Обновляем локацию пользователя
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя при выборе случайного моба', [
                'user_id' => $user->id,
                'old_location' => $user->statistics->current_location,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Проверка кулдауна
        $lastSelectTime = session('last_select_time', 0);
        $currentTime = time();
        if ($currentTime - $lastSelectTime < 2) {
            return redirect()->back()->with('error', 'Подождите перед следующим выбором цели!');
        }
        session(['last_select_time' => $currentTime]);

        // Логируем информацию о текущей цели пользователя
        Log::info('Текущая цель пользователя перед выбором случайного моба', [
            'user_id' => $user->id,
            'current_target_type' => $user->current_target_type,
            'current_target_id' => $user->current_target_id,
            'location' => $location,
            'outpost_id' => $id
        ]);

        // Получаем список всех мобов в локации для логирования
        // Используем как location, так и location_id для поиска мобов
        $allMobsInLocation = Mob::where(function ($query) use ($location, $outpostLocation) {
            $query->where('location', $location)
                ->orWhere('location_id', $outpostLocation->id);
        })
            ->where('hp', '>', 0)
            ->where(function ($query) {
                $query->whereNull('death_time')
                    ->orWhere('death_time', '<=', now()->subMinutes(3));
            })
            ->get();

        // Логируем информацию о всех мобах в локации
        Log::info('Все мобы в локации перед выбором случайного', [
            'location' => $location,
            'outpost_id' => $id,
            'mobs_count' => $allMobsInLocation->count(),
            'mobs' => $allMobsInLocation->map(function ($mob) {
                return [
                    'id' => $mob->id,
                    'name' => $mob->name,
                    'hp' => $mob->hp,
                    'death_time' => $mob->death_time
                ];
            })->toArray()
        ]);

        // Выбираем случайного моба в локации, исключая текущую цель пользователя
        // Используем как location, так и location_id для поиска мобов
        $query = Mob::where(function ($query) use ($location, $outpostLocation) {
            $query->where('location', $location)
                ->orWhere('location_id', $outpostLocation->id);
        })
            ->where('hp', '>', 0)
            ->where(function ($query) {
                $query->whereNull('death_time')
                    ->orWhere('death_time', '<=', now()->subMinutes(3));
            });

        // Если у пользователя уже есть выбранный моб, исключаем его из выборки
        if ($user->current_target_type === 'mob' && $user->current_target_id) {
            $query->where('id', '!=', $user->current_target_id);

            // Логируем информацию об исключении текущей цели
            Log::info('Исключаем текущую цель из выборки', [
                'user_id' => $user->id,
                'excluded_mob_id' => $user->current_target_id
            ]);
        }

        // ОПТИМИЗАЦИЯ: Заменяем медленный inRandomOrder() на быстрый выбор из кэша
        $optimizedTargetService = app(\App\Services\OptimizedTargetService::class);
        $mob = $optimizedTargetService->findRandomMobInLocation($location, $user->current_target_id);

        // Логируем результат первой попытки выбора моба
        Log::info('Результат первой попытки выбора случайного моба', [
            'user_id' => $user->id,
            'found_mob' => $mob ? true : false,
            'mob_id' => $mob ? $mob->id : null,
            'mob_name' => $mob ? $mob->name : null,
            'location' => $location,
            'outpost_id' => $id
        ]);

        // Если не нашли другого моба (возможно, в локации только один моб),
        // тогда проверяем количество мобов и выдаем соответствующее сообщение
        if (!$mob) {
            Log::info('Не найден другой моб, проверяем количество мобов в локации', [
                'user_id' => $user->id,
                'location' => $location,
                'outpost_id' => $id
            ]);

            // Проверяем, сколько всего мобов в локации
            // Используем как location, так и location_id для поиска мобов
            $totalMobsCount = Mob::where(function ($query) use ($location, $outpostLocation) {
                $query->where('location', $location)
                    ->orWhere('location_id', $outpostLocation->id);
            })
                ->where('hp', '>', 0)
                ->where(function ($query) {
                    $query->whereNull('death_time')
                        ->orWhere('death_time', '<=', now()->subMinutes(3));
                })
                ->count();

            Log::info('Общее количество доступных мобов в локации', [
                'location' => $location,
                'outpost_id' => $id,
                'total_mobs_count' => $totalMobsCount
            ]);

            // Если в локации всего один моб и он уже является целью
            if ($totalMobsCount === 1 && $user->current_target_type === 'mob' && $user->current_target_id) {
                // Получаем информацию о текущей цели для сообщения
                $currentTarget = Mob::find($user->current_target_id);
                $currentTargetName = $currentTarget ? $currentTarget->name : 'неизвестный моб';
                
                // Получаем ключ боевого лога для записи сообщения
                $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
                
                // Добавляем запись в боевой журнал
                $this->battleLogService->addLog(
                    $battleLogKey,
                    "🔍 {$currentTargetName} - последний моб в локации",
                    'info'
                );

                return redirect()->route('battle.outposts.show', $id)
                    ->with('info', "{$currentTargetName} - последний моб в локации.");
            }

            // Если в локации есть мобы больше одного, пытаемся выбрать любого
            if ($totalMobsCount > 1) {
                // ОПТИМИЗАЦИЯ: Используем оптимизированный сервис вместо медленного запроса
                $mob = $optimizedTargetService->findRandomMobInLocation($location);

                Log::info('Выбран любой доступный моб', [
                    'user_id' => $user->id,
                    'mob_id' => $mob ? $mob->id : null,
                    'mob_name' => $mob ? $mob->name : null,
                    'location' => $location,
                    'outpost_id' => $id
                ]);
            }
        }

        if (!$mob) {
            // Проверяем, есть ли мобы, которые можно респаунить
            $respawnableMobs = Mob::where(function ($query) use ($location, $outpostLocation) {
                $query->where('location', $location)
                    ->orWhere('location_id', $outpostLocation->id);
            })
                ->where('hp', '<=', 0)
                ->where(function ($query) {
                    $query->whereRaw('death_time <= NOW() - (respawn_time * INTERVAL \'1 minute\')')
                        ->orWhereNull('death_time');
                })
                ->count();

            if ($respawnableMobs > 0) {
                // Запускаем команду респауна
                \Artisan::call('mobs:respawn', ['location' => $location]);

                // Пытаемся найти моба снова
                $mob = Mob::where(function ($query) use ($location, $outpostLocation) {
                    $query->where('location', $location)
                        ->orWhere('location_id', $outpostLocation->id);
                })
                    ->where('hp', '>', 0)
                    ->inRandomOrder()
                    ->first();
            }

            // Если моба все еще нет
            if (!$mob) {
                // Получаем ключ боевого лога для записи сообщения
                $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
                
                // Добавляем запись в боевой журнал
                $this->battleLogService->addLog(
                    $battleLogKey,
                    "🏹 В локации нет доступных мобов для атаки",
                    'warning'
                );

                return redirect()->route('battle.outposts.show', $id)
                    ->with('info', 'В локации нет доступных мобов.');
            }
        }

        // Проверяем, найден ли моб
        if (!$mob) {
            Log::warning('Не удалось найти моба для выбора в качестве цели', [
                'user_id' => $user->id,
                'location' => $location,
                'outpost_id' => $id
            ]);

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Не удалось найти доступного моба в локации.');
        }

        // Логируем информацию о выбранном мобе перед установкой цели
        Log::info('Итоговый выбранный моб', [
            'user_id' => $user->id,
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'mob_hp' => $mob->hp,
            'mob_location' => $mob->location,
            'previous_target_id' => $user->current_target_id,
            'previous_target_type' => $user->current_target_type,
            'location' => $location,
            'outpost_id' => $id
        ]);

        // Проверяем, не является ли выбранный моб уже текущей целью
        if ($user->current_target_type === 'mob' && $user->current_target_id == $mob->id) {
            // Получаем ключ боевого лога для записи сообщения
            $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
            
            // Добавляем запись в боевой журнал
            $this->battleLogService->addLog(
                $battleLogKey,
                "⚠️ {$mob->name} уже является вашей целью",
                'warning'
            );

            return redirect()->route('battle.outposts.show', $id);
        }

        // Дополнительная проверка на валидность моба перед установкой цели
        $mob->refresh(); // Обновляем данные моба из БД
        if ($mob->hp <= 0) {
            // Получаем ключ боевого лога для записи сообщения
            $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
            
            // Добавляем запись в боевой журнал
            $this->battleLogService->addLog(
                $battleLogKey,
                "💀 {$mob->name} уже побежден другим игроком",
                'warning'
            );

            // Инвалидируем кэш мобов для локации
            app(\App\Services\GameCacheService::class)->invalidateLocationMobsCache($location);

            return redirect()->route('battle.outposts.show', $id)
                ->with('info', 'Моб уже побежден. Попробуйте выбрать другого.');
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'mob';
        $user->current_target_id = $mob->id;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Добавляем запись в боевой лог
        $this->battleLogService->addLog(
            $battleLogKey,
            "Вы выбрали {$mob->name} как цель",
            'info'
        );

        // Применяем эффект метки обелиска
        $this->processObeliskMark($user, $mob, $battleLogKey);

        // Сохраняем информацию о выбранном мобе в сессии для корректного отображения интерфейса
        session(['selected_mob' => $mob]);

        // Не отображаем флеш-сообщение, чтобы не мешать отображению интерфейса взаимодействия с мобом
        return redirect()->route('battle.outposts.show', $id);
    }

    /**
     * Выбор игрока в качестве цели
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @param int $playerId ID игрока
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectPlayer(Request $request, $id, $playerId)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по ID (используем таблицу locations, а не outpost_locations)
        $outpostLocation = Location::where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при смене цели', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id
        ]);

        // Проверяем, находится ли пользователь в этой локации
        // Если локация пользователя не совпадает с локацией аванпоста,
        // обновляем локацию пользователя на локацию аванпоста
        if ($user->statistics->current_location !== $location) {
            // Обновляем локацию пользователя
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя при смене цели', [
                'user_id' => $user->id,
                'old_location' => $user->statistics->current_location,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Находим игрока
        $targetPlayer = User::whereHas('statistics', function ($q) use ($location) {
            $q->where('current_location', $location);
        })
            ->where('id', $playerId)
            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp) // Проверка онлайн-статуса
            ->first();

        if (!$targetPlayer) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Игрок не найден в этой локации или не в сети.');
        }

        // Проверяем, не выбирает ли игрок сам себя
        if ($targetPlayer->id === $user->id) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Вы не можете выбрать себя в качестве цели.');
        }

        // Получаем актуальные данные о здоровье игрока из Redis
        $targetResources = $targetPlayer->profile->getActualResources();

        // Проверяем, жив ли игрок, используя актуальные данные из Redis
        if ($targetResources['current_hp'] <= 0 || $targetPlayer->profile->hp <= 0) {
            // Логируем информацию о попытке выбора побежденного игрока
            Log::info('Попытка выбрать побежденного игрока', [
                'user_id' => $user->id,
                'target_id' => $targetPlayer->id,
                'target_hp_db' => $targetPlayer->profile->hp,
                'target_hp_redis' => $targetResources['current_hp']
            ]);

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Этот игрок уже побежден.');
        }

        // Дополнительная проверка, что игрок действительно находится в локации
        if ($targetPlayer->statistics->current_location !== $location) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Этот игрок покинул локацию.');
        }

        // Проверяем, что игрок не выбирает игрока своей расы
        if ($user->profile->race === $targetPlayer->profile->race) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Вы не можете атаковать игрока своей расы!');
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'player';
        $user->current_target_id = $targetPlayer->id;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Добавляем запись в боевой лог
        $this->battleLogService->addLog(
            $battleLogKey,
            "Вы выбрали игрока {$targetPlayer->name} как цель",
            'info'
        );

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Игрок {$targetPlayer->name} выбран как цель");
    }

    /**
     * Выбор бота в качестве цели
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @param int $botId ID бота
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectBot(Request $request, $id, $botId)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по ID (используем таблицу locations, а не outpost_locations)
        $outpostLocation = Location::where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о выборе бота для отладки
        Log::info('Выбор бота в аванпосте', [
            'user_id' => $user->id,
            'bot_id' => $botId,
            'location_id' => $outpostLocation->id,
            'location_name' => $outpostLocation->name,
            'is_sublocation' => $outpostLocation->parent_id !== null
        ]);

        // Находим бота
        $bot = Bot::where('id', $botId)
            ->where('location', $location)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->first();

        if (!$bot) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Бот не найден, неактивен или уже побежден.');
        }

        // Проверяем, не выбирает ли игрок бота своей расы для атаки
        // Исключение: боты-жрецы (priest) той же расы доступны для лечения, но не для атаки
        if ($user->profile->race === $bot->race) {
            if ($bot->class === 'priest') {
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Боты-жрецы вашей расы предназначены для лечения, а не для атаки.');
            } else {
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Вы не можете выбрать бота своей расы в качестве цели для атаки.');
            }
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'bot';
        $user->current_target_id = $bot->id;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Добавляем запись в боевой лог
        // $this->battleLogService->addLog(
        //     $battleLogKey,
        //     "Вы выбрали {$bot->name} как цель",
        //     'info'
        // );

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Бот {$bot->name} выбран как цель");
    }

    /**
     * Выбор союзника в качестве цели
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @param int $allyId ID союзника
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectAlly(Request $request, $id, $allyId)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, что игрок находится в группе
        $activeParty = $user->activeParty();
        if (!$activeParty) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Вы не состоите в группе.');
        }

        $targetUser = User::find($allyId);
        if (!$targetUser) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Игрок не найден.');
        }

        // Проверяем, что цель в той же группе
        $isInSameParty = $activeParty->members()
            ->where('user_id', $allyId)
            ->exists();

        if (!$isInSameParty) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Этот игрок не состоит в вашей группе.');
        }

        // Проверяем, что игрок не выбирает себя
        if ($allyId == $user->id) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Вы не можете выбрать себя в качестве цели.');
        }

        // Находим локацию по ID
        $outpostLocation = Location::where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        // Устанавливаем союзника как цель
        $user->current_target_type = 'player';
        $user->current_target_id = $targetUser->id;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Добавляем запись в боевой лог
        $this->battleLogService->addLog(
            $battleLogKey,
            "🎯 Вы выбрали союзника {$targetUser->name} как цель для заклинаний",
            'info'
        );

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Союзник {$targetUser->name} выбран как цель");
    }

    /**
     * Атака игрока
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации (может быть из outpost_locations или locations)
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackPlayer(Request $request, $id)
    {
        $user = Auth::user();
        $damageCoefficient = min(1, max(0, floatval($request->input('damageCoefficient', 1))));

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'player' || !$user->current_target_id) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Сначала выберите игрока для атаки.');
        }

        // Находим локацию по ID (используем таблицу locations, а не outpost_locations)
        $outpostLocation = Location::where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при атаке игрока', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id
        ]);

        // Проверяем, находится ли пользователь в этой локации
        // Если локация пользователя не совпадает с локацией аванпоста,
        // обновляем локацию пользователя на локацию аванпоста
        if ($user->statistics->current_location !== $location) {
            // Обновляем локацию пользователя
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя при атаке игрока', [
                'user_id' => $user->id,
                'old_location' => $user->statistics->current_location,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Проверка кулдауна
        $lastAttackTime = session('last_pvp_attack_time', 0);
        $currentTime = time();
        if ($currentTime - $lastAttackTime < 4) {
            return redirect()->back()->with('error', 'Подождите перед следующей атакой!');
        }
        session(['last_pvp_attack_time' => $currentTime]);

        // Получаем цель
        $target = User::whereHas('profile', function ($q) {
            $q->where('hp', '>', 0);
        })
            ->whereHas('statistics', function ($q) use ($location) {
                $q->where('current_location', $location);
            })
            ->where('id', $user->current_target_id)
            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp) // Проверка онлайн-статуса
            ->first();

        if (!$target) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Проверяем, существует ли игрок вообще
            $playerExists = User::where('id', $user->current_target_id)->exists();

            if ($playerExists) {
                // Игрок существует, но не в этой локации или не онлайн
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Игрок покинул локацию или не в сети. Цель сброшена.');
            } else {
                // Игрок не найден или уже побежден
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Игрок не найден или уже побежден.');
            }
        }

        // Дополнительная проверка, что игрок действительно находится в локации
        if ($target->statistics->current_location !== $location) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Игрок покинул локацию. Цель сброшена.');
        }

        // Проверяем, что игрок не атакует игрока своей расы
        if ($user->profile->race === $target->profile->race) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Вы не можете атаковать игрока своей расы!');
        }

        // Сбрасываем флаг возможности ответной атаки при атаке игрока
        // (делаем это после всех проверок, чтобы флаг не сбрасывался при ошибках)
        Session::forget('can_retaliate');

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Рассчитываем урон
        $damage = round($this->calculatePvPDamage($user, $target) * $damageCoefficient);

        // Получаем сервис для работы со здоровьем игрока
        $playerHealthService = app(\App\Services\PlayerHealthService::class);

        // Применяем урон через Redis (синхронизация с БД происходит автоматически)
        $damageResult = $playerHealthService->applyDamage($target, $damage, "player:{$user->id}");
        
        // Устанавливаем время атаки для middleware
        session(['recent_attack_time' => time()]);

        // Регистрируем действие в обелиске (урон игрока по игроку)
        // Используем фактический урон (не больше оставшегося HP)
        $effectiveDamage = min($damage, $damageResult['old_hp']);
        $this->obeliskService->registerAction(
            $outpostLocation->name,
            'damage',
            $effectiveDamage,
            'player',
            (string) $user->id,
            'player',
            (string) $target->id,
            $damageResult['old_hp']
        );

        // Логируем информацию о нанесенном уроне
        Log::info('Нанесен урон игроку в PvP', [
            'attacker_id' => $user->id,
            'target_id' => $target->id,
            'damage' => $damage,
            'effective_damage' => $effectiveDamage,
            'target_hp_db_before' => $target->profile->hp + $damage,
            'target_hp_db_after' => $target->profile->hp,
            'target_hp_redis' => $damageResult['new_hp'],
            'is_dead' => $damageResult['is_dead']
        ]);

        // Обновляем информацию о последнем атакующем
        $target->update([
            'last_attacker_id' => $user->id,
            'last_attacker_type' => 'player'
        ]);

        // Логируем атаку
        $attackLog = $this->logFormatter->formatPlayerAttackPlayer($user, $target, $damage);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'info');

        // Добавляем запись в лог цели
        $targetBattleLogKey = $this->getBattleLogKey($target, $outpostLocation->name);
        $targetLog = $this->logFormatter->formatPlayerAttackedByPlayer($target, $user, $damage);
        $this->battleLogService->addLog($targetBattleLogKey, $targetLog, 'danger');

        // Опыт за урон начисляется автоматически в ObeliskService при регистрации действия

        // Проверяем, побежден ли игрок, используя актуальные данные из Redis
        if ($damageResult['is_dead'] || $target->profile->hp <= 0) {
            // Логируем победу
            $this->battleLogService->addLog(
                $battleLogKey,
                "Вы победили {$target->name}",
                'success'
            );

            // Начисляем опыт за победу над игроком (50% от максимального HP игрока)
            $bonusExperience = (int) round($target->profile->max_hp * 0.5);
            $experienceGained = $this->experienceService->awardExperienceForPlayer($user, $bonusExperience, true);

            // Устанавливаем флаг недавней победы в сессии победителя
            session(['recent_victory_time' => time()]);
            
            // Логируем установку флага победы
            Log::info("Установлен флаг победы для игрока #{$user->id}", [
                'winner_id' => $user->id,
                'loser_id' => $target->id,
                'recent_victory_time' => time(),
                'winner_hp' => $user->profile->getActualResources()['current_hp'],
                'loser_hp' => $target->profile->getActualResources()['current_hp']
            ]);

            // Устанавливаем флаг поражения для побежденного игрока через базу данных
            // Обновляем профиль побежденного игрока, чтобы отметить его как побежденного
            try {
                $target->profile->update([
                    'is_defeated' => true,
                    'defeated_by_type' => 'player',
                    'defeated_by_id' => $user->id,
                    'defeated_at' => now()
                ]);
            } catch (\Exception $e) {
                // Столбцы еще не созданы, пропускаем
                Log::warning("Не удалось обновить поля поражения для побежденного: " . $e->getMessage());
            }

            // Логируем информацию о победе
            Log::info('Игрок победил другого игрока в PvP', [
                'attacker_id' => $user->id,
                'target_id' => $target->id,
                'target_hp_db' => $target->profile->hp,
                'target_hp_redis' => $damageResult['new_hp'],
                'experience_gained' => $experienceGained
            ]);

            // Просто возвращаемся на страницу аванпоста без flash-сообщений
            // Информация о победе уже в журнале боя
            return redirect()->route('battle.outposts.show', $id);
        }

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Вы атаковали игрока {$target->name} и нанесли {$damage} урона.");
    }

    /**
     * Атака бота
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackBot(Request $request, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, выбрана ли цель пользователя
        if ($user->current_target_type !== 'bot' || !$user->current_target_id) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Сначала выберите бота для атаки.');
        }

        // Определяем правильный ID локации
        $locationId = $this->resolveLocationId($id);

        // Находим локацию по правильному ID
        $outpostLocation = Location::where('id', $locationId)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при атаке бота', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id
        ]);

        // Проверяем, находится ли пользователь в этой локации
        if ($user->statistics->current_location !== $location) {
            // Обновляем локацию пользователя
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя при атаке бота', [
                'user_id' => $user->id,
                'old_location' => $user->statistics->current_location,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Проверка кулдауна
        $lastAttackTime = session('last_pvp_attack_time', 0);
        $currentTime = time();
        if ($currentTime - $lastAttackTime < 4) {
            return redirect()->back()->with('error', 'Подождите перед следующей атакой!');
        }
        session(['last_pvp_attack_time' => $currentTime]);

        // Находим бота
        $bot = Bot::where('id', $user->current_target_id)
            ->where('location', $location)
            ->where('is_active', true)
            ->where('hp', '>', 0)
            ->first();

        if (!$bot) {
            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Бот не найден, неактивен или уже побежден.');
        }

        // Проверяем, что игрок не атакует бота своей расы
        // Исключение: боты-жрецы (priest) той же расы предназначены для лечения, а не для атаки
        if ($user->profile->race === $bot->race) {
            if ($bot->class === 'priest') {
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Боты-жрецы вашей расы предназначены для лечения, а не для атаки!');
            } else {
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Вы не можете атаковать бота своей расы!');
            }
        }

        // Сбрасываем флаг возможности ответной атаки при атаке бота
        Session::forget('can_retaliate');

        // Сбрасываем информацию о последнем атаковавшем при атаке бота
        $user->last_attacker_id = null;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Рассчитываем урон для бота
        $attackerStrength = $user->profile->getEffectiveStats()['strength'] ?? 1;
        $botArmor = $bot->armor ?? 0;
        $baseDamage = max(1, $attackerStrength - $botArmor);
        $randomFactor = rand(97, 103) / 100;
        $damage = (int) round($baseDamage * $randomFactor);

        // Проверяем критический удар (5% шанс)
        $isCrit = rand(1, 100) <= 5;
        if ($isCrit) {
            $damage = (int) round($damage * 1.5);
        }

        // Применяем урон боту
        $oldHp = $bot->hp;
        $effectiveDamage = min($damage, $oldHp); // Фактический урон не больше оставшегося HP
        $bot->hp = max(0, $oldHp - $damage);
        $bot->last_attacker_id = $user->id;
        $bot->last_attacker_type = 'player';
        $bot->save();

        // Регистрируем действие в обелиске (урон игрока по боту)
        $this->obeliskService->registerAction(
            $outpostLocation->name,
            'damage',
            $effectiveDamage,
            'player',
            (string) $user->id,
            'bot',
            (string) $bot->id,
            $oldHp
        );

        // Логируем информацию о нанесенном уроне
        Log::info('Нанесен урон боту', [
            'attacker_id' => $user->id,
            'bot_id' => $bot->id,
            'damage' => $damage,
            'effective_damage' => $effectiveDamage,
            'bot_hp_before' => $oldHp,
            'bot_hp_after' => $bot->hp,
            'is_crit' => $isCrit,
            'is_dead' => $bot->hp <= 0
        ]);

        // Логируем атаку на бота
        $attackLog = $this->logFormatter->formatPlayerAttack($user, $bot, $damage, $isCrit);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'info');

        // Начисляем опыт за фактически нанесенный урон
        $this->experienceService->awardExperienceForPlayer($user, $effectiveDamage, false);

        // Проверяем, побежден ли бот
        if ($bot->hp <= 0) {
            // Логируем победу над ботом
            $this->battleLogService->addLog(
                $battleLogKey,
                "💀 Вы победили бота {$bot->name}!",
                'success'
            );

            // Начисляем опыт за победу над ботом (30% от максимального HP бота)
            $bonusExperience = (int) round($bot->max_hp * 0.3);
            $experienceGained = $this->experienceService->awardExperienceForPlayer($user, $bonusExperience, true);

            // Обрабатываем смерть бота (деактивируем и устанавливаем время смерти)
            $bot->die();

            // Сбрасываем цель пользователя
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->save();

            // Логируем информацию о победе
            Log::info('Игрок победил бота', [
                'attacker_id' => $user->id,
                'bot_id' => $bot->id,
                'experience_gained' => $experienceGained
            ]);

            return redirect()->route('battle.outposts.show', $id)
                ->with('success', "Вы победили бота {$bot->name} и получили {$experienceGained} опыта!");
        }

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Вы атаковали бота {$bot->name} и нанесли {$damage} урона!");
    }

    /**
     * Атака случайного игрока или конкретной цели
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации (может быть из outpost_locations или locations)
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackAnyPlayer(Request $request, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Определяем правильный ID локации
        $locationId = $this->resolveLocationId($id);

        // Находим локацию по правильному ID
        $outpostLocation = Location::where('id', $locationId)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при атаке любого игрока', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id,
            'request_target_type' => $request->input('target_type'),
            'request_target_id' => $request->input('target_id')
        ]);

        // Проверяем, находится ли пользователь в этой локации
        // Если локация пользователя "Аванпосты (Общее)" или не совпадает с локацией аванпоста,
        // обновляем локацию пользователя на локацию аванпоста
        if ($user->statistics->current_location !== $location) {
            // Обновляем локацию пользователя
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя при атаке любого игрока', [
                'user_id' => $user->id,
                'old_location' => $user->statistics->current_location,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Проверка кулдауна
        $lastAttackTime = session('last_pvp_attack_time', 0);
        $currentTime = time();
        if ($currentTime - $lastAttackTime < 4) {
            return redirect()->back()->with('error', 'Подождите перед следующей атакой!');
        }
        session(['last_pvp_attack_time' => $currentTime]);

        // Проверяем, переданы ли конкретные данные цели из формы
        $targetType = $request->input('target_type');
        $targetId = $request->input('target_id');

        $target = null;

        if ($targetType && $targetId) {
            // Атакуем конкретную цель, переданную из формы
            Log::info('Атака конкретной цели из формы', [
                'user_id' => $user->id,
                'target_type' => $targetType,
                'target_id' => $targetId,
                'location' => $location
            ]);

            if ($targetType === 'bot') {
                // Находим конкретного бота
                $target = Bot::where('id', $targetId)
                    ->where('location', $location)
                    ->where('is_active', true)
                    ->where('hp', '>', 0)
                    ->first();

                if (!$target) {
                    return redirect()->route('battle.outposts.show', $id)
                        ->with('error', 'Выбранный бот не найден, неактивен или уже побежден.');
                }

                // Проверяем, что игрок не атакует бота своей расы
                // Исключение: боты-жрецы (priest) той же расы предназначены для лечения, а не для атаки
                if ($user->profile->race === $target->race) {
                    if ($target->class === 'priest') {
                        return redirect()->route('battle.outposts.show', $id)
                            ->with('error', 'Боты-жрецы вашей расы предназначены для лечения, а не для атаки!');
                    } else {
                        return redirect()->route('battle.outposts.show', $id)
                            ->with('error', 'Вы не можете атаковать бота своей расы!');
                    }
                }

            } elseif ($targetType === 'user') {
                // Находим конкретного игрока
                $target = User::whereHas('profile', function ($q) {
                    $q->where('hp', '>', 0);
                })
                    ->whereHas('statistics', function ($q) use ($location) {
                        $q->where('current_location', $location);
                    })
                    ->where('id', $targetId)
                    ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
                    ->first();

                if (!$target) {
                    return redirect()->route('battle.outposts.show', $id)
                        ->with('error', 'Выбранный игрок не найден, покинул локацию или не в сети.');
                }

                // Проверяем, что игрок не атакует игрока своей расы
                if ($user->profile->race === $target->profile->race) {
                    return redirect()->route('battle.outposts.show', $id)
                        ->with('error', 'Вы не можете атаковать игрока своей расы!');
                }
            }
        }

        if (!$target) {
            // Если конкретная цель не найдена или не передана, ищем случайную цель
            $target = $this->findRandomEnemyInLocation($user, $location);

            if (!$target) {
                // Получаем ключ боевого лога для записи сообщения
                $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
                
                // Добавляем запись в боевой журнал
                $this->battleLogService->addLog(
                    $battleLogKey,
                    "В локации нет других врагом для атаки",
                    'warning'
                );

                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'В локации нет других врагом для атаки');
            }
        }

        // Определяем тип цели и проводим дополнительные проверки
        if ($target instanceof User) {
            // Дополнительная проверка, что игрок действительно находится в локации
            if ($target->statistics->current_location !== $location) {
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Выбранный игрок покинул локацию. Попробуйте еще раз.');
            }
        } else {
            // Для ботов проверяем, что они активны и в правильной локации
            if (!$target->is_active || $target->location !== $location || $target->hp <= 0) {
                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Выбранный враг недоступен для атаки. Попробуйте еще раз.');
            }
        }

        // Устанавливаем цель пользователя в зависимости от типа
        if ($target instanceof User) {
            $user->current_target_type = 'player';
            $targetTypeName = 'игрока';
        } else {
            $user->current_target_type = 'bot';
            $targetTypeName = 'бота';
        }
        $user->current_target_id = $target->id;
        $user->save();

        // Логируем установку цели
        Log::info('Установлена цель для атаки', [
            'user_id' => $user->id,
            'target_type' => $user->current_target_type,
            'target_id' => $user->current_target_id,
            'target_name' => $target->name,
            'location' => $location,
            'was_specific_target' => ($targetType && $targetId) ? 'да' : 'нет'
        ]);

        // Сбрасываем флаг возможности ответной атаки при атаке случайного игрока/бота
        Session::forget('can_retaliate');

        // Сбрасываем информацию о последнем атаковавшем при атаке случайного игрока/бота
        $user->last_attacker_id = null;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Добавляем запись в боевой лог в зависимости от того, была ли это конкретная цель или случайная
        if ($targetType && $targetId) {
            $this->battleLogService->addLog(
                $battleLogKey,
                "Вы атакуете выбранного {$targetTypeName} {$target->name}",
                'info'
            );
        } else {
            $this->battleLogService->addLog(
                $battleLogKey,
                "Вы выбрали случайного {$targetTypeName} {$target->name} как цель",
                'info'
            );
        }

        // Рассчитываем урон в зависимости от типа цели
        if ($target instanceof User) {
            $damage = $this->calculatePvPDamage($user, $target);
        } else {
            // Для ботов используем упрощенный расчет урона
            $attackerStrength = $user->profile->getEffectiveStats()['strength'] ?? 1;
            $botArmor = $target->armor ?? 0;
            $baseDamage = max(1, $attackerStrength - $botArmor);
            $randomFactor = rand(97, 103) / 100;
            $damage = (int) round($baseDamage * $randomFactor);
        }

        // Применяем урон в зависимости от типа цели
        if ($target instanceof User) {
            // Получаем сервис для работы со здоровьем игрока
            $playerHealthService = app(\App\Services\PlayerHealthService::class);

            // Применяем урон через Redis
            $damageResult = $playerHealthService->applyDamage($target, $damage, "player:{$user->id}");

            // Регистрируем действие в обелиске (урон игрока по игроку)
            $effectiveDamage = min($damage, $damageResult['old_hp']);
            $this->obeliskService->registerAction(
                $outpostLocation->name,
                'damage',
                $effectiveDamage,
                'player',
                (string) $user->id,
                'player',
                (string) $target->id,
                $damageResult['old_hp']
            );

            // Обновляем значение в базе данных для синхронизации
            $target->profile->hp = max(0, $target->profile->hp - $damage);
            $target->profile->save();

            // Логируем информацию о нанесенном уроне
            Log::info('Нанесен урон игроку в PvP (случайная атака)', [
                'attacker_id' => $user->id,
                'target_id' => $target->id,
                'damage' => $damage,
                'effective_damage' => $effectiveDamage,
                'target_hp_db_before' => $target->profile->hp + $damage,
                'target_hp_db_after' => $target->profile->hp,
                'target_hp_redis' => $damageResult['new_hp'],
                'is_dead' => $damageResult['is_dead']
            ]);

            // Обновляем информацию о последнем атакующем
            $target->update([
                'last_attacker_id' => $user->id,
                'last_attacker_type' => 'player'
            ]);

            // Логируем атаку
            $attackLog = $this->logFormatter->formatPlayerAttackPlayer($user, $target, $damage);
            $this->battleLogService->addLog($battleLogKey, $attackLog, 'info');

            // Добавляем запись в лог цели
            $targetBattleLogKey = $this->getBattleLogKey($target, $outpostLocation->name);
            $targetLog = $this->logFormatter->formatPlayerAttackedByPlayer($target, $user, $damage);
            $this->battleLogService->addLog($targetBattleLogKey, $targetLog, 'danger');

            // Начисляем опыт за фактически нанесенный урон в PvP (случайная атака)
            $this->experienceService->awardExperienceForPlayer($user, $effectiveDamage, true);

            // Информация о последнем атакующем уже установлена выше в target->update()
        } else {
            // Для ботов применяем урон напрямую
            $oldHp = $target->hp;
            $effectiveDamage = min($damage, $oldHp); // Фактический урон не больше оставшегося HP
            $target->hp = max(0, $oldHp - $damage);
            $target->last_attacker_id = $user->id;
            $target->last_attacker_type = 'player';
            $target->save();

            // Регистрируем действие в обелиске (урон игрока по боту)
            $this->obeliskService->registerAction(
                $outpostLocation->name,
                'damage',
                $effectiveDamage,
                'player',
                (string) $user->id,
                'bot',
                (string) $target->id,
                $oldHp
            );

            // Логируем информацию о нанесенном уроне боту
            Log::info('Нанесен урон боту (случайная атака)', [
                'attacker_id' => $user->id,
                'target_id' => $target->id,
                'damage' => $damage,
                'effective_damage' => $effectiveDamage,
                'target_hp_before' => $oldHp,
                'target_hp_after' => $target->hp,
                'is_dead' => $target->hp <= 0
            ]);

            // Логируем атаку на бота
            $attackLog = $this->logFormatter->formatPlayerAttack($user, $target, $damage, false);
            $this->battleLogService->addLog($battleLogKey, $attackLog, 'info');

            // Начисляем опыт за фактически нанесенный урон по боту (случайная атака)
            $this->experienceService->awardExperienceForPlayer($user, $effectiveDamage, false);

            $damageResult = ['is_dead' => $target->hp <= 0, 'new_hp' => $target->hp];
        }



        // Проверяем, побеждена ли цель
        if ($damageResult['is_dead']) {
            if ($target instanceof User) {
                // Логируем победу над игроком
                $this->battleLogService->addLog(
                    $battleLogKey,
                    "Вы победили {$target->name}",
                    'success'
                );

                // Начисляем опыт за победу над игроком (50% от максимального HP игрока)
                $bonusExperience = (int) round($target->profile->max_hp * 0.5);
                $experienceGained = $this->experienceService->awardExperienceForPlayer($user, $bonusExperience, true);

                // Устанавливаем флаг недавней победы в сессии победителя
                session(['recent_victory_time' => time()]);
                
                // Логируем установку флага победы
                Log::info("Установлен флаг победы для игрока #{$user->id}", [
                    'winner_id' => $user->id,
                    'loser_id' => $target->id,
                    'recent_victory_time' => time(),
                    'winner_hp' => $user->profile->getActualResources()['current_hp'],
                    'loser_hp' => $target->profile->getActualResources()['current_hp']
                ]);

                // Устанавливаем флаг поражения для побежденного игрока через базу данных
                try {
                    $target->profile->update([
                        'is_defeated' => true,
                        'defeated_by_type' => 'player',
                        'defeated_by_id' => $user->id,
                        'defeated_at' => now()
                    ]);
                } catch (\Exception $e) {
                    Log::warning("Не удалось обновить поля поражения для побежденного: " . $e->getMessage());
                }

                // Логируем информацию о победе
                Log::info('Игрок победил другого игрока в случайной атаке', [
                    'attacker_id' => $user->id,
                    'target_id' => $target->id,
                    'target_hp_db' => $target->profile->hp,
                    'target_hp_redis' => $damageResult['new_hp'],
                    'experience_gained' => $experienceGained
                ]);

                // Просто возвращаемся на страницу аванпоста без flash-сообщений
                // Информация о победе уже в журнале боя
                return redirect()->route('battle.outposts.show', $id);
            } else {
                // Логируем победу над ботом
                $this->battleLogService->addLog(
                    $battleLogKey,
                    "💀 Вы победили бота {$target->name}!",
                    'success'
                );

                // Начисляем опыт за победу над ботом (30% от максимального HP бота)
                $bonusExperience = (int) round($target->max_hp * 0.3);
                $experienceGained = $this->experienceService->awardExperienceForPlayer($user, $bonusExperience, true);

                // Обрабатываем смерть бота (деактивируем и устанавливаем время смерти)
                $target->die();

                // Логируем информацию о победе
                Log::info('Игрок победил бота в случайной атаке', [
                    'attacker_id' => $user->id,
                    'target_id' => $target->id,
                    'target_hp_after' => $target->hp,
                    'experience_gained' => $experienceGained
                ]);

                return redirect()->route('battle.outposts.show', $id)
                    ->with('success', "Вы победили бота {$target->name} и получили {$experienceGained} опыта!");
            }
        }

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Вы атаковали {$targetTypeName} {$target->name} и нанесли {$damage} урона.");
    }

    /**
     * Ответная атака на последнего атаковавшего
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function retaliate(Request $request, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Проверяем, есть ли последний атаковавший
        if (!$user->last_attacker_id) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Нет информации о последнем атаковавшем.');
        }

        // Находим локацию по ID (используем таблицу locations, а не outpost_locations)
        $outpostLocation = Location::where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при ответной атаке', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $location,
            'outpost_id' => $id
        ]);

        // ИСПРАВЛЕНИЕ: НЕ обновляем локацию пользователя до проверки атакующего
        // Это позволяет корректно проверить, находится ли атакующий в той же локации,
        // где была совершена атака, а не в текущей локации аванпоста

        // Проверка кулдауна
        $lastAttackTime = session('last_pvp_attack_time', 0);
        $currentTime = time();
        if ($currentTime - $lastAttackTime < 4) {
            return redirect()->back()->with('error', 'Подождите перед следующей атакой!');
        }
        session(['last_pvp_attack_time' => $currentTime]);

        // Находим последнего атаковавшего игрока (более простой и надежный способ)
        // Убедимся, что получаем объект User, а не коллекцию
        $attacker = User::where('id', $user->last_attacker_id)->first();

        // Логируем информацию о найденном атакующем для отладки
        Log::info('Поиск атаковавшего игрока при ответной атаке', [
            'user_id' => $user->id,
            'last_attacker_id' => $user->last_attacker_id,
            'attacker_found' => $attacker ? 'да' : 'нет',
            'attacker_location' => $attacker && $attacker->statistics ? $attacker->statistics->current_location : 'неизвестно',
            'attacker_hp' => $attacker && $attacker->profile ? $attacker->profile->hp : 'неизвестно',
            'attacker_last_activity' => $attacker ? $attacker->last_activity_timestamp : 'неизвестно'
        ]);

        // Проверяем, существует ли атакующий
        if (!$attacker || !$attacker->statistics || !$attacker->profile) {
            // Сбрасываем last_attacker_id
            $user->last_attacker_id = null;
            $user->save();

            // Логируем информацию о локации перед редиректом
            Log::info('Локация пользователя перед редиректом после проверки существования атакующего', [
                'user_id' => $user->id,
                'current_location' => $user->statistics->current_location,
                'outpost_location' => $outpostLocation->name,
                'outpost_id' => $id
            ]);

            return redirect()->back()->with('error', 'Атаковавший вас игрок не найден.');
        }

        // Получаем актуальные данные о здоровье атакующего из Redis
        $attackerResources = $attacker->profile->getActualResources();

        // Проверяем, жив ли атакующий, используя актуальные данные из Redis
        if ($attackerResources['current_hp'] <= 0) {
            // Сбрасываем last_attacker_id
            $user->last_attacker_id = null;
            $user->save();

            // Логируем информацию о локации перед редиректом
            Log::info('Локация пользователя перед редиректом после проверки HP атакующего', [
                'user_id' => $user->id,
                'current_location' => $user->statistics->current_location,
                'outpost_location' => $outpostLocation->name,
                'outpost_id' => $id,
                'attacker_id' => $attacker->id,
                'attacker_hp_db' => $attacker->profile->hp,
                'attacker_hp_redis' => $attackerResources['current_hp']
            ]);

            // Используем redirect()->back() вместо route() для сохранения текущей локации
            return redirect()->back()->with('error', 'Атаковавший вас игрок уже побежден.');
        }

        // Проверяем, находится ли атакующий в той же локации, используя улучшенную логику
        $locationService = app(\App\Services\battle\UserLocationService::class);
        $areInSameLocation = $locationService->arePlayersInSameLocation($user, $attacker);

        if (!$areInSameLocation) {
            // Сбрасываем last_attacker_id
            $user->last_attacker_id = null;
            $user->save();

            // Логируем информацию о локации перед редиректом
            Log::info('Локация пользователя перед редиректом после проверки локации атакующего', [
                'user_id' => $user->id,
                'current_location' => $user->statistics->current_location,
                'outpost_location' => $outpostLocation->name,
                'outpost_id' => $id,
                'attacker_location' => $attacker->statistics->current_location,
                'are_in_same_location' => $areInSameLocation
            ]);

            return redirect()->back()->with('error', 'Атаковавший вас игрок покинул локацию.');
        }

        // Проверяем, онлайн ли атакующий (активность за последние 5 минут для синхронизации с подсчетом игроков)
        if ($attacker->last_activity_timestamp < now()->subMinutes(5)->timestamp) {
            // Сбрасываем last_attacker_id
            $user->last_attacker_id = null;
            $user->save();

            // Логируем информацию о локации перед редиректом
            Log::info('Локация пользователя перед редиректом после проверки онлайн-статуса атакующего', [
                'user_id' => $user->id,
                'current_location' => $user->statistics->current_location,
                'outpost_location' => $outpostLocation->name,
                'outpost_id' => $id,
                'attacker_last_activity' => $attacker->last_activity_timestamp,
                'current_time' => now()->timestamp,
                'time_diff_minutes' => round((now()->timestamp - $attacker->last_activity_timestamp) / 60)
            ]);

            return redirect()->back()->with('error', 'Атаковавший вас игрок не в сети.');
        }

        // Проверяем, что игрок не атакует игрока своей расы
        if ($user->profile->race === $attacker->profile->race) {
            // Сбрасываем last_attacker_id
            $user->last_attacker_id = null;
            $user->save();

            // Логируем информацию о локации перед редиректом
            Log::info('Локация пользователя перед редиректом после проверки расы атакующего', [
                'user_id' => $user->id,
                'current_location' => $user->statistics->current_location,
                'outpost_location' => $outpostLocation->name,
                'outpost_id' => $id,
                'user_race' => $user->profile->race,
                'attacker_race' => $attacker->profile->race
            ]);

            return redirect()->back()->with('error', 'Вы не можете атаковать игрока своей расы!');
        }

        // Обновляем локацию пользователя на локацию аванпоста ПОСЛЕ проверки атакующего
        if ($user->statistics->current_location !== $location) {
            $oldLocation = $user->statistics->current_location;
            $user->statistics->update([
                'current_location' => $location
            ]);

            Log::info('Обновлена локация пользователя после успешной проверки атакующего', [
                'user_id' => $user->id,
                'old_location' => $oldLocation,
                'new_location' => $location,
                'outpost_id' => $id
            ]);
        }

        // Устанавливаем цель пользователя
        $user->current_target_type = 'player';
        $user->current_target_id = $attacker->id;
        $user->save();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Добавляем запись в боевой лог
        // $this->battleLogService->addLog(
        //     $battleLogKey,
        //     "🎯 Вы выбрали игрока {$attacker->name} как цель для ответной атаки",
        //     'info'
        // );

        // Рассчитываем урон с бонусом за ответную атаку
        $damage = round($this->calculatePvPDamage($user, $attacker) * 1.2); // 20% бонус к урону

        // Получаем сервис для работы со здоровьем игрока
        $playerHealthService = app(\App\Services\PlayerHealthService::class);

        // Применяем урон через Redis (синхронизация с БД происходит автоматически)
        $damageResult = $playerHealthService->applyDamage($attacker, $damage, "player:{$user->id}");

        // Регистрируем действие в обелиске (урон игрока по игроку в ответной атаке)
        $effectiveDamage = min($damage, $damageResult['old_hp']);
        $this->obeliskService->registerAction(
            $outpostLocation->name,
            'damage',
            $effectiveDamage,
            'player',
            (string) $user->id,
            'player',
            (string) $attacker->id,
            $damageResult['old_hp']
        );

        // Логируем информацию о нанесенном уроне
        Log::info('Нанесен урон игроку в ответной атаке', [
            'attacker_id' => $user->id,
            'target_id' => $attacker->id,
            'damage' => $damage,
            'effective_damage' => $effectiveDamage,
            'target_hp_db_before' => $attacker->profile->hp + $damage,
            'target_hp_db_after' => $attacker->profile->hp,
            'target_hp_redis' => $damageResult['new_hp'],
            'is_dead' => $damageResult['is_dead']
        ]);

        // Обновляем информацию о последнем атакующем
        $attacker->update([
            'last_attacker_id' => $user->id,
            'last_attacker_type' => 'player'
        ]);

        // Логируем атаку
        $attackLog = $this->logFormatter->formatPlayerRetaliatePlayer($user, $attacker, $damage);
        $this->battleLogService->addLog($battleLogKey, $attackLog, 'info');

        // Добавляем запись в лог цели
        $targetBattleLogKey = $this->getBattleLogKey($attacker, $outpostLocation->name);
        $targetLog = $this->logFormatter->formatPlayerRetaliatedByPlayer($attacker, $user, $damage);
        $this->battleLogService->addLog($targetBattleLogKey, $targetLog, 'danger');

        // Начисляем опыт за фактически нанесенный урон в ответной атаке
        $this->experienceService->awardExperienceForPlayer($user, $effectiveDamage, true);

        // Проверяем, побежден ли игрок, используя актуальные данные из Redis
        if ($damageResult['is_dead'] || $attacker->profile->hp <= 0) {
            // Логируем победу
            $this->battleLogService->addLog(
                $battleLogKey,
                "💀 Вы победили игрока {$attacker->name} в ответной атаке!",
                'success'
            );

            // Начисляем опыт за победу над игроком с бонусом (60% от максимального HP игрока)
            $bonusExperience = (int) round($attacker->profile->max_hp * 0.6); // 20% бонус к опыту
            $experienceGained = $this->experienceService->awardExperienceForPlayer($user, $bonusExperience, true);

            // Логируем информацию о победе
            Log::info('Игрок победил другого игрока в ответной атаке', [
                'attacker_id' => $user->id,
                'target_id' => $attacker->id,
                'target_hp_db' => $attacker->profile->hp,
                'target_hp_redis' => $damageResult['new_hp'],
                'experience_gained' => $experienceGained
            ]);

            return redirect()->route('battle.outposts.show', $id)
                ->with('success', "Вы победили игрока {$attacker->name} в ответной атаке и получили {$experienceGained} опыта!");
        }

        return redirect()->route('battle.outposts.show', $id)
            ->with('success', "Вы атаковали игрока {$attacker->name} в ответной атаке и нанесли {$damage} урона.");
    }

    /**
     * Атака деревни
     *
     * @param int $id Идентификатор локации
     * @param int $villageId Идентификатор деревни
     * @return \Illuminate\Http\RedirectResponse
     */
    public function attackVillage($id, $villageId)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Находим локацию по ID (используем таблицу locations, а не outpost_locations)
        $outpostLocation = Location::where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Используем сервис для атаки на деревню
        $result = $this->villageAttackService->attackVillage($user, $villageId, $battleLogKey, $id);

        // Возвращаем редирект с соответствующим сообщением
        if (isset($result['redirect_params'])) {
            return redirect()->route($result['redirect_route'], $result['redirect_params'])
                ->with($result['message_type'], $result['message']);
        }

        return redirect()->route($result['redirect_route'])
            ->with($result['message_type'], $result['message']);
    }

    /**
     * Смена цели
     * Позволяет сменить цель только когда текущая цель - моб
     * Новая цель выбирается случайно только среди игроков и ботов противоположной расы
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeTarget(Request $request, $id)
    {
        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
        }

        // Rate limiting: проверяем частоту запросов смены цели
        $rateLimitKey = "change_target_rate_limit:{$user->id}";
        $lastChangeTime = cache()->get($rateLimitKey, 0);
        $currentTime = time();

        // Минимальный интервал между сменами цели - 1 секунда
        if ($currentTime - $lastChangeTime < 1) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Слишком частая смена цели. Подождите секунду.');
        }

        // Устанавливаем время последней смены цели
        cache()->put($rateLimitKey, $currentTime, 60);

        // Находим локацию по ID (используем таблицу locations, а не outpost_locations)
        $outpostLocation = Location::where('id', $id)
            ->where('is_active', true)
            ->firstOrFail();

        // Получаем текущую локацию пользователя
        $location = $outpostLocation->name;

        // Анти-чит проверка: проверяем, что пользователь действительно находится в этой локации
        // ИСПРАВЛЕНИЕ: Убираем строгую проверку локации, так как она вызывает ложные срабатывания
        // Вместо этого используем текущую локацию пользователя для поиска целей
        $actualLocation = $user->statistics->current_location;
        if ($actualLocation !== $location) {
            // Логируем для отладки, но не блокируем действие
            Log::info('Использование актуальной локации пользователя для смены цели', [
                'user_id' => $user->id,
                'user_location' => $actualLocation,
                'requested_location' => $location,
                'ip' => $request->ip()
            ]);
            // Используем актуальную локацию пользователя
            $location = $actualLocation;
        }

        // Получаем ключ боевого лога
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

        // Сохраняем информацию о текущей цели
        $currentTargetType = $user->current_target_type;
        $currentTargetId = $user->current_target_id;

        // Анти-чит проверка: смена цели разрешена только когда текущая цель - моб, бот или игрок
        if (!in_array($currentTargetType, ['mob', 'bot', 'player'])) {
            Log::warning('Попытка смены цели с недопустимым типом текущей цели', [
                'user_id' => $user->id,
                'current_target_type' => $currentTargetType,
                'current_target_id' => $currentTargetId,
                'location' => $location,
                'ip' => $request->ip()
            ]);

            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Смена цели доступна только когда выбран моб, бот или игрок.');
        }

        // Анти-чит проверка: проверяем, что у пользователя действительно выбрана цель
        if (!$currentTargetId) {
            return redirect()->route('battle.outposts.show', $id)
                ->with('error', 'Сначала выберите цель для атаки.');
        }

        // Проверяем текущую цель в зависимости от её типа
        $currentTarget = null;
        $currentTargetName = '';

        if ($currentTargetType === 'mob') {
            // Проверяем, что выбранный моб существует и находится в правильной локации
            $currentTarget = Mob::where('id', $currentTargetId)
                ->where('location', $location)
                ->where('hp', '>', 0)
                ->first();

            if (!$currentTarget) {
                // Сбрасываем недействительную цель
                $user->current_target_type = null;
                $user->current_target_id = null;
                $user->save();

                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Текущий моб недоступен. Цель сброшена.');
            }
            $currentTargetName = $currentTarget->name;
        } elseif ($currentTargetType === 'bot') {
            // Проверяем, что выбранный бот существует и находится в правильной локации
            $currentTarget = \App\Models\Bot::where('id', $currentTargetId)
                ->where('location', $location)
                ->where('hp', '>', 0)
                ->where('is_active', true)
                ->first();

            if (!$currentTarget) {
                // Если текущий бот недоступен (убит), автоматически ищем новую цель
                Log::info('Текущий бот недоступен, автоматически ищем новую цель', [
                    'user_id' => $user->id,
                    'dead_bot_id' => $currentTargetId,
                    'location' => $location
                ]);

                // Сбрасываем недействительную цель
                $user->current_target_type = null;
                $user->current_target_id = null;
                $user->save();

                // Ищем новую цель среди доступных игроков и ботов
                $newTarget = $this->findRandomEnemyInLocation($user, $location);

                if ($newTarget) {
                    // Устанавливаем новую цель
                    if ($newTarget instanceof User) {
                        $user->current_target_type = 'player';
                        $targetTypeName = 'игрока';
                    } else {
                        $user->current_target_type = 'bot';
                        $targetTypeName = 'бота';
                    }
                    $user->current_target_id = $newTarget->id;
                    $user->save();

                    // Добавляем запись в боевой лог
                    $this->battleLogService->addLog(
                        $battleLogKey,
                        "🔄 Предыдущая цель недоступна. Автоматически выбран {$targetTypeName}: {$newTarget->name}",
                        'info'
                    );

                    return redirect()->route('battle.outposts.show', $id)
                        ->with('success', "Предыдущая цель недоступна. Автоматически выбран {$targetTypeName}: {$newTarget->name}");
                } else {
                    // Если новых целей нет, сообщаем об этом
                    $this->battleLogService->addLog(
                        $battleLogKey,
                        "🔄 Предыдущая цель недоступна. В локации нет других доступных целей",
                        'warning'
                    );

                    return redirect()->route('battle.outposts.show', $id)
                        ->with('info', 'Предыдущая цель недоступна. В локации нет других доступных целей для атаки.');
                }
            }
            $currentTargetName = $currentTarget->name;
        } elseif ($currentTargetType === 'player') {
            // Проверяем, что выбранный игрок существует и находится в правильной локации
            $currentTarget = User::where('id', $currentTargetId)->first();

            // Получаем актуальное HP из Redis
            $currentTargetActualHp = 0;
            if ($currentTarget && $currentTarget->profile) {
                try {
                    $currentTargetActualResources = $currentTarget->profile->getActualResources();
                    $currentTargetActualHp = $currentTargetActualResources['current_hp'];
                } catch (\Exception $e) {
                    $currentTargetActualHp = $currentTarget->profile->hp ?? 0;
                }
            }

            if (
                !$currentTarget ||
                !$currentTarget->statistics ||
                $currentTarget->statistics->current_location !== $location ||
                !$currentTarget->profile ||
                $currentTargetActualHp <= 0 || // Используем актуальное HP из Redis
                $currentTarget->last_activity_timestamp < now()->subMinutes(20)->timestamp
            ) {

                // Сбрасываем недействительную цель
                $user->current_target_type = null;
                $user->current_target_id = null;
                $user->save();

                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Текущий игрок недоступен. Цель сброшена.');
            }
            $currentTargetName = $currentTarget->name;
        }

        // Логируем попытку смены цели
        Log::info('Попытка смены цели с текущей цели на игрока/бота', [
            'user_id' => $user->id,
            'location' => $location,
            'current_target_type' => $currentTargetType,
            'current_target_id' => $currentTargetId,
            'current_target_name' => $currentTargetName
        ]);

        // ОПТИМИЗАЦИЯ: Используем оптимизированный сервис для поиска целей
        $optimizedTargetService = app(\App\Services\OptimizedTargetService::class);
        $newTarget = $optimizedTargetService->findRandomEnemyInLocation($user, $location, $currentTargetType, $currentTargetId);

        // КРИТИЧЕСКОЕ ЛОГИРОВАНИЕ: Проверяем тип найденной цели
        if ($newTarget) {
            $targetType = '';
            $targetClass = get_class($newTarget);

            if ($newTarget instanceof User) {
                $targetType = 'ИГРОК';
            } elseif ($newTarget instanceof \App\Models\Bot) {
                $targetType = 'БОТ';
            } elseif ($newTarget instanceof \App\Models\Mob) {
                $targetType = '🚨 МОБ 🚨';
                Log::error('КРИТИЧЕСКАЯ ОШИБКА: findRandomEnemyInLocation вернул моба!', [
                    'user_id' => $user->id,
                    'location' => $location,
                    'target_class' => $targetClass,
                    'target_id' => $newTarget->id,
                    'target_name' => $newTarget->name
                ]);
            } else {
                $targetType = 'НЕИЗВЕСТНЫЙ ТИП';
            }

            Log::info('Результат findRandomEnemyInLocation в changeTarget', [
                'user_id' => $user->id,
                'location' => $location,
                'target_type' => $targetType,
                'target_class' => $targetClass,
                'target_id' => $newTarget->id,
                'target_name' => $newTarget->name
            ]);
        }

        if ($newTarget) {
            // Дополнительная анти-чит проверка: убеждаемся, что найденная цель действительно доступна
            $isValidTarget = false;

            if ($newTarget instanceof User) {
                // Проверяем игрока с актуальным HP из Redis
                $newTargetActualHp = 0;
                try {
                    $newTargetActualResources = $newTarget->profile->getActualResources();
                    $newTargetActualHp = $newTargetActualResources['current_hp'];
                } catch (\Exception $e) {
                    $newTargetActualHp = $newTarget->profile->hp ?? 0;
                }
                
                $isValidTarget = $newTarget->statistics->current_location === $location &&
                    $newTargetActualHp > 0 && // Используем актуальное HP из Redis
                    $newTarget->profile->race !== $user->profile->race &&
                    $newTarget->last_activity_timestamp >= now()->subMinutes(20)->timestamp;
            } else {
                // Проверяем бота
                $isValidTarget = $newTarget->location === $location &&
                    $newTarget->hp > 0 &&
                    $newTarget->race !== $user->profile->race &&
                    $newTarget->is_active;
            }

            if (!$isValidTarget) {
                Log::warning('Найденная цель не прошла финальную валидацию', [
                    'user_id' => $user->id,
                    'target_type' => $newTarget instanceof User ? 'player' : 'bot',
                    'target_id' => $newTarget->id,
                    'target_name' => $newTarget->name,
                    'location' => $location
                ]);

                return redirect()->route('battle.outposts.show', $id)
                    ->with('error', 'Выбранная цель недоступна для атаки.');
            }

            // Сбрасываем старую цель и устанавливаем новую
            $user->current_target_type = null;
            $user->current_target_id = null;

            // Сбрасываем информацию о последнем атаковавшем при смене цели
            $user->last_attacker_id = null;

            if ($newTarget instanceof User) {
                $user->current_target_type = 'player';
                $targetTypeName = 'игрока';
            } else {
                $user->current_target_type = 'bot';
                $targetTypeName = 'бота';
            }
            $user->current_target_id = $newTarget->id;
            $user->save();

            // Добавляем запись в боевой лог о смене цели
            $this->battleLogService->addLog(
                $battleLogKey,
                "🔄 Цель изменена с {$currentTargetName} на {$targetTypeName} {$newTarget->name}",
                'info'
            );

            Log::info('Цель успешно изменена на игрока/бота', [
                'user_id' => $user->id,
                'old_target_type' => $currentTargetType,
                'old_target_id' => $currentTargetId,
                'old_target_name' => $currentTargetName,
                'new_target_type' => $user->current_target_type,
                'new_target_id' => $user->current_target_id,
                'new_target_name' => $newTarget->name
            ]);

            // Проверяем кулдаун перед автоматической атакой
            $lastAttackTime = session('last_pvp_attack_time', 0);
            $currentTime = time();
            $cooldownRemaining = 4 - ($currentTime - $lastAttackTime);

            if ($cooldownRemaining > 0) {
                // Если кулдаун еще не прошел, просто меняем цель без атаки
                Log::info('Смена цели без автоматической атаки из-за кулдауна', [
                    'user_id' => $user->id,
                    'cooldown_remaining' => $cooldownRemaining,
                    'new_target_type' => $targetTypeName,
                    'new_target_name' => $newTarget->name
                ]);

                return redirect()->route('battle.outposts.show', $id)
                    ->with('success', "Цель изменена на {$targetTypeName}: {$newTarget->name}. Подождите {$cooldownRemaining} сек. перед атакой.");
            }

            // Выполняем автоматическую атаку после смены цели
            $attackResult = $this->performAutoAttackAfterTargetChange($user, $newTarget, $battleLogKey);

            // Устанавливаем время последней атаки
            session(['last_pvp_attack_time' => $currentTime]);

            return redirect()->route('battle.outposts.show', $id)
                ->with('success', "Цель изменена на {$targetTypeName}: {$newTarget->name}. " . $attackResult['message']);
        } else {
            // Если новая цель не найдена, сбрасываем текущую цель полностью
            $user->current_target_type = null;
            $user->current_target_id = null;
            $user->last_attacker_id = null;
            $user->save();

            $this->battleLogService->addLog(
                $battleLogKey,
                "🔄 Цель сброшена: в локации нет игроков или ботов противоположной расы",
                'warning'
            );

            Log::info('Смена цели не удалась - нет доступных игроков/ботов, цель сброшена', [
                'user_id' => $user->id,
                'old_target_type' => $currentTargetType,
                'old_target_id' => $currentTargetId,
                'old_target_name' => $currentTargetName,
                'location' => $location,
                'action' => 'target_reset'
            ]);

            return redirect()->route('battle.outposts.show', $id)
                ->with('info', 'В локации нет врагов противоположной расы для атаки.');
        }
    }

    /**
     * Выход из локации аванпоста
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function leaveLocation()
    {
        // Используем сервис для обработки выхода из локации
        $result = $this->leaveLocationService->leaveLocation();

        // Логируем успешный выход из локации
        Log::info('Пользователь покинул аванпост', [
            'user_id' => Auth::id(),
            'redirect_route' => $result['redirect_route'],
            'message' => $result['message']
        ]);

        // Возвращаем редирект с сообщением
        return redirect()->route($result['redirect_route'])
            ->with($result['message_type'], $result['message']);
    }

    /**
     * Отображение страницы локации аванпоста
     *
     * @param int $id Идентификатор локации
     * @return \Illuminate\View\View
     */
    public function index($id)
    {
        // Логируем запрос к локации
        Log::info('Запрос к локации аванпоста', [
            'id' => $id,
            'user_id' => Auth::id(),
            'url' => request()->fullUrl()
        ]);

        $user = Auth::user();

        // Проверка авторизации
        if (!$user) {
            Log::warning('Попытка доступа к локации аванпоста без авторизации', [
                'id' => $id,
                'ip' => request()->ip()
            ]);
            abort(403, 'Вы должны быть авторизованы, чтобы просматривать эту страницу.');
        }

        // Получаем актуальные ресурсы из Redis
        $actualResources = $user->profile->getActualResources();

        // Синхронизируем значение HP в базе данных с Redis для обеспечения согласованности
        if ($user->profile->hp != $actualResources['current_hp']) {
            $user->profile->hp = $actualResources['current_hp'];
            $user->profile->save();

            Log::info('Синхронизировано значение HP в базе данных с Redis', [
                'user_id' => $user->id,
                'old_hp_db' => $user->profile->hp,
                'new_hp_db' => $actualResources['current_hp'],
                'hp_redis' => $actualResources['current_hp']
            ]);
        }

        // ИСПРАВЛЕНИЕ: Убираем дублирующую проверку HP
        // Проверка HP выполняется в middleware CheckPlayerDefeat
        // Дублирующая проверка здесь вызывала проблему с перенаправлением победителей на главную страницу аванпостов
        // Если игрок имеет HP <= 0, middleware CheckPlayerDefeat перенаправит его на страницу поражения
        // Это правильное поведение, поскольку позволяет различать победителей и проигравших

        // Дополнительное логирование для отладки
        Log::info('Поиск локации аванпоста по ID', [
            'id' => $id,
            'user_id' => $user->id
        ]);

        // Находим локацию по ID
        try {
            // Сначала пытаемся найти в таблице outpost_locations (правильный подход)
            $outpostLocation = OutpostLocation::where('id', $id)
                ->where('is_active', true)
                ->first();

            if ($outpostLocation) {
                Log::info('Локация аванпоста найдена в outpost_locations', [
                    'outpost_id' => $id,
                    'outpost_name' => $outpostLocation->name,
                    'location_id' => $outpostLocation->location_id
                ]);
            } else {
                // Если не найдена в outpost_locations, пытаемся найти в locations (для обратной совместимости)
                $location = Location::where('id', $id)
                    ->where('is_active', true)
                    ->where('location_type', 'outpost')
                    ->first();

                if ($location) {
                    // Ищем соответствующую запись в outpost_locations
                    $outpostLocation = OutpostLocation::where('location_id', $location->id)
                        ->where('is_active', true)
                        ->first();

                    if ($outpostLocation) {
                        Log::info('Локация аванпоста найдена через location_id', [
                            'requested_id' => $id,
                            'location_name' => $location->name,
                            'outpost_id' => $outpostLocation->id,
                            'location_id' => $location->id
                        ]);
                    } else {
                        Log::error('Найдена запись в locations, но не найдена в outpost_locations', [
                            'location_id' => $id,
                            'location_name' => $location->name
                        ]);
                        throw new \Illuminate\Database\Eloquent\ModelNotFoundException("Соответствующая запись в outpost_locations не найдена для location_id '{$id}'");
                    }
                } else {
                    // Логируем все доступные локации для отладки
                    $allOutpostLocations = OutpostLocation::all();
                    Log::warning('Локация аванпоста не найдена ни в outpost_locations, ни в locations', [
                        'requested_id' => $id,
                        'all_outpost_locations' => $allOutpostLocations->map(function ($loc) {
                            return [
                                'id' => $loc->id,
                                'name' => $loc->name,
                                'slug' => $loc->slug,
                                'location_id' => $loc->location_id
                            ];
                        })->toArray()
                    ]);
                    throw new \Illuminate\Database\Eloquent\ModelNotFoundException("Локация аванпоста с ID '{$id}' не найдена");
                }
            }

            // Если локация все еще не найдена, выбрасываем исключение
            if (!$outpostLocation) {
                throw new \Illuminate\Database\Eloquent\ModelNotFoundException("Локация аванпоста с ID '{$id}' не найдена");
            }

        } catch (\Exception $e) {
            Log::error('Ошибка при поиске локации аванпоста', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Если локация не найдена, перенаправляем на список аванпостов
            return redirect()->route('battle.outposts.index')
                ->with('error', 'Локация аванпоста не найдена.');
        }

        // Получаем базовую локацию из таблицы locations
        $baseLocation = null;
        $locationId = null;

        if ($outpostLocation->location_id) {
            $baseLocation = Location::find($outpostLocation->location_id);
            $locationId = $baseLocation ? $baseLocation->id : null;
        }

        if (!$baseLocation) {
            Log::error('Базовая локация не найдена для аванпоста', [
                'outpost_id' => $outpostLocation->id,
                'outpost_name' => $outpostLocation->name,
                'location_id' => $outpostLocation->location_id
            ]);
            // Используем ID аванпоста как fallback
            $locationId = $outpostLocation->id;
        }

        // Получаем деревни для локации
        $villageData = $this->villageService->getVillagesByRacesForLocationId($locationId);
        $solariusVillage = $villageData['solariusVillage'];
        $lunariusVillage = $villageData['lunariusVillage'];
        $villages = $villageData['villages'];

        // Логируем информацию о найденных деревнях
        Log::info('Деревни для локации аванпоста', [
            'location_id' => $locationId,
            'location_name' => $outpostLocation->name,
            'villages_count' => count($villages),
            'solarius_village' => $solariusVillage ? $solariusVillage->name : 'не найдена',
            'lunarius_village' => $lunariusVillage ? $lunariusVillage->name : 'не найдена'
        ]);

        // Логируем информацию о локациях для отладки
        Log::info('Проверка локации при входе в аванпост', [
            'user_id' => $user->id,
            'current_location' => $user->statistics->current_location,
            'outpost_location' => $outpostLocation->name,
            'outpost_id' => $id
        ]);

        // Обновляем текущую локацию пользователя
        $oldLocation = $user->statistics->current_location;
        $updated = $user->statistics->update([
            'current_location' => $outpostLocation->name
        ]);

        if ($updated) {
            Log::info('Обновлена локация пользователя при входе в аванпост', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_race' => $user->profile->race ?? 'неизвестно',
                'user_class' => $user->profile->class ?? 'неизвестно',
                'old_location' => $oldLocation,
                'new_location' => $outpostLocation->name,
                'outpost_id' => $id,
                'outpost_location_id' => $outpostLocation->location_id,
                'base_location_name' => $baseLocation->name ?? 'не найдена',
                'timestamp' => now()->toDateTimeString()
            ]);
        } else {
            Log::error('Не удалось обновить локацию пользователя при входе в аванпост', [
                'user_id' => $user->id,
                'attempted_location' => $outpostLocation->name,
                'outpost_id' => $id
            ]);
        }

        // Получаем мобов в локации, учитывая точное название локации
        $mobsInLocation = Mob::where('location', $outpostLocation->name) // Моб должен быть точно в этой локации
            ->where('hp', '>', 0)
            ->whereNull('death_time') // Исключаем мертвых мобов
            ->get();

        // Получаем обелиск для локации
        $obelisk = $this->obeliskService->getObelisk($outpostLocation->name);

        // Получаем цель пользователя
        $targetMob = null;
        $target = null;

        if ($user->current_target_type === 'mob' && $user->current_target_id) {
            // Проверяем, есть ли информация о выбранном мобе в сессии
            $selectedMob = session('selected_mob');

            if ($selectedMob && $selectedMob->id == $user->current_target_id) {
                // Если моб есть в сессии и его ID совпадает с целью пользователя, используем его
                $targetMob = $selectedMob;
                // Также устанавливаем $target для компонента target-block
                $target = $selectedMob;
                Log::info('Использован моб из сессии', [
                    'mob_id' => $targetMob->id,
                    'mob_name' => $targetMob->name
                ]);
            } else {
                // Иначе получаем моба из базы данных
                $targetMob = Mob::find($user->current_target_id);
                // Также устанавливаем $target для компонента target-block
                $target = $targetMob;
                Log::info('Получен моб из базы данных', [
                    'mob_id' => $targetMob ? $targetMob->id : null,
                    'mob_name' => $targetMob ? $targetMob->name : 'не найден'
                ]);
            }
        } elseif ($user->current_target_type === 'player' && $user->current_target_id) {
            $target = User::find($user->current_target_id);
            Log::info('Получен игрок как цель из базы данных', [
                'player_id' => $target ? $target->id : null,
                'player_name' => $target ? $target->name : 'не найден'
            ]);

            // Проверяем, находится ли игрок-цель в той же локации и онлайн
            if ($target) {
                // Получаем актуальные ресурсы цели из Redis
                $targetActualResources = null;
                $targetActualHp = 0;
                try {
                    $targetActualResources = $target->profile->getActualResources();
                    $targetActualHp = $targetActualResources['current_hp'];
                } catch (\Exception $e) {
                    // Если не удается получить актуальные ресурсы, используем данные из БД
                    $targetActualHp = $target->profile->hp ?? 0;
                }

                $isTargetValid = $target->statistics &&
                    $target->statistics->current_location === $outpostLocation->name &&
                    $target->profile &&
                    $targetActualHp > 0 && // Используем актуальное HP из Redis
                    $target->last_activity_timestamp >= now()->subMinutes(5)->timestamp;

                // Если цель недействительна, сбрасываем её
                if (!$isTargetValid) {
                    Log::info('Сброс недействительной цели игрока в методе index', [
                        'user_id' => $user->id,
                        'target_id' => $target->id,
                        'target_location' => $target->statistics->current_location ?? 'не определена',
                        'current_location' => $outpostLocation->name,
                        'target_hp_db' => $target->profile->hp ?? 0,
                        'target_hp_actual' => $targetActualHp,
                        'target_last_activity' => $target->last_activity_timestamp,
                        'reason' => 'Игрок покинул локацию, умер или неактивен'
                    ]);

                    $user->current_target_type = null;
                    $user->current_target_id = null;
                    $user->save();

                    $target = null; // Сбрасываем цель для отображения
                } else {
                    // Цель валидна, но дополнительно проверяем актуальное HP перед передачей в view
                    if ($targetActualHp <= 0) {
                        Log::info('Обнаружена цель с 0 HP при передаче в view, сбрасываем', [
                            'user_id' => $user->id,
                            'target_id' => $target->id,
                            'target_name' => $target->name,
                            'target_hp_actual' => $targetActualHp
                        ]);
                        
                        $user->current_target_type = null;
                        $user->current_target_id = null;
                        $user->save();
                        $target = null;
                    }
                }
            }
        } elseif ($user->current_target_type === 'bot' && $user->current_target_id) {
            $target = \App\Models\Bot::find($user->current_target_id);

            // Проверяем, что бот-цель жив и активен
            if ($target && ($target->hp <= 0 || !$target->is_active || $target->location !== $outpostLocation->name)) {
                // Если бот мертв, неактивен или не в локации, сбрасываем цель
                Log::info('Сброс цели мертвого/неактивного бота при загрузке страницы', [
                    'user_id' => $user->id,
                    'bot_id' => $target->id,
                    'bot_name' => $target->name,
                    'bot_hp' => $target->hp,
                    'bot_is_active' => $target->is_active,
                    'bot_location' => $target->location,
                    'current_location' => $outpostLocation->name
                ]);

                $user->current_target_type = null;
                $user->current_target_id = null;
                $user->save();
                $target = null; // Сбрасываем цель для отображения
            }

            Log::info('Получен бот как цель из базы данных', [
                'bot_id' => $target ? $target->id : null,
                'bot_name' => $target ? $target->name : 'не найден'
            ]);
        }

        // Получаем боевые логи с фильтрацией по расе игрока
        $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);
        $userRace = $user->profile->race ?? null;
        $battleLogs = $this->battleLogService->getLogs($battleLogKey, $userRace);

        // Получаем активные эффекты пользователя
        $userEffects = $user->activeEffects()->with('skill')->get();

        // Проверяем, оглушен ли пользователь
        $isStunned = $userEffects->contains(function ($effect) {
            return $effect->skill_id == 14 && $effect->isActive();
        });

        // Получаем умения пользователя
        $userSkills = $user->skills()->with('skill')->get();

        // Получаем подлокации
        $subLocations = $outpostLocation->sublocations()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        // Сбрасываем цели для всех игроков, которые не в сети (активность более 5 минут назад)
        User::where('current_target_type', 'player')
            ->where('last_activity_timestamp', '<', now()->subMinutes(5)->timestamp)
            ->update(['current_target_id' => null, 'current_target_type' => null]);

        // Сбрасываем цели для всех игроков, выбравших игроков, которые не в сети
        $offlineTargetIds = User::where('last_activity_timestamp', '<', now()->subMinutes(5)->timestamp)
            ->pluck('id')
            ->toArray();

        if (!empty($offlineTargetIds)) {
            User::where('current_target_type', 'player')
                ->whereIn('current_target_id', $offlineTargetIds)
                ->update(['current_target_id' => null, 'current_target_type' => null]);
        }

        // Сбрасываем цели для всех игроков, выбравших мертвых или неактивных ботов
        $this->cleanupDeadBotTargets($outpostLocation->name);

        // Сбрасываем last_attacker_id для всех игроков, чьи атакующие не в сети или не в локации
        $this->cleanupInactiveAttackers($outpostLocation->name);

        // Используем FactionCountService для подсчета игроков и ботов по фракциям и классам
        // Передаем расу пользователя для правильной фильтрации ботов-жрецов
        $factionCounts = $this->factionCountService->getLocationFactionCounts($outpostLocation->name, $userRace);

        // Извлекаем данные из результата FactionCountService (используем total_counts для суммы игроков и ботов)
        $solWarriors = $factionCounts['total_counts']['solarius']['warriors'];
        $solMages = $factionCounts['total_counts']['solarius']['mages'];
        $solKnights = $factionCounts['total_counts']['solarius']['knights'];
        $lunWarriors = $factionCounts['total_counts']['lunarius']['warriors'];
        $lunMages = $factionCounts['total_counts']['lunarius']['mages'];
        $lunKnights = $factionCounts['total_counts']['lunarius']['knights'];

        // Логируем информацию о количестве игроков и ботов для отладки
        // Log::info('Количество игроков и ботов в локации по фракциям и классам (CustomOutpostController)', [
        //     'location' => $outpostLocation->name,
        //     'solWarriors' => $solWarriors . ' (игроки: ' . $factionCounts['player_counts']['solarius']['warriors'] . ', боты: ' . $factionCounts['bot_counts']['solarius']['warriors'] . ')',
        //     'solMages' => $solMages . ' (игроки: ' . $factionCounts['player_counts']['solarius']['mages'] . ', боты: ' . $factionCounts['bot_counts']['solarius']['mages'] . ')',
        //     'solKnights' => $solKnights . ' (игроки: ' . $factionCounts['player_counts']['solarius']['knights'] . ', боты: ' . $factionCounts['bot_counts']['solarius']['knights'] . ')',
        //     'lunWarriors' => $lunWarriors . ' (игроки: ' . $factionCounts['player_counts']['lunarius']['warriors'] . ', боты: ' . $factionCounts['bot_counts']['lunarius']['warriors'] . ')',
        //     'lunMages' => $lunMages . ' (игроки: ' . $factionCounts['player_counts']['lunarius']['mages'] . ', боты: ' . $factionCounts['bot_counts']['lunarius']['mages'] . ')',
        //     'lunKnights' => $lunKnights . ' (игроки: ' . $factionCounts['player_counts']['lunarius']['knights'] . ', боты: ' . $factionCounts['bot_counts']['lunarius']['knights'] . ')',
        //     'total_players' => $factionCounts['player_counts']['solarius']['total'] + $factionCounts['player_counts']['lunarius']['total'],
        //     'total_bots' => $factionCounts['bot_counts']['solarius']['total'] + $factionCounts['bot_counts']['lunarius']['total']
        // ]);

        // Формируем хлебные крошки
        $breadcrumbs = [
            [
                'name' => 'Главная',
                'url' => route('home')
            ],
            [
                'name' => 'Битва',
                'url' => route('battle.index')
            ],
            [
                'name' => 'Аванпосты',
                'url' => route('battle.outposts.index')
            ],
            [
                'name' => $outpostLocation->name,
                'url' => null
            ]
        ];

        // Получаем информацию о последнем атаковавшем
        $lastAttacker = null;
        $lastAttackerResources = null;

        if ($user->last_attacker_id) {
            $lastAttacker = User::find($user->last_attacker_id);
            if ($lastAttacker && $lastAttacker->profile) {
                try {
                    // Получаем актуальные ресурсы последнего атаковавшего
                    $lastAttackerResources = $lastAttacker->profile->getActualResources();
                } catch (\Exception $e) {
                    Log::error('Ошибка при получении ресурсов последнего атаковавшего', [
                        'user_id' => $user->id,
                        'last_attacker_id' => $user->last_attacker_id,
                        'error' => $e->getMessage()
                    ]);

                    // Используем значения по умолчанию
                    $lastAttackerResources = [
                        'current_hp' => $lastAttacker->profile->hp ?? 0,
                        'current_mp' => $lastAttacker->profile->mp ?? 0
                    ];
                }
            }
        }

        // Получаем данные о группе пользователя
        $activeParty = $user->parties()
            ->wherePivot('status', \App\Models\PartyMember::STATUS_ACTIVE)
            ->with(['activeUsers.profile'])
            ->first();

        $partyMembers = collect();
        if ($activeParty) {
            $partyMembers = $activeParty->activeUsers;
        }

        // Собираем все данные для передачи в представление
        $viewData = [
            'user' => $user,
            'userProfile' => $user->profile,
            'actualResources' => $user->profile->getActualResources(),
            'onlineCount' => User::where('updated_at', '>=', now()->subMinutes(15))->count(),
            'hasBrokenItems' => false, // Заглушка, можно реализовать позже
            'brokenItemsCount' => 0, // Заглушка, можно реализовать позже
            'mobsInLocation' => $mobsInLocation,
            'obelisk' => $obelisk,
            'targetMob' => $targetMob,
            'target' => $target,
            'battleLogs' => $battleLogs,
            'userEffects' => $userEffects,
            'isStunned' => $isStunned,
            'partyMembers' => $partyMembers, // Данные о группе
            'solWarriors' => $solWarriors,
            'solMages' => $solMages,
            'solKnights' => $solKnights,
            'lunWarriors' => $lunWarriors,
            'lunMages' => $lunMages,
            'lunKnights' => $lunKnights,
            'userSkills' => $userSkills,
            'outpostLocation' => $outpostLocation,
            'subLocations' => $subLocations,
            'breadcrumbs' => $breadcrumbs,
            'villages' => $villages, // Добавляем деревни для отображения в шаблоне
            'solariusVillage' => $solariusVillage, // Добавляем деревню Соляриан
            'lunariusVillage' => $lunariusVillage, // Добавляем деревню Лунариев
            'lastAttacker' => $lastAttacker, // Добавляем информацию о последнем атаковавшем
            'lastAttackerResources' => $lastAttackerResources // Добавляем ресурсы последнего атаковавшего
        ];

        // Если у пользователя есть цель, но она не была найдена в базе данных,
        // проверяем, есть ли информация о цели в сессии
        if ($user->current_target_type === 'mob' && $user->current_target_id && !$targetMob) {
            $selectedMob = session('selected_mob');
            if ($selectedMob && $selectedMob->id == $user->current_target_id) {
                $viewData['targetMob'] = $selectedMob;
                // Также устанавливаем $target для компонента target-block
                $viewData['target'] = $selectedMob;
                Log::info('Использован моб из сессии для отображения', [
                    'mob_id' => $selectedMob->id,
                    'mob_name' => $selectedMob->name
                ]);
            }
        }

        // Добавляем имя локации для отображения в заголовке шаблона
        $viewData['locationName'] = $outpostLocation->name;

        // Формируем правильный префикс маршрута для использования в компоненте skills-panel
        // Используем формат 'battle.outposts', без добавления slug в префикс
        $viewData['routePrefix'] = 'battle.outposts';

        // Очищаем slug от недопустимых символов для имени файла
        $cleanSlug = preg_replace('/[^\w\-]/', '', $outpostLocation->slug);

        // Выбор шаблона
        $templatePath = 'battle.outposts.locations.' . $cleanSlug;

        // Проверяем, существует ли шаблон
        if (!view()->exists($templatePath)) {
            // Если шаблон не существует, логируем ошибку
            Log::error('Шаблон для локации аванпоста не найден', [
                'location_id' => $outpostLocation->id,
                'location_name' => $outpostLocation->name,
                'location_slug' => $outpostLocation->slug,
                'clean_slug' => $cleanSlug,
                'template_path' => $templatePath
            ]);

            // Создаем шаблон для локации
            $templateService = app(\App\Services\OutpostTemplateService::class);

            // Если это подлокация, создаем шаблон для подлокации
            if ($outpostLocation->parent_id) {
                $templateCreated = $templateService->createSubLocationTemplate($outpostLocation);
            } else {
                // Иначе создаем шаблон для базовой локации
                $templateCreated = $templateService->createTemplate($outpostLocation);
            }

            if ($templateCreated) {
                Log::info('Шаблон для локации аванпоста успешно создан', [
                    'location_id' => $outpostLocation->id,
                    'location_name' => $outpostLocation->name,
                    'location_slug' => $outpostLocation->slug,
                    'clean_slug' => $cleanSlug,
                    'template_path' => $templatePath
                ]);
                
                // Очищаем кеш представлений Laravel
                \Artisan::call('view:clear');
                
                // Повторно проверяем существование шаблона
                if (!view()->exists($templatePath)) {
                    Log::error('Шаблон создан, но Laravel не может его найти', [
                        'template_path' => $templatePath,
                        'file_exists' => file_exists(resource_path("views/battle/outposts/locations/{$cleanSlug}.blade.php"))
                    ]);
                    
                    // Используем fallback шаблон
                    $templatePath = 'battle.outposts.locations.Elven_haven';
                }
            } else {
                Log::error('Не удалось создать шаблон для локации аванпоста', [
                    'location_id' => $outpostLocation->id,
                    'location_name' => $outpostLocation->name,
                    'location_slug' => $outpostLocation->slug,
                    'clean_slug' => $cleanSlug,
                    'template_path' => $templatePath
                ]);
                
                // Используем fallback шаблон
                $templatePath = 'battle.outposts.locations.Elven_haven';
            }
        }

        return view($templatePath, $viewData);
    }

    /**
     * Получение ключа для боевых логов
     * ИСПРАВЛЕНО: Используем персональный ключ для корректного отображения наград обелиска
     *
     * @param User $user Пользователь
     * @param string $locationName Название локации
     * @return string Ключ для боевых логов
     */
    protected function getBattleLogKey(User $user, string $locationName): string
    {
        // Проверяем, есть ли логи из Тарнмора, сохраненные с специфическим ключом
        $tarnmoreKey = "location:tarnmore_quarry:{$user->id}";
        if (\Illuminate\Support\Facades\Cache::has("{$tarnmoreKey}:logs")) {
            \Log::info("Найдены логи Тарнмора, будем использовать ключ {$tarnmoreKey}");
            return $tarnmoreKey;
        }

        // ИСПРАВЛЕНИЕ: Всегда используем персональный ключ для корректного отображения наград обелиска
        // Система обелисков записывает награды в персональный журнал боя игрока
        $personalKey = $this->battleLogService->getBattleLogKey($user->id);

        \Log::info("Используем персональный ключ для журнала боя в кастомной локации", [
            'user_id' => $user->id,
            'location' => $locationName,
            'battle_log_key' => $personalKey
        ]);

        return $personalKey;
    }

    /**
     * Получение игроков в локации по фракциям и классам
     *
     * @param string $locationName Название локации
     * @return array Массив с игроками по фракциям и классам
     */
    protected function getPlayersInLocation(string $locationName): array
    {
        // Получаем всех игроков в локации, которые онлайн
        // Используем last_activity_timestamp для проверки онлайн-статуса (активность за последние 5 минут)
        $players = User::whereHas('statistics', function ($query) use ($locationName) {
            $query->where('current_location', $locationName);
        })
            ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
            ->with(['profile', 'statistics'])
            ->get();

        // Разделяем игроков по фракциям и классам и получаем их количество
        $solWarriors = $players->filter(function ($player) {
            return $player->profile->race === 'solarius' && $player->profile->class === 'warrior';
        })->count();

        $solMages = $players->filter(function ($player) {
            return $player->profile->race === 'solarius' && $player->profile->class === 'mage';
        })->count();

        $solKnights = $players->filter(function ($player) {
            return $player->profile->race === 'solarius' && $player->profile->class === 'priest'; // Жрецы вместо рыцарей
        })->count();

        $lunWarriors = $players->filter(function ($player) {
            return $player->profile->race === 'lunarius' && $player->profile->class === 'warrior';
        })->count();

        $lunMages = $players->filter(function ($player) {
            return $player->profile->race === 'lunarius' && $player->profile->class === 'mage';
        })->count();

        $lunKnights = $players->filter(function ($player) {
            return $player->profile->race === 'lunarius' && $player->profile->class === 'priest'; // Жрецы вместо рыцарей
        })->count();

        // Логируем информацию о количестве игроков для отладки
        Log::info('Количество игроков в локации по фракциям и классам', [
            'location' => $locationName,
            'solWarriors' => $solWarriors,
            'solMages' => $solMages,
            'solKnights' => $solKnights,
            'lunWarriors' => $lunWarriors,
            'lunMages' => $lunMages,
            'lunKnights' => $lunKnights,
            'total' => $players->count()
        ]);

        return [
            'playersInLocation' => $players,
            'solWarriors' => $solWarriors,
            'solMages' => $solMages,
            'solKnights' => $solKnights,
            'lunWarriors' => $lunWarriors,
            'lunMages' => $lunMages,
            'lunKnights' => $lunKnights
        ];
    }

    /**
     * Расчет урона в PvP
     *
     * @param User $attacker Атакующий игрок
     * @param User $defender Защищающийся игрок
     * @return int Рассчитанный урон
     */
    protected function calculatePvPDamage($attacker, $defender)
    {
        // Получаем эффективные характеристики
        $attackerStrength = $attacker->profile->getEffectiveStats()['strength'] ?? 1;
        $defenderArmor = $defender->profile->getEffectiveStats()['armor'] ?? 0;

        // Используем CombatFormulaService для расчета урона
        $baseDamage = $this->combatFormulaService->calculateDamage($attackerStrength, $defenderArmor);

        // Применяем множитель урона для PvP
        $baseDamage = round($baseDamage * self::PVP_DAMAGE_MULTIPLIER);

        // Применяем случайность (±3%)
        $randomFactor = rand(97, 103) / 100;
        $damage = (int) round($baseDamage * $randomFactor);

        // Определяем критический удар (5% шанс)
        $isCritPvp = false;
        if (rand(1, 100) <= 5) {
            $damage = round($damage * 1.5);
            $isCritPvp = true;
        }

        // Логирование расчета урона
        Log::info('PvP Damage Calculation', [
            'attacker_id' => $attacker->id,
            'defender_id' => $defender->id,
            'attacker_strength' => $attackerStrength,
            'defender_armor' => $defenderArmor,
            'base_damage' => $baseDamage,
            'pvp_multiplier' => self::PVP_DAMAGE_MULTIPLIER,
            'random_factor' => $randomFactor,
            'is_crit' => $isCritPvp,
            'final_damage' => $damage
        ]);

        return max(1, $damage); // Минимальный урон 1
    }

    /**
     * Очистка целей для игроков, выбравших мертвых или неактивных ботов
     *
     * @param string $locationName Название локации
     * @return void
     */
    protected function cleanupDeadBotTargets(string $locationName): void
    {
        // Получаем всех игроков в локации, у которых цель - бот
        $playersWithBotTargets = User::whereHas('statistics', function ($query) use ($locationName) {
            $query->where('current_location', $locationName);
        })
            ->where('current_target_type', 'bot')
            ->whereNotNull('current_target_id')
            ->get();

        foreach ($playersWithBotTargets as $player) {
            $shouldClearTarget = false;
            $reason = '';

            // Находим бота-цель
            $targetBot = Bot::find($player->current_target_id);

            if (!$targetBot) {
                $shouldClearTarget = true;
                $reason = 'Бот-цель не найден в базе данных';
            } elseif ($targetBot->hp <= 0) {
                $shouldClearTarget = true;
                $reason = 'Бот-цель мертв (HP <= 0)';
            } elseif (!$targetBot->is_active) {
                $shouldClearTarget = true;
                $reason = 'Бот-цель неактивен';
            } elseif ($targetBot->location !== $locationName) {
                $shouldClearTarget = true;
                $reason = 'Бот-цель покинул локацию';
            }

            // Очищаем цель если нужно
            if ($shouldClearTarget) {
                $player->current_target_type = null;
                $player->current_target_id = null;
                $player->save(['timestamps' => false]);

                Log::info('Очищена цель мертвого/неактивного бота для игрока', [
                    'player_id' => $player->id,
                    'bot_id' => $player->current_target_id,
                    'location' => $locationName,
                    'reason' => $reason
                ]);
            }
        }
    }

    /**
     * Очистка неактивных атакующих для всех игроков в локации
     *
     * @param string $locationName Название локации
     * @return void
     */
    protected function cleanupInactiveAttackers(string $locationName): void
    {
        // Получаем всех игроков в текущей локации, у которых есть last_attacker_id
        $playersWithAttackers = User::whereHas('statistics', function ($query) use ($locationName) {
            $query->where('current_location', $locationName);
        })
            ->whereNotNull('last_attacker_id')
            ->get();

        foreach ($playersWithAttackers as $player) {
            $shouldClearAttacker = false;
            $reason = '';

            // Находим атакующего
            $attacker = User::find($player->last_attacker_id);

            if (!$attacker) {
                $shouldClearAttacker = true;
                $reason = 'Атакующий не найден в базе данных';
            } elseif ($attacker->last_activity_timestamp < now()->subMinutes(5)->timestamp) {
                $shouldClearAttacker = true;
                $reason = 'Атакующий неактивен более 5 минут';
            } elseif (!$attacker->statistics || $attacker->statistics->current_location !== $locationName) {
                $shouldClearAttacker = true;
                $reason = 'Атакующий покинул локацию';
            }

            // Очищаем last_attacker_id если нужно
            if ($shouldClearAttacker) {
                $player->last_attacker_id = null;
                $player->save(['timestamps' => false]);

                Log::info('Очищен last_attacker_id для игрока в аванпосте', [
                    'player_id' => $player->id,
                    'attacker_id' => $player->last_attacker_id,
                    'location' => $locationName,
                    'reason' => $reason
                ]);
            }
        }
    }

    /**
     * Использование умения игроком
     *
     * @param Request $request Запрос
     * @param int $id Идентификатор локации
     * @param int $skillId ID умения
     * @return \Illuminate\Http\RedirectResponse
     */
    public function useSkill(Request $request, $id, $skillId)
    {
        // Логируем вызов метода
        \Log::info('Вызов useSkill в CustomOutpostController', [
            'skillId' => $skillId,
            'id' => $id,
            'request' => $request->all(),
        ]);

        // Проверяем, что $skillId — это число
        if (!is_numeric($skillId)) {
            \Log::error('Skill ID не является числом', ['skillId' => $skillId]);
            return back()->with('error', 'Некорректный ID умения');
        }

        try {
            $user = Auth::user();

            // Проверка авторизации
            if (!$user) {
                abort(403, 'Вы должны быть авторизованы, чтобы выполнять это действие.');
            }

            // Находим локацию по ID (используем таблицу locations, а не outpost_locations)
            $outpostLocation = Location::where('id', $id)
                ->where('is_active', true)
                ->firstOrFail();

            // Получаем умение
            $skill = Skill::find($skillId);

            if (!$skill) {
                \Log::error('Умение не найдено', ['skillId' => $skillId]);
                return back()->with('error', 'Умение не найдено');
            }

            // Проверяем, что $skill - это объект Skill, а не коллекция
            if ($skill instanceof \Illuminate\Database\Eloquent\Collection) {
                \Log::error('$skill является коллекцией, а не объектом', [
                    'skillId' => $skillId,
                    'count' => $skill->count()
                ]);

                // Если это коллекция, возьмем первый элемент
                if ($skill->count() > 0) {
                    $skill = $skill->first();
                    \Log::info('Извлечен первый элемент из коллекции умений', [
                        'skill_id' => $skill->id,
                        'skill_name' => $skill->name
                    ]);
                } else {
                    return back()->with('error', 'Умение не найдено (пустая коллекция)');
                }
            }

            $targetType = $request->input('target_type'); // 'user' или 'mob'
            $targetId = $request->input('target_id');
            $location = $outpostLocation->name;

            // Логируем информацию о локациях для отладки
            Log::info('Проверка локации при использовании умения', [
                'user_id' => $user->id,
                'current_location' => $user->statistics->current_location,
                'outpost_location' => $location,
                'outpost_id' => $id
            ]);

            // Проверяем, находится ли пользователь в этой локации
            // Если локация пользователя не совпадает с локацией аванпоста,
            // обновляем локацию пользователя на локацию аванпоста
            if ($user->statistics->current_location !== $location) {
                // Обновляем локацию пользователя
                $user->statistics->update([
                    'current_location' => $location
                ]);

                Log::info('Обновлена локация пользователя при использовании умения', [
                    'user_id' => $user->id,
                    'old_location' => $user->statistics->current_location,
                    'new_location' => $location,
                    'outpost_id' => $id
                ]);
            }

            // Определяем цель
            $target = null;
            if ($skill->target_type !== 'all_allies' && $skill->target_type !== 'all_enemies') {
                if ($targetType === 'user') {
                    $target = User::findOrFail($targetId);
                } elseif ($targetType === 'mob') {
                    $target = Mob::findOrFail($targetId);
                } elseif ($skill->target_type === 'self') {
                    $target = $user;
                }
            }

            // Логируем типы данных перед вызовом useSkill
            \Log::info('Перед вызовом useSkill', [
                'caster_type' => get_class($user),
                'target_type' => $target ? get_class($target) : 'null',
                'skill_type' => get_class($skill),
                'skill_id' => $skill->id,
                'location' => $location
            ]);

            // Отключаем логирование баффов в SkillService, т.к. мы будем логировать результат здесь
            $this->skillService->setShouldLogBuffEffects(false);

            // Применяем скилл
            $result = $this->skillService->useSkill($user, $target, $skill, $location);

            if (!$result['success']) {
                \Log::warning('Неудачное использование умения', [
                    'skillName' => $skill->name,
                    'message' => $result['message']
                ]);
                return back()->with('error', $result['message']);
            }

            \Log::info('Успешное использование умения', [
                'skillName' => $skill->name,
                'message' => $result['message']
            ]);

            // Получаем ключ боевого лога
            $battleLogKey = $this->getBattleLogKey($user, $outpostLocation->name);

            // Добавляем запись в боевой лог
            $this->battleLogService->addLog(
                $battleLogKey,
                "🔮 Вы использовали умение {$skill->name}",
                'info'
            );

            // Если умение наносит урон, вносим вклад в обелиск (с проверкой контроля деревень)
            if (isset($result['damage']) && $result['damage'] > 0) {
                $contributionResult = $this->obeliskService->contributeToObelisk($user, $location, $result['damage']);

                // Проверяем, был ли принят вклад в обелиск
                if ($contributionResult) {
                    // Добавляем запись о вкладе в обелиск
                    $this->battleLogService->addLog(
                        $battleLogKey,
                        "🔮 Вы внесли вклад в обелиск: +{$result['damage']} энергии",
                        'info'
                    );
                } else {
                    // Логируем отклонение вклада
                    Log::info('Вклад в обелиск отклонен при использовании умения', [
                        'user_id' => $user->id,
                        'user_race' => $user->profile->race ?? 'не определена',
                        'location' => $location,
                        'damage' => $result['damage'],
                        'skill_name' => $skill->name
                    ]);
                }
            }

            return back()->with('success', $result['message']);
        } catch (\Exception $e) {
            \Log::error('Ошибка при использовании умения', [
                'skillId' => $skillId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return back()->with('error', 'Произошла ошибка при использовании умения: ' . $e->getMessage());
        }
    }

    /**
     * Находит случайного врага противоположной фракции в локации (игроков и ботов)
     * Исправленная версия с улучшенной логикой поиска целей
     *
     * @param User $attacker Атакующий пользователь
     * @param string $location Название локации
     * @param string|null $excludeTargetType Тип цели для исключения из поиска
     * @param int|null $excludeTargetId ID цели для исключения из поиска
     * @return mixed|null Случайный враг (User или Bot) или null, если врагов не найдено
     */
    protected function findRandomEnemyInLocation(User $attacker, string $location, $excludeTargetType = null, $excludeTargetId = null)
    {
        // Определяем фракцию атакующего и вражескую фракцию
        $attackerFaction = $attacker->profile->race; // solarius / lunarius
        $enemyFaction = ($attackerFaction === 'solarius') ? 'lunarius' : 'solarius';

        // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Используем current_location игрока вместо названия аванпоста
        // Это обеспечивает единообразие с FactionCountService, который также использует current_location

        // ДОПОЛНИТЕЛЬНОЕ ИСПРАВЛЕНИЕ: Принудительно обновляем данные пользователя для избежания кэширования
        $attacker->refresh();
        $attacker->load('statistics');
        $playerCurrentLocation = $attacker->statistics->current_location;

        // Нормализуем название локации для единообразия с FactionCountService
        $locationService = app(\App\Services\battle\UserLocationService::class);
        $normalizedLocation = $locationService->normalizeLocationName($playerCurrentLocation);

        Log::info("CustomOutpost: Начинаем поиск врагов в локации", [
            'attacker_id' => $attacker->id,
            'attacker_name' => $attacker->name,
            'outpost_location' => $location,
            'player_current_location' => $playerCurrentLocation,
            'normalized_location' => $normalizedLocation,
            'attacker_faction' => $attackerFaction,
            'enemy_faction' => $enemyFaction,
            'exclude_target_type' => $excludeTargetType,
            'exclude_target_id' => $excludeTargetId,
            'fix_note' => 'Используем current_location игрока для единообразия с FactionCountService'
        ]);

        // ИСПРАВЛЕНИЕ: Убираем кэширование для более точного поиска целей
        // Кэширование может возвращать устаревшие данные, что приводит к ложным сообщениям об отсутствии врагов

        // ИСПРАВЛЕНИЕ: Увеличиваем время онлайн-статуса с 5 до 20 минут (как в системе ботов)
        // Поиск игроков противоположной фракции с улучшенной логикой
        // ИСПРАВЛЕНИЕ: Используем нормализованное название локации
        $potentialEnemyPlayers = User::where('users.id', '!=', $attacker->id)
            ->join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
            ->join('user_statistics', 'users.id', '=', 'user_statistics.user_id')
            ->where('user_profiles.race', $enemyFaction)
            ->where('user_statistics.current_location', $normalizedLocation) // ИСПРАВЛЕНО: используем нормализованное название
            ->where('users.last_activity_timestamp', '>=', now()->subMinutes(20)->timestamp) // ИСПРАВЛЕНО: 20 минут вместо 5
            ->select('users.*')
            ->with(['profile', 'statistics']) // Подгружаем связи для проверки
            ->get();

        Log::info("CustomOutpost: Найдено потенциальных игроков-врагов до фильтрации", [
            'outpost_location' => $location,
            'player_current_location' => $playerCurrentLocation,
            'normalized_location' => $normalizedLocation,
            'enemy_faction' => $enemyFaction,
            'potential_players_count' => $potentialEnemyPlayers->count(),
            'players_list' => $potentialEnemyPlayers->map(function ($p) {
                return [
                    'id' => $p->id,
                    'name' => $p->name,
                    'race' => $p->profile->race ?? 'unknown',
                    'last_activity' => $p->last_activity_timestamp,
                    'db_hp' => $p->profile->hp ?? 0
                ];
            })->toArray()
        ]);

        // Исключаем текущую цель, если она является игроком
        if ($excludeTargetType === 'player' && $excludeTargetId) {
            $potentialEnemyPlayers = $potentialEnemyPlayers->where('id', '!=', $excludeTargetId);
        }

        // ИСПРАВЛЕНИЕ: Улучшенная фильтрация игроков с актуальным HP > 0
        $validEnemyPlayers = $potentialEnemyPlayers->filter(function ($player) {
            try {
                // Сначала проверяем базовое HP из БД
                if (!$player->profile || $player->profile->hp <= 0) {
                    Log::debug("CustomOutpost: Игрок {$player->name} исключен - базовое HP <= 0", [
                        'player_id' => $player->id,
                        'db_hp' => $player->profile->hp ?? 'null'
                    ]);
                    return false;
                }

                // Затем проверяем актуальное HP из Redis
                $actualResources = $player->profile->getActualResources();
                $isValid = $actualResources['current_hp'] > 0;

                Log::debug("CustomOutpost: Проверка HP игрока {$player->name}", [
                    'player_id' => $player->id,
                    'db_hp' => $player->profile->hp,
                    'actual_hp' => $actualResources['current_hp'],
                    'is_valid' => $isValid
                ]);

                return $isValid;
            } catch (\Exception $e) {
                Log::warning("CustomOutpost: Ошибка при получении актуальных ресурсов игрока {$player->name}", [
                    'player_id' => $player->id,
                    'error' => $e->getMessage(),
                    'fallback_to_db_hp' => $player->profile->hp ?? 0
                ]);
                // ИСПРАВЛЕНИЕ: Улучшенный fallback - проверяем базовое HP
                return $player->profile && $player->profile->hp > 0;
            }
        });

        // Поиск ботов противоположной фракции
        // ИСПРАВЛЕНИЕ: Используем нормализованное название локации для поиска ботов
        $enemyBotsQuery = Bot::where('race', $enemyFaction)
            ->where('location', $normalizedLocation) // ИСПРАВЛЕНО: используем нормализованное название
            ->where('is_active', true)
            ->where('hp', '>', 0);

        // Исключаем текущую цель, если она является ботом
        if ($excludeTargetType === 'bot' && $excludeTargetId) {
            $enemyBotsQuery->where('id', '!=', $excludeTargetId);
        }

        $enemyBots = $enemyBotsQuery->get();

        Log::info("CustomOutpost: Найдено потенциальных ботов-врагов", [
            'outpost_location' => $location,
            'player_current_location' => $playerCurrentLocation,
            'normalized_location' => $normalizedLocation,
            'enemy_faction' => $enemyFaction,
            'enemy_bots_count' => $enemyBots->count(),
            'bots_list' => $enemyBots->map(function ($b) {
                return [
                    'id' => $b->id,
                    'name' => $b->name,
                    'race' => $b->race,
                    'hp' => $b->hp,
                    'max_hp' => $b->max_hp,
                    'is_active' => $b->is_active,
                    'location' => $b->location
                ];
            })->toArray()
        ]);

        // Объединяем игроков и ботов
        $allEnemies = $validEnemyPlayers->merge($enemyBots);

        // ИСПРАВЛЕНИЕ: Детальное логирование для отладки проблемы с поиском целей
        Log::info("CustomOutpost: Итоговый результат поиска врагов в локации", [
            'attacker_id' => $attacker->id,
            'attacker_name' => $attacker->name,
            'outpost_location' => $location,
            'player_current_location' => $playerCurrentLocation,
            'normalized_location' => $normalizedLocation,
            'attacker_faction' => $attackerFaction,
            'enemy_faction' => $enemyFaction,
            'potential_players_before_hp_filter' => $potentialEnemyPlayers->count(),
            'valid_players_after_hp_filter' => $validEnemyPlayers->count(),
            'enemy_bots_count' => $enemyBots->count(),
            'total_enemies' => $allEnemies->count(),
            'exclude_target_type' => $excludeTargetType,
            'exclude_target_id' => $excludeTargetId,
            'fix_note' => 'Теперь используем current_location игрока для единообразия с FactionCountService',
            'final_enemies_list' => $allEnemies->map(function ($enemy) {
                if ($enemy instanceof User) {
                    return [
                        'type' => 'player',
                        'id' => $enemy->id,
                        'name' => $enemy->name,
                        'race' => $enemy->profile->race ?? 'unknown'
                    ];
                } else {
                    return [
                        'type' => 'bot',
                        'id' => $enemy->id,
                        'name' => $enemy->name,
                        'race' => $enemy->race,
                        'location' => $enemy->location
                    ];
                }
            })->toArray()
        ]);

        // ИСПРАВЛЕНИЕ: Убираем кэширование - возвращаем результат напрямую
        if ($allEnemies->isEmpty()) {
            Log::warning("CustomOutpost: Не найдено врагов в локации для атакующего {$attacker->name}", [
                'attacker_faction' => $attackerFaction,
                'enemy_faction' => $enemyFaction,
                'outpost_location' => $location,
                'player_current_location' => $playerCurrentLocation,
                'normalized_location' => $normalizedLocation,
                'fix_note' => 'Поиск теперь ведется в current_location игрока, а не в локации аванпоста'
            ]);
            return null;
        }

        // Возвращаем случайного врага
        $selectedEnemy = $allEnemies->random();

        Log::info("CustomOutpost: Выбран случайный враг", [
            'attacker_id' => $attacker->id,
            'selected_enemy_type' => $selectedEnemy instanceof User ? 'player' : 'bot',
            'selected_enemy_id' => $selectedEnemy->id,
            'selected_enemy_name' => $selectedEnemy->name
        ]);

        return $selectedEnemy;
    }

    /**
     * Обрабатывает метку обелиска для пользователя
     *
     * @param User $user Пользователь
     * @param Mob $mob Моб, выбранный игроком
     * @param string $battleLogKey Ключ боевого лога
     * @return void
     */
    private function processObeliskMark(User $user, Mob $mob, string $battleLogKey): void
    {
        // Получаем сервис управления метками обелисков
        $markService = app(\App\Services\ObeliskMarkService::class);

        // Получаем текущую локацию
        $currentLocation = $user->statistics->current_location ?? 'unknown';

        // Проверяем, есть ли уже активная метка у игрока в этой локации
        if ($markService->hasActiveMark($user->id, $currentLocation)) {
            $obeliskLog = "⚡ У вас уже есть активная метка обелиска!";

            // Добавляем временную метку только как скрытый HTML-комментарий для уникальности
            $timestamp = now()->format('H:i:s');
            $this->battleLogService->addLog($battleLogKey, $obeliskLog . "<!-- ts:{$timestamp} -->", 'warning');

            return;
        }

        // Проверяем существование умения "Метка обелиска"
        $obeliskSkill = \App\Models\Skill::where('id', 12)->first();

        if (!$obeliskSkill) {
            // Если умения нет в БД, создаем его
            $obeliskSkill = new \App\Models\Skill([
                'id' => 12,
                'name' => 'Метка обелиска',
                'description' => 'Метка обелиска делает вас заметным для мобов, которые будут атаковать вас с разной задержкой.',
                'icon' => 'assets/skills/obelisk_mark.png',
                'type' => 'debuff',
                'duration' => 60,
                'cooldown' => 0,
            ]);
            $obeliskSkill->save();

            Log::info("Создано умение 'Метка обелиска'", [
                'skill_id' => $obeliskSkill->id
            ]);
        }

        try {
            // Создаем новую метку обелиска через оптимизированный сервис
            $locationId = \App\Models\Location::where('name', $currentLocation)->value('id');

            $mark = $markService->createMark(
                $user,
                $mob,
                $currentLocation,
                $locationId,
                60 // Длительность 60 секунд
            );

            Log::info("Создана метка обелиска через новую систему", [
                'user_id' => $user->id,
                'mark_id' => $mark->id,
                'mob_id' => $mob->id,
                'location' => $currentLocation,
                'expires_at' => $mark->expires_at
            ]);

            // Добавляем сообщение в лог о наложении метки
            $obeliskLog = $this->logFormatter->formatGenericMessage(
                "🎯",
                "На вас наложена метка обелиска! Мобы будут атаковать вас в течение 60 секунд.",
                "",
                "text-orange-400"
            );

            // Добавляем временную метку только как скрытый HTML-комментарий для уникальности
            $timestamp = now()->format('H:i:s');
            $this->battleLogService->addLog($battleLogKey, $obeliskLog . "<!-- ts:{$timestamp} -->", 'warning');

        } catch (\Exception $e) {
            Log::error('Ошибка при создании метки обелиска', [
                'user_id' => $user->id,
                'mob_id' => $mob->id,
                'location' => $currentLocation,
                'error' => $e->getMessage()
            ]);

            // Fallback к старой системе
            $effect = new \App\Models\ActiveEffect([
                'target_type' => 'player',
                'target_id' => $user->id,
                'skill_id' => $obeliskSkill->id,
                'caster_type' => 'mob',
                'caster_id' => $mob->id,
                'duration' => 60,
                'ends_at' => now()->addSeconds(60),
                'effect_data' => ['damage' => 10],
            ]);

            $effect->save();

            $obeliskLog = $this->logFormatter->formatGenericMessage(
                "🎯",
                "На вас наложена метка обелиска! Мобы будут атаковать вас в течение 60 секунд.",
                "",
                "text-orange-400"
            );
            $timestamp = now()->format('H:i:s');
            $this->battleLogService->addLog($battleLogKey, $obeliskLog . "<!-- ts:{$timestamp} -->", 'warning');
        }
    }

    /**
     * Выполняет автоматическую атаку после смены цели
     *
     * @param User $attacker Атакующий игрок
     * @param mixed $target Цель атаки (User или Bot)
     * @param string $battleLogKey Ключ боевого лога
     * @return array Результат атаки
     */
    private function performAutoAttackAfterTargetChange(User $attacker, $target, string $battleLogKey): array
    {
        try {
            Log::info('Начало автоматической атаки после смены цели', [
                'attacker_id' => $attacker->id,
                'attacker_name' => $attacker->name,
                'target_type' => $target instanceof User ? 'player' : 'bot',
                'target_id' => $target->id,
                'target_name' => $target->name
            ]);

            if ($target instanceof User) {
                // Проверяем, что цель все еще существует и активна
                $target->refresh();
                if (!$target || !$target->profile) {
                    Log::warning('Цель-игрок не найдена или не имеет профиля', [
                        'target_id' => $target->id ?? 'null'
                    ]);
                    return [
                        'success' => false,
                        'message' => 'Цель недоступна для атаки.'
                    ];
                }

                // Атака игрока
                Log::info('Расчет урона для атаки игрока', [
                    'attacker_id' => $attacker->id,
                    'target_id' => $target->id
                ]);

                $damage = $this->calculatePvPDamage($attacker, $target);

                Log::info('Урон рассчитан', [
                    'damage' => $damage,
                    'attacker_id' => $attacker->id,
                    'target_id' => $target->id
                ]);

                // Получаем текущие ресурсы цели
                $actualResources = $target->profile->getActualResources();
                $currentHp = $actualResources['current_hp'];
                $newHp = max(0, $currentHp - $damage);

                Log::info('Применение урона к игроку', [
                    'target_id' => $target->id,
                    'current_hp' => $currentHp,
                    'damage' => $damage,
                    'new_hp' => $newHp
                ]);

                // Применяем урон через правильный метод Redis
                $target->profile->setRedisHp($newHp);

                // Также обновляем HP в базе данных для синхронизации
                $target->profile->hp = $newHp;
                $target->profile->save();

                // Устанавливаем информацию о последнем атаковавшем
                $target->last_attacker_id = $attacker->id;
                $target->save();

                Log::info('Урон успешно применен к игроку', [
                    'target_id' => $target->id,
                    'final_hp' => $newHp
                ]);

                // Логируем атаку
                $this->battleLogService->addLog(
                    $battleLogKey,
                    "⚔️ Вы атаковали {$target->name} и нанесли {$damage} урона",
                    'combat'
                );

                Log::info('Автоматическая атака игрока после смены цели завершена успешно', [
                    'attacker_id' => $attacker->id,
                    'target_id' => $target->id,
                    'damage' => $damage,
                    'target_hp_after' => $newHp
                ]);

                return [
                    'success' => true,
                    'message' => "Нанесено {$damage} урона игроку {$target->name}."
                ];

            } else {
                // Проверяем, что бот все еще существует и активен
                $target->refresh();
                if (!$target || $target->hp <= 0 || !$target->is_active) {
                    Log::warning('Цель-бот не найдена, мертва или неактивна', [
                        'target_id' => $target->id ?? 'null',
                        'hp' => $target->hp ?? 'null',
                        'is_active' => $target->is_active ?? 'null'
                    ]);
                    return [
                        'success' => false,
                        'message' => 'Цель недоступна для атаки.'
                    ];
                }

                // Атака бота
                Log::info('Расчет урона для атаки бота', [
                    'attacker_id' => $attacker->id,
                    'target_id' => $target->id
                ]);

                $damage = $this->calculateBotDamage($attacker, $target);

                Log::info('Урон рассчитан для бота', [
                    'damage' => $damage,
                    'attacker_id' => $attacker->id,
                    'target_id' => $target->id
                ]);

                // Применяем урон боту
                $currentBotHp = $target->hp;
                $target->hp = max(0, $currentBotHp - $damage);
                $target->last_attacker_id = $attacker->id;
                $target->last_attacker_type = 'player';
                $target->save();

                Log::info('Урон успешно применен к боту', [
                    'target_id' => $target->id,
                    'old_hp' => $currentBotHp,
                    'damage' => $damage,
                    'new_hp' => $target->hp
                ]);

                // Логируем атаку
                $this->battleLogService->addLog(
                    $battleLogKey,
                    "⚔️ Вы атаковали бота {$target->name} и нанесли {$damage} урона",
                    'combat'
                );

                Log::info('Автоматическая атака бота после смены цели завершена успешно', [
                    'attacker_id' => $attacker->id,
                    'target_id' => $target->id,
                    'damage' => $damage,
                    'target_hp_after' => $target->hp
                ]);

                return [
                    'success' => true,
                    'message' => "Нанесено {$damage} урона боту {$target->name}."
                ];
            }

        } catch (\Exception $e) {
            Log::error('Ошибка при автоматической атаке после смены цели', [
                'attacker_id' => $attacker->id,
                'attacker_name' => $attacker->name ?? 'unknown',
                'target_type' => $target instanceof User ? 'player' : 'bot',
                'target_id' => $target->id ?? 'null',
                'target_name' => $target->name ?? 'unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Произошла ошибка при атаке: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Вычисляет урон для атаки бота
     *
     * @param User $attacker Атакующий игрок
     * @param mixed $bot Бот-цель
     * @return int Урон
     */
    private function calculateBotDamage(User $attacker, $bot): int
    {
        // Базовый урон игрока
        $baseDamage = $attacker->profile->attack ?? 10;

        // Случайный множитель (80-120% от базового урона)
        $randomMultiplier = mt_rand(80, 120) / 100;

        // Итоговый урон
        $damage = (int) round($baseDamage * $randomMultiplier);

        return max(1, $damage); // Минимум 1 урон
    }

    /**
     * Обрабатывает награды за победу над мобом в кастомных аванпостах
     *
     * @param User $user Игрок, победивший моба
     * @param Mob $mob Побежденный моб
     * @param string $battleLogKey Ключ боевого лога
     */
    private function handleMobVictoryRewards($user, $mob, $battleLogKey)
    {
        // Загружаем моба с необходимыми связями для дропа
        $mobWithDrops = Mob::with(['itemDrops.item', 'currencyDrops', 'resourceDrops.resource', 'alchemyIngredientDrops'])
            ->where('id', $mob->id)
            ->first();

        if (!$mobWithDrops) {
            \Log::warning('Не удалось загрузить моба с дропами', ['mob_id' => $mob->id]);
            return;
        }

        \Log::info('Начинаем обработку наград за победу над мобом в кастомном аванпосте', [
            'user_id' => $user->id,
            'mob_id' => $mobWithDrops->id,
            'mob_name' => $mobWithDrops->name,
            'item_drops_count' => $mobWithDrops->itemDrops->count(),
            'currency_drops_count' => $mobWithDrops->currencyDrops->count(),
            'resource_drops_count' => $mobWithDrops->resourceDrops->count(),
            'alchemy_drops_count' => $mobWithDrops->alchemyIngredientDrops->count()
        ]);

        // Получаем RewardService
        $rewardService = app(\App\Services\RewardService::class);

        // Генерация наград (ресурсы, предметы, алхимия, валюта)
        $rewards = [];
        $rewards['currency'] = $rewardService->calculateCurrencyReward($mobWithDrops);
        $rewards['resources'] = $rewardService->generateMobLoot($mobWithDrops);
        $rewards['items'] = $rewardService->generateItemLoot($mobWithDrops, $user->id);
        $rewards['alchemy_ingredients'] = $rewardService->generateAlchemyLoot($mobWithDrops);

        \Log::info('Сгенерированы награды', [
            'user_id' => $user->id,
            'mob_id' => $mobWithDrops->id,
            'rewards' => $rewards
        ]);

        // Выдача наград через RewardService
        $rewardResults = $rewardService->giveRewards($user, $rewards);

        \Log::info('Результат выдачи наград', [
            'user_id' => $user->id,
            'mob_id' => $mobWithDrops->id,
            'reward_results' => $rewardResults
        ]);

        // Получаем LogFormattingService
        $logFormatter = app(\App\Services\LogFormattingService::class);

        // Логируем выданные предметы в стиле обелиска
        if (isset($rewardResults['given']['items']) && !empty($rewardResults['given']['items'])) {
            foreach ($rewardResults['given']['items'] as $item) {
                // Определяем стиль для отображения в зависимости от редкости
                $rarityStyles = [
                    'common' => 'text-gray-300',
                    'uncommon' => 'text-green-400',
                    'rare' => 'text-blue-400',
                    'epic' => 'text-purple-400',
                    'legendary' => 'text-orange-400'
                ];

                $quality = $item['quality'] ?? 'common';
                $style = $rarityStyles[$quality] ?? 'text-white';

                // Получаем реальную иконку предмета из базы данных (в стиле обелиска)
                $itemIcon = $this->getItemIcon($item); // Используем универсальную функцию

                // Отображаем сообщение о добытом предмете в стиле обелиска
                $itemMessage = $logFormatter->formatMobDropReward(
                    $itemIcon, // Иконка предмета (реальная или по умолчанию)
                    $item['name'], // Название предмета
                    "{$style} font-medium", // Стиль в зависимости от редкости
                    true // Включать название предмета
                );
                $this->battleLogService->addLog($battleLogKey, $itemMessage, 'success');

                \Log::info("Добавлен лог о выпадении предмета в боевой лог (стиль обелиска)", [
                    'user_id' => $user->id,
                    'item_id' => $item['id'],
                    'item_name' => $item['name'],
                    'quality' => $quality
                ]);
            }
        }

        // Логируем выданные ресурсы в стиле обелиска
        if (isset($rewardResults['given']['resources']) && !empty($rewardResults['given']['resources'])) {
            foreach ($rewardResults['given']['resources'] as $resourceId => $quantity) {
                // Получаем информацию о ресурсе
                $resource = \App\Models\Resource::find($resourceId);

                if ($resource) {
                    // Определяем стиль для отображения в зависимости от редкости
                    $rarityStyles = [
                        'common' => 'text-gray-300',
                        'uncommon' => 'text-green-400',
                        'rare' => 'text-blue-400',
                        'epic' => 'text-purple-400',
                        'legendary' => 'text-orange-400'
                    ];

                    $style = $rarityStyles[$resource->rarity] ?? 'text-white';

                    // Получаем реальную иконку ресурса из базы данных (в стиле обелиска)
                    $resourceIcon = "💎"; // Иконка по умолчанию
                    if (isset($resource->icon) && $resource->icon && file_exists(public_path($resource->icon))) {
                        $resourceIcon = "<img src='" . asset($resource->icon) . "' alt='{$resource->name}' class='w-3 h-3 inline-block'>";
                    }

                    // Отображаем сообщение о полученном ресурсе в стиле обелиска
                    $resourceMessage = $logFormatter->formatMobDropReward(
                        $resourceIcon, // Иконка ресурса (реальная или по умолчанию)
                        "{$quantity} {$resource->name}", // Количество и название ресурса
                        "{$style} font-medium", // Стиль в зависимости от редкости
                        true // Включать название ресурса
                    );
                    $this->battleLogService->addLog($battleLogKey, $resourceMessage, 'success');

                    \Log::info("Добавлен лог о выпадении ресурса в боевой лог (стиль обелиска)", [
                        'user_id' => $user->id,
                        'resource_id' => $resourceId,
                        'resource_name' => $resource->name,
                        'resource_rarity' => $resource->rarity,
                        'quantity' => $quantity
                    ]);
                }
            }
        }

        // Логируем выданные алхимические ингредиенты в стиле обелиска
        if (isset($rewardResults['given']['alchemy_ingredients']) && !empty($rewardResults['given']['alchemy_ingredients'])) {
            foreach ($rewardResults['given']['alchemy_ingredients'] as $ingredientId => $quantity) {
                $ingredient = \App\Models\AlchemyIngredient::find($ingredientId);
                if ($ingredient) {
                    // Получаем иконку ингредиента или используем эмодзи по умолчанию
                    $ingredientIcon = "🧪"; // Иконка по умолчанию
                    // TODO: В будущем можно добавить получение реальной иконки ингредиента из базы данных

                    // Отображаем сообщение о полученном ингредиенте в стиле обелиска
                    $ingredientLog = $logFormatter->formatMobDropReward(
                        $ingredientIcon, // Иконка ингредиента
                        "{$quantity} {$ingredient->name}", // Количество и название ингредиента
                        "text-purple-400 font-medium", // Стиль для алхимических ингредиентов
                        true // Включать название ингредиента
                    );
                    $this->battleLogService->addLog($battleLogKey, $ingredientLog, 'success');

                    \Log::info("Добавлен лог о выпадении алхимического ингредиента в боевой лог (стиль обелиска)", [
                        'user_id' => $user->id,
                        'ingredient_id' => $ingredientId,
                        'ingredient_name' => $ingredient->name,
                        'quantity' => $quantity
                    ]);
                }
            }
        }

        // Логируем выданную валюту из дропа в стиле обелиска
        if (isset($rewardResults['given']['currency']) && !empty($rewardResults['given']['currency'])) {
            foreach ($rewardResults['given']['currency'] as $currencyType => $amount) {
                if ($amount > 0) {
                    // Определяем реальную иконку и название валюты (в стиле обелиска)
                    $currencyIcons = [
                        'gold' => asset('assets/goldIcon.png'),
                        'silver' => asset('assets/silverIcon.png'),
                        'bronze' => asset('assets/bronzeIcon.png')
                    ];

                    $currencyNames = [
                        'gold' => 'золота',
                        'silver' => 'серебра',
                        'bronze' => 'бронзы'
                    ];

                    // Создаем HTML для иконки валюты
                    $iconPath = $currencyIcons[$currencyType] ?? null;
                    if ($iconPath) {
                        $icon = "<img src='{$iconPath}' alt='{$currencyType}' class='w-3 h-3 inline-block'>";
                    } else {
                        $icon = '💰'; // Иконка по умолчанию
                    }
                    $name = $currencyNames[$currencyType] ?? $currencyType;

                    // Отображаем сообщение о полученной валюте в стиле обелиска
                    $currencyMessage = $logFormatter->formatMobDropReward(
                        $icon, // Иконка валюты (реальная)
                        "{$amount} {$name}", // Количество и название валюты
                        "text-yellow-400 font-medium", // Стиль для валюты
                        true // Включать название валюты
                    );
                    $this->battleLogService->addLog($battleLogKey, $currencyMessage, 'success');

                    \Log::info("Добавлен лог о выпадении валюты в боевой лог (стиль обелиска)", [
                        'user_id' => $user->id,
                        'currency_type' => $currencyType,
                        'amount' => $amount
                    ]);
                }
            }
        }
    }

    /**
     * Получает HTML-код иконки предмета или иконку по умолчанию
     *
     * @param array $item Массив с данными предмета
     * @return string HTML-код иконки или иконка по умолчанию
     */
    private function getItemIcon(array $item): string
    {
        // Если есть item_id, загружаем предмет из базы данных для получения иконки
        if (isset($item['item_id'])) {
            $baseItem = \App\Models\Item::find($item['item_id']);
            if ($baseItem && isset($baseItem->icon) && $baseItem->icon && file_exists(public_path($baseItem->icon))) {
                return "<img src='" . asset($baseItem->icon) . "' alt='{$baseItem->name}' class='w-3 h-3 inline-block'>";
            }
        }

        // Проверяем, есть ли иконка у предмета напрямую
        if (isset($item['icon']) && $item['icon'] && file_exists(public_path($item['icon']))) {
            return "<img src='" . asset($item['icon']) . "' alt='{$item['name']}' class='w-3 h-3 inline-block'>";
        }

        // Если иконки нет, используем иконку по умолчанию (не эмодзи)
        $defaultIcon = 'assets/items/default.png';
        if (file_exists(public_path($defaultIcon))) {
            return "<img src='" . asset($defaultIcon) . "' alt='{$item['name']}' class='w-3 h-3 inline-block'>";
        }

        // Если даже дефолтной иконки нет, используем эмодзи как последний вариант
        return '🎁';
    }

    /**
     * Определяет правильный ID локации из таблицы locations
     * Если передан ID из outpost_locations, возвращает связанный location_id
     *
     * @param int $id ID, который может быть из outpost_locations или locations
     * @return int Правильный ID из таблицы locations
     */
    private function resolveLocationId($id)
    {
        // Сначала проверяем, есть ли ID в таблице outpost_locations
        $outpostLocation = \App\Models\OutpostLocation::find($id);
        if ($outpostLocation && $outpostLocation->location_id) {
            // Если это пользовательский аванпост, возвращаем связанный location_id
            return $outpostLocation->location_id;
        }

        // Если не найден в outpost_locations, возвращаем исходный ID
        // (предполагаем, что это ID из таблицы locations)
        return $id;
    }
}

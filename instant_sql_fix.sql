-- МГНОВЕННОЕ ИСПРАВЛЕНИЕ ВСЕХ ПРОБЛЕМ С БОТАМИ В ПОДЛОКАЦИИ xzxzxzx
-- Исправляет несоответствия между админкой и компонентом статуса фракций

-- 1. Получаем информацию о подлокации
SELECT 
    'ПОДЛОКАЦИЯ ИНФОРМАЦИЯ' as section,
    ml.id as mine_location_id,
    ml.name as mine_location_name,
    ml.location_id as base_location_id,
    l.name as base_location_name,
    ml.is_active
FROM mine_locations ml
LEFT JOIN locations l ON ml.location_id = l.id
WHERE ml.name = 'xzxzxzx';

-- 2. ТЕКУЩЕЕ СОСТОЯНИЕ: Что показывает админка
SELECT 
    'АДМИНКА - ТЕКУЩЕЕ СОСТОЯНИЕ' as section,
    COUNT(*) as total_bots,
    COUNT(CASE WHEN is_active = 1 AND hp > 0 THEN 1 END) as active_bots,
    GROUP_CONCAT(DISTINCT name ORDER BY name) as bot_names
FROM bots 
WHERE location = 'xzxzxzx' 
  AND created_by_admin = 1;

-- 3. ПОИСК ВСЕХ СКРЫТЫХ БОТОВ
SELECT 
    'СКРЫТЫЕ БОТЫ' as section,
    id,
    name,
    location,
    mine_location_id,
    is_active,
    hp,
    max_hp,
    race,
    class,
    CASE 
        WHEN location = 'xzxzxzx' THEN 'В АДМИНКЕ'
        WHEN location REGEXP '^[0-9]+$' THEN 'ID ЛОКАЦИИ'
        ELSE 'НЕПРАВИЛЬНАЯ ЛОКАЦИЯ'
    END as status
FROM bots 
WHERE created_by_admin = 1
  AND (
    name IN ('nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм')
    OR location = (SELECT CAST(id AS CHAR) FROM mine_locations WHERE name = 'xzxzxzx')
    OR mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx')
  )
ORDER BY name;

-- 4. ИСПРАВЛЕНИЕ 1: Боты по именам атакующих
UPDATE bots 
SET location = 'xzxzxzx',
    mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx'),
    next_action_time = NULL,
    updated_at = NOW()
WHERE name IN ('nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм')
  AND (location != 'xzxzxzx' OR mine_location_id != (SELECT id FROM mine_locations WHERE name = 'xzxzxzx'));

-- 5. ИСПРАВЛЕНИЕ 2: Боты с ID локации
UPDATE bots b
SET location = 'xzxzxzx',
    mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx'),
    next_action_time = NULL,
    updated_at = NOW()
WHERE b.location = (SELECT CAST(id AS CHAR) FROM mine_locations WHERE name = 'xzxzxzx')
  AND b.created_by_admin = 1;

-- 6. ИСПРАВЛЕНИЕ 3: Боты с правильным mine_location_id, но неправильной location
UPDATE bots b
SET location = 'xzxzxzx',
    next_action_time = NULL,
    updated_at = NOW()
WHERE b.mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx')
  AND b.location != 'xzxzxzx'
  AND b.created_by_admin = 1;

-- 7. ИСПРАВЛЕНИЕ 4: Боты в правильной location, но без mine_location_id
UPDATE bots b
SET mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx'),
    next_action_time = NULL,
    updated_at = NOW()
WHERE b.location = 'xzxzxzx'
  AND (b.mine_location_id IS NULL OR b.mine_location_id != (SELECT id FROM mine_locations WHERE name = 'xzxzxzx'))
  AND b.created_by_admin = 1;

-- 8. ПРОВЕРКА ПОСЛЕ ИСПРАВЛЕНИЯ: Админка
SELECT 
    'АДМИНКА - ПОСЛЕ ИСПРАВЛЕНИЯ' as section,
    COUNT(*) as total_bots,
    COUNT(CASE WHEN is_active = 1 AND hp > 0 THEN 1 END) as active_bots,
    COUNT(CASE WHEN race = 'solarius' THEN 1 END) as solarius_bots,
    COUNT(CASE WHEN race = 'lunarius' THEN 1 END) as lunarius_bots,
    GROUP_CONCAT(DISTINCT name ORDER BY name) as bot_names
FROM bots 
WHERE location = 'xzxzxzx' 
  AND created_by_admin = 1;

-- 9. ПРОВЕРКА: Подсчет по классам (как в компоненте)
SELECT 
    'ПОДСЧЕТ ПО КЛАССАМ' as section,
    race,
    class,
    COUNT(*) as count,
    GROUP_CONCAT(name ORDER BY name) as bot_names
FROM bots 
WHERE location = 'xzxzxzx' 
  AND created_by_admin = 1
  AND is_active = 1
  AND hp > 0
GROUP BY race, class
ORDER BY race, class;

-- 10. ПРОВЕРКА: Соответствие данных
SELECT 
    'ПРОВЕРКА СООТВЕТСТВИЯ' as section,
    COUNT(*) as total_bots,
    COUNT(CASE WHEN mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx') THEN 1 END) as correct_mine_id,
    COUNT(CASE WHEN location = 'xzxzxzx' THEN 1 END) as correct_location,
    COUNT(CASE WHEN location = 'xzxzxzx' AND mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx') THEN 1 END) as fully_correct
FROM bots 
WHERE created_by_admin = 1
  AND (location = 'xzxzxzx' OR mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx'));

-- 11. ПРОВЕРКА: Атакующие боты
SELECT 
    'АТАКУЮЩИЕ БОТЫ' as section,
    name,
    location,
    mine_location_id,
    is_active,
    hp,
    max_hp,
    race,
    class,
    CASE 
        WHEN location = 'xzxzxzx' AND mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx') THEN '✅ ПРАВИЛЬНО'
        WHEN location = 'xzxzxzx' THEN '⚠️ НЕТ MINE_ID'
        WHEN mine_location_id = (SELECT id FROM mine_locations WHERE name = 'xzxzxzx') THEN '⚠️ НЕПРАВИЛЬНАЯ ЛОКАЦИЯ'
        ELSE '❌ НЕПРАВИЛЬНО'
    END as status
FROM bots 
WHERE name IN ('nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм')
ORDER BY name;

-- 12. ИТОГОВАЯ СТАТИСТИКА
SELECT 
    'ИТОГОВАЯ СТАТИСТИКА' as section,
    (SELECT COUNT(*) FROM bots WHERE location = 'xzxzxzx' AND created_by_admin = 1) as bots_in_admin,
    (SELECT COUNT(*) FROM bots WHERE location = 'xzxzxzx' AND created_by_admin = 1 AND is_active = 1 AND hp > 0) as active_bots_in_admin,
    (SELECT COUNT(*) FROM bots WHERE name IN ('nvvng', 'nvvng2', 'вффвфц', 'cccccc', 'мммиммм') AND location = 'xzxzxzx') as attacking_bots_fixed,
    (SELECT COUNT(*) FROM bots WHERE location REGEXP '^[0-9]+$' AND created_by_admin = 1) as bots_with_id_location_remaining,
    NOW() as fix_timestamp;